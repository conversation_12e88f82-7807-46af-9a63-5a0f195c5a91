using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Data;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Repositories;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NSubstitute;

namespace Hubtel.CCI.Api.Tests.Components;

public class DiFixture
{
    public ServiceProvider ServiceProvider { get; private set; }
    public IActorService<MainActor> MainActorService { get; private set; }
    
    public ILookupService LookupService { get; private set; }

    public IProductTeamService ProductTeamService { get; private set; }
    public IHostEnvironment HostEnvironment { get; private set; }

    public IPdfGenerator PdfGenerator { get; private set; }
    
    public IAzureProxyService AzureProxyService { get; private set; }

    public IBaseRepository<Engineer> EngineerRepository { get; private set; }
    public IBaseRepository<DeploymentRequest> DeploymentRequestRepository { get; private set; }
    public IBaseRepository<ProductTeam> ProductTeamRepository { get; private set; }
    public IBaseRepository<Repository> RepositoryRepository { get; private set; }
    public IBaseRepository<ProductGroup> ProductGroupRepository { get; private set; }
    public IBaseRepository<CciRepositoryScore> CciRepositoryScoreRepository { get; private set; }
    public IBaseRepository<CciProductsRanking> CciProductsRankingRepository { get; private set; }
    public IBaseRepository<CciProductTeamsScoreTrend> CciProductTeamsScoreTrendRepository { get; private set; }

    public IBaseRepository<CciRepositoryScoreStatistic> CciRepositoryScoreStatisticRepository { get; private set; }

    public IBaseRepository<CciRoadMap> CciRoadMapRepository { get; private set; }

    public IBaseRepository<CciRoadMapRecord> CciRoadMapRecordRepository { get; private set; }

    public IBaseRepository<CciRoadMapMetricUpdateRecord> CciRoadMapMetricUpdateRecordRepository { get; private set; }

    public IBaseRepository<BypassedPr> BypassedPrRepository { get; private set; }

    public IBaseRepository<RepositorySyncLogBp> RepositorySyncLogBpRepository { get; private set; }

    public IBaseRepository<IssueTracker> IssueTrackerRepository { get; private set; }
    public IBaseRepository<ToolingTool> ToolingToolsRepository { get; private set; }
    public IBaseRepository<ToolingReport> ToolingReportRepository { get; private set; }

    public IRepositoryContext RepositoryContext { get; private set; }

    public IBypassedPrService BypassedPrService { get; private set; }

    public IBaseRepository<Service> ServiceRepository { get; private set; }
    
    public IBaseRepository<DciServiceScore> DciServiceScoreRepository { get; private set; }
    
    public IBaseRepository<DciProductsRanking> DciProductsRankingRepository { get; private set; }
    
    public IBaseRepository<ServiceStatistic> ServiceStatisticRepository { get; private set; }


    public string ValidPhone = "233245936308";

    public IConfiguration Configuration { get; set; }

    public DiFixture()
    {
        var services = new ServiceCollection();
        ServiceProvider = services.BuildServiceProvider();
        services.AddSingleton((sp) => ConfigurationManager.GetConfiguration());
        var config = ConfigurationManager.GetConfiguration();
        Configuration = config;
        ProductTeamRepository = Substitute.For<IBaseRepository<ProductTeam>>();
        ProductGroupRepository = Substitute.For<IBaseRepository<ProductGroup>>();
        RepositoryRepository = Substitute.For<IBaseRepository<Repository>>();
        EngineerRepository = Substitute.For<IBaseRepository<Engineer>>();
        DeploymentRequestRepository = Substitute.For<IBaseRepository<DeploymentRequest>>();
        CciRepositoryScoreStatisticRepository = Substitute.For<IBaseRepository<CciRepositoryScoreStatistic>>();
        RepositoryContext = Substitute.For<IRepositoryContext>();
        CciRepositoryScoreRepository = Substitute.For<IBaseRepository<CciRepositoryScore>>();
        CciProductsRankingRepository = Substitute.For<IBaseRepository<CciProductsRanking>>();
        CciProductTeamsScoreTrendRepository = Substitute.For<IBaseRepository<CciProductTeamsScoreTrend>>();
        LookupService = Substitute.For<ILookupService>();
        MainActorService = Substitute.For<IActorService<MainActor>>();
        ProductTeamService = Substitute.For<IProductTeamService>();
        CciRoadMapRepository = Substitute.For<IBaseRepository<CciRoadMap>>();
        CciRoadMapMetricUpdateRecordRepository = Substitute.For<IBaseRepository<CciRoadMapMetricUpdateRecord>>();
        BypassedPrRepository = Substitute.For<IBaseRepository<BypassedPr>>();
        RepositorySyncLogBpRepository = Substitute.For<IBaseRepository<RepositorySyncLogBp>>();
        BypassedPrService = Substitute.For<IBypassedPrService>();
        CciRoadMapRecordRepository = Substitute.For<IBaseRepository<CciRoadMapRecord>>();
        ServiceRepository = Substitute.For<IBaseRepository<Service>>();
        HostEnvironment = Substitute.For<IHostEnvironment>();
        PdfGenerator = Substitute.For<IPdfGenerator>();
        IssueTrackerRepository = Substitute.For<IBaseRepository<IssueTracker>>();
        ToolingToolsRepository = Substitute.For<IBaseRepository<ToolingTool>>();
        ToolingReportRepository = Substitute.For<IBaseRepository<ToolingReport>>();
        DciServiceScoreRepository = Substitute.For<IBaseRepository<DciServiceScore>>();
        DciProductsRankingRepository = Substitute.For<IBaseRepository<DciProductsRanking>>();
        ServiceStatisticRepository = Substitute.For<IBaseRepository<ServiceStatistic>>();
        AzureProxyService = Substitute.For<IAzureProxyService>();

        RepositoryContext.EngineerRepository.Returns(EngineerRepository);
        RepositoryContext.ProductGroupRepository.Returns(ProductGroupRepository);
        RepositoryContext.ProductTeamRepository.Returns(ProductTeamRepository);
        RepositoryContext.RepositoryRepository.Returns(RepositoryRepository);
        RepositoryContext.CciRepositoryScoreRepository.Returns(CciRepositoryScoreRepository);
        RepositoryContext.CciProductsRankingRepository.Returns(CciProductsRankingRepository);
        RepositoryContext.CciProductTeamsScoreTrendRepository.Returns(CciProductTeamsScoreTrendRepository);
        RepositoryContext.CciRepositoryScoreStatisticRepository.Returns(CciRepositoryScoreStatisticRepository);
        RepositoryContext.CciRoadMapRepository.Returns(CciRoadMapRepository);
        RepositoryContext.CciRoadMapMetricUpdateRecordRepository.Returns(CciRoadMapMetricUpdateRecordRepository);
        RepositoryContext.BypassedPrRepository.Returns(BypassedPrRepository);
        RepositoryContext.RepositorySyncLogBpRepository.Returns(RepositorySyncLogBpRepository);
        RepositoryContext.CciRoadMapRecordRepository.Returns(CciRoadMapRecordRepository);
        RepositoryContext.ServiceRepository.Returns(ServiceRepository);
        RepositoryContext.IssueTrackerRepository.Returns(IssueTrackerRepository);
        RepositoryContext.ToolingToolsRepository.Returns(ToolingToolsRepository);
        RepositoryContext.ToolingReportRepository.Returns(ToolingReportRepository);
        RepositoryContext.DciServiceScoreRepository.Returns(DciServiceScoreRepository);
        RepositoryContext.DciProductsRankingRepository.Returns(DciProductsRankingRepository);
        RepositoryContext.ServiceStatisticRepository.Returns(ServiceStatisticRepository);
    }

    public void ResetSharedResources()
    {
        var config = ConfigurationManager.GetConfiguration();
        Configuration = config;
        var services = new ServiceCollection();
        services.AddSingleton((sp) => ConfigurationManager.GetConfiguration());
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(config.GetConnectionString("DbConnection")));
        ServiceProvider = services.BuildServiceProvider();
    }
}