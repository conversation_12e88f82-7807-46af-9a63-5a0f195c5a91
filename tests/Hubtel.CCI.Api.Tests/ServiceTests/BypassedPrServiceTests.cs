using Akka.Actor;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MockQueryable.NSubstitute;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NSubstitute.ReturnsExtensions;
using Xunit;
using Repository = Hubtel.CCI.Api.Data.Entities.Repository;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class BypassedPrServiceTests
{
    private readonly BypassedPrService _bypassedPrService;
    private readonly DiFixture _fixture;

    public BypassedPrServiceTests()
    {
        IOptions<AzureOrgConfig> azureOrgConfig = Microsoft.Extensions.Options.Options.Create(new AzureOrgConfig()
        {
            Organization = "hubtel",
            AccessToken = "token",
            DevUrl = "http://localhost:6000",
            Projects = ["Back-End"]
        });
        _fixture = new DiFixture();
        _bypassedPrService = new BypassedPrService(
            Substitute.For<ILogger<BypassedPrService>>(), 
            _fixture.RepositoryContext,
            _fixture.MainActorService,
            azureOrgConfig);
    }


    #region Get By Id Async

    [Fact]
    public async Task GetByIdAsync_Should_Return_NotFound_Api_Response_When_Bypassed_Pr_Is_Not_Found()
    {
        // Arrange
        const string id = "invalidId";
        
        _fixture.RepositoryContext.BypassedPrRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .ReturnsNull();
        
        // Act
        var result = await _bypassedPrService.GetByIdAsync(id, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }
    
    
    [Fact]
    public async Task GetByIdAsync_Should_Return_OK_Api_Response_When_Bypassed_Pr_Is_Found()
    {
        // Arrange
        const string id = "invalidId";
        
        _fixture.RepositoryContext.BypassedPrRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((BypassedPr?)new BypassedPr());
        
        // Act
        var result = await _bypassedPrService.GetByIdAsync(id, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }

    #endregion

    #region TriggerByPassedPrsAsync
    
    [Fact]
    public async Task TriggerBypassedPrsAsync_Should_Return_OK_Api_Response_When_Called()
    {
        // Arrange
        _fixture.MainActorService
            .When(x => x.Tell(Arg.Any<AzureByPassedPrMessage>(), ActorRefs.Nobody))
            .Do(callInfo => { /* You can add any additional logic here if needed */ });
        
        // Act
        var result = await _bypassedPrService.TriggerBypassedPrsAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().BeTrue();
    }

    #endregion

    #region Execute Bypassed Prs Flow Async

    [Fact]
    public async Task ExecuteBypassedPrsFlowAsync_Should_Return_False_When_An_Exception_Occurs()
    {
        // Arrange
        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable().Throws(new Exception("Simulated Exception"));
        
        using var httpTest = new HttpTest();

        var repositories = new RepositoryListResponse()
        {
            Value = new List<AzureRepository>()
            {
                new AzureRepository()
                {
                    Id = "1",
                    Name = "Repo 1",
                    WebUrl = "repo1.com"
                }
            }
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(repositories),
            200);
        
        // Act
        var result = await _bypassedPrService.ExecuteBypassedPrsFlowAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ExecuteBypassedPrsFlowAsync_Should_Return_True_When_Processing_Completed()
    {
        // Arrange
        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(new List<RepositorySyncLogBp>()
            {
                new RepositorySyncLogBp(){LastSyncedAt = DateTime.Now.Date.AddMonths(-2)}
            }.AsQueryable().BuildMock());
        
        using var httpTest = new HttpTest();

        var repositories = new RepositoryListResponse()
        {
            Value = new List<AzureRepository>()
            {
                new AzureRepository()
                {
                    Id = "1",
                    Name = "Repo 1",
                    WebUrl = "repo1.com"
                }
            }
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(repositories),
            200);
        
        
        // Inner ProcessRepositoryAsync mocks
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
                    { new Repository(){Id="123", Name = "Repo1", Url = "repo1.com"} }
                .AsQueryable().BuildMock());
        

        var prRepositories = new PullRequestListResponse()
        {
            Value = new List<PullRequest>()
            {
                new PullRequest()
                {
                    PullRequestId = 1,
                    CodeReviewId = 12,
                    Status = "completed",
                    ClosedDate = DateTime.UtcNow.AddMonths(-3),
                    CompletionOptions = new CompletionOptions()
                    {
                        BypassPolicy = true,
                        BypassReason = "Reason"
                    }
                }
            }
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(prRepositories),
            200);
        
        _fixture.RepositoryContext.BypassedPrRepository
            .AddRangeAsync(Arg.Any<List<BypassedPr>>()).Returns(1);

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .AddAsync(Arg.Any<RepositorySyncLogBp>()).Returns(1);
        
        
        
        
        // Act
        var result = await _bypassedPrService.ExecuteBypassedPrsFlowAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }
    #endregion


    #region Process Repository Async

    [Fact]
    public async Task ProcessRepositoryAsync_Should_Return_True_When_Processing_Is_Done()
    {
        // Arrange
        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(new List<RepositorySyncLogBp>()
            {
                new RepositorySyncLogBp(){LastSyncedAt = DateTime.Now.Date.AddMonths(-2)}
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
                { new Repository(){Id="123", Name = "Repo1", Url = "repo1.com"} }
                .AsQueryable().BuildMock());
        
        using var httpTest = new HttpTest();

        var repositories = new PullRequestListResponse()
        {
            Value = new List<PullRequest>()
            {
                new PullRequest()
                {
                    PullRequestId = 1,
                    CodeReviewId = 12,
                    Status = "completed",
                    ClosedDate = DateTime.UtcNow.AddMonths(-3),
                    CompletionOptions = new CompletionOptions()
                    {
                        BypassPolicy = true,
                        BypassReason = "Reason"
                    }
                }
            }
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(repositories),
            200);
        
        _fixture.RepositoryContext.BypassedPrRepository
            .AddRangeAsync(Arg.Any<List<BypassedPr>>()).Returns(1);

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .AddAsync(Arg.Any<RepositorySyncLogBp>()).Returns(1);
        
        // Act
        var result = await _bypassedPrService.ProcessRepositoryAsync(
            "Back-End", new AzureRepositoryDto(), 
            CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }
    
    
    
    [Fact]
    public async Task ProcessRepositoryAsync_Should_Return_True_When_Processing_Is_Done_With_FlurlException()
    {
        // Arrange
        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(new List<RepositorySyncLogBp>()
            {
                new RepositorySyncLogBp(){LastSyncedAt = DateTime.Now.Date.AddMonths(-2)}
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
                { new Repository(){Id="123", Name = "Repo1", Url = "repo1.com"} }
                .AsQueryable().BuildMock());
        
        using var httpTest = new HttpTest();

        var repositories = new PullRequestListResponse()
        {
            Value = new List<PullRequest>()
            {
                new PullRequest()
                {
                    PullRequestId = 1,
                    CodeReviewId = 12,
                    Status = "completed",
                    ClosedDate = DateTime.UtcNow.AddMonths(-3),
                    CompletionOptions = new CompletionOptions()
                    {
                        BypassPolicy = true,
                        BypassReason = "Reason"
                    }
                }
            }
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(repositories),
            500);
        
        _fixture.RepositoryContext.BypassedPrRepository
            .AddRangeAsync(Arg.Any<List<BypassedPr>>()).Returns(1);

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .AddAsync(Arg.Any<RepositorySyncLogBp>()).Returns(1);
        
        // Act
        var result = await _bypassedPrService.ProcessRepositoryAsync(
            "Back-End", new AzureRepositoryDto(), 
            CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }
    

    #endregion


    #region Get Bypassed Prs Overview Async

    [Fact]
    public async Task GetBypassedPrsOverviewAsync_Should_Return_OK_Api_Response()
    {
        // Arrange
        _fixture.RepositoryContext.BypassedPrRepository
            .GetQueryable()
            .Returns(new List<BypassedPr>()
            {
                new BypassedPr()
                {
                    Id = "1",
                    PullRequestId = 1,
                    RepositoryId = "123",
                    CreationDate = DateTime.Now,
                    Repository = new PrRepository()
                    {
                        Name = "Repo 1"
                    }
                }
            }.AsQueryable().BuildMock());
        
        // Act
        var result = await _bypassedPrService.GetBypassedPrsOverviewAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    [Fact]
    public async Task GetBypassedPrsOverviewAsync_Should_Sort_DailySummary_By_Date_Descending()
    {
        // Arrange
        var now = DateTime.Now;
        var data = new List<BypassedPr>
        {
            new BypassedPr { CreationDate = now.AddDays(-1), Repository = new PrRepository { Name = "Repo1" } },
            new BypassedPr { CreationDate = now, Repository = new PrRepository { Name = "Repo1" } }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.BypassedPrRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsOverviewAsync();

        // Assert
        using var scope = new AssertionScope();
        var dailySummary = result.Data?.DailySummary;
        dailySummary.Should().NotBeNullOrEmpty();
        dailySummary.Should().BeInDescendingOrder(x => x.Date);
    }
    
    
    [Fact]
    public async Task GetBypassedPrsOverviewAsync_Should_Sort_RepositorySummary_By_TotalCount_Descending()
    {
        // Arrange
        var today = DateTime.Today;
        var data = new List<BypassedPr>
        {
            new BypassedPr { Repository = new PrRepository { Name = "Repo1" }, CreationDate = today },
            new BypassedPr { Repository = new PrRepository { Name = "Repo1" }, CreationDate = today },
            new BypassedPr { Repository = new PrRepository { Name = "Repo2" }, CreationDate = today }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.BypassedPrRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsOverviewAsync();

        // Assert
        using var scope = new AssertionScope();
        result.Data?.RepositorySummary.Should().NotBeNullOrEmpty();
        result.Data?.RepositorySummary.Should().BeInDescendingOrder(r => r.TotalCount);
    }
    
    
    [Fact]
    public async Task GetBypassedPrsOverviewAsync_Should_Sort_RepositoryDailyCounts_By_Date_Descending()
    {
        // Arrange
        var today = DateTime.Today;
        var data = new List<BypassedPr>
        {
            new BypassedPr { Repository = new PrRepository { Name = "Repo1" }, CreationDate = today },
            new BypassedPr { Repository = new PrRepository { Name = "Repo1" }, CreationDate = today.AddDays(-1) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.BypassedPrRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsOverviewAsync();

        // Assert
        using var scope = new AssertionScope();
        var dailyCounts = result.Data?.RepositorySummary?.First().DailyCounts;
        dailyCounts.Should().NotBeNullOrEmpty();
        dailyCounts.Should().BeInDescendingOrder(d => d.Date);
    }
    
    
    [Fact]
    public async Task GetBypassedPrsOverviewAsync_Should_Exclude_Records_With_Null_RepoName_Or_CreationDate()
    {
        // Arrange
        var valid = new BypassedPr
        {
            Repository = new PrRepository { Name = "Repo1" },
            CreationDate = DateTime.Today
        };
        var nullName = new BypassedPr
        {
            Repository = new PrRepository { Name = null },
            CreationDate = DateTime.Today
        };
        var nullDate = new BypassedPr
        {
            Repository = new PrRepository { Name = "Repo2" },
            CreationDate = null
        };

        var data = new List<BypassedPr> { valid, nullName, nullDate }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.BypassedPrRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsOverviewAsync();

        // Assert
        using var scope = new AssertionScope();
        var summary = result.Data?.RepositorySummary;
        summary.Should().HaveCount(1);
        summary?[0].RepositoryName.Should().Be("Repo1");
    }





    #endregion


    #region Get Bypassed Prs Sync Logs Overview Async

    [Fact]
    public async Task GetBypassedPrsSyncLogsOverviewAsync_Should_Return_OK_Api_Response()
    {
        // Arrange
        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(new List<RepositorySyncLogBp>()
            {
                new RepositorySyncLogBp()
                {
                    Id = "1",
                    RepositoryId = "123",
                    LastSyncedAt = DateTime.Now,
                    RepositoryName = "Repo 1"
                }
            }.AsQueryable().BuildMock());
        
        // Act
        var result = await _bypassedPrService.GetBypassedPrsSyncLogsOverviewAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    [Fact]
    public async Task GetBypassedPrsSyncLogsOverviewAsync_Should_Sort_DailySummary_By_Date_Descending()
    {
        // Arrange
        var now = DateTime.Today;
        var data = new List<RepositorySyncLogBp>
        {
            new RepositorySyncLogBp { LastSyncedAt = now.AddDays(-1), RepositoryName = "Repo1" },
            new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = "Repo1" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsSyncLogsOverviewAsync();

        // Assert
        result.Data?.DailySummary.Should().BeInDescendingOrder(x => x.Date);
    }

    [Fact]
    public async Task GetBypassedPrsSyncLogsOverviewAsync_Should_Sort_RepositorySummary_By_TotalCount_Descending()
    {
        // Arrange
        var now = DateTime.Today;
        var data = new List<RepositorySyncLogBp>
        {
            new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = "Repo1" },
            new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = "Repo1" },
            new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = "Repo2" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsSyncLogsOverviewAsync();

        // Assert
        result.Data?.RepositorySummary.Should().BeInDescendingOrder(x => x.TotalCount);
    }

    
    [Fact]
    public async Task GetBypassedPrsSyncLogsOverviewAsync_Should_Sort_Repository_DailyCounts_By_Date_Descending()
    {
        // Arrange
        var now = DateTime.Today;
        var data = new List<RepositorySyncLogBp>
        {
            new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = "Repo1" },
            new RepositorySyncLogBp { LastSyncedAt = now.AddDays(-1), RepositoryName = "Repo1" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsSyncLogsOverviewAsync();

        // Assert
        var nested = result.Data?.RepositorySummary?.First().DailyCounts;
        nested.Should().BeInDescendingOrder(x => x.Date);
    }

    
    [Fact]
    public async Task GetBypassedPrsSyncLogsOverviewAsync_Should_Exclude_Records_With_Null_RepoName_Or_LastSyncedAt()
    {
        // Arrange
        var now = DateTime.Today;
        var valid = new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = "Repo1" };
        var nullName = new RepositorySyncLogBp { LastSyncedAt = now, RepositoryName = null };
        var nullDate = new RepositorySyncLogBp { LastSyncedAt = null, RepositoryName = "Repo2" };

        var data = new List<RepositorySyncLogBp> { valid, nullName, nullDate }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.RepositorySyncLogBpRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _bypassedPrService.GetBypassedPrsSyncLogsOverviewAsync();

        // Assert
        var summary = result.Data?.RepositorySummary;
        using var scope = new AssertionScope();
        summary.Should().ContainSingle();
        summary?[0].RepositoryName.Should().Be("Repo1");
    }


    #endregion


    #region Execute Clear ByPassed Pr Records Async

    [Fact]
    public async Task ExecuteClearBypassedPrRecordsAsync_Should_Return_True_When_Processing_Is_Done()
    {
        // Arrange
        _fixture.RepositoryContext.BypassedPrRepository
            .RemoveRangeAsync(Arg.Any<IQueryable<BypassedPr>>()).Returns(1);
        
        // Act
        var result = await _bypassedPrService.ExecuteClearBypassedPrRecordsAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().BeTrue();
    }

    #endregion


    #region FetchCompletedPullRequests

    [Fact]
    public async Task FetchCompletedPullRequests_Should_Only_Return_Prs_Within_Date_Range()
    {
        // Arrange
        using var httpTest = new HttpTest();
        var from = DateTime.UtcNow.AddDays(-10);
        var to = DateTime.UtcNow;

        var prInRange = new PullRequest { CreationDate = from.AddDays(1), ClosedDate = to.AddDays(-1) };
        var prTooOld = new PullRequest { CreationDate = from.AddDays(-2), ClosedDate = to.AddDays(-1) };
        var prTooNew = new PullRequest { CreationDate = from.AddDays(1), ClosedDate = to.AddDays(1) };

        httpTest.RespondWithJson(new PullRequestListResponse
        {
            Value = [prInRange, prTooOld, prTooNew]
        }, 200);

        var result = new List<PullRequest>();
        await foreach (var pr in _bypassedPrService.FetchCompletedPullRequests(from, to, "project", "repo", CancellationToken.None))
        {
            result.Add(pr);
        }

        // Assert
        using var scope = new AssertionScope();
        result.Should().ContainSingle();
        result[0].CreationDate.Should().Be(prInRange.CreationDate);
    }
    
    [Fact]
    public async Task FetchCompletedPullRequests_Should_Fetch_Multiple_Pages_When_ContinuationToken_Is_Present()
    {
        // Arrange
        using var httpTest = new HttpTest();
        var from = DateTime.UtcNow.AddDays(-5);
        var to = DateTime.UtcNow;

        var prPage1 = new PullRequest { CreationDate = from.AddDays(1), ClosedDate = to.AddDays(-1) };
        var prPage2 = new PullRequest { CreationDate = from.AddDays(2), ClosedDate = to.AddDays(-1) };

        httpTest
            .RespondWithJson(new PullRequestListResponse
            {
                Value = [prPage1]
            }, 200, new { x_ms_continuationtoken = "next-token" });

        httpTest
            .RespondWithJson(new PullRequestListResponse
            {
                Value = [prPage2]
            }, 200);

        var results = new List<PullRequest>();
        await foreach (var pr in _bypassedPrService.FetchCompletedPullRequests(from, to, "project", "repo", CancellationToken.None))
        {
            results.Add(pr);
        }

        // Assert
        using var scope = new AssertionScope();
        results.Should().HaveCount(2);
        httpTest.ShouldHaveCalled("*pullrequests*")
            .WithQueryParam("continuationToken", "next-token")
            .Times(1);
    }
    
    
    [Fact]
    public async Task FetchCompletedPullRequests_Should_Handle_FlurlHttpException_Gracefully()
    {
        // Arrange
        using var httpTest = new HttpTest();
        var from = DateTime.UtcNow.AddDays(-5);
        var to = DateTime.UtcNow;

        httpTest.RespondWith(string.Empty, 500); // Simulate error response

        var results = new List<PullRequest>();
        await foreach (var pr in _bypassedPrService.FetchCompletedPullRequests(from, to, "project", "repo", CancellationToken.None))
        {
            results.Add(pr);
        }

        // Assert
        results.Should().BeEmpty();
    }




    #endregion
}