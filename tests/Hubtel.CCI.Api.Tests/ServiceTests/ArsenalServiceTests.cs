using System.Linq.Expressions;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Repository;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using Xunit;


namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ArsenalServiceTests
{
    private readonly ArsenalService _arsenalService;
    private readonly DiFixture _fixture;

    public ArsenalServiceTests()
    {
        _fixture = new DiFixture();
        _arsenalService = new ArsenalService(Substitute.For<ILogger<ArsenalService>>(), _fixture.RepositoryContext,
            _fixture.MainActorService);
    }

    [Fact]
    public async Task GetRepositoriesAsync_Should_Return_PagedResult_When_Filter_Is_Valid()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "test" };

        var data = new[] { new Repository() { Name = "test", Description = "hello" } }.AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);
        // Act
        var result = await _arsenalService.GetRepositoriesAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().NotBeEmpty();
            result.Data?.Results.Count.Should().Be(1);
        }
    }
    
    
    [Fact]
    public async Task GetRepositoriesAsync_Should_Return_Repositories_Sorted_By_CreatedAt_Descending()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10 };

        var repo1 = new Repository { Name = "Repo1", CreatedAt = new DateTime(2024, 1, 1) };
        var repo2 = new Repository { Name = "Repo2", CreatedAt = new DateTime(2024, 2, 1) };
        var repo3 = new Repository { Name = "Repo3", CreatedAt = new DateTime(2024, 3, 1) };

        var data = new[] { repo1, repo2, repo3 }.AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalService.GetRepositoriesAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        var returned = result.Data?.Results!;
        returned.Should().HaveCount(3);
        returned[0].Name.Should().Be("Repo3");
        returned[1].Name.Should().Be("Repo2");
        returned[2].Name.Should().Be("Repo1");
    }


    [Fact]
    public async Task GetRepositoryAsync_Should_Return_Repository_When_Id_Is_Valid()
    {
        // Arrange
        var id = "validId";


        var data = new[] { new Repository() { Name = "t", Description = "hello", Id = id } }.AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalService.GetRepositoryAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
        }
    }

    [Fact]
    public async Task AddRepositoryAsync_Should_Add_Repository_When_Request_Is_Valid()
    {
        // Arrange
        var request = new CreateRepositoryRequest
        {
            /* Set properties here */
        };

        _fixture.RepositoryRepository.AddAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _arsenalService.AddRepositoryAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
        }
    }
    
    
    [Fact]
    public async Task AddRepositoryAsync_Should_Return_BadRequest_When_SonarQubeKey_Already_Exists()
    {
        // Arrange
        var request = new CreateRepositoryRequest
        {
            SonarQubeKey = "sonar-key-1",
            Url = "https://example.com/repo"
        };

        var existingRepo = new Repository
        {
            SonarQubeKey = "sonar-key-1",
            Url = "https://other-url.com"
        };

        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>(), Arg.Any<CancellationToken>())
            .Returns(existingRepo);

        // Act
        var result = await _arsenalService.AddRepositoryAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status400BadRequest);
        result.Message.Should().Contain("already exists");
    }
    
    
    [Fact]
    public async Task AddRepositoryAsync_Should_Return_BadRequest_When_Url_Already_Exists()
    {
        // Arrange
        var request = new CreateRepositoryRequest
        {
            SonarQubeKey = "unique-key",
            Url = "https://existing-url.com"
        };

        var existingRepo = new Repository
        {
            SonarQubeKey = "another-key",
            Url = "https://existing-url.com"
        };

        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>(), Arg.Any<CancellationToken>())
            .Returns(existingRepo);

        // Act
        var result = await _arsenalService.AddRepositoryAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status400BadRequest);
        result.Message.Should().Contain("already exists");
    }
    
    
    [Fact]
    public async Task AddRepositoryAsync_Should_Create_When_SonarQubeKey_And_Url_Are_Unique()
    {
        // Arrange
        var request = new CreateRepositoryRequest
        {
            SonarQubeKey = "unique-key",
            Url = "https://new-repo.com"
        };

        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>(), Arg.Any<CancellationToken>())
            .Returns((Repository?)null);

        _fixture.RepositoryRepository
            .AddAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(1); // Simulate save success

        // Act
        var result = await _arsenalService.AddRepositoryAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status201Created);
        result.Data.Should().NotBeNull();
    }




    [Fact]
    public async Task UpdateRepositoryAsync_Should_Update_Repository_When_Id_And_Request_Are_Valid()
    {
        // Arrange
        var id = "validId";
        var request = new UpdateRepositoryRequest
        {
            /* Set properties here */
        };

        _fixture.RepositoryRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((Repository?)new Repository());

        _fixture.RepositoryRepository.UpdateAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }
    
    [Fact]
    public async Task UpdateRepositoryAsync_Should_Return_FailedDependency_When_Url_Already_Exists()
    {
        // Arrange
        var id = "repo-id";
        var request = new UpdateRepositoryRequest
        {
            Url = "https://new-url.com"
        };

        var existingRepo = new Repository
        {
            Id = id,
            Url = "https://old-url.com"
        };

        var duplicateRepo = new Repository
        {
            Id = "another-id",
            Url = "https://new-url.com"
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingRepo);
        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Is<Expression<Func<Repository, bool>>>(expr => expr.Compile()(duplicateRepo)), Arg.Any<CancellationToken>())
            .Returns(duplicateRepo);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
        result.Message.Should().Contain("Url already exists");
    }
    
    
    [Fact]
    public async Task UpdateRepositoryAsync_Should_Skip_Url_Update_When_Url_Is_Null()
    {
        // Arrange
        var id = "repo-id";
        var request = new UpdateRepositoryRequest
        {
            Url = null // No update intended
        };

        var existingRepo = new Repository
        {
            Id = id,
            Url = "https://unchanged.com"
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingRepo);
        _fixture.RepositoryRepository.UpdateAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task UpdateRepositoryAsync_Should_Skip_Url_Update_When_Url_Is_Same_As_Existing()
    {
        // Arrange
        var id = "repo-id";
        var sameUrl = "https://same.com";
        var request = new UpdateRepositoryRequest
        {
            Url = sameUrl
        };

        var existingRepo = new Repository
        {
            Id = id,
            Url = sameUrl
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingRepo);
        _fixture.RepositoryRepository.UpdateAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task UpdateRepositoryAsync_Should_Only_Return_FailedDependency_When_Repository_With_Exact_Url_Exists()
    {
        // Arrange
        var id = "repo-id";
        var request = new UpdateRepositoryRequest
        {
            Url = "https://existing.com"
        };

        var existingRepo = new Repository
        {
            Id = id,
            Url = "https://old.com"
        };

        var conflictingRepo = new Repository
        {
            Id = "another-id",
            Url = "https://existing.com"
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingRepo);
        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Is<Expression<Func<Repository, bool>>>(expr => expr.Compile()(conflictingRepo)), Arg.Any<CancellationToken>())
            .Returns(conflictingRepo);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Message.Should().Contain("Url already exists");
    }
    
    
    [Fact]
    public async Task UpdateRepositoryAsync_Should_Return_NotFound_When_Update_Affects_No_Rows()
    {
        // Arrange
        var id = "repo-id";
        var request = new UpdateRepositoryRequest
        {
            Name = "Updated Name"
        };

        var existingRepo = new Repository
        {
            Id = id,
            Name = "Old Name"
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingRepo);
        _fixture.RepositoryRepository.UpdateAsync(existingRepo, Arg.Any<CancellationToken>()).Returns(0);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Data.Should().BeFalse();
    }
    
    
    [Fact]
    public async Task UpdateRepositoryAsync_Should_Fail_When_Same_SonarQubeKey_Already_Exists_In_Another_Repository()
    {
        // Arrange
        var id = "repo-id";
        var request = new UpdateRepositoryRequest
        {
            SonarQubeKey = "duplicate-key"
        };

        var existingRepo = new Repository
        {
            Id = id,
            SonarQubeKey = "old-key"
        };

        var conflictingRepo = new Repository
        {
            Id = "different-repo-id",
            SonarQubeKey = "duplicate-key"
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingRepo);
        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Is<Expression<Func<Repository, bool>>>(expr => expr.Compile()(conflictingRepo)), Arg.Any<CancellationToken>())
            .Returns(conflictingRepo);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
        result.Message.Should().Contain("SonarQubeKey already exists");
    }


    [Fact]
    public async Task UpdateRepositoryAsync_Should_Skip_SonarQubeKey_Check_When_Key_Is_Same_As_Existing()
    {
        // Arrange
        var id = "repo-id";
        var sonarKey = "same-key";

        var request = new UpdateRepositoryRequest
        {
            SonarQubeKey = sonarKey,
            Status = "Updated"
        };

        var existingRepo = new Repository
        {
            Id = id,
            SonarQubeKey = sonarKey,
            Status = "Old"
        };

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(existingRepo);

        _fixture.RepositoryRepository
            .UpdateAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Assert: Ensure FindOneAsync is never called, because the key hasn't changed
        _fixture.RepositoryRepository
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>(), Arg.Any<CancellationToken>())
            .ReturnsNull();

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Contain("Success");

        await _fixture.RepositoryRepository
            .DidNotReceive()
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>(), Arg.Any<CancellationToken>());
    }





    [Fact]
    public async Task DeleteRepositoryAsync_Should_Delete_Repository_When_Id_Is_Valid()
    {
        // Arrange
        var id = "validId";
        var data = new Repository();
        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(data);


        _fixture.RepositoryRepository.DeleteAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>()).Returns(1);


        // Act
        var result = await _arsenalService.DeleteRepositoryAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task DeleteRepositoryAsync_Should_Return_NotFound_When_Deletion_Count_Is_Zero()
    {
        // Arrange
        var id = "validId";
        var repo = new Repository();

        _fixture.RepositoryRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(repo);

        _fixture.RepositoryRepository.DeleteAsync(Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(0); // Simulate that nothing was deleted

        // Act
        var result = await _arsenalService.DeleteRepositoryAsync(id, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse(); // Expecting false because deletedCount == 0
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }


    [Fact]
    public async Task GetRepositoriesAsync_Should_Return_Empty_When_No_Repositories_Exist()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "test" };

        var data = new[] { new Repository() { Name = "t", Description = "hello" } }.AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalService.GetRepositoriesAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task GetRepositoryAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";

        var data = new[] { new Repository() { Name = "t", Description = "hello", Id = "valid" } }.AsQueryable()
            .BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalService.GetRepositoryAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task AddRepositoryAsync_Should_Return_Error_When_Request_Is_Invalid()
    {
        // Arrange
        var request = new CreateRepositoryRequest
        {
            /* Set invalid properties here */
        };

        // Act
        var result = await _arsenalService.AddRepositoryAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task UpdateRepositoryAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";
        var request = new UpdateRepositoryRequest
        {
            /* Set properties here */
        };

        _fixture.RepositoryRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((Repository?)null);

        // Act
        var result = await _arsenalService.UpdateRepositoryAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task DeleteRepositoryAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";

        // Act
        var result = await _arsenalService.DeleteRepositoryAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }
}