using Akka.Actor;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.Azure;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MockQueryable.NSubstitute;
using Newtonsoft.Json;
using NSubstitute;
using Xunit;
using Repository = Hubtel.CCI.Api.Data.Entities.Repository;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class AzureServiceTests
{
    private readonly AzureService _azureService;
    private readonly DiFixture _fixture;

    public AzureServiceTests()
    {
        IOptions<AzureOrgConfig> azureOrgConfig = Microsoft.Extensions.Options.Options.Create(new AzureOrgConfig()
        {
            Organization = "hubtel",
            AccessToken = "token",
            DevUrl = "http://localhost:6000",
            ReleaseUrl = "http://localhost:6000",
            Projects = ["Back-End"]
        });
        
        _fixture = new DiFixture();
        _azureService = new AzureService(
            Substitute.For<ILogger<AzureService>>(), 
            azureOrgConfig,
            _fixture.RepositoryContext,
            _fixture.MainActorService,
            _fixture.BypassedPrService);
    }


    #region ImportAzureDevOpsReleaseDefinitionsAsync

    [Fact]
    public async Task
        ImportAzureDevOpsReleaseDefinitionsAsync_Should_Return_False_With_OK_Response_When_Publication_Exists_For_The_Given_Day()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var releaseDefinition = new AzureReleaseDefinitionDeployment()
        {
            DefinitionName = "Test Release Definition",
            DefinitionId = "1",
            PublicationDate = publicationDate
        };
        
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDefinition }.BuildMock();
        
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(releaseDefinitionList);
        
        // Act
        var result = await _azureService.ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Already processed for the day");
    }

    
    [Fact]
    public async Task
        ImportAzureDevOpsReleaseDefinitionsAsync_Should_Return_True_With_OK_Response_When_Publication_Does_Not_Exist_For_The_Given_Day()
    {
        // Arrange
        var releaseDefinition = new AzureReleaseDefinitionDeployment()
        {
            DefinitionName = "Test Release Definition",
            DefinitionId = "1",
            PublicationDate = DateTime.Today
        };
        
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDefinition }.BuildMock();
        
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(releaseDefinitionList);
        
        using var httpTest = new HttpTest();

        var releaseDefinitions = new AzureReleaseDefinitionResponse()
        {
            Value =
            [
                new ReleaseDefinition()
                {
                    Id = 1,
                    Name = "Repo 1",
                    Source = "Repo 1",
                    Description = "Test Repo 1",
                    CreatedBy = new Identity(),
                    CreatedOn = DateTime.UtcNow,
                    Url = "http://localhost:6000",
                    ModifiedBy = new Identity(),
                    ModifiedOn = DateTime.UtcNow,
                    IsDeleted = false,
                    IsDisabled = false,
                    ReleaseNameFormat = "Release-{ReleaseId}",
                    Comment = "Test Comment",
                    Path = "/",
                    Links = new Links()
                }
            ]
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(releaseDefinitions),
            200);
        
        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .Returns(new List<AzureReleaseDefinition>().BuildMock());

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .AddRangeAsync(Arg.Any<List<AzureReleaseDefinition>>(), Arg.Any<CancellationToken>())
            .Returns(1);

        _fixture.MainActorService.Tell(
                Arg.Any<AzureDeploymentRecordsMessage>(), Arg.Any<IActorRef>())
            .Returns(Task.CompletedTask);
        
        // Act
        var result = await _azureService.ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    
        [Fact]
    public async Task
        ImportAzureDevOpsReleaseDefinitionsAsync_Should_Return_False_With_OK_Response_When_Publication_Does_Not_Exist_For_The_Given_Day()
    {
        // Arrange
        var releaseDefinition = new AzureReleaseDefinitionDeployment()
        {
            DefinitionName = "Test Release Definition",
            DefinitionId = "1",
            PublicationDate = DateTime.Today
        };
        
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDefinition }.BuildMock();
        
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(releaseDefinitionList);
        
        using var httpTest = new HttpTest();

        var releaseDefinitions = new AzureReleaseDefinitionResponse()
        {
            Value =
            [
                new ReleaseDefinition()
                {
                    Id = 1,
                    Name = "Repo 1",
                    Source = "Repo 1",
                    Description = "Test Repo 1",
                    CreatedBy = new Identity(),
                    CreatedOn = DateTime.UtcNow,
                    Url = "http://localhost:6000",
                    ModifiedBy = new Identity(),
                    ModifiedOn = DateTime.UtcNow,
                    IsDeleted = false,
                    IsDisabled = false,
                    ReleaseNameFormat = "Release-{ReleaseId}",
                    Comment = "Test Comment",
                    Path = "/",
                    Links = new Links()
                }
            ]
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(releaseDefinitions),
            400);
        
        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .Returns(new List<AzureReleaseDefinition>().BuildMock());

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .AddRangeAsync(Arg.Any<List<AzureReleaseDefinition>>(), Arg.Any<CancellationToken>())
            .Returns(0);

        _fixture.MainActorService.Tell(
                Arg.Any<AzureDeploymentRecordsMessage>(), Arg.Any<IActorRef>())
            .Returns(Task.CompletedTask);
        
        // Act
        var result = await _azureService.ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    [Fact]
    public async Task ImportAzureDevOpsReleaseDefinitionsAsync_Should_Only_Save_New_CompositeDefinitionIds()
    {
        // Arrange
        var projectName = "Back-End"; // This must match what _projects will contain in the actual service
        var existingDefinitionId = 1;
        var newDefinitionId = 2;

        var existingCompositeId = AzureService.ComputeHashKey(projectName, existingDefinitionId);
        var newCompositeId = AzureService.ComputeHashKey(projectName, newDefinitionId);

        var fetchedReleaseDefinitions = new AzureReleaseDefinitionResponse()
        {
            Value =
            [
                new ReleaseDefinition() { Id = existingDefinitionId, Name = "Existing", CreatedBy = new Identity(), ModifiedBy = new Identity() },
                new ReleaseDefinition() { Id = newDefinitionId, Name = "New", CreatedBy = new Identity(), ModifiedBy = new Identity() }
            ]
        };

        var mockQueryable = new List<AzureReleaseDefinitionDeployment>().BuildMock();
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository.GetQueryable().Returns(mockQueryable);

        using var httpTest = new HttpTest();
        httpTest.RespondWith(JsonConvert.SerializeObject(fetchedReleaseDefinitions), 200);

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .Returns(new List<AzureReleaseDefinition>
            {
                new AzureReleaseDefinition { CompositeDefinitionId = existingCompositeId }
            }.BuildMock());

        List<AzureReleaseDefinition>? savedDefinitions = null;
        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .AddRangeAsync(Arg.Do<List<AzureReleaseDefinition>>(x => savedDefinitions = x), Arg.Any<CancellationToken>())
            .Returns(1);

        _fixture.MainActorService.Tell(Arg.Any<AzureDeploymentRecordsMessage>(), Arg.Any<IActorRef>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _azureService.ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().BeTrue();
        savedDefinitions.Should().ContainSingle();
        savedDefinitions![0].CompositeDefinitionId.Should().Be(newCompositeId);
        savedDefinitions.Should().NotContain(x => x.CompositeDefinitionId == existingCompositeId);
    }



    #endregion

    #region TriggerProcessDefinitionsAsync

    [Fact]
    public async Task
        TriggerProcessDefinitionsAsync_Should_Return_False_With_200_Api_Response_When_Publication_Exists_For_That_Day()
    {
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var releaseDefinition = new AzureReleaseDefinitionDeployment()
        {
            DefinitionName = "Test Release Definition",
            DefinitionId = "1",
            PublicationDate = publicationDate
        };
        var message = new AzureDeploymentRecordsMessage()
        {
            TriggerDate = DateTime.Today
        };
        
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDefinition }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList.BuildMock());
        
        // Act
        var result = await _azureService.TriggerProcessDefinitionsAsync(
            message,CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Already processed for the day");

    }
    
    
    [Fact]
    public async Task
        TriggerProcessDefinitionsAsync_Should_Return_True_With_200_Api_Response_When_Publication_Does_Not_Exist_For_That_Day()
    {
        // Arrange
        var releaseDefinition = new AzureReleaseDefinitionDeployment()
        {
            DefinitionName = "Test Release Definition",
            DefinitionId = "1",
            PublicationDate = DateTime.Today
        };
        var message = new AzureDeploymentRecordsMessage()
        {
            TriggerDate = DateTime.Today
        };
        
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDefinition }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList.BuildMock());
        
        var totalDefinitions = new List<AzureReleaseDefinition>()
        {
            new AzureReleaseDefinition()
            {
                Id = "1",
                Name = "Repo 1",
                Source = "Repo 1",
                Description = "Test Repo 1",
                CreatedBy = new Identity(),
                CreatedOn = DateTime.UtcNow,
                Url = "http://localhost:6000",
                ModifiedBy = new Identity(),
                ModifiedOn = DateTime.UtcNow,
                IsDeleted = false,
                IsDisabled = false,
                ReleaseNameFormat = "Release-{ReleaseId}",
                Comment = "Test Comment",
                Path = "/",
                Links = new Links()
            }
        }.BuildMock();
        
        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(totalDefinitions);
        
        
        using var httpTest = new HttpTest();

        var releaseDefinitions = new AzureReleaseDeploymentResponse()
        {
            Value =
            [
                new ReleaseDeployment()
                {
                    Id = 1,
                    Release = new AzureRelease()
                    {
                        Artifacts = [
                        new Artifact()
                        {
                            DefinitionReference = new DefinitionReference()
                            {
                                SourceVersion = new ArtifactDefinitionRef()
                                {
                                    Id = "1",
                                    Name = "Test Artifact",
                                }
                            }
                        }
                        ]
                    },
                    ReleaseDefinition = new ReleaseDefinitionDeployment(),
                    ReleaseEnvironment = new ReleaseEnvironment()
                    {
                        Name = "Test Environment",
                    },
                    ProjectReference = new ProjectReference(),
                    DefinitionEnvironmentId = 4,
                    Attempt = 3,
                    Reason = "Test Reason",
                    DeploymentStatus = "succeeded",
                    OperationStatus = "Test Operation",
                    RequestedBy = new Identity(),
                    RequestedFor = new Identity(),
                    QueuedOn = DateTime.UtcNow,
                    StartedOn = DateTime.UtcNow,
                    CompletedOn = DateTime.UtcNow.AddDays(-1),
                    LastModifiedOn = DateTime.UtcNow,
                    LastModifiedBy = new Identity(),
                    Conditions = new List<DeploymentCondition>(),
                    PreDeployApprovals = new List<Approval>(),
                    PostDeployApprovals = new List<Approval>()
                }
            ]
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(releaseDefinitions),
            200);
        
        // Act
        var result = await _azureService.TriggerProcessDefinitionsAsync(
            message,CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Processed successfully");

    }
    
    
        [Fact]
    public async Task
        TriggerProcessDefinitionsAsync_Should_Return_True_With_200_Api_Response_When_Publication_Does_Not_Exist_For_That_Day_And_Releases_Fetch_Fails()
    {
        // Arrange
        var releaseDefinition = new AzureReleaseDefinitionDeployment()
        {
            DefinitionName = "Test Release Definition",
            DefinitionId = "1",
            PublicationDate = DateTime.Today
        };
        var message = new AzureDeploymentRecordsMessage()
        {
            TriggerDate = DateTime.Today
        };
        
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDefinition }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList.BuildMock());
        
        var totalDefinitions = new List<AzureReleaseDefinition>()
        {
            new AzureReleaseDefinition()
            {
                Id = "1",
                Name = "Repo 1",
                Source = "Repo 1",
                Description = "Test Repo 1",
                CreatedBy = new Identity(),
                CreatedOn = DateTime.UtcNow,
                Url = "http://localhost:6000",
                ModifiedBy = new Identity(),
                ModifiedOn = DateTime.UtcNow,
                IsDeleted = false,
                IsDisabled = false,
                ReleaseNameFormat = "Release-{ReleaseId}",
                Comment = "Test Comment",
                Path = "/",
                Links = new Links()
            }
        }.BuildMock();
        
        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(totalDefinitions);
        
        
        using var httpTest = new HttpTest();

        var releaseDefinitions = new AzureReleaseDeploymentResponse()
        {
            Value =
            [
                new ReleaseDeployment()
                {
                    Id = 1,
                    Release = new AzureRelease()
                    {
                        Artifacts = [
                        new Artifact()
                        {
                            DefinitionReference = new DefinitionReference()
                            {
                                SourceVersion = new ArtifactDefinitionRef()
                                {
                                    Id = "1",
                                    Name = "Test Artifact",
                                }
                            }
                        }
                        ]
                    },
                    ReleaseDefinition = new ReleaseDefinitionDeployment(),
                    ReleaseEnvironment = new ReleaseEnvironment()
                    {
                        Name = "Test Environment",
                    },
                    ProjectReference = new ProjectReference(),
                    DefinitionEnvironmentId = 4,
                    Attempt = 3,
                    Reason = "Test Reason",
                    DeploymentStatus = "succeeded",
                    OperationStatus = "Test Operation",
                    RequestedBy = new Identity(),
                    RequestedFor = new Identity(),
                    QueuedOn = DateTime.UtcNow,
                    StartedOn = DateTime.UtcNow,
                    CompletedOn = DateTime.UtcNow.AddDays(-1),
                    LastModifiedOn = DateTime.UtcNow,
                    LastModifiedBy = new Identity(),
                    Conditions = new List<DeploymentCondition>(),
                    PreDeployApprovals = new List<Approval>(),
                    PostDeployApprovals = new List<Approval>()
                }
            ]
        };

        httpTest.RespondWith(
            JsonConvert.SerializeObject(releaseDefinitions),
            400);
        
        // Act
        var result = await _azureService.TriggerProcessDefinitionsAsync(
            message,CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Processed successfully");

    }
    
    [Fact]
    public async Task TriggerProcessDefinitionsAsync_Should_Not_Process_When_TotalDefinitions_Is_Zero()
    {
        // Arrange
        var message = new AzureDeploymentRecordsMessage();
        var releaseDefinitionList = new List<AzureReleaseDefinitionDeployment>().BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);

        var totalDefinitions = new List<AzureReleaseDefinition>().BuildMock(); // 0 definitions
        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(totalDefinitions);

        // Act
        var result = await _azureService.TriggerProcessDefinitionsAsync(message, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue(); // Because we hit end with no items to process
        result.Message.Should().Be("Processed successfully");
    }
    
    
    [Fact]
    public async Task TriggerProcessDefinitionsAsync_Should_Handle_Pagination_Correctly()
    {
        // Arrange
        var message = new AzureDeploymentRecordsMessage();

        var releaseDefinitionList = new List<AzureReleaseDefinitionDeployment>().BuildMock();
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);

        var totalDefinitions = new List<AzureReleaseDefinition>();
        for (var i = 0; i < 150; i++)
        {
            totalDefinitions.Add(new AzureReleaseDefinition { Id = i.ToString(), Name = $"Repo {i}" });
        }

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(totalDefinitions.BuildMock());

        using var httpTest = new HttpTest();
        httpTest.RespondWith(JsonConvert.SerializeObject(new AzureReleaseDeploymentResponse { Value = new List<ReleaseDeployment>() }), 200);

        // Act
        var result = await _azureService.TriggerProcessDefinitionsAsync(message, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Message.Should().Be("Processed successfully");
    }
    
    [Fact]
    public async Task TriggerProcessDefinitionsAsync_Should_Not_Save_When_ReleaseDefinitionDeployments_Is_Empty()
    {
        // Arrange
        var message = new AzureDeploymentRecordsMessage();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(new List<AzureReleaseDefinitionDeployment>().BuildMock());

        var definitions = new List<AzureReleaseDefinition>()
        {
            new AzureReleaseDefinition { Id = "1", Name = "Repo 1" }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(definitions);
        

        await _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .Received(0)
            .AddRangeAsync(Arg.Any<List<AzureReleaseDefinitionDeployment>>(), Arg.Any<CancellationToken>());

        // Act
        var result = await _azureService.TriggerProcessDefinitionsAsync(message, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().BeTrue();
        result.Message.Should().Be("Processed successfully");
    }


    #endregion

    #region FetchReleaseDefinitionsAsync

    [Fact]
    public async Task FetchReleaseDefinitionsAsync_Should_Return_Success_Paged_Response_With_Results()
    {
        // Arrange
        var filter = new FetchAzureDefinitionRequest()
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = "Test",
            ProjectName = "Test Project",
        };
        
        var releaseDefinition = new AzureReleaseDefinition()
        {
            Id = "1",
            Name = "Repo 1",
            Source = "Repo 1",
            Description = "Test Repo 1",
            CreatedBy = new Identity(),
            CreatedOn = DateTime.UtcNow,
            Url = "http://localhost:6000",
            ModifiedBy = new Identity(),
            ModifiedOn = DateTime.UtcNow,
            IsDeleted = false,
            IsDisabled = false,
            ReleaseNameFormat = "Release-{ReleaseId}",
            Comment = "Test Comment",
            Path = "/",
            Links = new Links(),
            ProjectName = "Test Project",
        };
        var releaseDefinitionList = 
            new List<AzureReleaseDefinition> { releaseDefinition }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);
        
        // Act
        var result = await _azureService.FetchReleaseDefinitionsAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().NotBeNull();
        result.Data?.Results.Should().NotBeEmpty();
        result.Data?.Results.Count.Should().Be(1);
    }
    
    
    [Fact]
    public async Task FetchReleaseDefinitionsAsync_Should_Handle_Null_SearchTerm_And_ProjectName()
    {
        // Arrange
        var filter = new FetchAzureDefinitionRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = null,
            ProjectName = null
        };

        var releaseDefinition = new AzureReleaseDefinition
        {
            Id = "1",
            Name = "SomeName",
            ProjectName = "SomeProject"
        };

        var releaseDefinitionList = new List<AzureReleaseDefinition> { releaseDefinition }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);

        // Act
        var result = await _azureService.FetchReleaseDefinitionsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().ContainSingle();
    }
    
    
    [Fact]
    public async Task FetchReleaseDefinitionsAsync_Should_Respect_Sort_Direction()
    {
        // Arrange
        var filter = new FetchAzureDefinitionRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SortColumn = nameof(AzureReleaseDefinition.Name),
            SortDir = "asc",  // This will trigger the `!= "desc"` mutation
            SearchTerm = "Repo",
            ProjectName = "Test Project"
        };

        var defs = new List<AzureReleaseDefinition>
        {
            new AzureReleaseDefinition { Name = "A Repo", ProjectName = "Test Project" },
            new AzureReleaseDefinition { Name = "Z Repo", ProjectName = "Test Project" }
        };

        var mock = defs.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(mock);

        // Act
        var result = await _azureService.FetchReleaseDefinitionsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data?.Results[0].Name.Should().Be("A Repo");
    }
    
    
    [Fact]
    public async Task FetchReleaseDefinitionsAsync_Should_Filter_By_SearchTerm_Only()
    {
        // Arrange
        var filter = new FetchAzureDefinitionRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = "Alpha",
            ProjectName = null
        };

        var definitions = new List<AzureReleaseDefinition>
        {
            new AzureReleaseDefinition { Name = "Alpha Release", ProjectName = "Random" },
            new AzureReleaseDefinition { Name = "Beta Release", ProjectName = "Random" }
        };

        var mock = definitions.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(mock);

        // Act
        var result = await _azureService.FetchReleaseDefinitionsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data?.Results.Should().ContainSingle()
            .Which.Name.Should().Be("Alpha Release");
    }




    #endregion

    #region FetchReleaseDeploymentsAsync

    [Fact]
    public async Task FetchReleaseDeploymentsAsync_Should_Return_Success_Paged_Response_With_Results()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);

        var filter = new FetchAzureDeploymentsRequest()
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = "Test",
            ProjectName = "Test Project",
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek,
            HasDeploymentRecords = false
        };
        
        var releaseDeployment = new AzureReleaseDefinitionDeployment()
        {
            Id = "1",
            DefinitionName = "Repo 1",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek
        };
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);
        
        // Act
        var result = await _azureService.FetchReleaseDeploymentsAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().NotBeNull();
        result.Data?.Results.Should().NotBeEmpty();
        result.Data?.Results.Count.Should().Be(1);
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsAsync_Should_Handle_Null_SearchTerm()
    {
        var filter = new FetchAzureDeploymentsRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = null, // ← Crucial
            ProjectName = "Test Project",
            PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
            PublicationWeek = Miscellaneous.GetCurrentPublicationWeek(DateTime.UtcNow),
            HasDeploymentRecords = false
        };

        var data = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                Id = "1",
                DefinitionName = "Sample",
                DefinitionProjectName = "Test Project",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking()
            .Returns(data);

        var result = await _azureService.FetchReleaseDeploymentsAsync(filter, CancellationToken.None);

        result.Data?.Results.Should().ContainSingle();
    }

    [Fact]
    public async Task FetchReleaseDeploymentsAsync_Should_Handle_Null_ProjectName()
    {
        var filter = new FetchAzureDeploymentsRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = "sample",
            ProjectName = null, // ← Crucial
            PublicationDate = null,
            PublicationWeek = null,
            HasDeploymentRecords = false
        };

        var data = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                Id = "1",
                DefinitionName = "sample",
                DefinitionProjectName = "Another Project"
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking()
            .Returns(data);

        var result = await _azureService.FetchReleaseDeploymentsAsync(filter, CancellationToken.None);

        result.Data?.Results.Should().ContainSingle();
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsAsync_Should_Handle_Null_Publication_Fields()
    {
        var filter = new FetchAzureDeploymentsRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = "sample",
            ProjectName = "Project A",
            PublicationDate = null, // ←
            PublicationWeek = null,  // ←
            HasDeploymentRecords = false
        };

        var data = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                Id = "1",
                DefinitionName = "sample",
                DefinitionProjectName = "Project A"
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking()
            .Returns(data);

        var result = await _azureService.FetchReleaseDeploymentsAsync(filter, CancellationToken.None);

        result.Data?.Results.Should().ContainSingle();
    }

    
    [Fact]
    public async Task FetchReleaseDeploymentsAsync_Should_Support_SortDir_Asc()
    {
        var filter = new FetchAzureDeploymentsRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SearchTerm = "sample",
            SortDir = "asc", // ← kills mutation
            SortColumn = "DefinitionName",
            HasDeploymentRecords = false
        };

        var data = new List<AzureReleaseDefinitionDeployment>
        {
            new() { Id = "1", DefinitionName = "sample" }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking()
            .Returns(data);

        var result = await _azureService.FetchReleaseDeploymentsAsync(filter, CancellationToken.None);

        result.Data?.Results.Should().ContainSingle();
    }

    [Fact]
    public async Task FetchReleaseDeploymentsAsync_Should_Support_SortDir_Desc()
    {
        var filter = new FetchAzureDeploymentsRequest
        {
            PageIndex = 1,
            PageSize = 10,
            SortDir = "desc", // ← Crucial to kill the mutation
            SortColumn = "DefinitionName",
            HasDeploymentRecords = false
        };

        var data = new List<AzureReleaseDefinitionDeployment>
        {
            new() { Id = "1", DefinitionName = "Zebra" },
            new() { Id = "2", DefinitionName = "Alpha" }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking()
            .Returns(data);

        var result = await _azureService.FetchReleaseDeploymentsAsync(filter, CancellationToken.None);

        var names = result.Data?.Results.Select(x => x.DefinitionName).ToList();
    
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data?.Results.Should().HaveCount(2);
        names.Should().ContainInOrder("Zebra", "Alpha"); // Ensures descending order
    }



    #endregion

    #region FetchReleaseDeploymentsPublicationStatisticsAsync

    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Return_Success_Response_With_Results()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);

        var filter = new FetchAzureDeploymentStatisticsRequest()
        {
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek
        };
        
        var releaseDeployment = new AzureReleaseDefinitionDeployment()
        {
            Id = "1",
            DefinitionName = "Repo 1",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek,
            TotalDeployments = 3m,
            TotalDeploymentsSuccess = 2m,
            TotalDeploymentsFailed = 1m,
            TotalDeploymentsInRolledBack = 5m
        };
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);
        
        // Act
        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Return_Success_Response_With_Results_When_No_Filter_Is_Passed()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);

        var filter = new FetchAzureDeploymentStatisticsRequest();
        
        var releaseDeployment = new AzureReleaseDefinitionDeployment()
        {
            Id = "1",
            DefinitionName = "Repo 1",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek,
            TotalDeployments = 3m,
            TotalDeploymentsSuccess = 2m,
            TotalDeploymentsFailed = 1m,
            TotalDeploymentsInRolledBack = 5m
        };
        var releaseDefinitionList = 
            new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);
        
        // Act
        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Filter_By_PublicationDate_Only()
    {
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = publicationDate,
            PublicationWeek = null // only one provided
        };

        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                Id = "1",
                DefinitionName = "Name",
                DefinitionProjectName = "Project",
                PublicationDate = publicationDate,
                PublicationWeek = "irrelevant",
                TotalDeployments = 1,
                TotalDeploymentsFailed = 0,
                TotalDeploymentsSuccess = 1,
                TotalDeploymentsInRolledBack = 0
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        result.Data?.TotalDeployments.Should().Be(1);
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Filter_By_PublicationWeek_Only()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = null,
            PublicationWeek = "2024-W45"
        };

        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                Id = "1",
                DefinitionName = "Name",
                DefinitionProjectName = "Project",
                PublicationDate = DateTime.UtcNow,
                PublicationWeek = "2024-W45",
                TotalDeployments = 3,
                TotalDeploymentsSuccess = 2,
                TotalDeploymentsFailed = 1,
                TotalDeploymentsInRolledBack = 0
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        result.Data?.TotalDeployments.Should().Be(3);
    }
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Use_CurrentTargetDay_When_No_Filter_Provided()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();

        var filter = new FetchAzureDeploymentStatisticsRequest(); // No filters

        var releaseDeployment = new AzureReleaseDefinitionDeployment
        {
            Id = "1",
            DefinitionName = "Repo 3",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationDate,
            TotalDeployments = 1m,
            TotalDeploymentsSuccess = 1m,
            TotalDeploymentsFailed = 0m,
            TotalDeploymentsInRolledBack = 0m
        };

        var releaseDefinitionList = new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(releaseDefinitionList);

        // Act
        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.TotalDeployments.Should().Be(1);
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Correctly_Sum_Deployment_Fields()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
            PublicationWeek = "2024-W45"
        };

        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                DefinitionName = "Def A",
                DefinitionProjectName = "Proj A",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 2,
                TotalDeploymentsSuccess = 1,
                TotalDeploymentsFailed = 1,
                TotalDeploymentsInRolledBack = 0
            },
            new()
            {
                DefinitionName = "Def B",
                DefinitionProjectName = "Proj B",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 3,
                TotalDeploymentsSuccess = 3,
                TotalDeploymentsFailed = 0,
                TotalDeploymentsInRolledBack = 1
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.TotalDeployments.Should().Be(5); // Sum of 2 + 3
        result.Data?.TotalSuccessful.Should().Be(4);  // 1 + 3
        result.Data?.TotalFailed.Should().Be(1);      // 1 + 0
        result.Data?.TotalRollbacks.Should().Be(1);   // 0 + 1
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Only_Include_Top10_By_Deployments()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest();

        var deployments = new List<AzureReleaseDefinitionDeployment>();
        for (var i = 0; i < 15; i++)
        {
            deployments.Add(new AzureReleaseDefinitionDeployment
            {
                DefinitionId = $"def-{i}",
                DefinitionName = $"Name {i}",
                DefinitionProjectName = "Proj",
                PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
                TotalDeployments = i, // Varying counts from 0 to 14
                TotalDeploymentsSuccess = 0,
                TotalDeploymentsFailed = 0,
                TotalDeploymentsInRolledBack = 0
            });
        }

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments.BuildMock());

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.TopDefinitionsByDeploymentCount.Should().HaveCount(10);
        result.Data?.TopDefinitionsByDeploymentCount[0].DeploymentCount.Should().Be(14); // Highest
        result.Data?.TopDefinitionsByDeploymentCount[9].DeploymentCount.Should().Be(5);  // 10th highest
    }


    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Aggregate_Grouped_Statistics_Using_Sum()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
            PublicationWeek = "2024-W50"
        };

        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                DefinitionId = "def-1",
                DefinitionName = "App A",
                DefinitionProjectName = "Proj A",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 2,
                TotalDeploymentsSuccess = 1,
                TotalDeploymentsFailed = 1,
                TotalDeploymentsInRolledBack = 0
            },
            new()
            {
                DefinitionId = "def-1",
                DefinitionName = "App A",
                DefinitionProjectName = "Proj A",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 3,
                TotalDeploymentsSuccess = 3,
                TotalDeploymentsFailed = 0,
                TotalDeploymentsInRolledBack = 2
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        using var scope = new AssertionScope();
        var grouped = result.Data?.TopDefinitionsByDeploymentCount.FirstOrDefault();
        grouped.Should().NotBeNull();
        grouped!.DeploymentCount.Should().Be(5); // 2 + 3
        grouped.SuccessfulCount.Should().Be(4);  // 1 + 3
        grouped.FailedCount.Should().Be(1);      // 1 + 0
        grouped.RollbackCount.Should().Be(2);    // 0 + 2
    }
    
    
    [Fact]
    public async Task FetchReleaseDeploymentsPublicationStatisticsAsync_Should_Sort_Top10_By_Other_Categories_Correctly()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest();

        var deployments = new List<AzureReleaseDefinitionDeployment>();
        for (var i = 0; i < 15; i++)
        {
            deployments.Add(new AzureReleaseDefinitionDeployment
            {
                DefinitionId = $"def-{i}",
                DefinitionName = $"Def {i}",
                DefinitionProjectName = "Proj",
                PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
                PublicationWeek = "2024-W45",
                TotalDeployments = 1,
                TotalDeploymentsSuccess = i,
                TotalDeploymentsFailed = 14 - i,
                TotalDeploymentsInRolledBack = i % 5
            });
        }

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments.BuildMock());

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.TopDefinitionsBySuccessCount.Should().HaveCount(10);
        result.Data?.TopDefinitionsBySuccessCount.First().SuccessfulCount.Should().Be(14); // Highest
        result.Data?.TopDefinitionsByFailedCount.First().FailedCount.Should().Be(14);      // Highest (14 - 0)
        result.Data?.TopDefinitionsByRollbackCount.First().RollbackCount.Should().Be(4);   // Highest modulo 5
    }
    
    
    [Fact]
    public async Task FetchStats_Should_Use_CurrentTargetDay_Only_When_Both_Filters_Are_NullOrWhiteSpace()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                DefinitionName = "Def A",
                DefinitionProjectName = "Proj A",
                PublicationDate = publicationDate,
                TotalDeployments = 2,
                TotalDeploymentsSuccess = 1,
                TotalDeploymentsFailed = 1,
                TotalDeploymentsInRolledBack = 0
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        var filter = new FetchAzureDeploymentStatisticsRequest(); // Both null/empty

        // Act
        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        // Assert
        result.Data?.TotalDeployments.Should().Be(2);
    }

    
    [Fact]
    public async Task FetchStats_Should_Correctly_Sum_Rollbacks_And_Failures()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
            PublicationWeek = "2024-W50"
        };

        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                DefinitionName = "Def A",
                DefinitionProjectName = "Proj A",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 1,
                TotalDeploymentsSuccess = 1,
                TotalDeploymentsFailed = 1,
                TotalDeploymentsInRolledBack = 3
            },
            new()
            {
                DefinitionName = "Def B",
                DefinitionProjectName = "Proj B",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 1,
                TotalDeploymentsSuccess = 1,
                TotalDeploymentsFailed = 2,
                TotalDeploymentsInRolledBack = 2
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        // Act
        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data?.TotalRollbacks.Should().Be(5); // 3 + 2
        result.Data?.TotalFailed.Should().Be(3);    // 1 + 2
    }
    
    
    [Fact]
    public async Task FetchStats_GroupedStats_Should_Sum_Not_Max()
    {
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = Miscellaneous.GetCurrentPublicationTargetDay(),
            PublicationWeek = "2024-W51"
        };

        var deployments = new List<AzureReleaseDefinitionDeployment>
        {
            new()
            {
                DefinitionId = "1",
                DefinitionName = "Def A",
                DefinitionProjectName = "Proj",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 1,
                TotalDeploymentsInRolledBack = 2,
                TotalDeploymentsFailed = 3,
                TotalDeploymentsSuccess = 0
            },
            new()
            {
                DefinitionId = "1", // Same definition
                DefinitionName = "Def A",
                DefinitionProjectName = "Proj",
                PublicationDate = filter.PublicationDate.Value,
                PublicationWeek = filter.PublicationWeek,
                TotalDeployments = 1,
                TotalDeploymentsInRolledBack = 1,
                TotalDeploymentsFailed = 1,
                TotalDeploymentsSuccess = 0
            }
        }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable().AsNoTracking().Returns(deployments);

        var result = await _azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter, CancellationToken.None);

        var grouped = result.Data!.TopDefinitionsByRollbackCount.First();

        using var scope = new AssertionScope();
        grouped.RollbackCount.Should().Be(3); // not Max(2)
        grouped.FailedCount.Should().Be(4);   // not Max(3)
    }





    #endregion

    #region UpdateRepositoriesAsync

    [Fact]
    public async Task UpdateRepositoriesAsync_Should_Return_Success_Response()
    {
        // Arrange
        _fixture.BypassedPrService.FetchAllRepositoriesAsync()
            .Returns(new Dictionary<string, List<AzureRepositoryDto>>()
            {
                {"repository-id", [ 
                    new AzureRepositoryDto()
                    {
                        Id = Guid.Empty,
                        AzureRepoId = "azure-repo-id",
                        Name = "Test Repository",
                        ProjectName = "Test Project",
                        WebUrl = "https://example.com/repo"
                    },
                    new AzureRepositoryDto()
                    {
                        Id = Guid.Empty,
                        AzureRepoId = "azure-repo-id-1",
                        Name = "Test Repository 1",
                        ProjectName = "Test Project",
                        WebUrl = "https://example.com/repo1"
                    }
                ] }
            });
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Url = "https://example.com/repo1",
                    SonarQubeKey = "repository-id-sq",
                    AzureRepositoryId = ""
                }
            }.BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .UpdateRangeAsync(Arg.Any<List<Repository>>(), Arg.Any<CancellationToken>())
            .Returns(1);
        
        // Act
        var result = await _azureService.UpdateRepositoriesAsync(CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    
    [Fact]
    public async Task UpdateRepositoriesAsync_Should_Handle_All_Repositories_NotFound()
    {
        // Arrange
        _fixture.BypassedPrService.FetchAllRepositoriesAsync()
            .Returns(new Dictionary<string, List<AzureRepositoryDto>>
            {
                {
                    "repo-map-key", new List<AzureRepositoryDto>
                    {
                        new AzureRepositoryDto
                        {
                            AzureRepoId = "azure-repo-id",
                            WebUrl = "https://nonexistent.com/repo"
                        }
                    }
                }
            });

        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>
            {
                new Repository { Url = "https://example.com/another-repo" } // Does NOT match
            }.BuildMock());

        // Act
        var result = await _azureService.UpdateRepositoriesAsync();

        // Assert
        using var scope = new AssertionScope();
        result.Data?.UpdatedCount.Should().Be(0);
        result.Data?.NotFoundCount.Should().Be(1);
        result.Data?.NotFoundRepositories.Should().ContainSingle();
    }
    
    
    [Fact]
    public async Task UpdateRepositoriesAsync_Should_Not_Call_Update_When_No_Repositories_Matched()
    {
        // Arrange
        _fixture.BypassedPrService.FetchAllRepositoriesAsync()
            .Returns(new Dictionary<string, List<AzureRepositoryDto>>
            {
                {
                    "repo-map-key", new List<AzureRepositoryDto>
                    {
                        new AzureRepositoryDto
                        {
                            AzureRepoId = "id-1",
                            WebUrl = "https://example.com/no-match"
                        }
                    }
                }
            });

        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>
            {
                new Repository { Url = "https://example.com/another-url" }
            }.BuildMock());

        // Act
        var result = await _azureService.UpdateRepositoriesAsync();

        // Assert
        using var scope = new AssertionScope();
        result.Data?.UpdatedCount.Should().Be(0);
        result.Data?.NotFoundCount.Should().Be(1);
        await _fixture.RepositoryContext.RepositoryRepository
            .DidNotReceive()
            .UpdateRangeAsync(Arg.Any<List<Repository>>(), Arg.Any<CancellationToken>());
    }



    #endregion


    #region FetchAzureReleaseDeployments

    [Fact]
    public async Task FetchAzureReleaseDeployments_Should_Handle_Pagination_With_ContinuationToken()
    {
        // Arrange
        var project = "SampleProject";
        var definitionId = 123;
        var cts = new CancellationTokenSource();

        using var httpTest = new HttpTest();

        var firstBatch = new AzureReleaseDeploymentResponse
        {
            Value = new List<ReleaseDeployment>
            {
                new ReleaseDeployment { Id = 1, ReleaseEnvironment = new ReleaseEnvironment(){Name = "Production"} }
            }
        };

        var secondBatch = new AzureReleaseDeploymentResponse
        {
            Value = [new ReleaseDeployment { Id = 2, ReleaseEnvironment = new ReleaseEnvironment(){Name = "Production"} }]
        };

        // First response with continuation token
        httpTest.RespondWith(JsonConvert.SerializeObject(firstBatch), 200, headers: new { x_ms_continuationtoken = "token-123" });
        httpTest.RespondWith(JsonConvert.SerializeObject(secondBatch), 200);

        var results = new List<ReleaseDeployment>();

        // Act
        await foreach (var deployment in _azureService.FetchAzureReleaseDeployments(project, definitionId, cts.Token))
        {
            results.Add(deployment);
        }

        // Assert
        using var scope = new AssertionScope();
        results.Should().HaveCount(2);
        results.Select(r => r.Id).Should().ContainInOrder(1, 2);
    }


    #endregion


    #region CalculateRollbacks

    [Fact]
    public void CalculateRollbacks_Should_Skip_When_SourceVersion_Is_Null_Or_Whitespace()
    {
        var deployments = new List<AzureReleaseDeployment>
        {
            new() {
                SourceVersion = "abc",
                CompletedOn = DateTime.UtcNow.AddDays(-1),
                ReleaseEnvironment = new ReleaseEnvironment { Name = "Prod" }
            }
        };

        var today = new List<AzureReleaseDeployment>
        {
            new() {
                SourceVersion = null,
                CompletedOn = DateTime.UtcNow,
                ReleaseEnvironment = new ReleaseEnvironment { Name = "Prod" }
            },
            new() {
                SourceVersion = "   ",
                CompletedOn = DateTime.UtcNow,
                ReleaseEnvironment = new ReleaseEnvironment { Name = "Prod" }
            }
        };

        _azureService.CalculateRollbacks(deployments, today);

        today.ForEach(d => d.IsRollBack.Should().BeFalse());
    }
    
    
    [Fact]
    public void CalculateRollbacks_Should_Skip_When_Environment_Is_Null_Or_Name_Is_Null()
    {
        var deployments = new List<AzureReleaseDeployment>();

        var today = new List<AzureReleaseDeployment>
        {
            new() {
                SourceVersion = "abc",
                CompletedOn = DateTime.UtcNow,
                ReleaseEnvironment = new ReleaseEnvironment()
                {
                    Name = null!
                }
            },
            new() {
                SourceVersion = "abc",
                CompletedOn = DateTime.UtcNow,
                ReleaseEnvironment = new ReleaseEnvironment { Name = null! }
            }
        };

        _azureService.CalculateRollbacks(deployments, today);

        today.ForEach(d => d.IsRollBack.Should().BeFalse());
    }

    [Fact]
    public void CalculateRollbacks_Should_Set_IsRollBack_When_Previous_Deployment_Matches()
    {
        var matchingTime = DateTime.UtcNow.AddDays(-1);

        var deployments = new List<AzureReleaseDeployment>
        {
            new()
            {
                SourceVersion = "abc123",
                CompletedOn = matchingTime,
                ReleaseEnvironment = new() { Name = "Staging" }
            }
        };

        var today = new List<AzureReleaseDeployment>
        {
            new()
            {
                SourceVersion = "abc123",
                CompletedOn = matchingTime.AddHours(5),
                ReleaseEnvironment = new() { Name = "Staging" }
            }
        };

        _azureService.CalculateRollbacks(deployments, today);

        today[0].IsRollBack.Should().BeTrue();
    }

    
    [Theory]
    [InlineData("wrong-version", "Staging", false)]
    [InlineData("abc123", "WrongEnv", false)]
    [InlineData("abc123", "Staging", true)]
    public void CalculateRollbacks_Should_Only_Set_IsRollBack_If_All_Conditions_Match(
        string sourceVersion, string envName, bool expected)
    {
        var prev = new AzureReleaseDeployment
        {
            SourceVersion = "abc123",
            CompletedOn = DateTime.UtcNow.AddHours(-3),
            ReleaseEnvironment = new ReleaseEnvironment { Name = "Staging" }
        };

        var todayDeployment = new AzureReleaseDeployment
        {
            SourceVersion = sourceVersion,
            CompletedOn = DateTime.UtcNow,
            ReleaseEnvironment = new ReleaseEnvironment { Name = envName }
        };
        
        _azureService.CalculateRollbacks([prev], [todayDeployment]);

        todayDeployment.IsRollBack.Should().Be(expected);
    }
    
    [Fact]
    public void CalculateRollbacks_Should_Skip_When_SourceVersion_Is_Empty()
    {
        var deploymentsToday = new List<AzureReleaseDeployment>
        {
            new() { SourceVersion = "", ReleaseEnvironment = new() { Name = "Prod" } }
        };

        var pastDeployments = new List<AzureReleaseDeployment>(); // empty

        _azureService.CalculateRollbacks(pastDeployments, deploymentsToday);

        deploymentsToday[0].IsRollBack.Should().BeFalse();
    }

    [Fact]
    public void CalculateRollbacks_Should_Skip_When_EnvironmentName_Is_Empty()
    {
        var deploymentsToday = new List<AzureReleaseDeployment>
        {
            new() { SourceVersion = "abc123", ReleaseEnvironment = new() { Name = "" } }
        };

        var pastDeployments = new List<AzureReleaseDeployment>();

        _azureService.CalculateRollbacks(pastDeployments, deploymentsToday);

        deploymentsToday[0].IsRollBack.Should().BeFalse();
    }

    [Fact]
    public void CalculateRollbacks_Should_Not_Consider_SameTime_Deployment_As_Rollback()
    {
        var time = DateTime.UtcNow;

        var deploymentsToday = new List<AzureReleaseDeployment>
        {
            new()
            {
                SourceVersion = "abc123",
                CompletedOn = time,
                ReleaseEnvironment = new() { Name = "Prod" }
            }
        };

        var pastDeployments = new List<AzureReleaseDeployment>
        {
            new()
            {
                SourceVersion = "abc123",
                CompletedOn = time, // exactly same time
                ReleaseEnvironment = new() { Name = "Prod" }
            }
        };

        _azureService.CalculateRollbacks(pastDeployments, deploymentsToday);

        deploymentsToday[0].IsRollBack.Should().BeFalse();
    }

    [Fact]
    public void CalculateRollbacks_Should_Mark_Rollback_When_Matching_Prior_Exists()
    {
        var deploymentsToday = new List<AzureReleaseDeployment>
        {
            new()
            {
                SourceVersion = "abc123",
                CompletedOn = DateTime.UtcNow,
                ReleaseEnvironment = new() { Name = "Prod" }
            }
        };

        var pastDeployments = new List<AzureReleaseDeployment>
        {
            new()
            {
                SourceVersion = "abc123",
                CompletedOn = DateTime.UtcNow.AddMinutes(-10),
                ReleaseEnvironment = new() { Name = "Prod" }
            }
        };

        _azureService.CalculateRollbacks(pastDeployments, deploymentsToday);

        deploymentsToday[0].IsRollBack.Should().BeTrue();
    }




    #endregion
    
    
    #region FetchAzureReleaseDefinitions

    [Fact]
    public async Task FetchAzureReleaseDefinitions_Should_Return_ReleaseDefinitions_From_Single_Page()
    {
        // Arrange
        using var httpTest = new HttpTest();
        var release = new ReleaseDefinition { Id = 1, Name = "Release A" };

        httpTest.RespondWithJson(new AzureReleaseDefinitionResponse
        {
            Value = [release]
        }, 200);

        var result = new List<ReleaseDefinition>();
        await foreach (var def in _azureService.FetchAzureReleaseDefinitions("TestProject", CancellationToken.None))
        {
            result.Add(def);
        }

        // Assert
        result.Should().ContainSingle();
        result[0].Name.Should().Be("Release A");
    }


    [Fact]
    public async Task FetchAzureReleaseDefinitions_Should_Handle_ContinuationToken_Across_Multiple_Pages()
    {
        // Arrange
        using var httpTest = new HttpTest();

        var def1 = new ReleaseDefinition { Id = 1, Name = "Def 1" };
        var def2 = new ReleaseDefinition { Id = 2, Name = "Def 2" };
        var expected = new[] { "Def 1", "Def 2" };


        httpTest
            .RespondWithJson(new AzureReleaseDefinitionResponse
            {
                Value = [def1]
            }, 200, new { x_ms_continuationtoken = "next-page" });

        httpTest
            .RespondWithJson(new AzureReleaseDefinitionResponse
            {
                Value = [def2]
            });

        var results = new List<ReleaseDefinition>();
        await foreach (var def in _azureService.FetchAzureReleaseDefinitions("TestProject", CancellationToken.None))
        {
            results.Add(def);
        }

        // Assert
        results.Should().HaveCount(2);
        results.Select(d => d.Name).Should().Contain(expected);
        httpTest.ShouldHaveCalled("*release/definitions*")
            .WithQueryParam("continuationToken", "next-page")
            .Times(1);
    }


    [Fact]
    public async Task FetchAzureReleaseDefinitions_Should_Handle_Http_Exception_Gracefully()
    {
        // Arrange
        using var httpTest = new HttpTest();

        httpTest.RespondWith(string.Empty, 500); // Simulate failure

        var results = new List<ReleaseDefinition>();
        await foreach (var def in _azureService.FetchAzureReleaseDefinitions("TestProject", CancellationToken.None))
        {
            results.Add(def);
        }

        // Assert
        results.Should().BeEmpty();
    }


    [Fact]
    public async Task FetchAzureReleaseDefinitions_Should_Include_ContinuationToken_In_Subsequent_Request()
    {
        // Arrange
        using var httpTest = new HttpTest();

        var def1 = new ReleaseDefinition { Id = 1, Name = "Def A" };
        var def2 = new ReleaseDefinition { Id = 2, Name = "Def B" };

        httpTest
            .RespondWithJson(new AzureReleaseDefinitionResponse
            {
                Value = [def1]
            }, 200, new { x_ms_continuationtoken = "next123" });

        httpTest
            .RespondWithJson(new AzureReleaseDefinitionResponse
            {
                Value = [def2]
            });

        var allDefs = new List<ReleaseDefinition>();
        await foreach (var d in _azureService.FetchAzureReleaseDefinitions("TestProject", CancellationToken.None))
        {
            allDefs.Add(d);
        }

        // Assert
        allDefs.Should().HaveCount(2);
        httpTest.ShouldHaveCalled("*definitions*")
            .WithQueryParam("continuationToken", "next123")
            .Times(1);
    }

    #endregion
    
    
    #region FetchAzureReleaseDeployments

    [Fact]
    public async Task FetchAzureReleaseDeployments_Should_Return_Deployments_From_Single_Page()
    {
        // Arrange
        using var httpTest = new HttpTest();
        var deployment = new ReleaseDeployment { Id = 101, ReleaseEnvironment = new ReleaseEnvironment(){Name = "Production"} };

        httpTest.RespondWithJson(new AzureReleaseDeploymentResponse
        {
            Value = [deployment]
        }, 200);

        var results = new List<ReleaseDeployment>();
        await foreach (var rd in _azureService.FetchAzureReleaseDeployments("MyProject", 123, CancellationToken.None))
        {
            results.Add(rd);
        }

        // Assert
        results.Should().ContainSingle();
        results[0].Id.Should().Be(101);
    }


    [Fact]
    public async Task FetchAzureReleaseDeployments_Should_Handle_ContinuationToken_Across_Multiple_Pages()
    {
        // Arrange
        using var httpTest = new HttpTest();
        var deployment1 = new ReleaseDeployment { Id = 1, ReleaseEnvironment = new ReleaseEnvironment(){Name = "Production"}};
        var deployment2 = new ReleaseDeployment { Id = 2, ReleaseEnvironment = new ReleaseEnvironment(){Name = "Production"} };
        var expectedArray = new[] { 1, 2 };


        httpTest
            .RespondWithJson(new AzureReleaseDeploymentResponse
            {
                Value = [deployment1]
            }, 200, new { x_ms_continuationtoken = "token-123" });

        httpTest
            .RespondWithJson(new AzureReleaseDeploymentResponse
            {
                Value = [deployment2]
            });

        var results = new List<ReleaseDeployment>();
        await foreach (var rd in _azureService.FetchAzureReleaseDeployments("MyProject", 456, CancellationToken.None))
        {
            results.Add(rd);
        }

        // Assert
        results.Should().HaveCount(2);
        results.Select(d => d.Id).Should().Contain(expectedArray);
    }


    [Fact]
    public async Task FetchAzureReleaseDeployments_Should_Include_ContinuationToken_In_Second_Request()
    {
        // Arrange
        using var httpTest = new HttpTest();

        httpTest.RespondWithJson(new AzureReleaseDeploymentResponse
        {
            Value = [new ReleaseDeployment { Id = 1 }]
        }, 200, new { x_ms_continuationtoken = "next-token" });

        httpTest.RespondWithJson(new AzureReleaseDeploymentResponse
        {
            Value = [new ReleaseDeployment { Id = 2 }]
        });

        var results = new List<ReleaseDeployment>();
        await foreach (var d in _azureService.FetchAzureReleaseDeployments("Proj", 789, CancellationToken.None))
        {
            results.Add(d);
        }

        // Assert
        httpTest.ShouldHaveCalled("*deployments*")
            .WithQueryParam("continuationToken", "next-token")
            .Times(1);
    }


    [Fact]
    public async Task FetchAzureReleaseDeployments_Should_Handle_FlurlHttpException_Gracefully()
    {
        // Arrange
        using var httpTest = new HttpTest();

        httpTest.RespondWith(string.Empty, 500); // Simulate HTTP error

        var results = new List<ReleaseDeployment>();
        await foreach (var d in _azureService.FetchAzureReleaseDeployments("TestProject", 1001, CancellationToken.None))
        {
            results.Add(d);
        }

        // Assert
        results.Should().BeEmpty();
    }


    [Fact]
    public async Task FetchAzureReleaseDeployments_Should_Handle_Generic_Exception_Gracefully()
    {
        // Arrange
        using var httpTest = new HttpTest();

        httpTest.SimulateTimeout(); // Simulate generic exception like timeout

        var results = new List<ReleaseDeployment>();
        await foreach (var d in _azureService.FetchAzureReleaseDeployments("GenericProject", 777, CancellationToken.None))
        {
            results.Add(d);
        }

        // Assert
        results.Should().BeEmpty();
    }

    #endregion


    #region DeleteAzureReleaseDeployment

    [Fact]
    public async Task
        DeleteAzureDeploymentPublicationAsync_Should_Return_False_Not_Found_Api_Response_When_Publication_Does_Not_Exist()
    {
        // Arrange
        var publicationId = DateTime.UtcNow.Date;
        
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(new List<AzureReleaseDefinitionDeployment>().BuildMock());
        
        // Act
        var result = await _azureService.DeleteAzureDeploymentPublicationAsync(publicationId, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Message.Should().Be("Azure Deployment Publication not found");
    }


    [Fact]
    public async Task
        DeleteAzureDeploymentPublicationAsync_Should_Return_True_OK_API_Response_When_Publication_Data_Is_Deleted_Successfully()
    {
        // Arrange
        var publicationId = DateTime.UtcNow.Date;
        
        var releaseDeployment = new AzureReleaseDefinitionDeployment
        {
            Id = "1",
            DefinitionName = "Repo 1",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationId,
            TotalDeployments = 3m,
            TotalDeploymentsSuccess = 2m,
            TotalDeploymentsFailed = 1m,
            TotalDeploymentsInRolledBack = 5m
        };
        
        var releaseDefinitionList = new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();
        
        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(releaseDefinitionList);

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .DeleteRangeAsync(Arg.Any<List<AzureReleaseDefinitionDeployment>>())
            .Returns(1);
        
        // Act
        var result = await _azureService.DeleteAzureDeploymentPublicationAsync(publicationId, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Azure Deployment Publication deleted successfully");
    }

    #endregion

    #region FetchReleaseDeploymentsPublicationDetailsAsync

    [Fact]
    public async Task FetchReleaseDeploymentsPublicationDetailsAsync_Should_Return_ReleaseDeploymentDetails()
    {
        // Arrange
        var publicationId = DateTime.UtcNow.Date;
        var filter = new FetchAzureDeploymentStatisticsRequest
        {
            PublicationDate = publicationId,
        };

        var releaseDeployment = new AzureReleaseDefinitionDeployment
        {
            Id = "1",
            DefinitionName = "Repo 1",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationId,
            TotalDeployments = 3m,
            TotalDeploymentsSuccess = 2m,
            TotalDeploymentsFailed = 1m,
            TotalDeploymentsInRolledBack = 5m,
            ReleaseDeployments = [
                new AzureReleaseDeployment()
                {
                    ReleaseId = 1,
                    SourceVersion = "v1.0",
                    CompletedOn = DateTime.UtcNow.AddDays(-1),
                    ReleaseEnvironment = new ReleaseEnvironment { Name = "Production" },
                    IsRollBack = false
                },
                new AzureReleaseDeployment()
                {
                    ReleaseId = 2,
                    SourceVersion = "v1.1",
                    CompletedOn = DateTime.UtcNow.AddDays(-2),
                    ReleaseEnvironment = new ReleaseEnvironment { Name = "Staging" },
                    IsRollBack = true
                },
                new AzureReleaseDeployment()
                {
                    ReleaseId = 3,
                    SourceVersion = "v1.2",
                    CompletedOn = DateTime.UtcNow.AddDays(-3),
                    ReleaseEnvironment = new ReleaseEnvironment { Name = "Development" },
                    IsRollBack = false
                }
            ]
        };

        var releaseDefinitionList = new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(releaseDefinitionList);

        // Act
        var result =
            await _azureService.FetchReleaseDeploymentsPublicationDetailsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    
        [Fact]
    public async Task FetchReleaseDeploymentsPublicationDetailsAsync_Should_Return_ReleaseDeploymentDetails_If_Publication_Date_And_Week_Is_Null()
    {
        // Arrange
        var publicationId = DateTime.UtcNow.Date;
        var filter = new FetchAzureDeploymentStatisticsRequest();

        var releaseDeployment = new AzureReleaseDefinitionDeployment
        {
            Id = "1",
            DefinitionName = "Repo 1",
            IsDeleted = false,
            DefinitionProjectName = "Test Project",
            PublicationDate = publicationId,
            TotalDeployments = 3m,
            TotalDeploymentsSuccess = 2m,
            TotalDeploymentsFailed = 1m,
            TotalDeploymentsInRolledBack = 5m,
            ReleaseDeployments = [
                new AzureReleaseDeployment()
                {
                    ReleaseId = 1,
                    SourceVersion = "v1.0",
                    CompletedOn = DateTime.UtcNow.AddDays(-1),
                    ReleaseEnvironment = new ReleaseEnvironment { Name = "Production" },
                    IsRollBack = false
                },
                new AzureReleaseDeployment()
                {
                    ReleaseId = 2,
                    SourceVersion = "v1.1",
                    CompletedOn = DateTime.UtcNow.AddDays(-2),
                    ReleaseEnvironment = new ReleaseEnvironment { Name = "Staging" },
                    IsRollBack = true
                },
                new AzureReleaseDeployment()
                {
                    ReleaseId = 3,
                    SourceVersion = "v1.2",
                    CompletedOn = DateTime.UtcNow.AddDays(-3),
                    ReleaseEnvironment = new ReleaseEnvironment { Name = "Development" },
                    IsRollBack = false
                }
            ]
        };

        var releaseDefinitionList = new List<AzureReleaseDefinitionDeployment> { releaseDeployment }.BuildMock();

        _fixture.RepositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .Returns(releaseDefinitionList);

        // Act
        var result =
            await _azureService.FetchReleaseDeploymentsPublicationDetailsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
    }

    #endregion


    
}