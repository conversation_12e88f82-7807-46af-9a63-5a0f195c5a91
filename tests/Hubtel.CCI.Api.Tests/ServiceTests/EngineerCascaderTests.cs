using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Cascaders;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class EngineerCascaderTests
{
    private readonly EngineerCascader _engineerCascader;
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger<EngineerCascader> _logger;

    public EngineerCascaderTests()
    {
        _repositoryContext = Substitute.For<IRepositoryContext>();
        _logger = Substitute.For<ILogger<EngineerCascader>>();
        _engineerCascader = new EngineerCascader(_logger, _repositoryContext);
    }

    #region CascadeEngineerUpdateForProductGroupsAsync
    
    
    [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_Update_When_Engineer_Is_Supervisor()
    {
        // Arrange
        var engineer = new Engineer
        {
            Id = "eng1",
            Name = "New Name",
            Email = "<EMAIL>",
            Domain = ["Backend"],
            JobLevel = "Team Lead"
        };

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group A",
                Supervisors = new()
                {
                    Items = new() { new Supervisor { Id = "eng1" } }
                },
                ProductTeams = new() { Items = new List<ProductTeamItem>() }
            }
        };

        var message = new CascadeEngineerUpdateMessage(engineer);

        _repositoryContext.GetProductGroupsForEngineerAsync("eng1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_Update_When_Engineer_Is_Member()
    {
        // Arrange
        var engineer = new Engineer
        {
            Id = "eng1",
            Name = "New Name",
            Email = "<EMAIL>",
            Domain = ["Backend"],
            JobLevel = "Senior"
        };

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group B",
                Supervisors = new(),
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "Team A",
                            Members = new()
                            {
                                Items = new() { new Member { Id = "eng1" } }
                            }
                        }
                    }
                }
            }
        };

        var message = new CascadeEngineerUpdateMessage(engineer);

        _repositoryContext.GetProductGroupsForEngineerAsync("eng1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_ReturnFalse_When_No_Engineer_Matches()
    {
        // Arrange
        var engineer = new Engineer { Id = "eng2" };
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group C",
                Supervisors = new() { Items = new() { new Supervisor { Id = "not-eng2" } } },
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "Team X",
                            Members = new()
                            {
                                Items = new() { new Member { Id = "not-eng2" } }
                            }
                        }
                    }
                }
            }
        };

        var message = new CascadeEngineerUpdateMessage(engineer);

        _repositoryContext.GetProductGroupsForEngineerAsync("eng2").Returns(productGroups);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }

    
    [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_ReturnFalse_When_Update_Fails()
    {
        // Arrange
        var engineer = new Engineer { Id = "eng1", Name = "n", Email = "e", Domain = ["Backend"], JobLevel = "j" };

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group X",
                Supervisors = new() { Items = new() { new Supervisor { Id = "eng1" } } },
                ProductTeams = new() { Items = new List<ProductTeamItem>() }
            }
        };

        var message = new CascadeEngineerUpdateMessage(engineer);

        _repositoryContext.GetProductGroupsForEngineerAsync("eng1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(0); // simulate failed update

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }
    
            [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_Return_True_When_Update_Succeeds()
    {
        // Arrange
        var message = new CascadeEngineerUpdateMessage(new Engineer { Id = "engineerId" });
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new()
                {
                    Items = new()
                    {
                        new Supervisor { Id = "engineerId" }
                    }
                },
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new()
                            {
                                Items = new()
                                {
                                    new Member { Id = "engineerId" }
                                }
                            }
                        }
                    }
                }
            }
        };
        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_Return_False_When_No_Updates_Found()
    {
        // Arrange
        var message = new CascadeEngineerUpdateMessage(new Engineer { Id = "engineerId" });
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new(),
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new()
                            {
                                Items = new()
                            }
                        }
                    }
                }
            }
        };
        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task CascadeEngineerUpdateForProductGroupsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var message = new CascadeEngineerUpdateMessage(new Engineer { Id = "engineerId" });
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new()
                {
                    Items = new()
                    {
                        new Supervisor { Id = "engineerId" }
                    }
                },
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new()
                            {
                                Items = new()
                                {
                                    new Member { Id = "engineerId" }
                                }
                            }
                        }
                    }
                }
            }
        };
        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(0);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }



    #endregion

    #region CascadeEngineerUpdateForProductTeamsAsync

    [Fact]
    public async Task CascadeEngineerUpdateForProductTeamsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var message = new CascadeEngineerUpdateMessage(new Engineer { Id = "engineerId" });

        var productTeams = new List<ProductTeam> { new ProductTeam { Id = "teamId", Members = new Members(){Items = [new Member(){Id="engineerId"}]} }, };
        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(productTeams).Returns(0);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductTeamsAsync(message);

        // Assert
        result.Should().BeFalse();
    }
    
    [Fact]
    public async Task CascadeEngineerUpdateForProductTeamsAsync_Should_Return_True_When_Update_Succeeds()
    {
        // Arrange
        var message = new CascadeEngineerUpdateMessage(new Engineer { Id = "engineerId" });
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId", 
                Members = new()
                {
                    Items = new()
                    {
                        new Member { Id = "engineerId" }
                    }
                }
            }
        };
        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(productTeams).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductTeamsAsync(message);

        // Assert
        result.Should().BeTrue();
    }
    
    [Fact]
    public async Task CascadeEngineerUpdateForProductTeamsAsync_Should_Return_False_When_No_Engineer_Found()
    {
        // Arrange
        var message = new CascadeEngineerUpdateMessage(new Engineer { Id = "engineerId" });

        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId",
                Members = new() { Items = new() { new Member { Id = "differentId" } } }
            }
        };

        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductTeamsAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeFalse();
        await _repositoryContext.ProductTeamRepository.Received(0).UpdateRangeAsync(Arg.Any<List<ProductTeam>>());
    }

    [Fact]
    public async Task CascadeEngineerUpdateForProductTeamsAsync_Should_Update_Matching_Member()
    {
        // Arrange
        var engineer = new Engineer
        {
            Id = "engineerId",
            Name = "Updated Name",
            Email = "<EMAIL>",
            Domain = ["Backend"],
            JobLevel = "L3"
        };

        var member = new Member { Id = "engineerId" };

        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId",
                Members = new() { Items = new() { member } }
            }
        };

        var message = new CascadeEngineerUpdateMessage(engineer);

        _repositoryContext.GetProductTeamsForEngineerAsync(engineer.Id).Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(productTeams).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerUpdateForProductTeamsAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();

        member.Name.Should().Be("Updated Name");
        member.Email.Should().Be("<EMAIL>");
        member.Domain[0].Should().Be("Backend");
        member.JobLevel.Should().Be("L3");
    }


    #endregion

    #region CascadeEngineerDeleteForProductTeamsAsync

    [Fact]
    public async Task CascadeEngineerDeleteForProductTeamsAsync_Should_Return_True_When_Delete_Succeeds()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });

        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId",
                Members = new Members { Items = new List<Member> { new Member { Id = "engineerId" } } }
            }
        };
        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(productTeams).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductTeamsAsync(message);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task CascadeEngineerDeleteForProductTeamsAsync_Should_Return_False_When_Delete_Fails()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId",
                Members = new Members { Items = new List<Member> { new Member { Id = "engineerId" } } }
            }
        };
        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(productTeams).Returns(0);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductTeamsAsync(message);

        // Assert
        result.Should().BeFalse();
    }
    
    [Fact]
    public async Task CascadeEngineerDeleteForProductTeamsAsync_Should_Return_False_When_No_Member_Found()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId",
                Name = "Team A",
                Members = new()
                {
                    Items = new()
                    {
                        new Member { Id = "otherId" } // doesn't match engineerId
                    }
                }
            }
        };

        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductTeamsAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeFalse();
        await _repositoryContext.ProductTeamRepository.Received(0).UpdateRangeAsync(Arg.Any<List<ProductTeam>>());
    }

    [Fact]
    public async Task CascadeEngineerDeleteForProductTeamsAsync_Should_Remove_Matching_Member_And_Return_True()
    {
        // Arrange
        var member = new Member { Id = "engineerId" };
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Id = "teamId",
                Name = "Team A",
                Members = new() { Items = new() { member } }
            }
        };

        _repositoryContext.GetProductTeamsForEngineerAsync(message.Engineer.Id).Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(productTeams).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductTeamsAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
        productTeams[0].Members.Items.Should().BeEmpty(); // Member was removed
    }



    #endregion

    #region CascadeEngineerDeleteForProductGroupsAsync

        [Fact]
    public async Task
        CascadeEngineerDeleteForProductGroupsAsync_Should_Return_True_When_Engineer_Found_And_Update_Succeeds()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new()
                {
                    Items = new()
                    {
                        new Supervisor { Id = "engineerId" }
                    }
                },
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new()
                            {
                                Items = new()
                                {
                                    new Member { Id = "engineerId" }
                                }
                            }
                        }
                    }
                }
            }
        };
        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task CascadeEngineerDeleteForProductGroupsAsync_Should_Return_False_When_No_Updates_Found()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new(),
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new()
                            {
                                Items = new()
                            }
                        }
                    }
                }
            }
        };
        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task CascadeEngineerDeleteForProductGroupsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new()
                {
                    Items = new()
                    {
                        new Supervisor { Id = "engineerId" }
                    }
                },
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new()
                            {
                                Items = new()
                                {
                                    new Member { Id = "engineerId" }
                                }
                            }
                        }
                    }
                }
            }
        };
        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(0);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }
    
    
    [Fact]
    public async Task CascadeEngineerDeleteForProductGroupsAsync_Should_Remove_Supervisor_And_Member()
    {
        // Arrange
        var message = new CascadeEngineerDeleteMessage(new Engineer { Id = "engineerId" });
        var supervisor = new Supervisor { Id = "engineerId" };
        var member = new Member { Id = "engineerId" };
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "groupName",
                Supervisors = new() { Items = new() { supervisor } },
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Name = "teamName",
                            Members = new() { Items = new() { member } }
                        }
                    }
                }
            }
        };

        _repositoryContext.GetProductGroupsForEngineerAsync(message.Engineer.Id).Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _engineerCascader.CascadeEngineerDeleteForProductGroupsAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();

        productGroups[0].Supervisors.Items.Should().NotContain(s => s.Id == "engineerId");
        productGroups[0].ProductTeams.Items[0].Members.Items.Should().NotContain(m => m.Id == "engineerId");
    }


    #endregion

}