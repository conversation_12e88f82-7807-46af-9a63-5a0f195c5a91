using System.Linq.Expressions;
using Akka.Actor;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.SonarQube;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MockQueryable.NSubstitute;
using Newtonsoft.Json;
using NSubstitute;
using Xunit;
using Repository = Hubtel.CCI.Api.Data.Entities.Repository;
namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class SonarQubeServiceTests
{
    private readonly SonarQubeService _sonarQubeService;
    private readonly DiFixture _fixture;

    public SonarQubeServiceTests()
    {
        IOptions<SonarQubeConfig> sonarQubeConfig = Microsoft.Extensions.Options.Options.Create(new SonarQubeConfig
        {
            Host = "https://sonarqube.example.com",
            Token = "token"
        });
        _fixture = new DiFixture();
        _sonarQubeService = new SonarQubeService(
            Substitute.For<ILogger<SonarQubeService>>(),
            sonarQubeConfig,
            _fixture.RepositoryContext,
            _fixture.MainActorService,
            _fixture.BypassedPrService);
    }


    #region Get New Metrics On Pr Async

    [Fact]
    public async Task GetNewMetricsOnPrAsync_Should_Return_NotFound_When_Repository_Is_Not_Found()
    {
        // Arrange
        const int pullRequestId = 1;
        const string repositoryId = "repository-id";

        var data = new List<Repository>().AsQueryable().BuildMock();
        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _sonarQubeService.GetNewMetricsOnPrAsync(pullRequestId, repositoryId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Data.Should().BeNull();
        }
    }




    [Fact]
    public async Task GetNewMetricsOnPrAsync_Should_Return_Success_When_Repository_Is_Found()
    {
        // Arrange
        const int pullRequestId = 1;
        const string repositoryId = "repository-id";

        using var httpTest = new HttpTest();



        var data = new Repository
        {
            Id = repositoryId,
            Name = "Test Repository"
        };

        _fixture.RepositoryContext.RepositoryRepository
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>())
            .Returns(data);

        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent()
        };

        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 200);


        // Act
        var result = await _sonarQubeService.GetNewMetricsOnPrAsync(pullRequestId, repositoryId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
        }
    }


    [Fact]
    public async Task GetNewMetricsOnPrAsync_Should_Return_NotFound_When_Get_SonarQube_Metrics_Fails()
    {
        // Arrange
        const int pullRequestId = 1;
        const string repositoryId = "repository-id";

        using var httpTest = new HttpTest();



        var data = new Repository
        {
            Id = repositoryId,
            Name = "Test Repository"
        };

        _fixture.RepositoryContext.RepositoryRepository
            .FindOneAsync(Arg.Any<Expression<Func<Repository, bool>>>())
            .Returns(data);

        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent()
        };

        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 500);


        // Act
        var result = await _sonarQubeService.GetNewMetricsOnPrAsync(pullRequestId, repositoryId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Data.Should().BeNull();
        }
    }


    #endregion

    #region Fetch Engineers Sonar Qube Metrics Async

    [Fact]
    public async Task
        FetchEngineersSonarQubeMetricsAsync_Should_Return_Success_With_False_When_Publication_Already_Exists_For_Date()
    {
        // Arrange
        var publication = Miscellaneous.GetCurrentPublicationTargetDay();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(new List<EngineerSonarQubeMetrics>()
            {
                new EngineerSonarQubeMetrics(){PublicationDate = publication}
            }.AsQueryable().BuildMock());

        // Act
        var result = await _sonarQubeService.FetchEngineersSonarQubeMetricsAsync();

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task FetchEngineersSonarQubeMetricsAsync_Should_Return_Success_When_Publication_Does_Not_Exist()
    {
        // Arrange
        var publication = Miscellaneous.GetCurrentPublicationTargetDay();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(new List<EngineerSonarQubeMetrics>()
            {
                new EngineerSonarQubeMetrics(){PublicationDate = publication.AddDays(-1)}
            }.AsQueryable().BuildMock());

        _fixture.MainActorService
            .Tell(Arg.Any<EngineerSonarQubeMetricsMessage>(), Arg.Any<IActorRef>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sonarQubeService.FetchEngineersSonarQubeMetricsAsync();

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeTrue();
        }
    }

    #endregion

    #region Execute Engineer Sq Metrics Flow Async

    [Fact]
    public async Task ExecuteEngineerSqMetricsFlowAsync_Should_Return_True_On_Success()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        
        _fixture.BypassedPrService.FetchAllRepositoriesAsync()
            .Returns(new Dictionary<string, List<AzureRepositoryDto>>()
        {
            {"repository-id", [ new AzureRepositoryDto()
            {
                Id = Guid.Empty,
                AzureRepoId = "azure-repo-id",
                Name = "Test Repository",
                ProjectName = "Test Project",
                WebUrl = "https://example.com/repo"
            }] }
        });
        
        var message = new EngineerSonarQubeMetricsMessage()
        {
            PublicationDate = publicationDate,
        };

        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Url = "https://example.com/repo1",
                    SonarQubeKey = "repository-id-sq",
                }
            }.BuildMock());

        _fixture.EngineerRepository
            .GetQueryable()
            .Returns(new List<Engineer>()
            {
                new Engineer()
                {
                    Id = "engineer-id",
                    Name = "Engineer Name",
                    Email = "<EMAIL>",
                    Domain = ["Backend"],
                }
            }.BuildMock());
        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(new List<EngineerSonarQubeMetrics>()
            {
                new EngineerSonarQubeMetrics()
                {
                    PublicationDate = DateTime.UtcNow,
                    RepositoryId = "repository-id",
                    EngineerEmail = "<EMAIL>",
                    EngineerName = "Quinton Assan",
                    SonarQubeMetrics = new SonarQubeComponent()
                    {
                        Key = "repository-id-sq",
                        Name = "Test Repository",
                        Measures = [
                            new SonarQubeMeasure()
                            {
                                Metric = "new_coverage",
                                Period = new Period()
                                {
                                    Index = 1,
                                    Value = "100"
                                }
                            }
                        ]
                    }
                }
            }.BuildMock());

        _fixture.RepositoryContext.CciEngineerScoreRepository.AddRangeAsync(Arg.Any<List<CciEngineerScore>>())
            .Returns(1);

        var data = new List<CciEngineerScore>()
        {
            new CciEngineerScore()
            {
                EngineerDomain = "Backend",
                EngineerEmail = "<EMAIL>",
                EngineerName = "Quinton Assan",
                PublicationDate = publicationDate,
                Score = 95.5m,
                PublicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate),
                AverageCoverage = 100m,
                AverageDuplications = 0m,
                TotalBugs = 0m,
                TotalCodeSmells = 0m,
                TotalVulnerabilities = 0m,
                TotalLinesOfCode = 0m,
                TotalPullRequests = 3m,
                TotalSecurityHotspots = 0m
            }
        }.AsQueryable().BuildMockDbSet();

        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data,data);

        _fixture.RepositoryContext.CciEngineerRankingRepository
            .AddAsync(Arg.Any<CciEngineerRanking>()).Returns(1);

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .AddAsync(Arg.Any<CciEngineersScoreStatistic>()).Returns(1);
        // Act
        var result = await _sonarQubeService.ExecuteEngineerSqMetricsFlowAsync(message);


        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }

    
    [Fact]
    public async Task ExecuteEngineerSqMetricsFlowAsync_Should_Return_True_On_Success_When_Repository_With_WebUrl_Exists()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        _fixture.BypassedPrService.FetchAllRepositoriesAsync()
            .Returns(new Dictionary<string, List<AzureRepositoryDto>>()
        {
            {"repository-id", [ new AzureRepositoryDto()
            {
                Id = Guid.Empty,
                AzureRepoId = "azure-repo-id",
                Name = "Test Repository",
                ProjectName = "Test Project",
                WebUrl = "https://example.com/repo"
            }] }
        });
        
        var message = new EngineerSonarQubeMetricsMessage()
        {
            PublicationDate = DateTime.UtcNow,
        };

        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Url = "https://example.com/repo",
                }
            }.BuildMock());
        
        _fixture.EngineerRepository
            .GetQueryable()
            .Returns(new List<Engineer>()
            {
                new Engineer()
                {
                    Id = "engineer-id",
                    Name = "Engineer Name",
                    Domain = ["Backend"],
                }
            }.BuildMock());
        
        var pullRequests = new List<PullRequest>
        {
            new PullRequest
            {
                PullRequestId = 12345,
                Title = "Test PR",
                Url = "https://example.com/pr"
            }
        };
        
        _fixture.BypassedPrService
            .FetchCompletedPullRequests(Arg.Any<DateTime>(), Arg.Any<DateTime>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(callInfo => pullRequests.ToAsyncEnumerable());
        
        var httpTest = new HttpTest();
        
        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent()
            {
                Key = "repository-id-sq",
                Name = "Test Repository",
                Measures = [
                    new SonarQubeMeasure()
                    {
                        Metric = "new_coverage",
                        Period = new Period()
                        {
                            Index = 1,
                            Value = "100"
                        }
                    }
                ]
            }
        };
        
        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 200);
        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(new List<EngineerSonarQubeMetrics>()
            {
                new EngineerSonarQubeMetrics()
                {
                    PublicationDate = DateTime.UtcNow,
                    RepositoryId = "repository-id",
                    EngineerEmail = "<EMAIL>",
                    EngineerName = "Quinton Assan",
                    SonarQubeMetrics = new SonarQubeComponent()
                    {
                        Key = "repository-id-sq",
                        Name = "Test Repository",
                        Measures = [
                            new SonarQubeMeasure()
                            {
                                Metric = "new_coverage",
                                Period = new Period()
                                {
                                    Index = 1,
                                    Value = "100"
                                }
                            }
                        ]
                    }
                }
            }.BuildMock());

        _fixture.RepositoryContext.CciEngineerScoreRepository.AddRangeAsync(Arg.Any<List<CciEngineerScore>>())
            .Returns(1);
        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(new List<CciEngineerScore>()
            {
                new CciEngineerScore()
                {
                    EngineerDomain = "Backend",
                    EngineerEmail = "<EMAIL>",
                    EngineerName = "Quinton Assan",
                    PublicationDate = publicationDate,
                    Score = 95.5m,
                    PublicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate),
                    AverageCoverage = 100m,
                    AverageDuplications = 0m,
                    TotalBugs = 0m,
                    TotalCodeSmells = 0m,
                    TotalVulnerabilities = 0m,
                    TotalLinesOfCode = 0m,
                    TotalPullRequests = 3m,
                    TotalSecurityHotspots = 0m
                }
            }.BuildMock());

        _fixture.RepositoryContext.CciEngineerRankingRepository
            .AddAsync(Arg.Any<CciEngineerRanking>()).Returns(1);

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .AddAsync(Arg.Any<CciEngineersScoreStatistic>()).Returns(1);
        
        // Act
        var result = await _sonarQubeService.ExecuteEngineerSqMetricsFlowAsync(message);


        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }

    
        [Fact]
    public async Task ExecuteEngineerSqMetricsFlowAsync_Should_Return_True_On_Success_When_Repository_With_WebUrl_Exists_But_No_SonarQube_Metrics_Found()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        _fixture.BypassedPrService.FetchAllRepositoriesAsync()
            .Returns(new Dictionary<string, List<AzureRepositoryDto>>()
        {
            {"repository-id", [ new AzureRepositoryDto()
            {
                Id = Guid.Empty,
                AzureRepoId = "azure-repo-id",
                Name = "Test Repository",
                ProjectName = "Test Project",
                WebUrl = "https://example.com/repo"
            }] }
        });
        
        var message = new EngineerSonarQubeMetricsMessage()
        {
            PublicationDate = DateTime.UtcNow,
        };

        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Url = "https://example.com/repo",
                }
            }.BuildMock());
        
        
        _fixture.EngineerRepository
            .GetQueryable()
            .Returns(new List<Engineer>()
            {
                new Engineer()
                {
                    Id = "engineer-id",
                    Name = "Engineer Name",
                    Domain = ["Backend"],
                }
            }.BuildMock());
        
        var pullRequests = new List<PullRequest>
        {
            new PullRequest
            {
                PullRequestId = 12345,
                Title = "Test PR",
                Url = "https://example.com/pr"
            }
        };
        
        _fixture.BypassedPrService
            .FetchCompletedPullRequests(Arg.Any<DateTime>(), Arg.Any<DateTime>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(callInfo => pullRequests.ToAsyncEnumerable());
        
        var httpTest = new HttpTest();
        
        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent()
            {
                Key = "repository-id-sq",
                Name = "Test Repository",
                Measures = [
                    new SonarQubeMeasure()
                    {
                        Metric = "new_coverage",
                        Period = new Period()
                        {
                            Index = 1,
                            Value = "100"
                        }
                    }
                ]
            }
        };
        
        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 500);
        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(new List<EngineerSonarQubeMetrics>()
            {
                new EngineerSonarQubeMetrics()
                {
                    PublicationDate = DateTime.UtcNow,
                    RepositoryId = "repository-id",
                    EngineerEmail = "<EMAIL>",
                    EngineerName = "Quinton Assan",
                    SonarQubeMetrics = new SonarQubeComponent()
                    {
                        Key = "repository-id-sq",
                        Name = "Test Repository",
                        Measures = [
                            new SonarQubeMeasure()
                            {
                                Metric = "new_coverage",
                                Period = new Period()
                                {
                                    Index = 1,
                                    Value = "100"
                                }
                            }
                        ]
                    }
                }
            }.BuildMock());
        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(new List<CciEngineerScore>()
            {
                new CciEngineerScore()
                {
                    EngineerDomain = "Backend",
                    EngineerEmail = "<EMAIL>",
                    EngineerName = "Quinton Assan",
                    PublicationDate = publicationDate,
                    Score = 95.5m,
                    PublicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate),
                    AverageCoverage = 100m,
                    AverageDuplications = 0m,
                    TotalBugs = 0m,
                    TotalCodeSmells = 0m,
                    TotalVulnerabilities = 0m,
                    TotalLinesOfCode = 0m,
                    TotalPullRequests = 3m,
                    TotalSecurityHotspots = 0m
                }
            }.BuildMock());

        _fixture.RepositoryContext.CciEngineerRankingRepository
            .AddAsync(Arg.Any<CciEngineerRanking>()).Returns(1);

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .AddAsync(Arg.Any<CciEngineersScoreStatistic>()).Returns(1);
        
        // Act
        var result = await _sonarQubeService.ExecuteEngineerSqMetricsFlowAsync(message);


        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }

    #endregion

    #region ProcessRepositoryAsync

    [Fact]
    public async Task ProcessRepositoryAsync_Should_ReturnFalse_When_Repository_Not_Found()
    {
        // Arrange
        var repo = new AzureRepositoryDto { WebUrl = "missing-url" };
        _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository>().BuildMock().AsQueryable());

        // Act
        var result = await _sonarQubeService.ProcessRepositoryAsync("project", DateTime.Today, repo, CancellationToken.None);

        // Assert
        result.Should().BeFalse(); // ensures mutation to true is killed
    }
    
    [Fact]
    public async Task ProcessRepositoryAsync_Should_ReturnTrue_When_No_PR_Metrics_Found()
    {
        // Arrange
        var repo = new AzureRepositoryDto { WebUrl = "url", AzureRepoId = "repo-id", Name = "Repo" };
        var publicationDate = DateTime.Today;

        var repository = new Repository { Url = "url", Id = "id", Type = "git", SonarQubeKey = "key" };
        _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository> { repository }.BuildMock().AsQueryable());

        _fixture.BypassedPrService.FetchCompletedPullRequests(
                Arg.Any<DateTime>(), Arg.Any<DateTime>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(AsyncEnumerable.Empty<PullRequest>());

        // Act
        var result = await _sonarQubeService.ProcessRepositoryAsync("project", publicationDate, repo, CancellationToken.None);

        // Assert
        result.Should().BeTrue(); // ensures mutation to false is killed
    }
    
    
    [Fact]
    public async Task ProcessRepositoryAsync_Should_ReturnTrue_When_PR_Metrics_Exist()
    {
        // Arrange
        var repo = new AzureRepositoryDto { WebUrl = "url", AzureRepoId = "repo-id", Name = "Repo" };
        var publicationDate = DateTime.Today;

        var repository = new Repository
        {
            Url = "url",
            Id = "id",
            Type = "git",
            SonarQubeKey = "sonar-key"
        };

        _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository> { repository }.BuildMock().AsQueryable());

        var pullRequest = new PullRequest
        {
            PullRequestId = 123,
            CreatedBy = new CreatedBy()
            {
                DisplayName = "Engineer", UniqueName = "<EMAIL>"
            }
        };

        _fixture.BypassedPrService.FetchCompletedPullRequests(
                Arg.Any<DateTime>(), Arg.Any<DateTime>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(AsyncEnumerable.Repeat(pullRequest, 1));

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(new SonarQubeMetricsResponse
        {
            Component = new SonarQubeComponent
            {
                Key = "sonar-key",
                Name = "Repo",
                Measures = new List<SonarQubeMeasure>
                {
                    new() { Metric = "new_bugs", Value = "0", Period = new Period { Index = 1, Value = "2024-01-01", BestValue = true } },
                    new() { Metric = "new_code_smells", Value = "5", Period = new Period { Index = 1, Value = "2024-01-01", BestValue = false } }
                }
            }
        });

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .AddRangeAsync(Arg.Any<List<EngineerSonarQubeMetrics>>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _sonarQubeService.ProcessRepositoryAsync("project", publicationDate, repo, CancellationToken.None);

        // Assert
        result.Should().BeTrue(); // ensures mutation to false is killed
    }





    #endregion

    #region ComputeRepoScoreAsync

    [Fact]
    public void ComputeRepoScoreAsync_Should_Calculate_Correct_Score_Based_On_Metrics()
    {
        // Arrange
        var sonarKey = "repo-key";

        var metrics = new List<EngineerSonarQubeMetrics>
        {
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_bugs", Period = new Period { Value = "2" } },
                        new() { Metric = "new_vulnerabilities", Period = new Period { Value = "1" } },
                        new() { Metric = "new_code_smells", Period = new Period { Value = "10" } },
                        new() { Metric = "cognitive_complexity", Period = new Period { Value = "5" } },
                        new() { Metric = "new_coverage", Period = new Period { Value = "80" } },
                        new() { Metric = "new_duplicated_lines_density", Period = new Period { Value = "3" } }
                    }
                }
            },
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_bugs", Period = new Period { Value = "1" } },
                        new() { Metric = "new_vulnerabilities", Period = new Period { Value = "0" } },
                        new() { Metric = "new_code_smells", Period = new Period { Value = "5" } },
                        new() { Metric = "cognitive_complexity", Period = new Period { Value = "2" } },
                        new() { Metric = "new_coverage", Period = new Period { Value = "90" } },
                        new() { Metric = "new_duplicated_lines_density", Period = new Period { Value = "2" } }
                    }
                }
            }
        };

        var repositories = new List<Repository>
        {
            new()
            {
                SonarQubeKey = "repo-key",
                Type = "Backend",
                SemanticScoreComputation = 10,
                FrameworkUpgradeComputation = 15
            }
        };
        // Act
        var score = _sonarQubeService.ComputeRepoScoreAsync(sonarKey, metrics, repositories);

        // Assert
        // Expected:
        // Bugs = 3
        // Vulnerabilities = 1
        // CodeSmells = 15
        // Cognitive = 7
        // Coverage = (80 + 90) / 2 = 85
        // Duplications = (3 + 2) / 2 = 2.5
        // Semantic = 10, Framework = 15
        // Final score = evaluated via CciEvaluator for "Backend"
        score.Should().BeGreaterThan(0); // Basic assertion to ensure real computation
    }
    
    [Fact]
    public void ComputeRepoScoreAsync_Should_Use_Average_For_DuplicatedLinesDensity()
    {
        // Arrange
        var sonarKey = "repo-key";

        var metrics = new List<EngineerSonarQubeMetrics>
        {
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_duplicated_lines_density", Period = new Period { Value = "2.5" } }
                    }
                }
            },
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_duplicated_lines_density", Period = new Period { Value = "7.5" } }
                    }
                }
            }
        };

        var repositories = new List<Repository>
        {
            new()
            {
                SonarQubeKey = "repo-key",
                Type = "Backend",
                SemanticScoreComputation = 0,
                FrameworkUpgradeComputation = 0
            }
        };
        
        // Act
        var score = _sonarQubeService.ComputeRepoScoreAsync(sonarKey, metrics, repositories);

        // Assert
        // Average(2.5, 7.5) = 5.0, but if Min(2.5, 7.5) = 2.5 → Mutation would change the final score
        // Ensure the score reflects average
        var duplicatedDensityUsed = 5.0m;
        var expectedScore = CciEvaluator.ComputeEngineerFinalAverage(
            "Backend",
            new CciMetrics()
            {
                Bugs = 0,
                Vulnerabilities = 0,
                CodeSmells = 0,
                CognitiveComplexity = 0,
                Coverage = 0,
                DuplicatedLinesDensity = duplicatedDensityUsed,
                SemanticScore = 0,
                FrameworkUpgrade = 0
            });

        score.Should().Be(expectedScore);
    }
    
    
    [Fact]
    public void ComputeRepoScoreAsync_Should_Use_Average_For_Coverage()
    {
        // Arrange
        var sonarKey = "repo-key";

        var metrics = new List<EngineerSonarQubeMetrics>
        {
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_coverage", Period = new Period { Value = "60.0" } }
                    }
                }
            },
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_coverage", Period = new Period { Value = "80.0" } }
                    }
                }
            }
        };

        var repositories = new List<Repository>
        {
            new()
            {
                SonarQubeKey = sonarKey,
                Type = "Backend",
                SemanticScoreComputation = 0,
                FrameworkUpgradeComputation = 0
            }
        };

        var expectedCoverage = (60.0m + 80.0m) / 2; // 70.0

        var expectedScore = CciEvaluator.ComputeEngineerFinalAverage(
            "Backend",
            new CciMetrics
            {
                Bugs = 0,
                Vulnerabilities = 0,
                CodeSmells = 0,
                CognitiveComplexity = 0,
                Coverage = expectedCoverage,
                DuplicatedLinesDensity = 0,
                SemanticScore = 0,
                FrameworkUpgrade = 0
            });

        // Act
        var score = _sonarQubeService.ComputeRepoScoreAsync(sonarKey, metrics, repositories);

        // Assert
        score.Should().Be(expectedScore);
    }


    
    [Fact]
    public void ComputeRepoScoreAsync_Should_Compute_CorrectScore_From_Metrics()
    {
        // Arrange
        var sonarKey = "repo-key";
        var repositories = new List<Repository>
        {
            new Repository
            {
                SonarQubeKey = sonarKey,
                SemanticScoreComputation = 0.3m,
                FrameworkUpgradeComputation = 0.2m,
                Type = "DotNet"
            }
        };

        var metrics = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_bugs", Period = new Period { Value = "1" } },
                        new() { Metric = "new_vulnerabilities", Period = new Period { Value = "2" } },
                        new() { Metric = "new_code_smells", Period = new Period { Value = "3" } },
                        new() { Metric = "cognitive_complexity", Period = new Period { Value = "4" } },
                        new() { Metric = "new_coverage", Period = new Period { Value = "60" } },
                        new() { Metric = "new_duplicated_lines_density", Period = new Period { Value = "5" } }
                    }
                }
            },
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_bugs", Period = new Period { Value = "2" } },
                        new() { Metric = "new_vulnerabilities", Period = new Period { Value = "3" } },
                        new() { Metric = "new_code_smells", Period = new Period { Value = "4" } },
                        new() { Metric = "cognitive_complexity", Period = new Period { Value = "6" } },
                        new() { Metric = "new_coverage", Period = new Period { Value = "80" } },
                        new() { Metric = "new_duplicated_lines_density", Period = new Period { Value = "7" } }
                    }
                }
            }
        };

        // Act
        var score = _sonarQubeService.ComputeRepoScoreAsync(sonarKey, metrics, repositories);

        // Assert
        // Raw values (to help verify):
        // Bugs = 1+2 = 3
        // Vulnerabilities = 2+3 = 5
        // CodeSmells = 3+4 = 7
        // Cognitive = 4+6 = 10
        // Coverage = (60+80)/2 = 70
        // Duplication = (5+7)/2 = 6
        // Semantic = 0.3, Framework = 0.2
        score.Should().Be(CciEvaluator.ComputeEngineerFinalAverage("DotNet", new CciMetrics
        {
            Bugs = 3,
            Vulnerabilities = 5,
            CodeSmells = 7,
            CognitiveComplexity = 10,
            Coverage = 70,
            DuplicatedLinesDensity = 6,
            SemanticScore = 0.3m,
            FrameworkUpgrade = 0.2m
        }));
    }
    
    
    [Fact]
    public void ComputeRepoScoreAsync_Should_Use_Default_Score_When_Repository_NotFound()
    {
        // Arrange
        var sonarKey = "missing-key";
        var repositories = new List<Repository>(); // Empty list

        var metrics = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = "new_bugs", Period = new Period { Value = "1" } }
                    }
                }
            }
        };
        
        // Act
        var score = _sonarQubeService.ComputeRepoScoreAsync(sonarKey, metrics, repositories);

        // Assert
        score.Should().Be(CciEvaluator.ComputeEngineerFinalAverage("", new CciMetrics
        {
            Bugs = 1,
            Vulnerabilities = 0,
            CodeSmells = 0,
            CognitiveComplexity = 0,
            Coverage = 0,
            DuplicatedLinesDensity = 0,
            SemanticScore = 0,
            FrameworkUpgrade = 0
        }));
    }
    
    
    [Fact]
    public void ComputeRepoScoreAsync_Should_Use_Repository_With_Matching_SonarQubeKey()
    {
        // Arrange
        var sonarKey = "repo-key-1";

        var metrics = new List<EngineerSonarQubeMetrics>
        {
            new()
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = new List<SonarQubeMeasure>
                    {
                        new() { Metric = ValidationConstants.SonarMetricKeys.NewBugs, Period = new Period { Value = "1" } },
                        new() { Metric = ValidationConstants.SonarMetricKeys.NewVulnerabilities, Period = new Period { Value = "2" } },
                        new() { Metric = ValidationConstants.SonarMetricKeys.NewCodeSmells, Period = new Period { Value = "3" } },
                        new() { Metric = ValidationConstants.SonarMetricKeys.CognitiveComplexity, Period = new Period { Value = "4" } },
                        new() { Metric = ValidationConstants.SonarMetricKeys.NewCoverage, Period = new Period { Value = "70" } },
                        new() { Metric = ValidationConstants.SonarMetricKeys.NewDuplicatedLinesDensity, Period = new Period { Value = "5" } }
                    }
                }
            }
        };

        var correctRepo = new Repository
        {
            SonarQubeKey = "repo-key-1",
            SemanticScoreComputation = 0.5m,
            FrameworkUpgradeComputation = 0.3m,
            Type = "DotNet"
        };

        var wrongRepo = new Repository
        {
            SonarQubeKey = "repo-key-2", // should be ignored
            SemanticScoreComputation = 0.0m,
            FrameworkUpgradeComputation = 0.0m,
            Type = "DotNet"
        };

        var repositories = new List<Repository> { wrongRepo, correctRepo };

        var score = _sonarQubeService.ComputeRepoScoreAsync(sonarKey, metrics, repositories);

        // Act
        var scoreWithWrongRepo = _sonarQubeService.ComputeRepoScoreAsync("repo-key-2", metrics, new List<Repository> { correctRepo });

        // Assert
        score.Should().NotBe(scoreWithWrongRepo); // ensures it selected the correct repo and not any unmatched one
    }





    #endregion

    #region BuildCciEngineerScoreAsync

    [Fact]
    public void BuildCciEngineerScoreAsync_Should_Use_Engineer_With_Matching_Email()
    {
        // Arrange
        var engineerEmail = "<EMAIL>";
        var matchingEngineer = new Engineer { Email = engineerEmail, Id = "eng123", Name = "Test User", Domain = new List<string> { "Backend" } };
        var nonMatchingEngineer = new Engineer { Email = "<EMAIL>" };
    
        var engineers = new List<Engineer> { nonMatchingEngineer, matchingEngineer };

        var prList = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Key = "repo-key",
                    Measures = [
                        new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "0" } },
                        new SonarQubeMeasure { Metric = "new_vulnerabilities", Period = new Period { Value = "0" } },
                        new SonarQubeMeasure { Metric = "new_code_smells", Period = new Period { Value = "0" } },
                        new SonarQubeMeasure { Metric = "cognitive_complexity", Period = new Period { Value = "0" } },
                        new SonarQubeMeasure { Metric = "new_coverage", Period = new Period { Value = "100" } },
                        new SonarQubeMeasure { Metric = "new_duplicated_lines_density", Period = new Period { Value = "0" } }
                    ]
                }
            }
        };

        var allRepositories = new List<Repository>
        {
            new Repository { SonarQubeKey = "repo-key", Type = "Backend" }
        };
        // Act
        var result = _sonarQubeService.BuildCciEngineerScoreAsync(engineerEmail, prList, DateTime.UtcNow, engineers, allRepositories);

        // Assert
        result.EngineerEmail.Should().Be(engineerEmail);
        result.EngineerId.Should().Be("eng123");
        result.EngineerName.Should().Be("Test User");
    }
    
    
    [Fact]
    public void BuildCciEngineerScoreAsync_Should_Include_Repository_Regardless_Of_Engineer_Domain()
    {
        // Arrange
        var engineerEmail = "<EMAIL>";
        var engineer = new Engineer
        {
            Email = engineerEmail,
            Id = "eng123",
            Name = "Test User",
            Domain = new List<string> { "Frontend" } // Not relevant to logic
        };

        var engineers = new List<Engineer> { engineer };

        var prList = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Key = "repo-key",
                    Measures = [
                        new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "1" } },
                        new SonarQubeMeasure { Metric = "new_vulnerabilities", Period = new Period { Value = "1" } },
                        new SonarQubeMeasure { Metric = "new_code_smells", Period = new Period { Value = "1" } },
                        new SonarQubeMeasure { Metric = "cognitive_complexity", Period = new Period { Value = "1" } },
                        new SonarQubeMeasure { Metric = "new_coverage", Period = new Period { Value = "0" } },
                        new SonarQubeMeasure { Metric = "new_duplicated_lines_density", Period = new Period { Value = "1" } }
                    ]
                }
            }
        };

        var allRepositories = new List<Repository>
        {
            new Repository { SonarQubeKey = "repo-key", Type = "Backend" } // Still included
        };

        // Act
        var result = _sonarQubeService.BuildCciEngineerScoreAsync(engineerEmail, prList, DateTime.UtcNow, engineers, allRepositories);

        // Assert - score should reflect the PR was included
        result.Score.Should().BeGreaterThan(0);
    }
    
    
    
    [Fact]
public void BuildCciEngineerScoreAsync_Should_Calculate_Average_Score_From_Multiple_Prs()
{
    // Arrange
    var engineerEmail = "<EMAIL>";
    var engineer = new Engineer
    {
        Email = engineerEmail,
        Id = "eng123",
        Name = "Test User",
        Domain = new List<string> { "Backend" }
    };

    var engineers = new List<Engineer> { engineer };

    var prList = new List<EngineerSonarQubeMetrics>
    {
        new EngineerSonarQubeMetrics
        {
            SonarQubeMetrics = new SonarQubeComponent
            {
                Key = "repo-key",
                Measures = [
                    new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "0" } },
                    new SonarQubeMeasure { Metric = "new_vulnerabilities", Period = new Period { Value = "0" } },
                    new SonarQubeMeasure { Metric = "new_code_smells", Period = new Period { Value = "0" } },
                    new SonarQubeMeasure { Metric = "cognitive_complexity", Period = new Period { Value = "0" } },
                    new SonarQubeMeasure { Metric = "new_coverage", Period = new Period { Value = "100" } },
                    new SonarQubeMeasure { Metric = "new_duplicated_lines_density", Period = new Period { Value = "0" } }
                ]
            }
        },
        new EngineerSonarQubeMetrics
        {
            SonarQubeMetrics = new SonarQubeComponent
            {
                Key = "repo-key",
                Measures = [
                    new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "1" } },
                    new SonarQubeMeasure { Metric = "new_vulnerabilities", Period = new Period { Value = "1" } },
                    new SonarQubeMeasure { Metric = "new_code_smells", Period = new Period { Value = "1" } },
                    new SonarQubeMeasure { Metric = "cognitive_complexity", Period = new Period { Value = "1" } },
                    new SonarQubeMeasure { Metric = "new_coverage", Period = new Period { Value = "50" } },
                    new SonarQubeMeasure { Metric = "new_duplicated_lines_density", Period = new Period { Value = "1" } }
                ]
            }
        }
    };

    var allRepositories = new List<Repository>
    {
        new Repository { SonarQubeKey = "repo-key", Type = "Backend" }
    };

    // Act
    var result = _sonarQubeService.BuildCciEngineerScoreAsync(engineerEmail, prList, DateTime.UtcNow, engineers, allRepositories);

    // Assert - score should reflect average from two PRs
    result.Score.Should().BeGreaterThan(0).And.BeLessThan(100);
}



    [Fact]
    public void BuildCciEngineerScoreAsync_Should_Not_Use_Engineer_With_NonMatching_Email()
    {
        // Arrange
        var engineerEmail = "<EMAIL>";
        var engineers = new List<Engineer>
        {
            new Engineer { Email = "<EMAIL>", Id = "eng999", Name = "Wrong User", Domain = new List<string> { "Mismatch" } }
        };

        var prList = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Key = "repo-key",
                    Measures = [ new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "0" } } ]
                }
            }
        };

        var allRepositories = new List<Repository>
        {
            new Repository { SonarQubeKey = "repo-key", Type = "Backend" }
        };

        // Act
        var result = _sonarQubeService.BuildCciEngineerScoreAsync(engineerEmail, prList, DateTime.UtcNow, engineers, allRepositories);

        // Assert
        result.EngineerId.Should().Be(engineerEmail);
        result.EngineerName.Should().Be(engineerEmail);
    }

    
    [Fact]
    public void BuildCciEngineerScoreAsync_Should_Return_Correct_Engineer_Domain()
    {
        // Arrange
        var engineerEmail = "<EMAIL>";
        var engineer = new Engineer
        {
            Email = engineerEmail,
            Id = "eng789",
            Name = "Domain Tester",
            Domain = new List<string> { "Platform", "Backend" }
        };

        var engineers = new List<Engineer> { engineer };

        var prList = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Key = "repo-key",
                    Measures = [ new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "0" } } ]
                }
            }
        };

        var allRepositories = new List<Repository>
        {
            new Repository { SonarQubeKey = "repo-key", Type = "Platform" }
        };

        // Act
        var result = _sonarQubeService.BuildCciEngineerScoreAsync(engineerEmail, prList, DateTime.UtcNow, engineers, allRepositories);

        // Assert
        result.EngineerDomain.Should().Be("Platform"); // Ensures .First() is not accidentally used over .FirstOrDefault()
    }
    
    
    [Fact]
    public void BuildCciEngineerScoreAsync_Should_Not_Use_Min_Score_For_Final_Score()
    {
        // Arrange
        var engineerEmail = "<EMAIL>";
        var engineer = new Engineer
        {
            Email = engineerEmail,
            Id = "eng456",
            Name = "Scorer",
            Domain = new List<string> { "Backend" }
        };

        var engineers = new List<Engineer> { engineer };

        var prList = new List<EngineerSonarQubeMetrics>
        {
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Key = "repo-key",
                    Measures =
                    [
                        new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "0" } },
                        new SonarQubeMeasure { Metric = "new_coverage", Period = new Period { Value = "90" } }
                    ]
                }
            },
            new EngineerSonarQubeMetrics
            {
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Key = "repo-key",
                    Measures =
                    [
                        new SonarQubeMeasure { Metric = "new_bugs", Period = new Period { Value = "1" } },
                        new SonarQubeMeasure { Metric = "new_coverage", Period = new Period { Value = "30" } }
                    ]
                }
            }
        };

        var allRepositories = new List<Repository>
        {
            new Repository { SonarQubeKey = "repo-key", Type = "Backend" }
        };

        // Act
        var result = _sonarQubeService.BuildCciEngineerScoreAsync(engineerEmail, prList, DateTime.UtcNow, engineers, allRepositories);

        // Assert
        var firstScore = _sonarQubeService.ComputeRepoScoreAsync("repo-key", [prList[0]], allRepositories);
        var secondScore = _sonarQubeService.ComputeRepoScoreAsync("repo-key", [prList[1]], allRepositories);

        result.Score.Should().BeGreaterThan(Math.Min(firstScore, secondScore));
        result.Score.Should().BeLessThan(Math.Max(firstScore, secondScore));

    }





    #endregion

    #region FetchRepositoryLanguageDistributionAsync
    
    [Fact]
    public async Task FetchRepositoryLanguageDistributionAsync_Should_Return_True_When_Language_Distribution_Fetched_Successfully()
    {
        // Arrange
        const string repositoryId = "repo-id";

        _fixture.RepositoryContext.RepositoryLanguageDistributionRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageDistribution>
            {
                new RepositoryLanguageDistribution
                {
                    RepositoryId = repositoryId,
                }
            }.BuildMock().AsQueryable());

        _fixture.MainActorService.Tell(Arg.Any<RepositoryLanguageDistributionMessage>(), Arg.Any<IActorRef>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sonarQubeService.FetchRepositoryLanguageDistributionAsync();

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeTrue();
        result.Message.Should().Be("Repository language distribution flow triggered successfully");
    }
    
    [Fact]
    public async Task FetchRepositoryLanguageDistributionAsync_Should_Return_False_When_No_Repositories_Found()
    {
        // Arrange
        const string repositoryId = "repo-id";

        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        _fixture.RepositoryContext.RepositoryLanguageDistributionRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageDistribution>
            {
                new RepositoryLanguageDistribution
                {
                    RepositoryId = repositoryId,
                    PublicationDate = publicationDate
                }
            }.BuildMock().AsQueryable());

        // Act
        var result = await _sonarQubeService.FetchRepositoryLanguageDistributionAsync();

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().BeFalse();
        result.Message.Should().Be($"Repository language distribution for {publicationDate} already exists");
    }
    

    #endregion

    #region ExecuteFetchRepoLanguageDistributionFlowAsync

    [Fact]
    public async Task ExecuteFetchRepoLanguageDistributionFlowAsync_Should_Return_false_When_Projects_Count_Is_0()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        var message = new RepositoryLanguageDistributionMessage()
        {
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek
        };
        
        using var httpTest = new HttpTest();
        httpTest.RespondWith(JsonConvert.SerializeObject(new SonarQubeProjectsResponse()
        {
            Paging = new SonarQubePaging()
            {
                PageSize = 100,
                PageIndex = 1,
                Total = 0
            }
        }), 200);
        
        // Act
        var result = await _sonarQubeService.ExecuteFetchRepoLanguageDistributionFlowAsync(message);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().BeFalse();
    }
    
    
    [Fact]
    public async Task ExecuteFetchRepoLanguageDistributionFlowAsync_Should_Return_True_When_Aggregation_Is_Successful()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        var message = new RepositoryLanguageDistributionMessage()
        {
            PublicationDate = publicationDate,
            PublicationWeek = publicationWeek
        };
        
        using var httpTest = new HttpTest();
        httpTest
            .ForCallsTo("*/api/projects/search")
            .RespondWith(JsonConvert.SerializeObject(new SonarQubeProjectsResponse()
        {
            Paging = new SonarQubePaging()
            {
                PageSize = 100,
                PageIndex = 1,
                Total = 2
            },
            Components = new List<SonarQubeProject>()
            {
                new SonarQubeProject()
                {
                    Key = "repo-id",
                    Name = "Test Repo",
                    Qualifier = "TRK",
                    Visibility = "Public",
                    Managed = false
                },
                new SonarQubeProject()
                {
                    Key = "another-repo-id",
                    Name = "Another Repo",
                    Qualifier = "TRK",
                    Visibility = "Public",
                    Managed = false
                }
            }
        }), 200);
        
        
        httpTest
            .ForCallsTo("*/api/measures/component*")
            .RespondWith(JsonConvert.SerializeObject(new SonarQubeMetricsResponse
            {
                Component = new SonarQubeComponent
                {
                    Key = "repo-id",
                    Name = "Test Repo",
                    Measures = new List<SonarQubeMeasure>
                    {
                        new SonarQubeMeasure { Metric = "ncloc_language_distribution", Value = "cs=5000;js=1000" },
                        new SonarQubeMeasure { Metric = "ncloc", Value = "6000" }
                    }
                }
            }), 200);
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Id = "repo-id",
                    SonarQubeKey = "repo-id",
                    Name = "Test Repo",
                    Type = "git",
                    Url = "https://example.com/repo.git"
                },
                new Repository()
                {
                    Id = "another-repo-id",
                    SonarQubeKey = "another-repo-id",
                    Name = "Another Repo",
                    Type = "git",
                    Url = "https://example.com/another-repo.git"
                }
            }.BuildMock().AsQueryable());

        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(Arg.Any<string>(), Arg.Any<string>(),
                Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(new ProductGroupDetails()
            {
                ProductGroupId = "pg-123",
                ProductGroupName = "Test Product Group",
                ProductTeams = new ProductTeams()
                {
                    Items =
                    [
                        new ProductTeamItem()
                        {
                            Id = "pt-123",
                            Name = "Test Product Team",
                            Repositories = new Data.Entities.Repositories()
                            {
                                Items =
                                [
                                    new RepositoryItem()
                                    {
                                        Id = "repo-id",
                                        SonarQubeKey = "repo-id",
                                    }
                                ]
                            }
                        }
                    ]
                }
            });
        
        _fixture.RepositoryContext.RepositoryLanguageDistributionRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageDistribution>
            {
                new RepositoryLanguageDistribution
                {
                    RepositoryId = "repo-id",
                    PublicationDate = publicationDate.AddDays(-1),
                    PublicationWeek = publicationWeek,
                    RepositoryType = "Backend",
                    LanguageDistributions = [
                        new LanguageDistribution()
                    {
                        Language = "cs",
                        Ncloc = 60000,
                        Percentage = 83.33m,
                        
                    },
                        new LanguageDistribution()
                    {
                        Language = "js",
                        Ncloc = 12000,
                        Percentage = 16.67m
                    }
                    ],
                    NewNcloc = 25,
                    NewLanguageDistributionsDelta = [
                    new LanguageDistribution()
                    {
                        Language = "cs",
                        Ncloc = 60000,
                        Percentage = 83.33m
                    }],
                    ProjectKey = "repo-id",
                },
                new RepositoryLanguageDistribution()
                {
                    RepositoryId = "another-repo-id",
                    PublicationDate = publicationDate,
                    PublicationWeek = publicationWeek,
                    RepositoryType = "Frontend",
                    LanguageDistributions = [
                        new LanguageDistribution()
                    {
                        Language = "js",
                        Ncloc = 10000,
                        Percentage = 100.00m
                    }
                    ],
                    NewNcloc = 13,
                    NewLanguageDistributionsDelta = [
                        new LanguageDistribution()
                        {
                            Language = "js",
                            Ncloc = 10000,
                            Percentage = 100.00m
                        }
                    ]
                }
            }.BuildMock().AsQueryable());

        _fixture.RepositoryContext.RepositoryLanguageStatisticRepository
            .AddAsync(Arg.Any<RepositoryLanguageStatistic>())
            .Returns(1);


        _fixture.RepositoryContext.RepositoryLanguageDistributionRepository
            .AddRangeAsync(Arg.Any<List<RepositoryLanguageDistribution>>()).Returns(2);
        
        
        
        // Act
        var result = await _sonarQubeService.ExecuteFetchRepoLanguageDistributionFlowAsync(message);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }

    #endregion

    #region GetLanguagePublicationStatisticsAsync

    [Fact]
    public async Task GetLanguagePublicationStatisticsAsync_Should_Return_OK_Api_Response_When_Statistics_Is_Available()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);

        var filter = new GetLanguageDistributionStatisticsRequest()
        {
            PublicationDate = publicationDate
        };
        
        _fixture.RepositoryContext.RepositoryLanguageStatisticRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageStatistic>
            {
                new RepositoryLanguageStatistic
                {
                    PublicationDate = publicationDate,
                    PublicationWeek = publicationWeek,
                    TotalLinesOfCode = 10
                }
            }.BuildMock().AsQueryable());
        
        // Act
        var result = await _sonarQubeService.GetLanguagePublicationStatisticsAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data?.TotalLinesOfCode.Should().Be(10);
        result.Message.Should().Be("Language publication statistics fetched successfully");

    }
    
    
    [Fact]
    public async Task GetLanguagePublicationStatisticsAsync_Should_Return_Recent_Statistic_When_No_Filter_Is_Passed()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);

        var filter = new GetLanguageDistributionStatisticsRequest();
        
        _fixture.RepositoryContext.RepositoryLanguageStatisticRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageStatistic>
            {
                new RepositoryLanguageStatistic()
                {
                    PublicationDate = publicationDate,
                    PublicationWeek = publicationWeek,
                    TotalLinesOfCode = 5
                },
                new RepositoryLanguageStatistic()
                {
                    PublicationDate = publicationDate.Date.AddDays(-1),
                    TotalLinesOfCode = 15

                },
                new RepositoryLanguageStatistic()
                {
                    PublicationDate = publicationDate.Date.AddDays(-2),
                    TotalLinesOfCode = 10
                }
            }.BuildMock().AsQueryable());
        
        // Act
        var result = await _sonarQubeService.GetLanguagePublicationStatisticsAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data?.TotalLinesOfCode.Should().Be(5);
        result.Message.Should().Be("Language publication statistics fetched successfully");

    }

    #endregion


    #region GetLanguagePublicationTableAsync
    
    [Fact]
    public async Task GetLanguagePublicationTableAsync_Should_Return_Empty_Table_When_No_Statistics_Available()
    {
        // Arrange
        var filter = new GetLanguageDistributionStatisticsRequest()
        {
            PageIndex = 1,
            PageSize = 10
        };
        
        _fixture.RepositoryContext.RepositoryLanguageDistributionRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageDistribution>().BuildMock().AsQueryable());
        
        // Act
        var result = await _sonarQubeService.GetLanguagePublicationTableAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data?.Results.Should().BeEmpty();
        result.Message.Should().Be("Language publication statistics fetched successfully");
    }
    
    
    [Fact]
    public async Task GetLanguagePublicationTableAsync_Should_Return_Table_With_Statistics_When_Available()
    {
        // Arrange
        var filter = new GetLanguageDistributionStatisticsRequest();
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        
        _fixture.RepositoryContext.RepositoryLanguageDistributionRepository
            .GetQueryable()
            .Returns(new List<RepositoryLanguageDistribution>
            {
                new RepositoryLanguageDistribution()
                {
                    PublicationDate = publicationDate.AddDays(-1),
                    PublicationWeek = publicationWeek,
                    RepositoryId = "repo-id",
                    RepositoryType = "Backend",
                    ProjectName = "Test Project",
                    LanguageDistributions = new List<LanguageDistribution>
                    {
                        new LanguageDistribution { Language = "C#", Ncloc = 50, Percentage = 50 },
                        new LanguageDistribution { Language = "JavaScript", Ncloc = 30, Percentage = 30 },
                        new LanguageDistribution { Language = "Python", Ncloc = 20, Percentage = 20 }
                    },
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                },
                new RepositoryLanguageDistribution()
                {
                    PublicationDate = publicationDate,
                    PublicationWeek = publicationWeek,
                    RepositoryId = "another-repo-id",
                    RepositoryType = "Frontend",
                    ProjectName = "Another Project",
                    LanguageDistributions = new List<LanguageDistribution>
                    {
                        new LanguageDistribution { Language = "JavaScript", Ncloc = 100, Percentage = 100 }
                    },
                    CreatedAt = DateTime.UtcNow
                }
            }.BuildMock().AsQueryable());
        
        // Act
        var result = await _sonarQubeService.GetLanguagePublicationTableAsync(filter, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data?.Results.Should().HaveCount(2);
        result.Data?.Results[0].LanguageDistributions.Should().HaveCount(1);
        result.Data?.Results[0].LanguageDistributions[0].Language.Should().Be("JavaScript");
        result.Data?.Results[0].LanguageDistributions[0].Ncloc.Should().Be(100);
        result.Message.Should().Be("Language publication statistics fetched successfully");
    }

    #endregion
    
}