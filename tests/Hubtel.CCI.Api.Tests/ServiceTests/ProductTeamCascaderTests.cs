using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Cascaders;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ProductTeamCascaderTests
{
    private readonly ProductTeamCascader _productTeamCascader;
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger<ProductTeamCascader> _logger;

    public ProductTeamCascaderTests()
    {
        _repositoryContext = Substitute.For<IRepositoryContext>();
        _logger = Substitute.For<ILogger<ProductTeamCascader>>();
        _productTeamCascader = new ProductTeamCascader(_logger, _repositoryContext);
    }

    #region CascadeProductTeamUpdateForProductGroupsAsync

    [Fact]
    public async Task CascadeProductTeamUpdateForProductGroupsAsync_Should_Return_True_When_Updates_Are_Found()
    {
        // Arrange
        var productTeam = new ProductTeam { Id = "1", Name = "Updated Team" };
        var message = new CascadeProductTeamUpdateMessage(productTeam);
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group1",
                ProductTeams = new ProductTeams
                    { Items = new List<ProductTeamItem> { new ProductTeamItem { Id = "1" } } }
            }
        };

        _repositoryContext.GetProductGroupsForProductTeamAsync("1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _productTeamCascader.CascadeProductTeamUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task CascadeProductTeamUpdateForProductGroupsAsync_Should_Return_False_When_No_Updates_Are_Found()
    {
        // Arrange
        var productTeam = new ProductTeam { Id = "1", Name = "Updated Team" };
        var message = new CascadeProductTeamUpdateMessage(productTeam);
        var productGroups = new List<ProductGroup>();

        _repositoryContext.GetProductGroupsForProductTeamAsync("1").Returns(productGroups);

        // Act
        var result = await _productTeamCascader.CascadeProductTeamUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }
    
    [Fact]
    public async Task CascadeProductTeamUpdateForProductGroupsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var productTeam = new ProductTeam { Id = "1", Name = "Updated Team" };
        var message = new CascadeProductTeamUpdateMessage(productTeam);
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group1",
                ProductTeams = new ProductTeams
                    { Items = new List<ProductTeamItem> { new ProductTeamItem { Id = "1" } } }
            }
        };

        _repositoryContext.GetProductGroupsForProductTeamAsync("1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(0); // simulate failed update

        // Act
        var result = await _productTeamCascader.CascadeProductTeamUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }


    #endregion

    #region CascadeProductTeamDeleteForProductGroupsAsync

    [Fact]
    public async Task CascadeProductTeamDeleteForProductGroupsAsync_Should_Return_True_When_Deletes_Are_Found()
    {
        // Arrange
        var productTeam = new ProductTeam { Id = "1" };
        var message = new CascadeProductTeamDeleteMessage(productTeam);
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group1",
                ProductTeams = new ProductTeams
                    { Items = new List<ProductTeamItem> { new ProductTeamItem { Id = "1" } } }
            }
        };

        _repositoryContext.GetProductGroupsForProductTeamAsync("1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(1);

        // Act
        var result = await _productTeamCascader.CascadeProductTeamDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task CascadeProductTeamDeleteForProductGroupsAsync_Should_Return_False_When_No_Deletes_Are_Found()
    {
        // Arrange
        var productTeam = new ProductTeam { Id = "1" };
        var message = new CascadeProductTeamDeleteMessage(productTeam);
        var productGroups = new List<ProductGroup>();

        _repositoryContext.GetProductGroupsForProductTeamAsync("1").Returns(productGroups);

        // Act
        var result = await _productTeamCascader.CascadeProductTeamDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task CascadeProductTeamDeleteForProductGroupsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var productTeam = new ProductTeam { Id = "1" };
        var message = new CascadeProductTeamDeleteMessage(productTeam);
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                GroupName = "Group1",
                ProductTeams = new ProductTeams
                    { Items = new List<ProductTeamItem> { new ProductTeamItem { Id = "1" } } }
            }
        };

        _repositoryContext.GetProductGroupsForProductTeamAsync("1").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups).Returns(0); // simulate failure

        // Act
        var result = await _productTeamCascader.CascadeProductTeamDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }


    #endregion
    
    #region CascadeProductTeamsUpdateForRepositoriesAsync

    // Arrange
    [Fact]
    public async Task CascadeProductTeamsUpdateForRepositoriesAsync_Should_Return_True_When_Updates_Are_Successful()
    {
        // Arrange
        var productTeam = new ProductTeam
        {
            Id="1", 
            Repositories = new Data.Entities.Repositories(){Items = new List<RepositoryItem>{new RepositoryItem
            {
                Id = "1"
            }}}
        };

        var message = new CascadeProductTeamsUpdateRepositoriesMessage(new List<ProductTeam> { productTeam });

        _repositoryContext.RepositoryRepository.GetQueryable() 
            .Returns(new List<Repository>() { new() { Id= "1",Name = "Banana" } }
                .AsQueryable().BuildMock());

        _repositoryContext.RepositoryRepository.UpdateRangeAsync(Arg.Any<List<Repository>>()).Returns(1);

        // Act
        var result = await _productTeamCascader.CascadeProductTeamsUpdateForRepositoriesAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task CascadeProductTeamsUpdateForRepositoriesAsync_Should_Return_False_When_Updates_Fails()
    {
        // Arrange
        var productTeam = new ProductTeam
        {
            Id="1", 
            Repositories = new Data.Entities.Repositories(){Items = new List<RepositoryItem>{new RepositoryItem
            {
                Id = "1"
            }}}
        };

        var message = new CascadeProductTeamsUpdateRepositoriesMessage(new List<ProductTeam> { productTeam });

        _repositoryContext.RepositoryRepository.GetQueryable() 
            .Returns(new List<Repository>() { new() { Id= "1",Name = "Banana" } }
                .AsQueryable().BuildMock());

        _repositoryContext.RepositoryRepository.UpdateRangeAsync(Arg.Any<List<Repository>>()).Returns(0);

        // Act
        var result = await _productTeamCascader.CascadeProductTeamsUpdateForRepositoriesAsync(message);

        // Assert
        using var scope = new AssertionScope();
        result.Should().BeFalse();
    }

    #endregion
}