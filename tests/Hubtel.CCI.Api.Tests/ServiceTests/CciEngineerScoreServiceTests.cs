using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.CciEngineerScore;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;
using Xunit.Abstractions;
using Repository = Hubtel.CCI.Api.Dtos.Responses.Azure.Repository;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class CciEngineerScoreServiceTests
{
    private readonly CciEngineerScoreService _cciEngineerScoreService;
    private readonly DiFixture _fixture;
    private readonly ILogger<CciEngineerScoreService> _logger;
    ITestOutputHelper _outputHelper;

    public CciEngineerScoreServiceTests(
        ITestOutputHelper outputHelper)
    {
        _fixture = new DiFixture();
        _logger = Substitute.For<ILogger<CciEngineerScoreService>>();
        _cciEngineerScoreService =
            new CciEngineerScoreService(_logger, _fixture.RepositoryContext);
        _outputHelper = outputHelper;
    }


    #region Get CciEngineerScore By Id Async

    [Fact]
    public async Task GetCciEngineerScoreByIdAsync_Should_Return_NotFound_Api_Response_When_EngineerScore_Is_Not_Found()
    {
        // Arrange
        const string id = "non-existing-id";
        
        var data = new[] { new CciEngineerScore() { Id="existingId" } }.AsQueryable().BuildMock();

        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerScoreByIdAsync(id, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Message.Should().Be("Cci Engineer Score not found");
    }
    
    
    [Fact]
    public async Task GetCciEngineerScoreByIdAsync_Should_Return_Success_Api_Response_When_EngineerScore_Is_Found()
    {
        // Arrange
        const string id = "existingId";
        
        var data = new[] { new CciEngineerScore() { Id="existingId" } }.AsQueryable().BuildMock();

        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerScoreByIdAsync(id, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Score fetched successfully");
    }

    #endregion



    #region GetCciEngineerSonarQubeMetrics

    [Fact]
    public async Task GetCciEngineerSonarQubeMetricsByIdAsync_Should_Return_NotFound_Api_Response_When_EngineerSQMetrics_Is_Not_Found()
    {
        // Arrange
        const string id = "non-existing-id";
        
        var data = new[] { new EngineerSonarQubeMetrics() { Id="existingId" } }.AsQueryable().BuildMock();

        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerSonarQubeMetricsByIdAsync(id, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Message.Should().Be("Cci Engineer SonarQube Metrics not found");
    }
    
    
    [Fact]
    public async Task GetCciEngineerSonarQubeMetricsByIdAsync_Should_Return_Success_Api_Response_When_EngineerSQMetrics_Is_Found()
    {
        // Arrange
        const string id = "existingId";
        
        var data = new[] { new EngineerSonarQubeMetrics() { Id="existingId" } }.AsQueryable().BuildMock();

        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerSonarQubeMetricsByIdAsync(id, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer SonarQube Metrics fetched successfully");
    }

    #endregion


    #region GetCciEngineerTableScoresAsync

    [Fact]
    public async Task GetCciEngineerTableScoresAsync_Should_Return_OK_Api_Response_On_Success()
    {
        // Arrange
        var request = new GetCciEngineerTableScoresFilter()
        {
            PublicationDate = DateTime.UtcNow.AddDays(-7),
            PublicationWeek = "2025-18",
            PageIndex = 1,
            PageSize = 10
        };
        
        var data = new[]
        {
            new CciEngineerScore()
            {
                PublicationWeek="2025-18", 
                EngineerEmail = "<EMAIL>", 
                EngineerName = "Quinton Assan"
            }
        }.AsQueryable().BuildMock();
        
        var productGroups = new [] {new ProductGroup()
        {
            Id = "product-group-id",
            ProductTeams = new ProductTeams()
            {
                Items = [
                new ProductTeamItem()
                {
                    Members = new Members()
                    {
                        Items = [
                        new Member()
                        {
                            Email = "<EMAIL>",
                            Name = "Quinton Assan",
                            Id = "member-id"
                        }]
                    }
                }]
            }
        }
        
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data);
        
        _fixture.RepositoryContext.ProductGroupRepository
            .GetQueryable()
            .Returns(productGroups);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Table Scores fetched successfully");
    }
    
    
        [Fact]
    public async Task GetCciEngineerTableScoresAsync_Should_Return_OK_Api_Response_On_Success_When_Filter_By_ProductGroupId()
    {
        // Arrange
        var request = new GetCciEngineerTableScoresFilter()
        {
            ProductGroupId = "pg-123",
            PageIndex = 1,
            PageSize = 10
        };
        
        var data = new[]
        {
            new CciEngineerScore { EngineerEmail = "<EMAIL>" },
            new CciEngineerScore { EngineerEmail = "<EMAIL>" }
        }.AsQueryable().BuildMock();
        
        var productGroups = new [] {new ProductGroup()
        {
            Id = request.ProductGroupId,
            ProductTeams = new ProductTeams()
            {
                Items = [
                new ProductTeamItem()
                {
                    Members = new Members()
                    {
                        Items = [
                            new Member { Email = "<EMAIL>" },
                            new Member { Email = "<EMAIL>" }]
                    }
                }]
            }
        }
        
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data);
        
        _fixture.RepositoryContext.ProductGroupRepository
            .GetQueryable()
            .Returns(productGroups);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Table Scores fetched successfully");
        result.Data?.Results.Should().Contain(e => e.EngineerEmail == "<EMAIL>");
        result.Data?.Results.Should().NotContain(e => e.EngineerEmail == "<EMAIL>");

    }

    
        [Fact]
    public async Task GetCciEngineerTableScoresAsync_Should_Return_OK_Api_Response_On_Success_When_Publication_Week_Is_Null()
    {
        // Arrange
        var request = new GetCciEngineerTableScoresFilter()
        {
            PublicationWeek = null,
            PageIndex = 1,
            PageSize = 10
        };
        
        var data = new[]
        {
            new CciEngineerScore()
            {
                PublicationWeek="2025-18", 
                EngineerEmail = "<EMAIL>", 
                EngineerName = "Quinton Assan"
            }
        }.AsQueryable().BuildMock();
        
        var productGroups = new [] {new ProductGroup()
        {
            Id = "product-group-id",
            ProductTeams = new ProductTeams()
            {
                Items = [
                new ProductTeamItem()
                {
                    Members = new Members()
                    {
                        Items = [
                        new Member()
                        {
                            Email = "<EMAIL>",
                            Name = "Quinton Assan",
                            Id = "member-id"
                        }]
                    }
                }]
            }
        }
        
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data);
        
        _fixture.RepositoryContext.ProductGroupRepository
            .GetQueryable()
            .Returns(productGroups);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Table Scores fetched successfully");
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableScoresAsync_Should_Return_OK_Api_Response_On_Success_When_Publication_Week_Exists()
    {
        // Arrange
        var request = new GetCciEngineerTableScoresFilter()
        {
            PublicationWeek = "2025-23",
            PageIndex = 1,
            PageSize = 10
        };
        
        var data = new[]
        {
            new CciEngineerScore()
            {
                PublicationWeek="2025-23", 
                EngineerEmail = "<EMAIL>", 
                EngineerName = "Quinton Assan"
            }
        }.AsQueryable().BuildMock();
        
        var productGroups = new [] {new ProductGroup()
        {
            Id = "product-group-id",
            ProductTeams = new ProductTeams()
            {
                Items = [
                new ProductTeamItem()
                {
                    Members = new Members()
                    {
                        Items = [
                        new Member()
                        {
                            Email = "<EMAIL>",
                            Name = "Quinton Assan",
                            Id = "member-id"
                        }]
                    }
                }]
            }
        }
        
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(data);
        
        _fixture.RepositoryContext.ProductGroupRepository
            .GetQueryable()
            .Returns(productGroups);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Table Scores fetched successfully");
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableScoresAsync_Should_Only_Return_Entries_With_Matching_PublicationWeek()
    {
        // Arrange
        var request = new GetCciEngineerTableScoresFilter
        {
            PublicationWeek = "2025-25",
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new CciEngineerScore { PublicationWeek = "2025-25", EngineerEmail = "<EMAIL>" },
            new CciEngineerScore { PublicationWeek = "2025-24", EngineerEmail = "<EMAIL>" }
        }.AsQueryable().BuildMock();

        var productGroups = new[] { new ProductGroup
        {
            Id = "pg-1",
            ProductTeams = new ProductTeams
            {
                Items = [ new ProductTeamItem
                {
                    Members = new Members
                    {
                        Items = [
                            new Member { Email = "<EMAIL>" },
                            new Member { Email = "<EMAIL>" }
                        ]
                    }
                }]
            }
        }}.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerScoreRepository.GetQueryable().Returns(data);
        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable().Returns(productGroups);

        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().OnlyContain(x => x.PublicationWeek == "2025-25");
        result.Data?.Results.Should().NotContain(x => x.EngineerEmail == "<EMAIL>");
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableScoresAsync_Should_Filter_By_PublicationDate_Correctly()
    {
        // Arrange
        var filterDate = new DateTime(2025, 5, 28);
        var request = new GetCciEngineerTableScoresFilter
        {
            PublicationDate = filterDate,
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new CciEngineerScore { PublicationDate = filterDate.AddHours(10), EngineerEmail = "<EMAIL>" },
            new CciEngineerScore { PublicationDate = filterDate.AddDays(-1), EngineerEmail = "<EMAIL>" },
            new CciEngineerScore { PublicationDate = filterDate.AddDays(1), EngineerEmail = "<EMAIL>" }
        }.AsQueryable().BuildMock();

        var productGroups = new[] { new ProductGroup
        {
            Id = "pg",
            ProductTeams = new ProductTeams
            {
                Items = [ new ProductTeamItem
                {
                    Members = new Members
                    {
                        Items = [
                            new Member { Email = "<EMAIL>" },
                            new Member { Email = "<EMAIL>" },
                            new Member { Email = "<EMAIL>" }
                        ]
                    }
                }]
            }
        }}.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerScoreRepository.GetQueryable().Returns(data);
        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable().Returns(productGroups);

        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);

        // Assert
        using var score = new AssertionScope();
        result.Data?.Results.Should().ContainSingle(x => x.EngineerEmail == "<EMAIL>");
        result.Data?.Results.Should().NotContain(x => x.EngineerEmail == "<EMAIL>");
        result.Data?.Results.Should().NotContain(x => x.EngineerEmail == "<EMAIL>");
    }
    
    
    [Theory]
    [InlineData("asc", new[] { "Alice", "Bob", "Charlie" })]
    [InlineData("desc", new[] { "Charlie", "Bob", "Alice" })]
    public async Task GetCciEngineerTableScoresAsync_Should_Sort_By_EngineerName_Based_On_SortDir(string sortDir, string[] expectedOrder)
    {
        // Arrange
        var request = new GetCciEngineerTableScoresFilter
        {
            SortColumn = "EngineerName",
            SortDir = sortDir,
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new CciEngineerScore { EngineerName = "Charlie", EngineerEmail = "<EMAIL>" },
            new CciEngineerScore { EngineerName = "Alice", EngineerEmail = "<EMAIL>" },
            new CciEngineerScore { EngineerName = "Bob", EngineerEmail = "<EMAIL>" }
        }.AsQueryable().BuildMock();

        var productGroups = new[] { new ProductGroup
        {
            Id = "pg",
            ProductTeams = new ProductTeams
            {
                Items = [ new ProductTeamItem
                {
                    Members = new Members
                    {
                        Items = [
                            new Member { Email = "<EMAIL>" },
                            new Member { Email = "<EMAIL>" },
                            new Member { Email = "<EMAIL>" }
                        ]
                    }
                }]
            }
        }}.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerScoreRepository.GetQueryable().Returns(data);
        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable().Returns(productGroups);

        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        var actualOrder = result.Data?.Results.Select(r => r.EngineerName).ToArray();
        actualOrder.Should().Equal(expectedOrder);
    }
    
    
    [Fact]
public async Task GetCciEngineerTableScoresAsync_Should_Include_Results_On_Boundary_PublicationDate()
{
    // Arrange
    var filterDate = new DateTime(2025, 5, 29); // e.g., today
    var startDate = filterDate.Date; // 2025-05-29 00:00:00
    var endDate = filterDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59); // 2025-05-29 23:59:59

    var request = new GetCciEngineerTableScoresFilter
    {
        PublicationDate = filterDate,
        PageIndex = 1,
        PageSize = 10
    };

    var data = new[]
    {
        new CciEngineerScore { PublicationDate = startDate, EngineerEmail = "<EMAIL>" },
        new CciEngineerScore { PublicationDate = endDate, EngineerEmail = "<EMAIL>" },
        new CciEngineerScore { PublicationDate = startDate.AddHours(12), EngineerEmail = "<EMAIL>" },
        new CciEngineerScore { PublicationDate = startDate.AddDays(-1), EngineerEmail = "<EMAIL>" },
        new CciEngineerScore { PublicationDate = endDate.AddSeconds(1), EngineerEmail = "<EMAIL>" }
    }.AsQueryable().BuildMock();

    var productGroups = new[] { new ProductGroup
    {
        Id = "pg",
        ProductTeams = new ProductTeams
        {
            Items = [ new ProductTeamItem
            {
                Members = new Members
                {
                    Items = [
                        new Member { Email = "<EMAIL>" },
                        new Member { Email = "<EMAIL>" },
                        new Member { Email = "<EMAIL>" },
                        new Member { Email = "<EMAIL>" },
                        new Member { Email = "<EMAIL>" }
                    ]
                }
            }]
        }
    }}.AsQueryable().BuildMock();

    _fixture.RepositoryContext.CciEngineerScoreRepository.GetQueryable().Returns(data);
    _fixture.RepositoryContext.ProductGroupRepository.GetQueryable().Returns(productGroups);

    // Act
    var result = await _cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, CancellationToken.None);

    // Assert
    using var scope = new AssertionScope();
    result.Data?.Results.Should().Contain(x => x.EngineerEmail == "<EMAIL>");
    result.Data?.Results.Should().Contain(x => x.EngineerEmail == "<EMAIL>");
    result.Data?.Results.Should().Contain(x => x.EngineerEmail == "<EMAIL>");
    result.Data?.Results.Should().NotContain(x => x.EngineerEmail == "<EMAIL>");
    result.Data?.Results.Should().NotContain(x => x.EngineerEmail == "<EMAIL>");
}
    
    #endregion


    #region Fetch Engineer Cci Publication Statistics

    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Return_OK_Api_Response_On_Success()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest()
        {
            PublicationDate = DateTime.UtcNow.AddDays(-7),
            PublicationWeek = "2025-18"
        };
        
        var data = new[]
        {
            new CciEngineersScoreStatistic() { PublicationWeek="2025-18" }
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Publication Statistics fetched successfully");
    }
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Not_Filter_By_PublicationDate_When_Null()
    {
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = null,
            PublicationWeek = "2025-21"
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationWeek = "2025-21", PublicationDate = DateTime.UtcNow.AddDays(-5) },
            new CciEngineersScoreStatistic { PublicationWeek = "2025-21", PublicationDate = DateTime.UtcNow.AddDays(-10) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationWeek.Should().Be("2025-21");
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Filter_By_PublicationDate()
    {
        var cutoff = new DateTime(2025, 5, 25);
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = cutoff,
            PublicationWeek = null
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationDate = cutoff.AddDays(-1) }, // exclude
            new CciEngineersScoreStatistic { PublicationDate = cutoff },             // include
            new CciEngineersScoreStatistic { PublicationDate = cutoff.AddDays(1) }   // include
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().BeOnOrAfter(cutoff);
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Filter_By_PublicationWeek()
    {
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = null,
            PublicationWeek = "2025-30"
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationWeek = "2025-29", PublicationDate = DateTime.UtcNow.AddDays(-3) },
            new CciEngineersScoreStatistic { PublicationWeek = "2025-30", PublicationDate = DateTime.UtcNow.AddDays(-1) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationWeek.Should().Be("2025-30");
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Return_Most_Recent_Publication()
    {
        var request = new FetchCciEngineerStatisticsRequest();

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationDate = new DateTime(2025, 5, 10) },
            new CciEngineersScoreStatistic { PublicationDate = new DateTime(2025, 5, 25) },
            new CciEngineersScoreStatistic { PublicationDate = new DateTime(2025, 5, 28) } // most recent
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);
        
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().Be(new DateTime(2025, 5, 28));
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Include_Exact_PublicationDate_On_Boundary()
    {
        // Arrange
        var boundaryDate = new DateTime(2025, 6, 1);

        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = boundaryDate,
            PublicationWeek = null
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationDate = boundaryDate },               // ✅ should be returned
            new CciEngineersScoreStatistic { PublicationDate = boundaryDate.AddDays(-1) }    // ❌ should be excluded
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().Be(boundaryDate); // Ensures >= not mutated to >
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Only_Include_Matching_PublicationWeek()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationWeek = "2025-32",
            PublicationDate = null
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationWeek = "2025-31", PublicationDate = new DateTime(2025, 6, 1) },
            new CciEngineersScoreStatistic { PublicationWeek = "2025-32", PublicationDate = new DateTime(2025, 6, 2) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationWeek.Should().Be("2025-32"); // Kills `==` → `!=` mutation
    }
    
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Return_Boundary_As_Most_Recent()
    {
        // Arrange
        var cutoffDate = new DateTime(2025, 6, 10);

        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = cutoffDate,
            PublicationWeek = null
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationDate = cutoffDate },             // ✅ should be selected
            new CciEngineersScoreStatistic { PublicationDate = cutoffDate.AddDays(-2) }  // lower date
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().Be(cutoffDate); // Enforces sort + boundary match
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationStatisticsAsync_Should_Not_Filter_By_PublicationWeek_When_Null()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = null,
            PublicationWeek = null // <-- this triggers the branch
        };

        var data = new[]
        {
            new CciEngineersScoreStatistic { PublicationWeek = "2025-01", PublicationDate = DateTime.UtcNow.AddDays(-10) },
            new CciEngineersScoreStatistic { PublicationWeek = "2025-50", PublicationDate = DateTime.UtcNow.AddDays(-5) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _cciEngineerScoreService
            .FetchEngineerCciPublicationStatisticsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationWeek.Should().Be("2025-50");
    }
    
    [Fact]
    public async Task FetchEngineerCciPublicationRankingsAsync_Should_Not_Filter_By_PublicationDate_When_Null()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = null,
            PublicationWeek = "2025-20"
        };

        var data = new[]
        {
            new CciEngineerRanking { PublicationWeek = "2025-20", PublicationDate = DateTime.UtcNow.AddDays(-3) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerRankingRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationWeek.Should().Be("2025-20");
    }
    
    [Fact]
    public async Task FetchEngineerCciPublicationRankingsAsync_Should_Filter_By_PublicationDate_When_Provided()
    {
        // Arrange
        var cutoffDate = new DateTime(2025, 5, 20);

        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = cutoffDate,
            PublicationWeek = null
        };

        var data = new[]
        {
            new CciEngineerRanking { PublicationDate = cutoffDate.AddDays(-1) }, // should be excluded
            new CciEngineerRanking { PublicationDate = cutoffDate },             // should be included
            new CciEngineerRanking { PublicationDate = cutoffDate.AddDays(1) }   // should be included
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerRankingRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().BeOnOrAfter(cutoffDate);
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationRankingsAsync_Should_Filter_By_PublicationWeek()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = null,
            PublicationWeek = "2025-19"
        };

        var data = new[]
        {
            new CciEngineerRanking { PublicationWeek = "2025-18" },
            new CciEngineerRanking { PublicationWeek = "2025-19" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerRankingRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationWeek.Should().Be("2025-19");
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationRankingsAsync_Should_Return_Most_Recent_Ranking()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest();

        var data = new[]
        {
            new CciEngineerRanking { PublicationDate = new DateTime(2025, 5, 10) },
            new CciEngineerRanking { PublicationDate = new DateTime(2025, 5, 28) }, // most recent
            new CciEngineerRanking { PublicationDate = new DateTime(2025, 5, 20) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerRankingRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().Be(new DateTime(2025, 5, 28));
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationRankingsAsync_Should_Include_Result_With_Exact_PublicationDate()
    {
        // Arrange
        var filterDate = new DateTime(2025, 5, 29); // 00:00:00 by default

        var request = new FetchCciEngineerStatisticsRequest
        {
            PublicationDate = filterDate,
            PublicationWeek = null
        };

        var data = new[]
        {
            new CciEngineerRanking { PublicationDate = filterDate },               // exact match
            new CciEngineerRanking { PublicationDate = filterDate.AddDays(-1) }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerRankingRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.PublicationDate.Should().Be(filterDate);
    }

    #endregion


    #region Fetch Engineer Cci Publication Rankings

    
    [Fact]
    public async Task FetchEngineerCciPublicationRankingsAsync_Should_Return_OK_Api_Response_On_Success()
    {
        // Arrange
        var request = new FetchCciEngineerStatisticsRequest()
        {
            PublicationDate = DateTime.UtcNow.AddDays(-7),
            PublicationWeek = "2025-18"
        };
        
        var data = new[]
        {
            new CciEngineerRanking() { PublicationWeek="2025-18" }
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.CciEngineerRankingRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Publication Rankings fetched successfully");
    }
    #endregion


    #region DeleteEngineerCciPublicationAsync

    [Fact]
    public async Task DeleteEngineerCciPublicationAsync_Should_Return_OK_Api_Response_When_Publication_Exists()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var cciEngineerScore= new[]{ new CciEngineerScore() { PublicationDate=publicationDate } }.AsQueryable().BuildMock();
        var cciEngineerRanking= new[]{ new CciEngineerRanking() { PublicationDate=publicationDate } }.AsQueryable().BuildMock();
        var engineerSonarQubeMetrics= new[]{ new EngineerSonarQubeMetrics() { PublicationDate=publicationDate } }.AsQueryable().BuildMock();
        var cciEngineersScoreStatistic= new[]{ new CciEngineersScoreStatistic() { PublicationDate=publicationDate } }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(cciEngineerScore);

        _fixture.RepositoryContext.CciEngineerRankingRepository
            .GetQueryable()
            .Returns(cciEngineerRanking);

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(engineerSonarQubeMetrics);

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .GetQueryable()
            .Returns(cciEngineersScoreStatistic);
        
        // Act
        var result = await _cciEngineerScoreService.DeleteEngineerCciPublicationAsync(publicationDate, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Publication deleted successfully");
        result.Data.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task DeleteEngineerCciPublicationAsync_Should_Return_Not_Found_Api_Response_When_Publication_Does_Not_Exist()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var cciEngineerScore= new[]{ new CciEngineerScore() { PublicationDate=DateTime.Today } }.AsQueryable().BuildMock();
        var cciEngineerRanking= new[]{ new CciEngineerRanking() { PublicationDate=DateTime.Today } }.AsQueryable().BuildMock();
        var engineerSonarQubeMetrics= new[]{ new EngineerSonarQubeMetrics() { PublicationDate=DateTime.Today } }.AsQueryable().BuildMock();
        var cciEngineersScoreStatistic= new[]{ new CciEngineersScoreStatistic() { PublicationDate=DateTime.Today } }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .Returns(cciEngineerScore);

        _fixture.RepositoryContext.CciEngineerRankingRepository
            .GetQueryable()
            .Returns(cciEngineerRanking);

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(engineerSonarQubeMetrics);

        _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
            .GetQueryable()
            .Returns(cciEngineersScoreStatistic);
        
        // Act
        var result = await _cciEngineerScoreService.DeleteEngineerCciPublicationAsync(publicationDate, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Message.Should().Be("Cci Engineer Publication not found");
        result.Data.Should().BeFalse();
    }

    
    public static IEnumerable<object[]> SingleRepoHasDataCases()
{
    var date = Miscellaneous.GetCurrentPublicationTargetDay();

    yield return new object[]
    {
        "OnlyScores", date,
        new[] { new CciEngineerScore { PublicationDate = date } },
        Enumerable.Empty<CciEngineerRanking>(),
        Enumerable.Empty<EngineerSonarQubeMetrics>(),
        Enumerable.Empty<CciEngineersScoreStatistic>()
    };

    yield return new object[]
    {
        "OnlyRankings", date,
        Enumerable.Empty<CciEngineerScore>(),
        new[] { new CciEngineerRanking { PublicationDate = date } },
        Enumerable.Empty<EngineerSonarQubeMetrics>(),
        Enumerable.Empty<CciEngineersScoreStatistic>()
    };

    yield return new object[]
    {
        "OnlySonarQube", date,
        Enumerable.Empty<CciEngineerScore>(),
        Enumerable.Empty<CciEngineerRanking>(),
        new[] { new EngineerSonarQubeMetrics { PublicationDate = date } },
        Enumerable.Empty<CciEngineersScoreStatistic>()
    };

    yield return new object[]
    {
        "OnlyStatistics", date,
        Enumerable.Empty<CciEngineerScore>(),
        Enumerable.Empty<CciEngineerRanking>(),
        Enumerable.Empty<EngineerSonarQubeMetrics>(),
        new[] { new CciEngineersScoreStatistic { PublicationDate = date } }
    };
}

[Theory]
[MemberData(nameof(SingleRepoHasDataCases))]
public async Task DeleteEngineerCciPublicationAsync_Should_Return_Ok_When_Only_One_Repo_Has_Data(
    string scenario,
    DateTime publicationDate,
    IEnumerable<CciEngineerScore> scores,
    IEnumerable<CciEngineerRanking> rankings,
    IEnumerable<EngineerSonarQubeMetrics> sonar,
    IEnumerable<CciEngineersScoreStatistic> statistics)
{

    _outputHelper.WriteLine($"Running scenario: {scenario}");
    
    // Arrange
    _fixture.RepositoryContext.CciEngineerScoreRepository
        .GetQueryable()
        .Returns(scores.AsQueryable().BuildMock());

    _fixture.RepositoryContext.CciEngineerRankingRepository
        .GetQueryable()
        .Returns(rankings.AsQueryable().BuildMock());

    _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
        .GetQueryable()
        .Returns(sonar.AsQueryable().BuildMock());

    _fixture.RepositoryContext.CciEngineersScoreStatisticRepository
        .GetQueryable()
        .Returns(statistics.AsQueryable().BuildMock());

    // Act
    var result = await _cciEngineerScoreService.DeleteEngineerCciPublicationAsync(publicationDate, CancellationToken.None);

    // Assert
    using var scope = new AssertionScope();
    result.Code.Should().Be(StatusCodes.Status200OK);
    result.Data.Should().BeTrue();
}
    #endregion

    
    #region GetCciEngineerTableSqAsync

    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Return_OK_Api_Response_On_Success()
    {
        // Arrange
        var request = new GetCciEngineerSqMetricsTableScoresFilter()
        {
            PublicationDate = DateTime.UtcNow.AddDays(-7),
            PublicationWeek = "2025-18",
            PageIndex = 1,
            PageSize = 10
        };
        
        var data = new[]
        {
            new EngineerSonarQubeMetrics() { PublicationWeek="2025-18" }
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Table Sq Metrics fetched successfully");
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Not_Filter_By_PublicationDate_When_Null()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationDate = null,
            PublicationWeek = "2025-22",
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationDate = DateTime.UtcNow.AddDays(-1), PublicationWeek = "2025-22" },
            new EngineerSonarQubeMetrics { PublicationDate = DateTime.UtcNow.AddDays(-5), PublicationWeek = "2025-22" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.Results.Should().HaveCount(2);
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Include_Exact_PublicationDate_Match()
    {
        var date = new DateTime(2025, 6, 1);
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationDate = date,
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationDate = date }, // boundary match
            new EngineerSonarQubeMetrics { PublicationDate = date.AddDays(1) }, // later
            new EngineerSonarQubeMetrics { PublicationDate = date.AddDays(-1) } // should be excluded
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.Results.Should().Contain(x => x.PublicationDate == date);
        result.Data?.Results.Should().NotContain(x => x.PublicationDate == date.AddDays(-1));
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Filter_By_PublicationWeek()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationWeek = "2025-40",
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationWeek = "2025-40" },
            new EngineerSonarQubeMetrics { PublicationWeek = "2025-39" } // should be excluded
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.Results.Should().OnlyContain(x => x.PublicationWeek == "2025-40");
    }

    
    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Filter_By_EngineerEmail()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            EngineerEmail = "<EMAIL>",
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { EngineerEmail = "<EMAIL>" },
            new EngineerSonarQubeMetrics { EngineerEmail = "<EMAIL>" } // exclude
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.Results.Should().OnlyContain(x => x.EngineerEmail == "<EMAIL>");
    }

    
    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Filter_By_Search()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            Search = "Quinton",
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { EngineerName = "Quinton Assan", EngineerEmail = "<EMAIL>" },
            new EngineerSonarQubeMetrics { EngineerName = "Ama Mensah", EngineerEmail = "<EMAIL>" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);
        
        using var scope = new AssertionScope();
        result.Data?.Results.Should().OnlyContain(x => x.EngineerName!.Contains("Quinton") || x.EngineerEmail!.Contains("Quinton"));
    }


    [Theory]
    [InlineData("asc", new[] { "Ama", "Kojo", "Zoe" })]
    [InlineData("desc", new[] { "Zoe", "Kojo", "Ama" })]
    public async Task GetCciEngineerTableSqAsync_Should_Sort_By_EngineerName(string sortDir, string[] expected)
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            SortColumn = "EngineerName",
            SortDir = sortDir,
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { EngineerName = "Zoe" },
            new EngineerSonarQubeMetrics { EngineerName = "Kojo" },
            new EngineerSonarQubeMetrics { EngineerName = "Ama" }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        var names = result.Data?.Results.Select(x => x.EngineerName).ToArray();
        names.Should().Equal(expected);
    }
    
    
    [Fact]
    public async Task GetCciEngineerTableSqAsync_Should_Include_Record_With_Exact_EndDate()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1);
        var startDate = date.Date;
        var endDate = date.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationDate = date,
            PageIndex = 1,
            PageSize = 10
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationDate = startDate },    // ✅ should be included
            new EngineerSonarQubeMetrics { PublicationDate = endDate },      // ✅ this is the one that tests the mutation
            new EngineerSonarQubeMetrics { PublicationDate = endDate.AddSeconds(1) } // ❌ should be excluded
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciEngineerScoreService.GetCciEngineerTableSqAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Data?.Results.Should().Contain(x => x.PublicationDate == startDate);
        result.Data?.Results.Should().Contain(x => x.PublicationDate == endDate); // ✅ this line kills the mutation
        result.Data?.Results.Should().NotContain(x => x.PublicationDate > endDate);
    }



    #endregion


    #region FetchEngineerCciPublicationSqMetricsOverviewAsync

    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Return_OK_Api_Response_On_Success()
    {
        // Arrange
        var request = new GetCciEngineerSqMetricsTableScoresFilter()
        {
            PublicationDate = DateTime.UtcNow.AddDays(-7),
            PublicationWeek = "2025-18"
        };
        
        var data = new[]
        {
            new EngineerSonarQubeMetrics()
            {
                Id="existingId",
                PublicationWeek="2025-18",
                PublicationDate =DateTime.UtcNow.AddDays(-7),
                EngineerEmail = "<EMAIL>",
                EngineerName = "Quinton Assan",
                PullRequest = new PullRequest()
                {
                    PullRequestId = 3,
                    Repository = new Repository()
                    {
                        Name = "Hubtel.CCI.Api",
                        Id = "repo-id"
                    },
                    Url = "http://example.com/pull/3",
                },
                SonarQubeMetrics = new SonarQubeComponent()
                {
                    Measures = new List<SonarQubeMeasure>()
                    {
                        new SonarQubeMeasure()
                        {
                            Metric = "ncloc",
                            Value = "100"
                        },
                        new SonarQubeMeasure()
                        {
                            Metric = "coverage",
                            Value = "100"
                        },
                    }
                }
            }
        }.AsQueryable().BuildMock();

        
        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .Returns(data);
        
        // Act
        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("Cci Engineer Publication Sq Metrics Overview fetched successfully");
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Not_Filter_By_PublicationDate_When_Null()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationDate = null,
            PublicationWeek = "2025-30"
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationDate = DateTime.UtcNow.AddDays(-2), PublicationWeek = "2025-30", EngineerEmail = "<EMAIL>", RepositoryId = "repo1", PullRequest = new()
            {
                PullRequestId = 1, Repository = new() { Id = "repo1", Name = "Repo1" }, Url = "url1"
            }, SonarQubeMetrics = new() { Measures = new() } }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data.Should().NotBeNull();
        result.Data?.UniqueEngineerCount.Should().Be(1);
        result.Data?.PublicationWeek.Should().Be("2025-30");
    }
    
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Include_Exact_PublicationDate_Match()
    {
        var date = new DateTime(2025, 6, 1);

        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationDate = date,
            PublicationWeek = null
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationDate = date, EngineerEmail = "<EMAIL>", RepositoryId = "r1", PullRequest = new() { PullRequestId = 1, Repository = new() { Id = "r1", Name = "Repo1" }, Url = "url1" }, SonarQubeMetrics = new() { Measures = new() } },
            new EngineerSonarQubeMetrics { PublicationDate = date.AddDays(-1), EngineerEmail = "<EMAIL>", RepositoryId = "r2", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        using var scope = new AssertionScope();
        result.Data?.Engineers.Should().ContainSingle(e => e.EngineerEmail == "<EMAIL>");
        result.Data?.Engineers.Should().NotContain(e => e.EngineerEmail == "<EMAIL>");
    }
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Filter_By_PublicationWeek()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            PublicationWeek = "2025-31",
            PublicationDate = null
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { PublicationWeek = "2025-31", EngineerEmail = "<EMAIL>", RepositoryId = "r1", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } },
            new EngineerSonarQubeMetrics { PublicationWeek = "2025-30", EngineerEmail = "<EMAIL>", RepositoryId = "r2", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        result.Data?.Engineers.Should().OnlyContain(e => e.EngineerEmail == "<EMAIL>");
    }


    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Filter_By_EngineerEmail()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            EngineerEmail = "<EMAIL>"
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { EngineerEmail = "<EMAIL>", RepositoryId = "repo1", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } },
            new EngineerSonarQubeMetrics { EngineerEmail = "<EMAIL>", RepositoryId = "repo2", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        result.Data?.Engineers.Should().OnlyContain(e => e.EngineerEmail == "<EMAIL>");
    }
    
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Filter_By_Search_Term()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter
        {
            Search = "Kojo"
        };

        var data = new[]
        {
            new EngineerSonarQubeMetrics { EngineerName = "Kojo Mensah", EngineerEmail = "<EMAIL>", RepositoryId = "r1", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } },
            new EngineerSonarQubeMetrics { EngineerName = "Alice Doe", EngineerEmail = "<EMAIL>", RepositoryId = "r2", PullRequest = new(), SonarQubeMetrics = new() { Measures = new() } }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        result.Data?.Engineers.Should().OnlyContain(x => x.EngineerName!.Contains("Kojo") || x.EngineerEmail!.Contains("Kojo"));
    }
    
    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Calculate_UniquePullRequestCount_Correctly()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter();

        var data = new[]
        {
            new EngineerSonarQubeMetrics
            {
                EngineerEmail = "<EMAIL>",
                EngineerName = "Engineer 1",
                RepositoryId = "repo1",
                PullRequest = new() { PullRequestId = 1, Repository = new() { Id = "repo1", Name = "Repo 1" }, Url = "url" },
                SonarQubeMetrics = new() { Measures = new() }
            },
            new EngineerSonarQubeMetrics
            {
                EngineerEmail = "<EMAIL>",
                EngineerName = "Engineer 1",
                RepositoryId = "repo1",
                PullRequest = new() { PullRequestId = 2, Repository = new() { Id = "repo1", Name = "Repo 1" }, Url = "url" },
                SonarQubeMetrics = new() { Measures = new() }
            },
            new EngineerSonarQubeMetrics
            {
                EngineerEmail = "<EMAIL>",
                EngineerName = "Engineer 1",
                RepositoryId = "repo1",
                PullRequest = new() { PullRequestId = 1, Repository = new() { Id = "repo1", Name = "Repo 1" }, Url = "url" },
                SonarQubeMetrics = new() { Measures = new() }
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        var engineer = result.Data?.Engineers.Single();
        engineer?.UniquePullRequestCount.Should().Be(2);
    }

    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Parse_Metrics_From_Correct_Measure_Keys()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter();

        var data = new[]
        {
            new EngineerSonarQubeMetrics
            {
                EngineerEmail = "<EMAIL>",
                EngineerName = "Engineer",
                RepositoryId = "r1",
                PullRequest = new PullRequest
                {
                    PullRequestId = 1,
                    Repository = new Repository { Id = "r1", Name = "Repo" },
                    Url = "url"
                },
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures =
                    [
                        new SonarQubeMeasure { Metric = ValidationConstants.SonarMetricKeys.Ncloc, Value = "150" },
                        new SonarQubeMeasure { Metric = ValidationConstants.SonarMetricKeys.Coverage, Value = "82.5" },
                        new SonarQubeMeasure { Metric = "other", Value = "999" }
                    ]
                }
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        var pr = result.Data?.Engineers.Single().PullRequests.Single();
        pr?.LinesOfCode.Should().Be(150m);
        pr?.Coverage.Should().Be(82.5m);
    }

    
    
    [Fact]
    public async Task FetchEngineerCciPublicationSqMetricsOverviewAsync_Should_Set_LinesOfCode_And_Coverage_To_Zero_If_Metrics_Missing()
    {
        var request = new GetCciEngineerSqMetricsTableScoresFilter();

        var data = new[]
        {
            new EngineerSonarQubeMetrics
            {
                EngineerEmail = "<EMAIL>",
                EngineerName = "Engineer",
                RepositoryId = "r1",
                PullRequest = new PullRequest
                {
                    PullRequestId = 1,
                    Repository = new Repository { Id = "r1", Name = "Repo" },
                    Url = "url"
                },
                SonarQubeMetrics = new SonarQubeComponent
                {
                    Measures = []
                }
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().Returns(data);

        var result = await _cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(request, CancellationToken.None);

        var pr = result.Data?.Engineers.Single().PullRequests.Single();
        pr?.LinesOfCode.Should().Be(0m);
        pr?.Coverage.Should().Be(0m);
    }



    #endregion

}