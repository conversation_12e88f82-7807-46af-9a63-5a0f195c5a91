using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Cascaders;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ArsenalCascaderTests
{
    private readonly ArsenalCascader _arsenalCascader;
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger<ArsenalCascader> _logger;

    public ArsenalCascaderTests()
    {
        _repositoryContext = Substitute.For<IRepositoryContext>();
        _logger = Substitute.For<ILogger<ArsenalCascader>>();
        _arsenalCascader = new ArsenalCascader(_logger, _repositoryContext);
    }

    #region CascadeRepositoryUpdateForProductGroupsAsync

    [Fact]
    public async Task CascadeRepositoryUpdateForProductGroupsAsync_Should_Update_When_Repository_Exists()
    {
        // Arrange
        var repository = new Repository
        {
            Id = "repoId", Name = "newName", Description = "newDescription", Url = "newUrl", Type = "newType",
            SonarQubeKey = "newKey"
        };
        var message = new CascadeRepositoryUpdateMessage(repository);
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem()
                        {
                            Repositories = new()
                            {
                                Items = new()
                                {
                                    new RepositoryItem { Id = "repoId" }
                                }
                            }
                        }
                    }
                }
            }
        };

        _repositoryContext.GetProductGroupsForRepositoryAsync("repoId").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(Arg.Any<List<ProductGroup>>()).Returns(1);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryUpdateForProductGroupsAsync(message);

        // Assert
        using (new AssertionScope())
        {
            result.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task CascadeRepositoryUpdateForProductGroupsAsync_Should_Not_Update_When_Repository_Not_Found()
    {
        // Arrange
        var repository = new Repository
        {
            Id = "repoId", Name = "newName", Description = "newDescription", Url = "newUrl", Type = "newType",
            SonarQubeKey = "newKey"
        };
        var message = new CascadeRepositoryUpdateMessage(repository);

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Repositories = new Data.Entities.Repositories()
                            {
                                Items = []
                            }
                        }
                    }
                }
            }
        };

        _repositoryContext.GetProductGroupsForRepositoryAsync("repoId").Returns(productGroups);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse(); // Triggers `!anyUpdates`
    }
    
    
    [Fact]
    public async Task CascadeRepositoryUpdateForProductGroupsAsync_Should_Return_False_When_Update_Returns_Zero()
    {
        // Arrange
        var repository = new Repository
        {
            Id = "repoId", Name = "newName", Description = "newDescription", Url = "newUrl", Type = "newType",
            SonarQubeKey = "newKey"
        };
        var message = new CascadeRepositoryUpdateMessage(repository);

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Repositories = new()
                            {
                                Items = new() { new RepositoryItem { Id = "repoId" } }
                            }
                        }
                    }
                }
            }
        };

        _repositoryContext.GetProductGroupsForRepositoryAsync("repoId").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(Arg.Any<List<ProductGroup>>()).Returns(0);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryUpdateForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }



    #endregion


    #region CascadeRepositoryDeleteForProductGroupsAsync

        [Fact]
    public async Task CascadeRepositoryDeleteForProductGroupsAsync_Should_Delete_When_Repository_Exists()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryDeleteMessage(repository);
        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                ProductTeams = new ProductTeams
                {
                    Items =
                    [
                        new ProductTeamItem()
                        {
                            Repositories = new()
                            {
                                Items = new()
                                {
                                    new RepositoryItem { Id = "repoId" }
                                }
                            }
                        }
                    ]
                }
            }
        };

        _repositoryContext.GetProductGroupsForRepositoryAsync("repoId").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(Arg.Any<List<ProductGroup>>()).Returns(1);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryDeleteForProductGroupsAsync(message);

        // Assert
        using (new AssertionScope())
        {
            result.Should().BeTrue();
        }
    }
    
    [Fact]
    public async Task CascadeRepositoryDeleteForProductGroupsAsync_Should_Return_False_When_Repository_Not_Found()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryDeleteMessage(repository);

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Repositories = new Data.Entities.Repositories()
                            {
                                Items = [] // No matching repo
                            }
                        }
                    }
                }
            }
        };

        _repositoryContext.GetProductGroupsForRepositoryAsync("repoId").Returns(productGroups);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }
    
    
    [Fact]
    public async Task CascadeRepositoryDeleteForProductGroupsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryDeleteMessage(repository);

        var productGroups = new List<ProductGroup>
        {
            new ProductGroup
            {
                ProductTeams = new()
                {
                    Items = new()
                    {
                        new ProductTeamItem
                        {
                            Repositories = new Data.Entities.Repositories()
                            {
                                Items = new List<RepositoryItem>
                                {
                                    new RepositoryItem { Id = "repoId" }
                                }
                            }
                        }
                    }
                }
            }
        };

        _repositoryContext.GetProductGroupsForRepositoryAsync("repoId").Returns(productGroups);
        _repositoryContext.ProductGroupRepository.UpdateRangeAsync(Arg.Any<List<ProductGroup>>()).Returns(0); // update fails

        // Act
        var result = await _arsenalCascader.CascadeRepositoryDeleteForProductGroupsAsync(message);

        // Assert
        result.Should().BeFalse();
    }


    #endregion


    #region CascadeRepositoryUpdateForProductTeamsAsync

    [Fact]
    public async Task CascadeRepositoryUpdateForProductTeamsAsync_Should_Update_When_Repository_Exists()
    {
        // Arrange
        var repository = new Repository
        {
            Id = "repoId", Name = "newName", Description = "newDescription", Url = "newUrl", Type = "newType",
            SonarQubeKey = "newKey"
        };
        var message = new CascadeRepositoryUpdateMessage(repository);
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Repositories = new()
                {
                    Items = new()
                    {
                        new RepositoryItem() { Id = "repoId" }
                    }
                }
            }
        };

        _repositoryContext.GetProductTeamsForRepositoryAsync("repoId").Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(Arg.Any<List<ProductTeam>>()).Returns(1);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryUpdateForProductTeamsAsync(message);

        // Assert
        using (new AssertionScope())
        {
            result.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task CascadeRepositoryUpdateForProductTeamsAsync_Should_Return_False_When_Repository_Not_Found()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryUpdateMessage(repository);
    
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Repositories = new() { Items = new() } // Empty list → no match
            }
        };

        _repositoryContext.GetProductTeamsForRepositoryAsync("repoId").Returns(productTeams);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryUpdateForProductTeamsAsync(message);

        // Assert
        using (new AssertionScope())
        {
            result.Should().BeFalse();
        }
    }
    
    
    [Fact]
    public async Task CascadeRepositoryUpdateForProductTeamsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryUpdateMessage(repository);

        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Repositories = new Data.Entities.Repositories
                {
                    Items = [new RepositoryItem { Id = "repoId" }]
                }
            }
        };

        _repositoryContext.GetProductTeamsForRepositoryAsync("repoId").Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(Arg.Any<List<ProductTeam>>()).Returns(0);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryUpdateForProductTeamsAsync(message);

        // Assert
        using (new AssertionScope())
        {
            result.Should().BeFalse();
        }
    }



    #endregion


    #region CascadeRepositoryDeleteForProductTeamsAsync

    [Fact]
    public async Task CascadeRepositoryDeleteForProductTeamsAsync_Should_Delete_When_Repository_Exists()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryDeleteMessage(repository);
        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Repositories = new()
                {
                    Items = new()
                    {
                        new RepositoryItem() { Id = "repoId" }
                    }
                }
            }
        };

        _repositoryContext.GetProductTeamsForRepositoryAsync("repoId").Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(Arg.Any<List<ProductTeam>>()).Returns(1);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryDeleteForProductTeamsAsync(message);

        // Assert
        using (new AssertionScope())
        {
            result.Should().BeTrue();
        }
    }
    
    [Fact]
    public async Task CascadeRepositoryDeleteForProductTeamsAsync_Should_Return_False_When_Repository_Not_Found()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryDeleteMessage(repository);

        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Repositories = new() { Items = new() } // No repository present
            }
        };

        _repositoryContext.GetProductTeamsForRepositoryAsync("repoId").Returns(productTeams);

        // Act
        var result = await _arsenalCascader.CascadeRepositoryDeleteForProductTeamsAsync(message);

        // Assert
        result.Should().BeFalse();
    }

    
    [Fact]
    public async Task CascadeRepositoryDeleteForProductTeamsAsync_Should_Return_False_When_Update_Fails()
    {
        // Arrange
        var repository = new Repository { Id = "repoId" };
        var message = new CascadeRepositoryDeleteMessage(repository);

        var productTeams = new List<ProductTeam>
        {
            new ProductTeam
            {
                Repositories = new()
                {
                    Items = new() { new RepositoryItem { Id = "repoId" } }
                }
            }
        };

        _repositoryContext.GetProductTeamsForRepositoryAsync("repoId").Returns(productTeams);
        _repositoryContext.ProductTeamRepository.UpdateRangeAsync(Arg.Any<List<ProductTeam>>()).Returns(0); // Simulate failed update

        // Act
        var result = await _arsenalCascader.CascadeRepositoryDeleteForProductTeamsAsync(message);

        // Assert
        result.Should().BeFalse();
    }


    #endregion
}