using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System;
using Xunit;
using FluentAssertions;
using FluentAssertions.Execution;
using NSubstitute;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Dtos.Common;
using Microsoft.Extensions.Logging;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using MockQueryable.NSubstitute;

namespace Hubtel.CCI.Api.Tests.ServiceTests
{
    public class IssueTrackerServiceTests
    {
        private readonly DiFixture _fixture;
        private readonly IssueTrackerService _issueTrackerService;
        private readonly ILogger<IssueTrackerService> _logger;

        public IssueTrackerServiceTests()
        {
            _fixture = new DiFixture();
            _logger = Substitute.For<ILogger<IssueTrackerService>>();
            _issueTrackerService = new IssueTrackerService(_fixture.RepositoryContext, _logger);
        }

        [Fact]
        public async Task CreateIssueAsync_Should_CreateIssueSuccessfully_When_ValidRequestProvided()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            var productGroup = CreateValidProductGroup();
            var tool = CreateValidTool();
            var service = CreateValidService();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            _fixture.RepositoryContext.ServiceRepository.FindOneAsync(Arg.Any<Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(service);

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(tool);

            _fixture.RepositoryContext.IssueTrackerRepository.AddAsync(Arg.Any<IssueTracker>(), Arg.Any<CancellationToken>())
                .Returns(1);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(201);
                result.Data.Should().NotBeNull();
                result.Data!.ReportedBy.Should().Be(request.ReportedBy);
                result.Data.RecordedBy.Should().Be(request.RecordedBy);
                result.Data.IncidentDescription.Should().Be(request.IncidentDescription);
                result.Data.ActionTaken.Should().Be(request.ActionTaken);
                result.Data.Domain.Should().Be(request.Domain);
                result.Data.Status.Should().Be(request.Status);
                result.Data.Severity.Should().Be(request.Severity);
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnBadRequest_When_ProductGroupIsNullOrEmpty()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            request.ProductGroup = new List<string> { "" };

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(400);
                result.Message.Should().Be("Product Group cannot be null or empty");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnBadRequest_When_ProductGroupNotFound()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns((ProductGroup?)null);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(400);
                result.Message.Should().Contain("Product Group").And.Contain("not found");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnBadRequest_When_ProductTeamIsNullOrEmpty()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            request.ProductTeam = new List<string> { "" };
            var productGroup = CreateValidProductGroup();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(400);
                result.Message.Should().Be("Product Team cannot be null or empty");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnBadRequest_When_ProductTeamNotFoundInGroup()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            request.ProductTeam = new List<string> { "invalid-team-id" };
            var productGroup = CreateValidProductGroup();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(400);
                result.Message.Should().Contain("Product Team not found in Product Group");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnBadRequest_When_ServiceNotFound()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            var productGroup = CreateValidProductGroup();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            _fixture.RepositoryContext.ServiceRepository.FindOneAsync(Arg.Any<Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
                .Returns((Service?)null);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(400);
                result.Message.Should().Contain("Service").And.Contain("not found");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnBadRequest_When_ServiceDoesNotBelongToRepository()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            var productGroup = CreateValidProductGroup();
            var service = CreateValidService();
            service.RepositoryId = "different-repo-id";

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            _fixture.RepositoryContext.ServiceRepository.FindOneAsync(Arg.Any<Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(service);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(400);
                result.Message.Should().Contain("does not belong to any repository");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnNotFound_When_ToolNotFound()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            var productGroup = CreateValidProductGroup();
            var service = CreateValidService();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            _fixture.RepositoryContext.ServiceRepository.FindOneAsync(Arg.Any<Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(service);

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns((ToolingTool?)null);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Message.Should().Be("Tool not found! Please try again");
            }
        }

        [Fact]
        public async Task CreateIssueAsync_Should_ReturnFailedDependency_When_SaveFails()
        {
            // Arrange
            var request = CreateValidCreateIssueRequest();
            var productGroup = CreateValidProductGroup();
            var tool = CreateValidTool();
            var service = CreateValidService();

            _fixture.RepositoryContext.ProductGroupRepository.FindOneAsync(Arg.Any<Expression<Func<ProductGroup, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(productGroup);

            _fixture.RepositoryContext.ServiceRepository.FindOneAsync(Arg.Any<Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(service);

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(tool);

            _fixture.RepositoryContext.IssueTrackerRepository.AddAsync(Arg.Any<IssueTracker>(), Arg.Any<CancellationToken>())
                .Returns(0);

            // Act
            var result = await _issueTrackerService.CreateIssueAsync(request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(424);
                result.Message.Should().Be("Could not create Issue! Please try again");
            }
        }

        [Fact]
        public async Task GetIssueByIdAsync_Should_ReturnIssueSuccessfully_When_ValidIdProvided()
        {
            // Arrange
            var issueId = "test-issue-id";
            var issue = CreateValidIssueTracker();
            var mockQueryable = new List<IssueTracker> { issue }.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssueByIdAsync(issueId);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
                result.Data!.Id.Should().Be(issue.Id);
                result.Data.ReportedBy.Should().Be(issue.ReportedBy);
                result.Data.IncidentDescription.Should().Be(issue.IncidentDescription);
            }
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData(" ")]
        public async Task GetIssueByIdAsync_Should_ReturnNotFound_When_IdIsNullOrEmpty(string? id)
        {
            // Act
            var result = await _issueTrackerService.GetIssueByIdAsync(id!);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Message.Should().Be("Issue ID cannot be null or empty");
            }
        }

        [Fact]
        public async Task GetIssueByIdAsync_Should_ReturnNotFound_When_IssueNotFound()
        {
            // Arrange
            var issueId = "non-existent-id";
            var mockQueryable = new List<IssueTracker>().AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssueByIdAsync(issueId);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Message.Should().Be("Issue not found! Please try again");
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_ReturnFilteredIssuesSuccessfully_When_ValidFilterProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().NotBeEmpty();
                result.Data.PageIndex.Should().Be(filter.PageIndex);
                result.Data.PageSize.Should().Be(filter.PageSize);
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_FilterByDomain_When_DomainProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            filter.Domain = "Backend";
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_FilterByProductGroup_When_ProductGroupProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            filter.ProductGroup = new List<string> { "Test Group" };
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_FilterByProductTeam_When_ProductTeamProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            filter.ProductTeam = new List<string> { "Test Team" };
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_FilterByTool_When_ToolProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            filter.Tool = "Test Tool";
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_FilterByReportedBy_When_ReportedByProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            filter.ReportedBy = "<EMAIL>";
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task GetIssuesAsync_Should_FilterByStatus_When_StatusProvided()
        {
            // Arrange
            var filter = CreateValidGetIssueTrackerRequest();
            filter.Status = "Open";
            var issues = CreateValidIssueTrackerList();
            var mockQueryable = issues.AsQueryable().BuildMock();

            _fixture.RepositoryContext.IssueTrackerRepository.GetQueryable()
                .Returns(mockQueryable);

            // Act
            var result = await _issueTrackerService.GetIssuesAsync(filter, CancellationToken.None);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task UpdateIssueAsync_Should_UpdateIssueSuccessfully_When_ValidRequestProvided()
        {
            // Arrange
            var issueId = "test-issue-id";
            var request = CreateValidUpdateIssueRequest();
            var existingIssue = CreateValidIssueTracker();
            var tool = CreateValidTool();

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(tool);

            _fixture.RepositoryContext.IssueTrackerRepository.FindOneAsync(Arg.Any<Expression<Func<IssueTracker, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(existingIssue);

            _fixture.RepositoryContext.IssueTrackerRepository.UpdateAsync(Arg.Any<IssueTracker>(), Arg.Any<CancellationToken>())
                .Returns(1);

            // Act
            var result = await _issueTrackerService.UpdateIssueAsync(issueId, request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
                result.Data!.ReportedBy.Should().Be(request.ReportedBy);
                result.Data.IncidentDescription.Should().Be(request.IncidentDescription);
                result.Data.ActionTaken.Should().Be(request.ActionTaken);
                result.Data.Domain.Should().Be(request.Domain);
                result.Data.Status.Should().Be(request.Status);
            }
        }

        [Fact]
        public async Task UpdateIssueAsync_Should_ReturnNotFound_When_ToolNotFound()
        {
            // Arrange
            var issueId = "test-issue-id";
            var request = CreateValidUpdateIssueRequest();

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns((ToolingTool?)null);

            // Act
            var result = await _issueTrackerService.UpdateIssueAsync(issueId, request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Message.Should().Be("Tool not found! Please try again");
            }
        }

        [Fact]
        public async Task UpdateIssueAsync_Should_ReturnNotFound_When_IssueNotFound()
        {
            // Arrange
            var issueId = "non-existent-id";
            var request = CreateValidUpdateIssueRequest();
            var tool = CreateValidTool();

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(tool);

            _fixture.RepositoryContext.IssueTrackerRepository.FindOneAsync(Arg.Any<Expression<Func<IssueTracker, bool>>>(), Arg.Any<CancellationToken>())
                .Returns((IssueTracker?)null);

            // Act
            var result = await _issueTrackerService.UpdateIssueAsync(issueId, request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Message.Should().Be("Issue not found! Please try again");
            }
        }

        [Fact]
        public async Task UpdateIssueAsync_Should_ReturnFailedDependency_When_UpdateFails()
        {
            // Arrange
            var issueId = "test-issue-id";
            var request = CreateValidUpdateIssueRequest();
            var existingIssue = CreateValidIssueTracker();
            var tool = CreateValidTool();

            _fixture.RepositoryContext.ToolingToolsRepository.FindOneAsync(Arg.Any<Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(tool);

            _fixture.RepositoryContext.IssueTrackerRepository.FindOneAsync(Arg.Any<Expression<Func<IssueTracker, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(existingIssue);

            _fixture.RepositoryContext.IssueTrackerRepository.UpdateAsync(Arg.Any<IssueTracker>(), Arg.Any<CancellationToken>())
                .Returns(0);

            // Act
            var result = await _issueTrackerService.UpdateIssueAsync(issueId, request);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(424);
                result.Message.Should().Be("Could not update Tool! Please try again");
            }
        }

        [Fact]
        public async Task PatchIssueAsync_Should_ThrowNotImplementedException_When_Called()
        {
            // Arrange
            var issueId = "test-issue-id";
            var request = CreateValidUpdateIssueRequest();

            // Act & Assert
            await Assert.ThrowsAsync<NotImplementedException>(() =>
                _issueTrackerService.PatchIssueAsync(issueId, request));
        }

        private static CreateIssueTrackerRequest CreateValidCreateIssueRequest()
        {
            return new CreateIssueTrackerRequest
            {
                ReportedBy = "<EMAIL>",
                RecordedBy = "<EMAIL>",
                ProductGroup = new List<string> { "test-group-id" },
                ProductTeam = new List<string> { "test-team-id" },
                ToolName = "test-tool-id",
                ServicesAffected = new List<string> { "test-service-id" },
                Status = IssueStatus.Open,
                IncidentDescription = "Test incident description",
                ActionTaken = "Test action taken",
                Domain = Domain.Backend,
                AssignedTo = "<EMAIL>",
                Severity = IncidentSeverity.Medium
            };
        }

        private static UpdateIssueTrackerRequest CreateValidUpdateIssueRequest()
        {
            return new UpdateIssueTrackerRequest
            {
                ReportedBy = "<EMAIL>",
                ProductGroup = new[] { "updated-group" },
                ProductTeam = new[] { "updated-team" },
                Tool = "test-tool-id",
                ServicesAffected = new List<string> { "updated-service" },
                Domain = Domain.Frontend,
                IncidentDescription = "Updated incident description",
                ActionTaken = "Updated action taken",
                Status = IssueStatus.InProgress
            };
        }

        private static GetIssueTrackerRequest CreateValidGetIssueTrackerRequest()
        {
            return new GetIssueTrackerRequest
            {
                PageIndex = 1,
                PageSize = 10,
                ProductGroup = new List<string>(),
                ProductTeam = new List<string>(),
                Tool = null,
                ReportedBy = null,
                Domain = null,
                Status = null,
                Services = new List<string>(),
                ToolVersion = "",
                PageNumber = 1
            };
        }

        private static ProductGroup CreateValidProductGroup()
        {
            return new ProductGroup
            {
                Id = "test-group-id",
                GroupName = "Test Group",
                ProductTeams = new ProductTeams
                {
                    Items = new List<ProductTeamItem>
                    {
                        new ProductTeamItem
                        {
                            Id = "test-team-id",
                            Name = "Test Team",
                            Repositories = new Hubtel.CCI.Api.Data.Entities.Repositories
                            {
                                Items = new List<RepositoryItem>
                                {
                                    new RepositoryItem
                                    {
                                        Id = "test-repo-id",
                                        Name = "Test Repository",
                                        Url = "https://github.com/test/repo"
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }

        private static ToolingTool CreateValidTool()
        {
            return new ToolingTool
            {
                Id = "test-tool-id",
                Name = "Test Tool",
                Domain = Domain.Backend,
                Description = "Test tool description",
                ToolType = "Testing",
                DocumentationUrl = "https://docs.example.com",
                LatestVersion = "1.0.0"
            };
        }

        private static Service CreateValidService()
        {
            return new Service
            {
                Id = "test-service-id",
                Name = "Test Service",
                Description = "Test service description",
                RepositoryId = "test-repo-id",
                RepositoryName = "Test Repository"
            };
        }

        private static IssueTracker CreateValidIssueTracker()
        {
            return new IssueTracker
            {
                Id = "test-issue-id",
                ReportedBy = "<EMAIL>",
                RecordedBy = "<EMAIL>",
                ProductGroup = new List<string> { "Test Group" },
                ProductTeam = new List<string> { "Test Team" },
                Tool = CreateValidTool(),
                ServicesAffected = new List<string> { "Test Service" },
                Status = IssueStatus.Open,
                IncidentDescription = "Test incident description",
                ActionTaken = "Test action taken",
                Domain = Domain.Backend,
                AssignedTo = "<EMAIL>",
                Severity = IncidentSeverity.Medium,
                ToolVersion = "1.0.0",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        private static List<IssueTracker> CreateValidIssueTrackerList()
        {
            return new List<IssueTracker>
            {
                CreateValidIssueTracker(),
                new IssueTracker
                {
                    Id = "test-issue-id-2",
                    ReportedBy = "<EMAIL>",
                    RecordedBy = "<EMAIL>",
                    ProductGroup = new List<string> { "Test Group 2" },
                    ProductTeam = new List<string> { "Test Team 2" },
                    Tool = CreateValidTool(),
                    ServicesAffected = new List<string> { "Test Service 2" },
                    Status = IssueStatus.InProgress,
                    IncidentDescription = "Test incident description 2",
                    ActionTaken = "Test action taken 2",
                    Domain = Domain.Frontend,
                    AssignedTo = "<EMAIL>",
                    Severity = IncidentSeverity.High,
                    ToolVersion = "1.1.0",
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                    UpdatedAt = DateTime.UtcNow.AddDays(-1)
                }
            };
        }
    }
}
