using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MockQueryable.NSubstitute;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class CciRoadMapMetricUpdateRecordsServiceTests{
    
    private readonly CciRoadMapMetricUpdateRecordsService _cciRoadMapMetricUpdateRecordsService;
    private readonly DiFixture _fixture;

    public CciRoadMapMetricUpdateRecordsServiceTests()
    {
        _fixture = new DiFixture();
        var logger = Substitute.For<ILogger<CciRoadMapMetricUpdateRecordsService>>();
        IOptions<SonarQubeConfig> sonarQubeConfig = Microsoft.Extensions.Options.Options.Create(new SonarQubeConfig
        {
            Host = "https://sonarqube.example.com",
            Token = "token"
        });
            
        _cciRoadMapMetricUpdateRecordsService =
            new CciRoadMapMetricUpdateRecordsService(
                logger,
                _fixture.RepositoryContext,
                sonarQubeConfig);
    }


    #region Get Recent CciRoadMapMetricUpdateRecord Async

    [Fact]
    public async Task DGetRecentCciRoadMapMetricUpdateRecordAsync_Should_Return_NotFound_When_Road_Map_Is_Not_Found()
    {

        var data = new List<CciRoadMapMetricUpdateRecord>().AsQueryable().BuildMock();
        _fixture.CciRoadMapMetricUpdateRecordRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService
            .GetRecentCciRoadMapMetricUpdateRecordAsync();

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }
    
    
    [Fact]
    public async Task DGetRecentCciRoadMapMetricUpdateRecordAsync_Should_Return_OK_When_Road_Map_Is_Found()
    {

        var data = new List<CciRoadMapMetricUpdateRecord>()
        {
            new CciRoadMapMetricUpdateRecord()
        }.AsQueryable().BuildMock();
        _fixture.CciRoadMapMetricUpdateRecordRepository.GetQueryable().Returns(data);

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService
            .GetRecentCciRoadMapMetricUpdateRecordAsync();

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
        }
    }
    
    
    [Fact]
    public async Task DGetRecentCciRoadMapMetricUpdateRecordAsync_Should_Return_Failed_Dependency_Error_When_An_Exception_Is_Thrown()
    {

        var data = new List<CciRoadMapMetricUpdateRecord>()
        {
            new CciRoadMapMetricUpdateRecord()
        }.AsQueryable().BuildMock();
        _fixture.CciRoadMapMetricUpdateRecordRepository.GetQueryable().Throws(new Exception("Simulated exception"));

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService
            .GetRecentCciRoadMapMetricUpdateRecordAsync();

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status424FailedDependency);
        }
    }
    
    
    [Fact]
    public async Task DGetRecentCciRoadMapMetricUpdateRecordAsync_Should_Return_Record_With_Latest_CreatedAt()
    {
        // Arrange
        var oldest = new CciRoadMapMetricUpdateRecord
        {
            CreatedAt = new DateTime(2025, 1, 1)
        };

        var middle = new CciRoadMapMetricUpdateRecord
        {
            CreatedAt = new DateTime(2025, 5, 1)
        };

        var latest = new CciRoadMapMetricUpdateRecord
        {
            CreatedAt = new DateTime(2025, 12, 1)
        };

        var data = new List<CciRoadMapMetricUpdateRecord> { oldest, middle, latest }
            .AsQueryable()
            .BuildMock();

        _fixture.CciRoadMapMetricUpdateRecordRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.GetRecentCciRoadMapMetricUpdateRecordAsync();

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().NotBeNull();
        result.Data!.CreatedAt.Should().Be(new DateTime(2025, 12, 1)); // ✅ Most recent date
    }


    #endregion

    #region Process CciRoadMapMetricUpdateRecord Async

    [Fact]
    public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Return_NotFound_When_No_Roadmaps_Exist()
    {
        // Arrange
        var mockRoadmaps = new List<CciRoadMap>().AsQueryable().BuildMockDbSet();
        _fixture.CciRoadMapRepository.GetQueryable().Returns(mockRoadmaps);

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(DateTime.UtcNow, DateTime.UtcNow);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Message.Should().Be("No roadmaps found for processing");
        result.Data.Should().BeFalse();
    }
    
    
    [Fact]
    public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Return_NotFound_When_No_Repositories_Exist()
    {
        // Arrange: Some roadmaps, but no matching repositories
        var roadmaps = new List<CciRoadMap>
        {
            new CciRoadMap { RepositoryId = Guid.NewGuid().ToString(), TargetDate = DateTime.UtcNow.AddDays(5) }
        }.AsQueryable().BuildMock();
        _fixture.CciRoadMapRepository.GetQueryable().Returns(roadmaps);

        var emptyRepositories = new List<Repository>().AsQueryable().BuildMock();
        _fixture.RepositoryRepository.GetQueryable().Returns(emptyRepositories);

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(DateTime.UtcNow, DateTime.UtcNow);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Data.Should().BeFalse();
        }
    }
    
    
    [Fact]
    public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Include_Roadmap_With_Exact_TargetDate()
    {
        // Arrange
        var runDate = new DateTime(2025, 6, 10);

        var roadmap = new CciRoadMap
        {
            RepositoryId = "repo-123",
            TargetDate = runDate // boundary value to test <=
        };

        var repo = new Repository
        {
            Id = "repo-123"
        };

        var mockRoadmaps = new List<CciRoadMap> { roadmap }.AsQueryable().BuildMock();
        _fixture.CciRoadMapRepository.GetQueryable().Returns(mockRoadmaps);

        var mockRepositories = new List<Repository> { repo }.AsQueryable().BuildMock();
        _fixture.RepositoryRepository.GetQueryable().Returns(mockRepositories);

        // Mock fetch scores
        using var httpTest = new HttpTest();
        
        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent
            {
                Measures = new List<SonarQubeMeasure>
                {
                    new SonarQubeMeasure { Metric = "bugs", Value = "5" },
                    new SonarQubeMeasure { Metric = "vulnerabilities", Value = "2" }
                }
            }
        };

        // Mock HTTP response
        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 200);

        _fixture.CciRoadMapRepository.UpdateRangeAsync(Arg.Any<List<CciRoadMap>>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(1));

        _fixture.CciRoadMapMetricUpdateRecordRepository
            .AddAsync(Arg.Any<CciRoadMapMetricUpdateRecord>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(1));

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(runDate, DateTime.UtcNow);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().BeTrue();
    }
    
    
    [Fact]
    public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Return_FailedDependency_When_FetchScores_Fails()
    {
        // Arrange
        var roadmap = new CciRoadMap
        {
            RepositoryId = "repo-123",
            TargetDate = DateTime.UtcNow.AddDays(5)
        };

        var repo = new Repository { Id = "repo-123" };

        var mockRoadmaps = new List<CciRoadMap> { roadmap }.AsQueryable().BuildMock();
        _fixture.CciRoadMapRepository.GetQueryable().Returns(mockRoadmaps);

        var mockRepositories = new List<Repository> { repo }.AsQueryable().BuildMock();
        _fixture.RepositoryRepository.GetQueryable().Returns(mockRepositories);

        // Mock the FetchCciRepositoryScores method to return a failed response
        // This depends on how you're mocking it - you might need to adjust based on your setup
        using var httpTest = new HttpTest();
        httpTest.RespondWith("Server Error", 500); // This should make Code != 200

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(DateTime.UtcNow, DateTime.UtcNow);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
        result.Message.Should().Be("Failed to fetch cci repository scores");
        result.Data.Should().BeFalse(); // This will kill the mutation for scenario #3
    }
    
    
    [Fact]
    public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Return_FailedDependency_When_UpdateRange_Fails()
    {
        // Arrange
        var roadmap = new CciRoadMap
        {
            RepositoryId = "repo-123",
            TargetDate = DateTime.UtcNow.AddDays(5)
        };

        var repo = new Repository { Id = "repo-123" };

        var mockRoadmaps = new List<CciRoadMap> { roadmap }.AsQueryable().BuildMock();
        _fixture.CciRoadMapRepository.GetQueryable().Returns(mockRoadmaps);

        var mockRepositories = new List<Repository> { repo }.AsQueryable().BuildMock();
        _fixture.RepositoryRepository.GetQueryable().Returns(mockRepositories);

        // Mock successful fetch scores
        using var httpTest = new HttpTest();
        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent
            {
                Measures = new List<SonarQubeMeasure>
                {
                    new SonarQubeMeasure { Metric = "bugs", Value = "5" },
                    new SonarQubeMeasure { Metric = "vulnerabilities", Value = "2" }
                }
            }
        };
        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 200);

        // Mock UpdateRangeAsync to return 0 (failure)
        _fixture.CciRoadMapRepository.UpdateRangeAsync(Arg.Any<List<CciRoadMap>>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(DateTime.UtcNow, DateTime.UtcNow);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
        result.Message.Should().Be("Failed to update roadmaps");
        result.Data.Should().BeFalse(); // This will kill the mutation for scenario #4
    }
    
    
    [Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Return_FailedDependency_When_AddRecord_Fails()
{
    // Arrange
    var roadmap = new CciRoadMap
    {
        RepositoryId = "repo-123",
        TargetDate = DateTime.UtcNow.AddDays(5)
    };

    var repo = new Repository { Id = "repo-123" };

    var mockRoadmaps = new List<CciRoadMap> { roadmap }.AsQueryable().BuildMock();
    _fixture.CciRoadMapRepository.GetQueryable().Returns(mockRoadmaps);

    var mockRepositories = new List<Repository> { repo }.AsQueryable().BuildMock();
    _fixture.RepositoryRepository.GetQueryable().Returns(mockRepositories);

    // Mock successful fetch scores
    using var httpTest = new HttpTest();
    var sonarResponse = new SonarQubeMetricsResponse()
    {
        Component = new SonarQubeComponent
        {
            Measures = new List<SonarQubeMeasure>
            {
                new SonarQubeMeasure { Metric = "bugs", Value = "5" },
                new SonarQubeMeasure { Metric = "vulnerabilities", Value = "2" }
            }
        }
    };
    httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 200);

    // Mock successful update
    _fixture.CciRoadMapRepository.UpdateRangeAsync(Arg.Any<List<CciRoadMap>>(), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(1));

    // Mock AddAsync to return 0 (failure)
    _fixture.CciRoadMapMetricUpdateRecordRepository
        .AddAsync(Arg.Any<CciRoadMapMetricUpdateRecord>(), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(0));

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    result.Message.Should().Be("Failed to add cciRoadMapMetricUpdateRecord");
    result.Data.Should().BeFalse(); // This will kill the mutation for scenario #5
}


    [Fact]
    public async Task ProcessCciRoadMapMetricUpdateRecordAsync_Should_Return_FailedDependency_When_Exception_Occurs()
    {
        // Arrange
        // Mock the repository to throw an exception
        _fixture.CciRoadMapRepository.GetQueryable()
            .Returns(x => { throw new InvalidOperationException("Database connection failed"); });

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.ProcessCciRoadMapMetricUpdateRecordAsync(DateTime.UtcNow, DateTime.UtcNow);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
        result.Message.Should().Be("An error occurred while processing cciRoadMapMetricUpdateRecord");
        result.Data.Should().BeFalse(); // This will kill the mutation for scenario #6
    }

    
    #endregion


    #region FetchCciRepositoryScores
    [Fact]
    public async Task FetchCciRepositoryScores_Should_Return_OK_With_Metrics_When_Repositories_Exist()
    {
        
        using var httpTest = new HttpTest();
        
        // Arrange
        var repositories = new List<Repository>
        {
            new Repository { Id = "repo1", Name = "Repo 1", SonarQubeKey = "repo1-key" },
            new Repository { Id = "repo2", Name = "Repo 2", SonarQubeKey = "repo2-key" }
        };

        var sonarResponse = new SonarQubeMetricsResponse()
        {
            Component = new SonarQubeComponent
            {
                Measures = new List<SonarQubeMeasure>
                {
                    new SonarQubeMeasure { Metric = "bugs", Value = "5" },
                    new SonarQubeMeasure { Metric = "vulnerabilities", Value = "2" }
                }
            }
        };

        // Mock HTTP response
        httpTest.RespondWith(JsonConvert.SerializeObject(sonarResponse), 200);

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.FetchCciRepositoryScores(repositories);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeEmpty();
            result.Data.Should().ContainKey("repo1");
            result.Data!["repo1"].Bugs.Should().Be(5);
            result.Data["repo1"].Vulnerabilities.Should().Be(2);
        }
    }
    
    
    [Fact]
    public async Task FetchCciRepositoryScores_Should_Return_OK_With_Empty_Metrics_When_No_Repositories()
    {
        using var httpTest = new HttpTest(); // ✅ Mock HTTP calls

        // Arrange
        var repositories = new List<Repository>(); // No repositories

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.FetchCciRepositoryScores(repositories);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task FetchCciRepositoryScores_Should_Return_FailedDependency_When_Api_Fails()
    {
        using var httpTest = new HttpTest();

        // Arrange
        var repositories = new List<Repository>
        {
            new Repository { Id = "repo1", Name = "Repo 1", SonarQubeKey = "repo1-key" }
        };

        httpTest.RespondWith("API Failure", 500); // ✅ Simulate API failure

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.FetchCciRepositoryScores(repositories);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task FetchCciRepositoryScores_Should_Handle_Http_Errors_Gracefully()
    {
        using var httpTest = new HttpTest();

        // Arrange
        var repositories = new List<Repository>
        {
            new Repository { Id = "repo1", Name = "Repo 1", SonarQubeKey = "repo1-key" }
        };

        httpTest.RespondWith("Not Found", 200); // ✅ Simulate 404 response

        // Act
        var result = await _cciRoadMapMetricUpdateRecordsService.FetchCciRepositoryScores(repositories);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeEmpty();
        }
    }
    

    #endregion


#region Process CciRoadMapMetricUpdateRecordAsyncNew

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Return_NotFound_When_No_RoadmapRecordsExist()
{
    // Arrange
    var emptyRecords = new List<CciRoadMapRecord>().AsQueryable().BuildMock();
    _fixture.CciRoadMapRecordRepository.GetQueryable().Returns(emptyRecords);

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status404NotFound);
    result.Message.Should().Be("No roadmaps found for processing");
    result.Data.Should().BeFalse();
}

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Return_NotFound_When_No_RepositoriesExist()
{
    // Arrange: one roadmap record, but no matching Repository
    var rec = new CciRoadMapRecord
    {
        Repository = new RepositoryInfo { Id = "repo-x" }
    };
    _fixture.CciRoadMapRecordRepository.GetQueryable()
        .Returns(new[] { rec }.AsQueryable().BuildMock());

    _fixture.RepositoryRepository.GetQueryable()
        .Returns(new List<Repository>().AsQueryable().BuildMock());

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status404NotFound);
    result.Message.Should().Be("No repositories found for processing");
    result.Data.Should().BeFalse();
}

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Return_FailedDependency_When_FetchScoresFails()
{
    // Arrange
    var rec = new CciRoadMapRecord
    {
        Repository = new RepositoryInfo { Id = "repo-fail" },
    };
    _fixture.CciRoadMapRecordRepository.GetQueryable()
        .Returns(new[] { rec }.AsQueryable().BuildMock());

    var repo = new Repository { Id = "repo-fail", SonarQubeKey = "key" };
    _fixture.RepositoryRepository.GetQueryable()
        .Returns(new[] { repo }.AsQueryable().BuildMock());

    // Simulate Sonar API failures → no metrics added
    using var httpTest = new HttpTest();
    httpTest.RespondWith("Server Error", 500);

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    result.Message.Should().Be("Failed to fetch cci repository scores");
    result.Data.Should().BeFalse();
}

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Return_FailedDependency_When_UpdateRangeFails()
{
    // Arrange happy path up to update:
    var rec = new CciRoadMapRecord
    {
        Repository = new RepositoryInfo { Id = "repo1" },
    };
    _fixture.CciRoadMapRecordRepository.GetQueryable()
        .Returns(new[] { rec }.AsQueryable().BuildMock());
    _fixture.RepositoryRepository.GetQueryable()
        .Returns(new[] { new Repository { Id = "repo1", SonarQubeKey = "k" } }
            .AsQueryable().BuildMock());

    using var httpTest = new HttpTest();
    var sonar = new SonarQubeMetricsResponse
    {
        Component = new SonarQubeComponent
        {
            Measures = new List<SonarQubeMeasure>
            {
                new() { Metric = "bugs", Value = "1" },
                new() { Metric = "vulnerabilities", Value = "2" },
                new() { Metric = "code_smells", Value = "3" },
                new() { Metric = "coverage", Value = "4" },
                new() { Metric = "duplicated_lines_density", Value = "5" },
                new() { Metric = "security_hotspots", Value = "6" }
            }
        }
    };
    httpTest.RespondWith(JsonConvert.SerializeObject(sonar), 200);

    // Stub CCI‐score query to include one match
    var today = Miscellaneous.GetCurrentPublicationTargetDay();
    var cciScore = new CciRepositoryScore
    {
        RepositoryId = "repo1",
        PublicationStartDate = today.AddDays(-1),
        PublicationEndDate   = today.AddDays(+1),
        FinalAverage         = 42m
    };
    _fixture.CciRepositoryScoreRepository.GetQueryable()
        .Returns(new[] { cciScore }.AsQueryable().BuildMock());

    // Make UpdateRangeAsync fail
    _fixture.CciRoadMapRecordRepository
        .UpdateRangeAsync(Arg.Any<List<CciRoadMapRecord>>(), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(0));

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    result.Message.Should().Be("Failed to update roadmaps");
    result.Data.Should().BeFalse();
}

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Return_FailedDependency_When_AddRecordFails()
{
    // Arrange happy path up to add:
    var rec = new CciRoadMapRecord
    {
        Repository = new RepositoryInfo { Id = "repoA" },
    };
    _fixture.CciRoadMapRecordRepository.GetQueryable()
        .Returns(new[] { rec }.AsQueryable().BuildMock());
    _fixture.RepositoryRepository.GetQueryable()
        .Returns(new[] { new Repository { Id = "repoA", SonarQubeKey = "k" } }
            .AsQueryable().BuildMock());

    using var httpTest = new HttpTest();
    var sonar = new SonarQubeMetricsResponse
    {
        Component = new SonarQubeComponent
        {
            Measures = new List<SonarQubeMeasure>
            {
                new() { Metric = "bugs", Value = "1" },
                new() { Metric = "vulnerabilities", Value = "2" },
                new() { Metric = "code_smells", Value = "3" },
                new() { Metric = "coverage", Value = "4" },
                new() { Metric = "duplicated_lines_density", Value = "5" },
                new() { Metric = "security_hotspots", Value = "6" }
            }
        }
    };
    httpTest.RespondWith(JsonConvert.SerializeObject(sonar), 200);

    var today = Miscellaneous.GetCurrentPublicationTargetDay();
    _fixture.CciRepositoryScoreRepository.GetQueryable()
        .Returns(new[]
        {
            new CciRepositoryScore
            {
                RepositoryId = "repoA",
                PublicationStartDate = today.AddDays(-1),
                PublicationEndDate   = today.AddDays(+1),
                FinalAverage         = 99m
            }
        }.AsQueryable().BuildMock());

    _fixture.CciRoadMapRecordRepository
        .UpdateRangeAsync(Arg.Any<List<CciRoadMapRecord>>(), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(1));

    // Fail the AddAsync
    _fixture.CciRoadMapMetricUpdateRecordRepository
        .AddAsync(Arg.Any<CciRoadMapMetricUpdateRecord>(), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(0));

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    result.Message.Should().Be("Failed to add cciRoadMapMetricUpdateRecord");
    result.Data.Should().BeFalse();
}

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Return_FailedDependency_When_Exception_Occurs()
{
    // Arrange: force an exception from the record repo
    _fixture.CciRoadMapRecordRepository.GetQueryable()
        .Throws(new InvalidOperationException("ouch"));

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.UtcNow, DateTime.UtcNow);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    result.Message.Should().Be("An error occurred while processing cciRoadMapMetricUpdateRecord");
    result.Data.Should().BeFalse();
}

[Fact]
public async Task ProcessCciRoadMapMetricUpdateRecordAsyncNew_Should_Process_And_Map_Metrics_And_FinalAverage_Correctly()
{
    // Arrange
    var runDate      = DateTime.UtcNow.Date;
    var runStartTime = runDate.AddHours(1);
    var repoId       = "r1";
    var key          = "key1";

    // 1) a roadmap record with all MetricData pre‐initialized
    var record = new CciRoadMapRecord
    {
        Repository       = new RepositoryInfo { Id = repoId, Name = "Repo1" },
        Bugs             = new MetricData(),
        CodeSmells       = new MetricData(),
        Coverage         = new MetricData(),
        DuplicatedLines  = new MetricData(),
        Vulnerabilities  = new MetricData(),
        SecurityHotspots = new MetricData()
    };
    _fixture.CciRoadMapRecordRepository.GetQueryable()
        .Returns(new[] { record }.AsQueryable().BuildMock());

    // 2) the matching Repository
    _fixture.RepositoryRepository.GetQueryable()
        .Returns(new[]
        {
            new Repository { Id = repoId, SonarQubeKey = key }
        }.AsQueryable().BuildMock());

    // 3) stub Sonar returns exactly six measures
    using var httpTest = new HttpTest();
    var measures = new List<SonarQubeMeasure>
    {
        new() { Metric = "bugs",                       Value = "10" },
        new() { Metric = "code_smells",                Value = "20" },
        new() { Metric = "coverage",                   Value = "30" },
        new() { Metric = "duplicated_lines_density",   Value = "40" },
        new() { Metric = "vulnerabilities",            Value = "50" },
        new() { Metric = "security_hotspots",          Value = "60" }
    };
    var sonarResp = new SonarQubeMetricsResponse
    {
        Component = new SonarQubeComponent { Measures = measures }
    };
    httpTest.RespondWith(JsonConvert.SerializeObject(sonarResp), 200);

    // 4) a single CCI repository‐score that covers today
    var today    = Miscellaneous.GetCurrentPublicationTargetDay();
    var finalAvg = 77m;
    var cciScore = new CciRepositoryScore
    {
        RepositoryId         = repoId,
        PublicationStartDate = today.AddDays(-1),
        PublicationEndDate   = today.AddDays(+1),
        FinalAverage         = finalAvg
    };
    _fixture.CciRepositoryScoreRepository.GetQueryable()
        .Returns(new[] { cciScore }.AsQueryable().BuildMock());

    // 5) stub update & capture the AddAsync payload
    _fixture.CciRoadMapRecordRepository
        .UpdateRangeAsync(Arg.Any<List<CciRoadMapRecord>>(), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(1));

    CciRoadMapMetricUpdateRecord captured = null!;
    _fixture.CciRoadMapMetricUpdateRecordRepository
        .AddAsync(Arg.Do<CciRoadMapMetricUpdateRecord>(x => captured = x), Arg.Any<CancellationToken>())
        .Returns(Task.FromResult(1));

    // Act
    var result = await _cciRoadMapMetricUpdateRecordsService
        .ProcessCciRoadMapMetricUpdateRecordAsyncNew(runDate, runStartTime);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status200OK);
    result.Data.Should().BeTrue();

    // Inspect the single updated roadmap inside the payload
    var updated = captured.CciRoadMapRecords.Items.Single();
    updated.Bugs?.CurrentValue.Should().Be(10m);
    updated.CodeSmells?.CurrentValue.Should().Be(20m);
    updated.Coverage?.CurrentValue.Should().Be(30m);
    updated.DuplicatedLines?.CurrentValue.Should().Be(40m);
    updated.Vulnerabilities?.CurrentValue.Should().Be(50m);
    updated.SecurityHotspots?.CurrentValue.Should().Be(60m);

    updated.OverallQuality.Current.Should().Be(finalAvg);
    updated.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
}

#endregion

    
}