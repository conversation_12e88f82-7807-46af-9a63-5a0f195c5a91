using Akka.Actor;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Net;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models.Azure;
using Hubtel.CCI.Api.Services.Interfaces;
using Xunit;
using ReleaseEnvironment = Hubtel.CCI.Api.Dtos.Responses.Azure.ReleaseEnvironment;
using Repository = Hubtel.CCI.Api.Data.Entities.Repository;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class DciServiceScoreServiceTests
{
    private readonly DciServiceScoreService _dciServiceScoreService;
    private readonly DiFixture _fixture;
    private readonly ILogger<DciServiceScoreService> _logger;
    private readonly IAzureProxyService _azureProxyService;

    public DciServiceScoreServiceTests()
    {
        _fixture = new DiFixture();
        _logger = Substitute.For<ILogger<DciServiceScoreService>>();

        _azureProxyService = _fixture.AzureProxyService;
        
        _dciServiceScoreService = new DciServiceScoreService(
            _logger,
            _fixture.RepositoryContext,
            _azureProxyService,
            _fixture.MainActorService);
    }

    #region TriggerDciServiceScorePublicationAsync

    [Fact]
    public async Task TriggerDciServiceScorePublicationAsync_Should_Return_OK_Api_Response_On_Success()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        
        _fixture.RepositoryContext.DciServiceScoreRepository.GetQueryable().Returns(new List<DciServiceScore>().AsQueryable().BuildMock());

        // Act
        var result = await _dciServiceScoreService.TriggerDciServiceScorePublicationAsync(CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be("DciServiceScore publication triggered successfully");
        result.Data.Should().BeTrue();

        await _fixture.MainActorService.Received(1).Tell(
            Arg.Is<PublishDciServiceScoresMessage>(m => 
                m.PublicationWeek == publicationWeek),
            ActorRefs.Nobody);
    }
    
    [Fact]
    
    public async Task TriggerDciServiceScorePublicationAsync_Should_Return_OK_Api_Response_When_Scores_Already_Published()
    {
        // Arrange
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        var startDate = publicationDate.Date;
        var endDate = publicationDate.Date.AddDays(1);
        _fixture.RepositoryContext.DciServiceScoreRepository.GetQueryable().Returns(new List<DciServiceScore>()
        {
            new DciServiceScore()
            {
                PublicationStartDate = startDate,
                PublicationEndDate = endDate
            }
        }.AsQueryable().BuildMock());

        // Act
        var result = await _dciServiceScoreService.TriggerDciServiceScorePublicationAsync(CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Message.Should().Be($"DCI scores already published for day: {publicationDate} in week: {publicationWeek}");
        result.Data.Should().BeTrue();
    }

    #endregion

    #region PublishDciServiceScoresAsync

    [Fact]
    public async Task PublishDciServiceScoresAsync_Should_Return_NotFound_When_No_Repositories_Found()
    {
        // Arrange
        var request = new PublishDciServiceScoresMessage
        {
            PublicationDate = DateTime.UtcNow,
            PublicationWeek = "2025-01",
            PublicationStartDate = DateTime.UtcNow.Date,
            PublicationEndDate = DateTime.UtcNow.Date.AddDays(1)
        };

        var repositories = new List<Repository>()
        {
          
        }.AsQueryable().BuildMock();
        
        _fixture.RepositoryContext.RepositoryRepository.GetQueryable().Returns(repositories);
        
        
   

        // Act
        var result = await _dciServiceScoreService.PublishDciServiceScoresAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Message.Should().Be("No repositories found");
        result.Data.Should().BeFalse();
    }

    [Fact]
    public async Task PublishDciServiceScoresAsync_Should_Return_OK_When_Repositories_Exist()
    {
        // Arrange
        var request = new PublishDciServiceScoresMessage
        {
            PublicationDate = DateTime.UtcNow,
            PublicationWeek = "2025-01",
            PublicationStartDate = DateTime.UtcNow.Date,
            PublicationEndDate = DateTime.UtcNow.Date.AddDays(1)
        };

        var repositories = new List<Repository>()
        {
            new Repository
            {
                Id = "repo-1",
                Name = "Test Repo 1",
                Status = ValidationConstants.ProductStatus.Active,
                Type = "Backend",
                Url = "https://dev.azure.com/hubtel/Back-End/_git/repo-1",
                SonarQubeKey = "repo-1-key",
                CreatedAt = DateTime.UtcNow.AddDays(-10)
            },
            new Repository
            {
                Id = "repo-2", 
                Name = "Test Repo 2",
                Status = ValidationConstants.ProductStatus.Active,
                Type = "Frontend",
                Url = "https://dev.azure.com/hubtel/Front-End/_git/repo-2",
                SonarQubeKey = "repo-2-key",
                CreatedAt = DateTime.UtcNow.AddDays(-5)
            }
        }.AsQueryable().BuildMock();
        
        
        var services = new List<Service>()
        {
            new Service()
            {
                Id = "service-1",
                Name = "Test Service 1",
                RepositoryId = "repo-1",
                RepositoryName = "Test Repo 1"
            },
            new Service()
            {
                Id = "service-2",
                Name = "Test Service 2",
                RepositoryId = "repo-2",
                RepositoryName = "Test Repo 2"
            }
        }.AsQueryable().BuildMock();
        
        
        var productTeams = new List<ProductTeam>()
        {
            new ProductTeam()
            {
                Id = "team-1",
                Name = "Test Team 1",
                Repositories = new Data.Entities.Repositories()
                {
                    Items = new List<RepositoryItem>()
                    {
                        new RepositoryItem()
                        {
                            Id = "repo-1",
                            Name = "Test Repo 1",
                            Type = "Backend",
                            SonarQubeKey = "repo-1-key",
                            Url = "https://dev.azure.com/hubtel/Back-End/_git/repo-1"
                        },
                        new RepositoryItem()
                        {
                            Id = "repo-2",
                            Name = "Test Repo 2",
                            Type = "Frontend",
                            SonarQubeKey = "repo-2-key",
                            Url = "https://dev.azure.com/hubtel/Front-End/_git/repo-2"
                        }
                    }
                }
            }
        }.AsQueryable().BuildMock();
        
        var productGroups = new List<ProductGroup>()
        {
            new ProductGroup()
            {
                Id = "group-1",
                GroupName = "Test Group 1",
                ProductTeams = new ProductTeams()
                {
                    Items = new List<ProductTeamItem>()
                    {
                        new ProductTeamItem()
                        {
                            Id = "team-1",
                            Name = "Test Team 1",
                            Repositories = new()
                            {
                                Items = new List<RepositoryItem>()
                                {
                                    new RepositoryItem()
                                    {
                                        Id = "repo-1",
                                        Name = "Test Repo 1",
                                        Type = "Backend",
                                        SonarQubeKey = "repo-1-key",
                                        Url = "https://dev.azure.com/hubtel/Back-End/_git/repo-1"
                                    },
                                    new RepositoryItem()
                                    {
                                        Id = "repo-2",
                                        Name = "Test Repo 2",
                                        Type = "Frontend",
                                        SonarQubeKey = "repo-2-key",
                                        Url = "https://dev.azure.com/hubtel/Front-End/_git/repo-2"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.ServiceRepository.GetQueryable().Returns(services);
        _fixture.RepositoryContext.ProductTeamRepository.GetQueryable().Returns(productTeams);
        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable().Returns(productGroups);
        
        _fixture.AzureProxyService.GetRepositoryDetailAsync("Back-End", "repo-1").Returns(new ApiResponse<AzureRepositoryResponse>("Success", 200, new AzureRepositoryResponse() { Id = "repo-1", Name = "Test Repo 1", WebUrl = "https://dev.azure.com/hubtel/Back-End/_git/repo-1" }));
        
        _fixture.AzureProxyService.GetRepositoryDetailAsync("Front-End", "repo-2").Returns(new ApiResponse<AzureRepositoryResponse>("Success", 200, new AzureRepositoryResponse() { Id = "repo-2", Name = "Test Repo 2", WebUrl = "https://dev.azure.com/hubtel/Front-End/_git/repo-2" }));
        
        _fixture.AzureProxyService.GetRecentReleasesAsync(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<CancellationToken>()).Returns(new ApiResponse<List<AzureReleaseDetail>>("Success", 200, new List<AzureReleaseDetail>()
        {
            new AzureReleaseDetail()
            {
                Id = 1,
                Name = "Test Release 1",
                CreatedOn = DateTime.UtcNow.AddDays(-1),
                Environments = new List<Hubtel.CCI.Api.Dtos.Models.Azure.ReleaseEnvironment>()
                {
                    new ()
                    {
                        Name = "Production",
                        DeploySteps = new List<DeployStep>()
                        {
                            new DeployStep()
                            {
                                ReleaseDeployPhases = new List<ReleaseDeployPhase>()
                                {
                                    new ReleaseDeployPhase()
                                    {
                                        DeploymentJobs = new List<DeploymentJobContainer>()
                                        {
                                            new DeploymentJobContainer()
                                            {
                                                Tasks = new List<DeploymentTaskItem>()
                                                {
                                                    new DeploymentTaskItem()
                                                    {
                                                        FinishTime = DateTime.UtcNow
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }));
        
        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(Arg.Any<string>(), Arg.Any<string>(),
                Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(new ProductGroupDetails()
            {
                ProductGroupId = "group-1",
                ProductGroupName = "Test Group 1",
                ProductTeams = new ProductTeams()
                {
                    Items = new List<ProductTeamItem>()
                    {
                        new ProductTeamItem()
                        {
                            Id = "team-1",
                            Name = "Test Team 1",
                            Repositories = new()
                            {
                                Items = new List<RepositoryItem>()
                                {
                                    new RepositoryItem()
                                    {
                                        Id = "repo-1",
                                        Name = "Test Repo 1",
                                        Type = "Backend",
                                        SonarQubeKey = "repo-1-key",
                                        Url = "https://dev.azure.com/hubtel/Back-End/_git/repo-1"
                                    }
                                }
                            }
                        }
                    }
                }
            });
        
        
        _fixture.RepositoryContext.RepositoryRepository.GetQueryable().Returns(repositories);
        
        _fixture.AzureProxyService.CalculateDeploymentMetricsAsync(Arg.Any<CalculateDeploymentMetricsRequest>(), Arg.Any<CancellationToken>()).Returns(new ApiResponse<EnhancedDeploymentMetrics>("Success", 200, new EnhancedDeploymentMetrics()
        {
            StabilityScore = 0.5m,
            ProcessComplianceScore = 0.5m,
            SpeedScore = 0.5m,
            SuccessRate = 0.5m,
            IssueSeverityScore = 0.5m,
            DeploymentCount = 1,
            DataQuality = DataQuality.PeriodBased
        }));
        
        _fixture.AzureProxyService.ExtractProjectAndRepository("https://dev.azure.com/hubtel/Back-End/_git/repo-1").Returns(("Back-End", "repo-1"));
        _fixture.AzureProxyService.ExtractProjectAndRepository("https://dev.azure.com/hubtel/Front-End/_git/repo-2").Returns(("Front-End", "repo-2"));
        
        _fixture.RepositoryContext.DciServiceScoreRepository.AddRangeAsync(Arg.Any<List<DciServiceScore>>(), Arg.Any<CancellationToken>()).Returns(2);


        // Act
        var result = await _dciServiceScoreService.PublishDciServiceScoresAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().BeTrue();
    }

    // [Fact]
    // public async Task PublishDciServiceScoresAsync_Should_Filter_Out_Retired_Repositories()
    // {
    //     // Arrange
    //     var request = new PublishDciServiceScoresMessage
    //     {
    //         PublicationDate = DateTime.UtcNow,
    //         PublicationWeek = "2025-01",
    //         PublicationStartDate = DateTime.UtcNow.Date,
    //         PublicationEndDate = DateTime.UtcNow.Date.AddDays(1)
    //     };
    //
    //     var repositories = new[]
    //     {
    //         new Repository
    //         {
    //             Id = "repo-active",
    //             Name = "Active Repo",
    //             Status = ValidationConstants.ProductStatus.Active,
    //             CreatedAt = DateTime.UtcNow.AddDays(-10)
    //         },
    //         new Repository
    //         {
    //             Id = "repo-retired",
    //             Name = "Retired Repo", 
    //             Status = ValidationConstants.ProductStatus.Retired,
    //             CreatedAt = DateTime.UtcNow.AddDays(-5)
    //         }
    //     }.AsQueryable().BuildMock();
    //
    //     _fixture.RepositoryContext.RepositoryRepository.GetQueryable().Returns(repositories);
    //
    //     // Act
    //     var result = await _dciServiceScoreService.PublishDciServiceScoresAsync(request, CancellationToken.None);
    //
    //     // Assert - Should process only 1 active repository
    //     result.Data.Should().BeTrue();
    //     _logger.Received().LogInformation(
    //         Arg.Is<string>(s => s.Contains("Processing {Total} repositories")),
    //         1, // Only 1 active repository should be counted
    //         Arg.Any<object[]>());
    // }

    #endregion

    #region GetDciServiceScoresAsync

    [Fact]
    public async Task GetDciServiceScoresAsync_Should_Return_PagedResult()
    {
        // Arrange
        var filter = new Dtos.Common.SearchFilter { SearchTerm = "Some Service"};
        var scores = new List<DciServiceScore> { new DciServiceScore()
        {
            Id = "1",
            ServiceName = "Some Service"
        }}.AsQueryable().BuildMock();
        _fixture.RepositoryContext.DciServiceScoreRepository.GetQueryable().Returns(scores);

        // Act
        var result = await _dciServiceScoreService.GetDciServiceScoresAsync(filter, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().NotBeEmpty();
    }

    #endregion

    #region GetDciServiceScoreAsync

    [Fact]
    public async Task GetDciServiceScoreAsync_Should_Return_Score_When_Id_Exists()
    {
        // Arrange
        var id = "score-1";
        var score = new DciServiceScore { Id = id };
        _fixture.RepositoryContext.DciServiceScoreRepository.GetByIdAsync(id, CancellationToken.None).Returns(score);

        // Act
        var result = await _dciServiceScoreService.GetDciServiceScoreAsync(id, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Id.Should().Be(id);
    }

    [Fact]
    public async Task GetDciServiceScoreAsync_Should_Return_NotFound_When_Id_Does_Not_Exist()
    {
        // Arrange
        var id = "non-existent";
        _fixture.RepositoryContext.DciServiceScoreRepository.GetQueryable().Returns(new List<DciServiceScore>().AsQueryable().BuildMock());

        // Act
        var result = await _dciServiceScoreService.GetDciServiceScoreAsync(id, CancellationToken.None);

        // Assert
        result.Code.Should().Be(StatusCodes.Status404NotFound);
        result.Data.Should().BeNull();
    }

    #endregion

    #region UpdateDciServiceScoreAsync

    [Fact]
    public async Task UpdateDciServiceScoreAsync_Should_Return_True_On_Success()
    {
        // Arrange
        var id = "score-1";
        var request = new Dtos.Requests.DciServiceScore.UpdateDciServiceScoreRequest { RepositoryId = "1"};
        var score = new DciServiceScore { Id = id };
        _fixture.RepositoryContext.DciServiceScoreRepository.GetByIdAsync(id, CancellationToken.None).Returns(score);
        _fixture.RepositoryContext.DciServiceScoreRepository.UpdateAsync(score).Returns(1);

        // Act
        var result = await _dciServiceScoreService.UpdateDciServiceScoreAsync(id, request, CancellationToken.None);

        // Assert
        result.Data.Should().BeTrue();
    }

    #endregion

    #region DeleteDciServiceScoreAsync

    [Fact]
    public async Task DeleteDciServiceScoreAsync_Should_Return_True_On_Success()
    {
        // Arrange
        var id = "score-1";
        var score = new DciServiceScore { Id = id };
        _fixture.RepositoryContext.DciServiceScoreRepository.GetByIdAsync(id, CancellationToken.None).Returns(score);
        
        _fixture.RepositoryContext.DciServiceScoreRepository.DeleteAsync(score).Returns(1);

        // Act
        var result = await _dciServiceScoreService.DeleteDciServiceScoreAsync(id, CancellationToken.None);

        // Assert
        result.Data.Should().BeTrue();
    }

    #endregion

    #region CurateDciProductRankingAsync

    [Fact]
    public async Task CurateDciProductRankingAsync_Should_Return_True_On_Valid_Data()
    {
        // Arrange
        var rankingData = new List<RepositoryDciScore>
        {
            new RepositoryDciScore()
            {
                RepositoryId = "1",
                RepositoryName = "2",
                DciScore = 0.4m,
                ProductTeamId = "1",
                ProductTeamName = "2",
                ProductGroupId = "1",
                ProductGroupName = "2",
                RepositoryType = "Backend"
            }
        };
        var publicationDate = DateTime.UtcNow;
        var publicationWeek = "2025-01";
        var startDate = DateTime.UtcNow.Date;
        var endDate = DateTime.UtcNow.Date.AddDays(1);
        var services = new List<DciServiceScore>()
        {
            new DciServiceScore()
            {
                RepositoryId = "1",
                RepositoryName = "2",
                PublicationStartDate = startDate,
                PublicationEndDate = endDate,
                PublicationDate = publicationDate,
                PublicationWeek = publicationWeek
            }
        }.BuildMock();
        
        _fixture.RepositoryContext.DciServiceScoreRepository.GetQueryable().Returns(services);
        
        _fixture.RepositoryContext.DciProductsRankingRepository.AddAsync(Arg.Any<DciProductsRanking>()).Returns(1);

        // Act
        var result = await _dciServiceScoreService.CurateDciProductRankingAsync(rankingData, publicationDate, publicationWeek, startDate, endDate, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
    }

    #endregion
  
}