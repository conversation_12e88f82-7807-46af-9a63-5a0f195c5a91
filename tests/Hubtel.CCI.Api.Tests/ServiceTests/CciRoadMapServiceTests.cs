using System.Linq.Expressions;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMapRecord;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class CciRoadMapServiceTests
{
    private readonly CciRoadMapService _cciRoadMapService;
    private readonly DiFixture _fixture;
    private readonly ILogger<CciRoadMapService> _logger;

    public CciRoadMapServiceTests()
    {
        _fixture = new DiFixture();
        _logger = Substitute.For<ILogger<CciRoadMapService>>();
        _cciRoadMapService =
            new CciRoadMapService(
                _logger,
                _fixture.RepositoryContext);
    }


    #region Delete Cci RoadMap

        [Fact]
    public async Task DeleteCciRoadMapAsync_Should_Return_NotFound_When_Road_Map_Is_Not_Found()
    {
        // Arrange
        const string id = "not-found";


        _fixture.CciRoadMapRepository.FindOneAsync(Arg.Any<Expression<Func<CciRoadMap, bool>>>()).ReturnsNull();

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }
    
    
    [Fact]
    public async Task DeleteCciRoadMapAsync_Should_Return_True_When_Road_Map_Is_Deleted_Successfully()
    {
        // Arrange
        const string id = "exists";


        _fixture.CciRoadMapRepository.FindOneAsync(Arg.Any<Expression<Func<CciRoadMap, bool>>>()).Returns(new CciRoadMap());

        _fixture.CciRoadMapRepository.DeleteAsync(Arg.Any<CciRoadMap>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task DeleteCciRoadMapAsync_Should_Return_False_When_Road_Map_Is_Not_Deleted_Successfully()
    {
        // Arrange
        const string id = "exists";


        _fixture.CciRoadMapRepository.FindOneAsync(Arg.Any<Expression<Func<CciRoadMap, bool>>>()).Returns(new CciRoadMap());

        _fixture.CciRoadMapRepository.DeleteAsync(Arg.Any<CciRoadMap>(), Arg.Any<CancellationToken>()).Returns(0);

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status424FailedDependency);
            result.Data.Should().BeFalse();
        }
    }
    
    
    [Fact]
    public async Task DeleteCciRoadMapAsync_Should_Delete_Only_If_Id_Matches_Exactly()
    {
        // Arrange
        var targetId = "target-roadmap-id";

        var roadmapToDelete = new CciRoadMap
        {
            Id = targetId,
            RepositoryId = "repo-id"
        };

        _fixture.RepositoryContext.CciRoadMapRepository.FindOneAsync(
                Arg.Is<Expression<Func<CciRoadMap, bool>>>(expr =>
                    expr.Compile().Invoke(roadmapToDelete)), Arg.Any<CancellationToken>())
            .Returns(roadmapToDelete);

        _fixture.RepositoryContext.CciRoadMapRepository.DeleteAsync(roadmapToDelete, Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapAsync(targetId, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().BeTrue();
    }


    #endregion

    #region Add Cci Roadmap

    [Fact]
    public async Task AddCciRoadMapAsync_Should_Return_Created_If_CciRoadMap_Is_Created_Successfully()
    {
        // Arrange
        var request = new CreateCciRoadMapRequest()
        {
            ProductTeamId = "product-team-id",
            RepositoryName = "repository-name",
            RepositoryId = "repository-id",
            EngineerAssignedId = "engineer-assigned-id",
            EngineerName = "engineer-name",
            TargetBugs = 10,
            TargetCoverage = 30,
            TargetCodeSmells = 20,
            TargetVulnerabilities = 5,
            TargetDuplicatedLines = 10,
            TargetSecurityHotspots = 5,
            
        };
        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = request.ProductTeamId,
                    Name = "product-team-name"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Id = request.RepositoryId,
                    Name = request.RepositoryName,
                    SonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.EngineerRepository
            .GetQueryable()
            .Returns(new List<Engineer>()
            {
                new Engineer()
                {
                    Id = request.EngineerAssignedId,
                    Name = request.EngineerName
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .Returns(new List<CciRepositoryScore>()
            {
                new CciRepositoryScore()
                {
                    Bugs = 5,
                    Coverage = 20,
                    CodeSmells = 10,
                    Vulnerabilities = 2,
                    DuplicatedLinesDensity = 5,
                    SecurityHotspots = 3
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.CciRoadMapRepository.AddAsync(Arg.Any<CciRoadMap>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status201Created);
            result.Data.Should().NotBeNull();
        }
    }
    
    
       [Fact]
    public async Task AddCciRoadMapAsync_Should_Return_Bad_Request_Response_When_Product_Team_Is_Not_Found()
    {
        // Arrange
        var request = new CreateCciRoadMapRequest()
        {
            ProductTeamId = "product-team-id",
            RepositoryName = "repository-name",
            RepositoryId = "repository-id",
            EngineerAssignedId = "engineer-assigned-id",
            EngineerName = "engineer-name",
            TargetBugs = 10,
            TargetCoverage = 30,
            TargetCodeSmells = 20,
            TargetVulnerabilities = 5,
            TargetDuplicatedLines = 10,
            TargetSecurityHotspots = 5,
            
        };
        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>().AsQueryable().BuildMock());
        
        // Act
        var result = await _cciRoadMapService.AddCciRoadMapAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Data.Should().BeNull();
        }
    }
    
    
        
    [Fact]
    public async Task AddCciRoadMapAsync_Should_Return_Bad_Request_Response_When_Repository_Is_Not_Found()
    {
        // Arrange
        var request = new CreateCciRoadMapRequest()
        {
            ProductTeamId = "product-team-id",
            RepositoryName = "repository-name",
            RepositoryId = "repository-id",
            EngineerAssignedId = "engineer-assigned-id",
            EngineerName = "engineer-name",
            TargetBugs = 10,
            TargetCoverage = 30,
            TargetCodeSmells = 20,
            TargetVulnerabilities = 5,
            TargetDuplicatedLines = 10,
            TargetSecurityHotspots = 5,
            
        };
        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = request.ProductTeamId,
                    Name = "product-team-name"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>().AsQueryable().BuildMock());
        
        // Act
        var result = await _cciRoadMapService.AddCciRoadMapAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Data.Should().BeNull();
        }
    }
    
    
    
    [Fact]
    public async Task AddCciRoadMapAsync_Should_Return_Bad_Request_Response_When_Engineer_Is_Not_Found()
    {
        // Arrange
        var request = new CreateCciRoadMapRequest()
        {
            ProductTeamId = "product-team-id",
            RepositoryName = "repository-name",
            RepositoryId = "repository-id",
            EngineerAssignedId = "engineer-assigned-id",
            EngineerName = "engineer-name",
            TargetBugs = 10,
            TargetCoverage = 30,
            TargetCodeSmells = 20,
            TargetVulnerabilities = 5,
            TargetDuplicatedLines = 10,
            TargetSecurityHotspots = 5,
            
        };
        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = request.ProductTeamId,
                    Name = "product-team-name"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Id = request.RepositoryId,
                    Name = request.RepositoryName,
                    SonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.EngineerRepository
            .GetQueryable()
            .Returns(new List<Engineer>().AsQueryable().BuildMock());
        
        // Act
        var result = await _cciRoadMapService.AddCciRoadMapAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Data.Should().BeNull();
        }
    }
    
    
    [Fact]
public async Task AddCciRoadMapAsync_Should_Use_Most_Recent_CciRepositoryScore()
{
    // Arrange
    var request = new CreateCciRoadMapRequest()
    {
        ProductTeamId = "product-team-id",
        RepositoryName = "repository-name",
        RepositoryId = "repository-id",
        EngineerAssignedId = "engineer-assigned-id",
        EngineerName = "engineer-name"
    };

    var recentScore = new CciRepositoryScore
    {
        RepositorySonarQubeKey = "sonar-key",
        Bugs = 50,
        Coverage = 75,
        CodeSmells = 12,
        Vulnerabilities = 3,
        DuplicatedLinesDensity = 4,
        SecurityHotspots = 2,
        CreatedAt = new DateTime(2025, 5, 25)
    };

    var olderScore = new CciRepositoryScore
    {
        RepositorySonarQubeKey = "sonar-key",
        Bugs = 10,
        Coverage = 30,
        CodeSmells = 6,
        Vulnerabilities = 1,
        DuplicatedLinesDensity = 2,
        SecurityHotspots = 1,
        CreatedAt = new DateTime(2024, 1, 1)
    };

    _fixture.RepositoryContext.ProductTeamRepository.GetQueryable()
        .Returns(new List<ProductTeam> { new() { Id = request.ProductTeamId, Name = "PT" } }
            .AsQueryable().BuildMock());

    _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
        .Returns(new List<Repository> { new() { Id = request.RepositoryId, Name = "Repo", SonarQubeKey = "sonar-key" } }
            .AsQueryable().BuildMock());

    _fixture.RepositoryContext.EngineerRepository.GetQueryable()
        .Returns(new List<Engineer> { new() { Id = request.EngineerAssignedId, Name = "Engineer" } }
            .AsQueryable().BuildMock());

    _fixture.RepositoryContext.CciRepositoryScoreRepository.GetQueryable()
        .Returns(new List<CciRepositoryScore> { olderScore, recentScore }
            .AsQueryable().BuildMock());

    _fixture.RepositoryContext.CciRoadMapRepository.AddAsync(Arg.Any<CciRoadMap>(), Arg.Any<CancellationToken>())
        .Returns(1);

    // Act
    var result = await _cciRoadMapService.AddCciRoadMapAsync(request, CancellationToken.None);

    // Assert
    using var scope = new AssertionScope();
    result.Should().NotBeNull();
    result.Code.Should().Be(StatusCodes.Status201Created);
    result.Data.Should().NotBeNull();

    result.Data?.Bugs.Should().Be(recentScore.Bugs);
    result.Data?.Coverage.Should().Be(recentScore.Coverage);
    result.Data?.CodeSmells.Should().Be(recentScore.CodeSmells);
    result.Data?.Vulnerabilities.Should().Be(recentScore.Vulnerabilities);
    result.Data?.DuplicatedLines.Should().Be(recentScore.DuplicatedLinesDensity);
    result.Data?.SecurityHotspots.Should().Be(recentScore.SecurityHotspots);
}


    [Fact]
    public async Task AddCciRoadMapAsync_Should_Return_FailedDependency_When_SaveCount_Is_Zero()
    {
        // Arrange
        var request = new CreateCciRoadMapRequest
        {
            ProductTeamId = "product-team-id",
            RepositoryId = "repository-id",
            EngineerAssignedId = "engineer-id"
        };

        _fixture.RepositoryContext.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam> { new() { Id = request.ProductTeamId, Name = "Product Team" } }
                .AsQueryable().BuildMock());

        _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository> { new() { Id = request.RepositoryId, Name = "Repo", SonarQubeKey = "sonar-key" } }
                .AsQueryable().BuildMock());

        _fixture.RepositoryContext.EngineerRepository.GetQueryable()
            .Returns(new List<Engineer> { new() { Id = request.EngineerAssignedId, Name = "Engineer" } }
                .AsQueryable().BuildMock());

        _fixture.RepositoryContext.CciRepositoryScoreRepository.GetQueryable()
            .Returns(new List<CciRepositoryScore>
            {
                new()
                {
                    Bugs = 1,
                    Coverage = 1,
                    CodeSmells = 1,
                    Vulnerabilities = 1,
                    DuplicatedLinesDensity = 1,
                    SecurityHotspots = 1,
                    CreatedAt = DateTime.UtcNow
                }
            }.AsQueryable().BuildMock());

        // Simulate failure to save
        _fixture.RepositoryContext.CciRoadMapRepository.AddAsync(Arg.Any<CciRoadMap>(), Arg.Any<CancellationToken>())
            .Returns(0);

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapAsync(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency); // Indicates failure
        result.Data.Should().BeNull();
    }



    #endregion


    #region GetCci RoadMaps Async

    [Fact]
    public async Task GetCciRoadMapsAsync_Should_Return_Results_Sorted_By_Most_Recent_CreatedAt()
    {
        // Arrange
        var filter = new SearchCciRoadMapRequest
        {
            PageIndex = 1,
            PageSize = 10
        };

        var older = new CciRoadMap
        {
            Id = "1",
            CreatedAt = new DateTime(2024, 5, 1),
            RepositoryName = "Older Repo"
        };

        var latest = new CciRoadMap
        {
            Id = "2",
            CreatedAt = new DateTime(2025, 5, 1),
            RepositoryName = "Latest Repo"
        };

        var mockData = new List<CciRoadMap> { older, latest }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciRoadMapRepository.GetQueryable()
            .Returns(mockData);

        // Act
        var result = await _cciRoadMapService.GetCciRoadMapsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().NotBeNull();
        result.Data!.Results.Should().HaveCount(2);
        result.Data.Results.First().RepositoryName.Should().Be("Latest Repo"); // ✅ The newest should come first
    }


    #endregion

    #region Add CCI Roadmap Records V2

    [Fact]
    public async Task AddCciRoadMapV2Async_Should_Return_Created_If_CciRoadMapRecord_Is_Created_Successfully()
    {
        // Arrange
        var request = new CreateCciRoadMapRecordRequest
        {
            Bugs = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            CodeSmells = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Coverage = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Vulnerabilities = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            DuplicatedLines = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            SecurityHotspots = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            StartDate = DateTime.UtcNow,
            OverallQuality = new QualityProgress
            {
                Start = 50,
                Current = 55,
                Target = 100
            },
            Repository = new RepositoryInfo
            {
                Id = "d97f2ef6fb524005b31d3addc91df6c8",
                Name = "Hubtel.CCI",
                Type = "Backend"
            }
        };

        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "product-team-name",
                    Repositories = new Data.Entities.Repositories()
                    {
                        Items = new List<RepositoryItem>()
                        {
                            new RepositoryItem()
                            {
                                Id = request.Repository.Id,
                                Name = request.Repository.Name,
                                Type = "Backend"
                            }
                        }
                    }
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Id = request.Repository.Id,
                    Name = request.Repository.Name,
                    SonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());
        

        _fixture.RepositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .Returns(new List<CciRepositoryScore>()
            {
                new CciRepositoryScore()
                {
                    Bugs = 5,
                    Coverage = 20,
                    CodeSmells = 10,
                    Vulnerabilities = 2,
                    DuplicatedLinesDensity = 5,
                    SecurityHotspots = 3,
                    FinalAverage = 70,
                    RepositorySonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.CciRoadMapRecordRepository.AddAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapV2Async(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status201Created);
            result.Data.Should().NotBeNull();
        }
    }

    
    [Fact]
    public async Task AddCciRoadMapV2Async_Should_Return_BadRequest_When_Product_Team_Is_Not_Valid()
    {
        // Arrange
        var request = new CreateCciRoadMapRecordRequest
        {
            Bugs = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            CodeSmells = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Coverage = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Vulnerabilities = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            DuplicatedLines = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            SecurityHotspots = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            StartDate = DateTime.UtcNow,
            OverallQuality = new QualityProgress
            {
                Start = 50,
                Current = 55,
                Target = 100
            },
            Repository = new RepositoryInfo
            {
                Id = "d97f2ef6fb524005b31d3addc91df6c8",
                Name = "Hubtel.CCI",
                Type = "Backend"
            }
        };

        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "product-team-name",
                    Repositories = new Data.Entities.Repositories()
                    {
                        Items = new List<RepositoryItem>()
                        {
                            new RepositoryItem()
                            {
                                Id = "not-exist",
                                Name = request.Repository.Name,
                                Type = "Backend"
                            }
                        }
                    }
                }
            }.AsQueryable().BuildMock());
        

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapV2Async(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Product team does not exist");
            result.Data.Should().BeNull();
        }
    }

    
    [Fact]
    public async Task AddCciRoadMapV2Async_Should_Return_BadRequest_When_Repository_Is_Not_Valid()
    {
        // Arrange
        var request = new CreateCciRoadMapRecordRequest
        {
            Bugs = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            CodeSmells = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Coverage = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Vulnerabilities = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            DuplicatedLines = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            SecurityHotspots = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            StartDate = DateTime.UtcNow,
            OverallQuality = new QualityProgress
            {
                Start = 50,
                Current = 55,
                Target = 100
            },
            Repository = new RepositoryInfo
            {
                Id = "d97f2ef6fb524005b31d3addc91df6c8",
                Name = "Hubtel.CCI",
                Type = "Backend"
            }
        };

        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "product-team-name",
                    Repositories = new Data.Entities.Repositories()
                    {
                        Items = new List<RepositoryItem>()
                        {
                            new RepositoryItem()
                            {
                                Id = request.Repository.Id,
                                Name = request.Repository.Name,
                                Type = "Backend"
                            }
                        }
                    }
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Id = "not-valid",
                    Name = request.Repository.Name,
                    SonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapV2Async(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Be("Repository does not exist");
            result.Data.Should().BeNull();
        }
    }
    
    
    [Fact]
    public async Task AddCciRoadMapV2Async_Should_Return_FailedDependency_When_CciRoadMapRecord_Creation_Fails()
    {
        // Arrange
        var request = new CreateCciRoadMapRecordRequest
        {
            Bugs = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            CodeSmells = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Coverage = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            Vulnerabilities = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            DuplicatedLines = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            SecurityHotspots = new MetricData
            {
                StartWeek = 1,
                EndWeek = 4,
                StartValue = 5,
                CurrentValue = 5,
                TargetValue = 0,
                EngineerAssigned = new EngineerInfo
                {
                    Id = "22515adb4ed14892a1badb0a566b1b1c",
                    Name = "Quinton Smith-Assan"
                }
            },
            StartDate = DateTime.UtcNow,
            OverallQuality = new QualityProgress
            {
                Start = 50,
                Current = 55,
                Target = 100
            },
            Repository = new RepositoryInfo
            {
                Id = "d97f2ef6fb524005b31d3addc91df6c8",
                Name = "Hubtel.CCI",
                Type = "Backend"
            }
        };

        
        _fixture.RepositoryContext.ProductTeamRepository
            .GetQueryable()
            .Returns(new List<ProductTeam>()
            {
                new ProductTeam()
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "product-team-name",
                    Repositories = new Data.Entities.Repositories()
                    {
                        Items = new List<RepositoryItem>()
                        {
                            new RepositoryItem()
                            {
                                Id = request.Repository.Id,
                                Name = request.Repository.Name,
                                Type = "Backend"
                            }
                        }
                    }
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>()
            {
                new Repository()
                {
                    Id = request.Repository.Id,
                    Name = request.Repository.Name,
                    SonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());
        

        _fixture.RepositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .Returns(new List<CciRepositoryScore>()
            {
                new CciRepositoryScore()
                {
                    Bugs = 5,
                    Coverage = 20,
                    CodeSmells = 10,
                    Vulnerabilities = 2,
                    DuplicatedLinesDensity = 5,
                    SecurityHotspots = 3,
                    FinalAverage = 70,
                    RepositorySonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.CciRoadMapRecordRepository.AddAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>())
            .Returns(0);

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapV2Async(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status424FailedDependency);
            result.Message.Should().Be("Could not create CciRoadMap! Please try again");
            result.Data.Should().BeNull();
        }
    }
    
    
    [Fact]
    public async Task AddCciRoadMapV2Async_Should_Not_Return_ProductTeam_When_Repository_Match_Is_Not_All()
    {
        // Arrange
        var repositoryId = "repo-match-id";
        var request = new CreateCciRoadMapRecordRequest
        {
            Repository = new RepositoryInfo
            {
                Id = repositoryId,
                Name = "Repo A",
                Type = "Backend"
            },
            StartDate = DateTime.UtcNow,
            OverallQuality = new QualityProgress()
        };

        var productTeam = new ProductTeam
        {
            Id = "pt-id",
            Name = "Team Alpha",
            Repositories = new Data.Entities.Repositories
            {
                Items = new List<RepositoryItem>
                {
                    new RepositoryItem { Id = repositoryId },
                    new RepositoryItem { Id = "other-id" } // makes All() fail
                }
            }
        };

        _fixture.RepositoryContext.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam> { productTeam }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository>
            {
                new Repository
                {
                    Id = repositoryId,
                    Name = "Repo A",
                    SonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.CciRepositoryScoreRepository.GetQueryable()
            .Returns(new List<CciRepositoryScore>
            {
                new CciRepositoryScore
                {
                    FinalAverage = 85,
                    RepositorySonarQubeKey = "sonar-key"
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.CciRoadMapRecordRepository.AddAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapV2Async(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status201Created);
        result.Data.Should().NotBeNull();
    }


    [Fact]
    public async Task AddCciRoadMapV2Async_Should_Use_Latest_CciScore_Based_On_CreatedAt()
    {
        // Arrange
        var repoId = "repo-1";
        var sonarKey = "sonar-key";

        var request = new CreateCciRoadMapRecordRequest
        {
            Repository = new RepositoryInfo
            {
                Id = repoId,
                Name = "Hubtel.CCI",
                Type = "Backend"
            },
            StartDate = DateTime.UtcNow,
            OverallQuality = new QualityProgress()
        };

        _fixture.RepositoryContext.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>
            {
                new ProductTeam
                {
                    Id = "pt-id",
                    Name = "Team",
                    Repositories = new Data.Entities.Repositories
                    {
                        Items = new List<RepositoryItem>
                        {
                            new RepositoryItem { Id = repoId }
                        }
                    }
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository>
            {
                new Repository
                {
                    Id = repoId,
                    Name = "Hubtel.CCI",
                    SonarQubeKey = sonarKey
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.CciRepositoryScoreRepository.GetQueryable()
            .Returns(new List<CciRepositoryScore>
            {
                new CciRepositoryScore
                {
                    FinalAverage = 40,
                    CreatedAt = DateTime.UtcNow.AddDays(-10),
                    RepositorySonarQubeKey = sonarKey
                },
                new CciRepositoryScore
                {
                    FinalAverage = 90, // the latest
                    CreatedAt = DateTime.UtcNow,
                    RepositorySonarQubeKey = sonarKey
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.CciRoadMapRecordRepository.AddAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _cciRoadMapService.AddCciRoadMapV2Async(request, CancellationToken.None);

        // Assert
        result.Data?.OverallQuality.Start.Should().Be(90);
    }

    
    #endregion


    #region Get Cci RoadMapRecord By Id Async

    [Fact]
    public async Task GetCciRoadMapRecordByIdAsync_Should_Return_Not_Found_Response_When__CciRoadMapRecord_Is_Not_Found()
    {
        // Arrange
        const string id = "not-found";
        
        var data = new[]
        {
            new CciRoadMapRecord
            {
                Bugs = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                CodeSmells = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                Coverage = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                Vulnerabilities = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                DuplicatedLines = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                SecurityHotspots = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                StartDate = DateTime.UtcNow,
                OverallQuality = new QualityProgress
                {
                    Start = 50,
                    Current = 55,
                    Target = 100
                },
                Product = new ProductInfo
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "Code Quality Team"
                },
                Repository = new RepositoryInfo
                {
                    Id = "d97f2ef6fb524005b31d3addc91df6c8",
                    Name = "Hubtel.CCI",
                    Type = "Backend"
                }
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciRoadMapRecordRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _cciRoadMapService.GetCciRoadMapRecordByIdAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Data.Should().BeNull();
        }
    }
    
    
    
        [Fact]
    public async Task GetCciRoadMapRecordByIdAsync_Should_Return_Success_Response_When__CciRoadMapRecord_Is_Found()
    {
        // Arrange
        const string id = "exists";
        
        var data = new[]
        {
            new CciRoadMapRecord
            {
                Id = "exists",
                Bugs = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                CodeSmells = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                Coverage = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                Vulnerabilities = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                DuplicatedLines = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                SecurityHotspots = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                StartDate = DateTime.UtcNow,
                OverallQuality = new QualityProgress
                {
                    Start = 50,
                    Current = 55,
                    Target = 100
                },
                Product = new ProductInfo
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "Code Quality Team"
                },
                Repository = new RepositoryInfo
                {
                    Id = "d97f2ef6fb524005b31d3addc91df6c8",
                    Name = "Hubtel.CCI",
                    Type = "Backend"
                }
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryContext.CciRoadMapRecordRepository
            .GetQueryable()
            .Returns(data);

        // Act
        var result = await _cciRoadMapService.GetCciRoadMapRecordByIdAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
        }
    }

    #endregion


    #region Get Cci RoadMap Records V2 Async

    [Fact]
    public async Task GetCciRoadMapRecordsV2Async_Should_Return_Success_Response()
    {
        // Arrange
        var data = new[]
        {
            new CciRoadMapRecord
            {
                Id = "exists",
                Bugs = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                CodeSmells = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                Coverage = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                Vulnerabilities = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                DuplicatedLines = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                SecurityHotspots = new MetricData
                {
                    StartWeek = 1,
                    EndWeek = 4,
                    StartValue = 5,
                    CurrentValue = 5,
                    TargetValue = 0,
                    EngineerAssigned = new EngineerInfo
                    {
                        Id = "22515adb4ed14892a1badb0a566b1b1c",
                        Name = "Quinton Smith-Assan"
                    }
                },
                StartDate = DateTime.UtcNow,
                OverallQuality = new QualityProgress
                {
                    Start = 50,
                    Current = 55,
                    Target = 100
                },
                Product = new ProductInfo
                {
                    Id = "a21131d632714ea8a6a9172c5e8d797a",
                    Name = "Code Quality Team"
                },
                Repository = new RepositoryInfo
                {
                    Id = "d97f2ef6fb524005b31d3addc91df6c8",
                    Name = "Hubtel.CCI",
                    Type = "Backend"
                }
            }
        }.AsQueryable().BuildMock();
                
        
        _fixture.RepositoryContext.CciRoadMapRecordRepository
            .GetQueryable()
            .Returns(data);
        
        var request = new SearchCciRoadMapRequest()
        {
            PageIndex = 1,
            PageSize = 10,
        };
        
        // Act
        var result = await _cciRoadMapService.GetCciRoadMapRecordsV2Async(request, CancellationToken.None);
        
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().NotBeEmpty();
        }
                
    }
    
    
    [Fact]
    public async Task GetCciRoadMapRecordsV2Async_Should_Return_Records_Sorted_By_CreatedAt_Descending()
    {
        // Arrange
        var recent = new CciRoadMapRecord
        {
            Id = "recent",
            CreatedAt = DateTime.UtcNow,
            Repository = new RepositoryInfo { Name = "Repo Recent" },
            Product = new ProductInfo(),
            Bugs = new MetricData(), CodeSmells = new MetricData(),
            Coverage = new MetricData(), Vulnerabilities = new MetricData(),
            DuplicatedLines = new MetricData(), SecurityHotspots = new MetricData(),
            OverallQuality = new QualityProgress(), StartDate = DateTime.UtcNow
        };

        var older = new CciRoadMapRecord
        {
            Id = "older",
            CreatedAt = DateTime.UtcNow.AddDays(-10),
            Repository = new RepositoryInfo { Name = "Repo Older" },
            Product = new ProductInfo(),
            Bugs = new MetricData(), CodeSmells = new MetricData(),
            Coverage = new MetricData(), Vulnerabilities = new MetricData(),
            DuplicatedLines = new MetricData(), SecurityHotspots = new MetricData(),
            OverallQuality = new QualityProgress(), StartDate = DateTime.UtcNow
        };

        var data = new[] { older, recent }
            .AsQueryable()
            .BuildMock();

        _fixture.RepositoryContext.CciRoadMapRecordRepository
            .GetQueryable()
            .Returns(data);

        var request = new SearchCciRoadMapRequest
        {
            PageIndex = 1,
            PageSize = 10
        };

        // Act
        var result = await _cciRoadMapService.GetCciRoadMapRecordsV2Async(request, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().HaveCount(2);
        result.Data?.Results[0].Id.Should().Be("recent"); // most recent first
    }


    #endregion
    
    
      #region Delete Cci RoadMap Record

        [Fact]
    public async Task DeleteCciRoadMapRecordAsync_Should_Return_NotFound_When_Road_Map_Is_Not_Found()
    {
        // Arrange
        const string id = "not-found";


        _fixture.CciRoadMapRecordRepository.FindOneAsync(Arg.Any<Expression<Func<CciRoadMapRecord, bool>>>()).ReturnsNull();

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapRecordAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }
    
    
    [Fact]
    public async Task DeleteCciRoadMapRecordAsync_Should_Return_True_When_Road_Map_Is_Deleted_Successfully()
    {
        // Arrange
        const string id = "exists";


        _fixture.CciRoadMapRecordRepository.FindOneAsync(Arg.Any<Expression<Func<CciRoadMapRecord, bool>>>()).Returns(new CciRoadMapRecord());

        _fixture.CciRoadMapRecordRepository.DeleteAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapRecordAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task DeleteCciRoadMapRecordAsync_Should_Return_False_When_Road_Map_Is_Not_Deleted_Successfully()
    {
        // Arrange
        const string id = "exists";


        _fixture.CciRoadMapRecordRepository.FindOneAsync(Arg.Any<Expression<Func<CciRoadMapRecord, bool>>>()).Returns(new CciRoadMapRecord());

        _fixture.CciRoadMapRecordRepository.DeleteAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>()).Returns(0);

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapRecordAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status424FailedDependency);
            result.Data.Should().BeFalse();
        }
    }
    
    
    
    [Fact]
    public async Task DeleteCciRoadMapRecordAsync_Should_Return_True_When_Id_Matches_Exactly()
    {
        // Arrange
        const string id = "exists";

        _fixture.CciRoadMapRecordRepository
            .FindOneAsync(Arg.Is<Expression<Func<CciRoadMapRecord, bool>>>(expr =>
                expr.Compile().Invoke(new CciRoadMapRecord { Id = id })
            ), Arg.Any<CancellationToken>())
            .Returns(new CciRoadMapRecord { Id = id });

        _fixture.CciRoadMapRecordRepository
            .DeleteAsync(Arg.Any<CciRoadMapRecord>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _cciRoadMapService.DeleteCciRoadMapRecordAsync(id, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status200OK);
        result.Data.Should().BeTrue();
    }


    #endregion
    
}