using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests
{
    public class ToolingToolServiceTests
    {
        private readonly DiFixture _fixture;
        private readonly ToolingToolService _service;

        public ToolingToolServiceTests()
        {

            _fixture = new DiFixture();
            var hostEnv = Substitute.For<IHostEnvironment>();
            _service = new ToolingToolService(_fixture.RepositoryContext, Substitute.For<ILogger<ToolingToolService>>(),hostEnv);
        }

        [Fact]
        public async Task CreateToolAsync_Should_ReturnCreated_When_ToolIsCreated()
        {
            var request = new CreateToolRequest { Name = "ToolA", Domain = Domain.Backend };
            _fixture.ToolingToolsRepository.AddAsync(Arg.Any<ToolingTool>(), Arg.Any<CancellationToken>()).Returns(1);

            var result = await _service.CreateToolAsync(request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(201);
                result.Data.Should().NotBeNull();
                result.Data!.Name.Should().Be("ToolA");
            }
        }

        [Fact]
        public async Task CreateToolAsync_Should_ReturnFailedDependency_When_AddFails()
        {
            var request = new CreateToolRequest { Name = "ToolA", Domain = Domain.Backend };
            _fixture.ToolingToolsRepository.AddAsync(Arg.Any<ToolingTool>(), Arg.Any<CancellationToken>()).Returns(0);

            var result = await _service.CreateToolAsync(request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(424);
                result.Data.Should().BeNull();
            }
        }

       

        [Fact]
        public async Task GetToolAsync_Should_ReturnOk_When_ToolIsFound()
        {
            var id = Guid.NewGuid().ToString("N");
            var tool = new ToolingTool { Id = id, Name = "ToolA", Domain = Domain.Backend };
            _fixture.ToolingToolsRepository.FindOneAsync(Arg.Any<System.Linq.Expressions.Expression<Func<ToolingTool, bool>>>(), Arg.Any<CancellationToken>())
                .Returns(tool);

            var result = await _service.GetToolAsync(id, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
                result.Data!.Name.Should().Be("ToolA");
            }
        }

        [Fact]
        public async Task GetToolAsync_Should_ReturnNotFound_When_ToolIsMissing()
        {
            var id = Guid.NewGuid().ToString("N");
            _fixture.ToolingToolsRepository.FindOneAsync(Arg.Any<System.Linq.Expressions.Expression<Func<ToolingTool, bool>>>(), default)
                .Returns((ToolingTool?)null);

            var result = await _service.GetToolAsync(id, default);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Data.Should().BeNull();
            }
        }

        [Fact]
        public async Task GetToolsAsync_Should_ReturnPagedResult_When_AllToolsAreFound()
        {
            var filter = new SearchToolFilter { PageIndex = 1, PageSize = 10 };
            var data = new List<ToolingTool> { new ToolingTool { Name = "ToolA", Domain = Domain.Backend }, new ToolingTool { Name = "ToolB", Domain = Domain.Backend } };
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingToolsRepository.GetQueryable().Returns(mock);
            var result = await _service.GetToolsAsync(filter, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().HaveCount(2);
            }
        }

        [Fact]
        public async Task GetToolsAsync_Should_ReturnFilteredResult_When_FilteredBySearchTerm()
        {
            var filter = new SearchToolFilter { PageIndex = 1, PageSize = 10, SearchTerm = "ToolA" };
            var data = new List<ToolingTool> { new ToolingTool { Name = "ToolA", Domain = Domain.Backend }, new ToolingTool { Name = "ToolB", Domain = Domain.Backend } };
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingToolsRepository.GetQueryable().Returns(mock);

            var result = await _service.GetToolsAsync(filter, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().ContainSingle(x => x.Name == "ToolA");
            }
        }

        [Fact]
        public async Task GetToolsAsync_Should_ReturnEmptyResult_When_NoToolsAreFound()
        {
            var filter = new SearchToolFilter { PageIndex = 1, PageSize = 10 };
            var data = new List<ToolingTool>();
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingToolsRepository.GetQueryable().Returns(mock);

            var result = await _service.GetToolsAsync(filter, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().BeEmpty();
            }
        }

        [Fact]
        public async Task UpdateToolAsync_Should_ReturnOk_When_UpdateIsSuccessful()
        {
            var id = Guid.NewGuid().ToString("N");
            var tool = new ToolingTool { Id = id, Name = "ToolA", Domain = Domain.Backend };
            var request = new UpdateToolRequest { Name = "ToolA", Domain = Domain.Backend };
            _fixture.ToolingToolsRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(tool);
            _fixture.ToolingToolsRepository.UpdateAsync(Arg.Any<ToolingTool>(), Arg.Any<CancellationToken>()).Returns(1);

            var result = await _service.UpdateToolAsync(id, request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
                result.Data!.Name.Should().Be("ToolA");
            }
        }

        [Fact]
        public async Task UpdateToolAsync_Should_ReturnNotFound_When_ToolIsMissing()
        {
            var id = Guid.NewGuid().ToString("N");
            var request = new UpdateToolRequest { Name = "ToolA", Domain = Domain.Backend };
            _fixture.ToolingToolsRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns((ToolingTool?)null);

            var result = await _service.UpdateToolAsync(id, request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Data.Should().BeNull();
            }
        }

        [Fact]
        public async Task UpdateToolAsync_Should_ReturnFailedDependency_When_UpdateFails()
        {
            var id = Guid.NewGuid().ToString("N");
            var tool = new ToolingTool { Id = id, Name = "ToolA", Domain = Domain.Backend };
            var request = new UpdateToolRequest { Name = "ToolA", Domain = Domain.Backend };
            _fixture.ToolingToolsRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(tool);
            _fixture.ToolingToolsRepository.UpdateAsync(Arg.Any<ToolingTool>(), Arg.Any<CancellationToken>()).Returns(0);

            var result = await _service.UpdateToolAsync(id, request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(424);
                result.Data.Should().BeNull();
            }
        }

        [Fact]
        public async Task GetToolingReportsAsync_Should_ReturnPagedResult_When_AllReportsAreFound()
        {
            var request = new GetToolingReportsRequest { PageIndex = 1, PageSize = 10 };
            var data = new List<ToolingReport> { new ToolingReport { ProductGroupName = "Group1", Domain = Domain.Backend }, new ToolingReport { ProductGroupName = "Group2", Domain = Domain.Backend } };
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingReportRepository.GetQueryable().Returns(mock);
          

            var result = await _service.GetToolingReportsAsync(request, default);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().HaveCount(2);
            }
        }

        [Fact]
        public async Task GetToolingReportsAsync_Should_ReturnFilteredResult_When_FilteredByProductGroup()
        {
            var request = new GetToolingReportsRequest { PageIndex = 1, PageSize = 10, SearchTerm = "Group1" };
            var data = new List<ToolingReport> { new ToolingReport { ProductGroupName = "Group1", Domain = Domain.Backend }, new ToolingReport { ProductGroupName = "Group2", Domain = Domain.Backend } };
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingReportRepository.GetQueryable().Returns(mock);
          

            var result = await _service.GetToolingReportsAsync(request, default);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().ContainSingle(x => x.ProductGroupName == "Group1");
            }
        }

        [Fact]
        public async Task GetToolingReportsAsync_Should_ReturnFilteredResult_When_FilteredByDomain()
        {
            var request = new GetToolingReportsRequest { PageIndex = 1, PageSize = 10, Domain = Domain.Backend.ToString() };
            var data = new List<ToolingReport> { new ToolingReport { ProductGroupName = "Group1", Domain = Domain.Backend }, new ToolingReport { ProductGroupName = "Group2", Domain = Domain.Frontend } };
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingReportRepository.GetQueryable().Returns(mock);


            var result = await _service.GetToolingReportsAsync(request, default);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().ContainSingle(x => x.Domain == Domain.Backend);
            }
        }

        [Fact]
        public async Task GetToolingReportsAsync_Should_ReturnFilteredResult_When_FilteredByDate()
        {
            var date = DateTime.UtcNow.Date;
            var request = new GetToolingReportsRequest { PageIndex = 1, PageSize = 10, Date = date };
            var data = new List<ToolingReport> { new ToolingReport { ProductGroupName = "Group1", Domain = Domain.Backend, CreatedAt = date }, new ToolingReport { ProductGroupName = "Group2", Domain = Domain.Backend, CreatedAt = date.AddDays(-1) } };
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingReportRepository.GetQueryable().Returns(mock);

            var result = await _service.GetToolingReportsAsync(request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().ContainSingle(x => x.CreatedAt.Date == date);
            }
        }

        [Fact]
        public async Task GetToolingReportsAsync_Should_ReturnEmptyResult_When_NoReportsAreFound()
        {
            var request = new GetToolingReportsRequest { PageIndex = 1, PageSize = 10 };
            var data = new List<ToolingReport>();
            var mock = data.AsQueryable().BuildMock();
            _fixture.ToolingReportRepository.GetQueryable().Returns(mock);

            var result = await _service.GetToolingReportsAsync(request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Data.Should().NotBeNull();
                result.Data!.Results.Should().BeEmpty();
            }
        }

        [Fact]
        public async Task UpdateToolingReportAsync_Should_ReturnOk_When_UpdateIsSuccessful()
        {
            var id = Guid.NewGuid().ToString("N");
            var report = new ToolingReport { Id = id, ProductGroupName = "Group1", Domain = Domain.Backend };
            var request = new UpdateToolingReportRequest { PlugAndPlaySolutionsInUse = new List<string> { "A" } };
            _fixture.ToolingReportRepository.GetByIdAsync(id,default).Returns(report);
            _fixture.ToolingReportRepository.UpdateAsync(Arg.Any<ToolingReport>(), default).Returns(1);

            var result = await _service.UpdateToolingReportAsync(id, request, default);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(200);
                result.Data.Should().NotBeNull();
                result.Data!.ProductGroupName.Should().Be("Group1");
            }
        }

        [Fact]
        public async Task UpdateToolingReportAsync_Should_ReturnNotFound_When_ReportIsMissing()
        {
            var id = Guid.NewGuid().ToString("N");
            var request = new UpdateToolingReportRequest { PlugAndPlaySolutionsInUse = new List<string> { "A" } };
            _fixture.ToolingReportRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns((ToolingReport?)null);

            var result = await _service.UpdateToolingReportAsync(id, request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(404);
                result.Data.Should().BeNull();
            }
        }

        [Fact]
        public async Task UpdateToolingReportAsync_Should_ReturnFailedDependency_When_UpdateFails()
        {
            var id = Guid.NewGuid().ToString("N");
            var report = new ToolingReport { Id = id, ProductGroupName = "Group1", Domain = Domain.Backend };
            var request = new UpdateToolingReportRequest { PlugAndPlaySolutionsInUse = new List<string> { "A" } };
            _fixture.ToolingReportRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(report);
            _fixture.ToolingReportRepository.UpdateAsync(Arg.Any<ToolingReport>(), Arg.Any<CancellationToken>()).Returns(0);

            var result = await _service.UpdateToolingReportAsync(id, request, CancellationToken.None);

            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Code.Should().Be(424);
                result.Data.Should().BeNull();
            }
        }

    }
}