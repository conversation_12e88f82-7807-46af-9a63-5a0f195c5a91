using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.RepositoryService;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Hubtel.CCI.Api.Dtos.Common;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ArsenalServiceServiceTests
{
    private readonly ArsenalServiceService _arsenalServiceService;
    private readonly DiFixture _fixture;

    public ArsenalServiceServiceTests()
    {
        _fixture = new DiFixture();
        _arsenalServiceService = new ArsenalServiceService(_fixture.RepositoryContext,
            Substitute.For<ILogger<ArsenalServiceService>>());
    }

    #region GetRepositoryServicesAsync

    [Fact]
    public async Task GetRepositoryServicesAsync_Should_Return_PagedResult_When_Filter_Is_Valid()
    {
        // Arrange
        var filter = new RepositoryServiceSearchFilter { PageIndex = 1, PageSize = 10 };

        var data = new List<Service>
        {
            new Service { Name = "Service1", CreatedAt = DateTime.UtcNow },
            new Service { Name = "Service2", CreatedAt = DateTime.UtcNow.AddDays(-1) }
        }.AsQueryable().BuildMock();

        _fixture.ServiceRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalServiceService.GetRepositoryServicesAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().HaveCount(2);
        }
    }

    [Fact]
    public async Task GetRepositoryServicesAsync_Should_Filter_By_SearchTerm_When_Provided()
    {
        // Arrange
        var filter = new RepositoryServiceSearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "Service1" };

        var data = new List<Service>
        {
            new Service { Name = "Service1", CreatedAt = DateTime.UtcNow },
            new Service { Name = "Service2", CreatedAt = DateTime.UtcNow.AddDays(-1) }
        }.AsQueryable().BuildMock();

        _fixture.ServiceRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalServiceService.GetRepositoryServicesAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().HaveCount(1);
            result.Data?.Results[0].Name.Should().Be("Service1");
        }
    }

    [Fact]
    public async Task GetRepositoryServicesAsync_Should_Return_Empty_When_No_Services_Exist()
    {
        // Arrange
        var filter = new RepositoryServiceSearchFilter { PageIndex = 1, PageSize = 10 };

        var data = new List<Service>().AsQueryable().BuildMock();

        _fixture.ServiceRepository.GetQueryable().Returns(data);

        // Act
        var result = await _arsenalServiceService.GetRepositoryServicesAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().BeEmpty();
        }
    }

    #endregion

    #region AddRepositoryServiceAsync

    [Fact]
    public async Task AddRepositoryServiceAsync_Should_Add_Service_When_Request_Is_Valid()
    {
        // Arrange
        var request = new CreateRepositoryServiceRequest
        {
            Name = "New Service",
            RepositoryId = "repo-id",
            Description = "Service description"
        };

        var repository = new Repository
        {
            Id = "repo-id",
            Name = "Repository Name",
            Type = "Git"
        };

        _fixture.RepositoryRepository.GetByIdAsync(request.RepositoryId, Arg.Any<CancellationToken>())
            .Returns(repository);

        _fixture.ServiceRepository.FindOneAsync(Arg.Any<System.Linq.Expressions.Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
            .Returns((Service?)null);

        _fixture.ServiceRepository.AddAsync(Arg.Any<Service>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _arsenalServiceService.AddRepositoryServiceAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status201Created);
            result.Data.Should().NotBeNull();
            result.Data?.Name.Should().Be(request.Name);
            result.Data?.RepositoryId.Should().Be(request.RepositoryId);
            result.Data?.RepositoryName.Should().Be(repository.Name);
            result.Data?.RepositoryType.Should().Be(repository.Type);
        }
    }

    [Fact]
    public async Task AddRepositoryServiceAsync_Should_Return_BadRequest_When_Repository_Does_Not_Exist()
    {
        // Arrange
        var request = new CreateRepositoryServiceRequest
        {
            Name = "New Service",
            RepositoryId = "non-existent-repo-id"
        };

        _fixture.RepositoryRepository.GetByIdAsync(request.RepositoryId, Arg.Any<CancellationToken>())
            .Returns((Repository?)null);

        // Act
        var result = await _arsenalServiceService.AddRepositoryServiceAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Contain("Repository does not exist");
        }
    }

    [Fact]
    public async Task AddRepositoryServiceAsync_Should_Return_BadRequest_When_Service_With_Same_Name_Exists()
    {
        // Arrange
        var request = new CreateRepositoryServiceRequest
        {
            Name = "Existing Service",
            RepositoryId = "repo-id"
        };

        var repository = new Repository
        {
            Id = "repo-id",
            Name = "Repository Name"
        };

        var existingService = new Service
        {
            Name = "Existing Service"
        };

        _fixture.RepositoryRepository.GetByIdAsync(request.RepositoryId, Arg.Any<CancellationToken>())
            .Returns(repository);

        _fixture.ServiceRepository.FindOneAsync(Arg.Any<System.Linq.Expressions.Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
            .Returns(existingService);

        // Act
        var result = await _arsenalServiceService.AddRepositoryServiceAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Contain("Service with the same name already exists");
        }
    }

    [Fact]
    public async Task AddRepositoryServiceAsync_Should_Return_FailedDependency_When_Save_Fails()
    {
        // Arrange
        var request = new CreateRepositoryServiceRequest
        {
            Name = "New Service",
            RepositoryId = "repo-id"
        };

        var repository = new Repository
        {
            Id = "repo-id",
            Name = "Repository Name",
            Type = "Git"
        };

        _fixture.RepositoryRepository.GetByIdAsync(request.RepositoryId, Arg.Any<CancellationToken>())
            .Returns(repository);

        _fixture.ServiceRepository.FindOneAsync(Arg.Any<System.Linq.Expressions.Expression<Func<Service, bool>>>(), Arg.Any<CancellationToken>())
            .Returns((Service?)null);

        _fixture.ServiceRepository.AddAsync(Arg.Any<Service>(), Arg.Any<CancellationToken>())
            .Returns(0); // Simulate save failure

        // Act
        var result = await _arsenalServiceService.AddRepositoryServiceAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status424FailedDependency);
            result.Message.Should().Contain("Could not create Service");
        }
    }

    #endregion
}