using System.Net;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.ProductGroup;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ProductGroupServiceTests
{
    private readonly ProductGroupService _engineerService;
    private readonly DiFixture _fixture;

    public ProductGroupServiceTests()
    {
        _fixture = new DiFixture();
        _engineerService =
            new ProductGroupService(Substitute.For<ILogger<ProductGroupService>>(), _fixture.RepositoryContext);
    }

    [Fact]
    public async Task GetProductGroupsAsync_Should_Return_PagedResult_When_Filter_Is_Valid()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "test" };

        var data = new[] { new ProductGroup() { GroupName = "test" } }.AsQueryable().BuildMock();

        _fixture.ProductGroupRepository.GetQueryable().Returns(data);
        // Act
        var result = await _engineerService.GetProductGroupsAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().NotBeEmpty();
            result.Data?.Results.Count.Should().Be(1);
        }
    }

    [Fact]
    public async Task GetProductGroupAsync_Should_Return_ProductGroup_When_Id_Is_Valid()
    {
        // Arrange
        var id = "validId";


        var data = new[]
                { new ProductGroup() { GroupName = "Baba", Id = id, ProductTeams = new(), Supervisors = new() } }
            .AsQueryable().BuildMock();

        _fixture.ProductGroupRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductGroupAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
        }
    }
    
    
    [Fact]
    public async Task GetProductGroupsAsync_Should_Return_PagedResult_In_Descending_Order_By_CreatedAt()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "" };

        var now = DateTime.UtcNow;
        var data = new[]
        {
            new ProductGroup { GroupName = "Older", CreatedAt = now.AddDays(-2) },
            new ProductGroup { GroupName = "Newest", CreatedAt = now },
            new ProductGroup { GroupName = "Middle", CreatedAt = now.AddDays(-1) },
        }.AsQueryable().BuildMock();

        _fixture.ProductGroupRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductGroupsAsync(filter, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().HaveCount(3);
        result.Data?.Results[0].GroupName.Should().Be("Newest");
    }


    [Fact]
    public async Task AddProductGroupAsync_Should_Add_ProductGroup_When_Request_Is_Valid()
    {
        // Arrange
        var request = new CreateProductGroupRequest
        {
            /* Set properties here */
            ProductTeams = new(),
            Supervisors = new()
        };

        _fixture.ProductGroupRepository.GetQueryable().Returns(new List<ProductGroup>().AsQueryable().BuildMock());

        _fixture.ProductGroupRepository.AddAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>()).Returns(1);

        _fixture.ProductTeamRepository.GetQueryable() .Returns(new List<ProductTeam>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());
        
        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.AddProductGroupAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
        }
    }


    [Fact]
    public async Task AddProductGroupAsync_Should_Fail_To_Add_ProductGroup_When_Product_GroupName_Already_Exists()
    {
        // Arrange
        var request = new CreateProductGroupRequest
        {
            GroupName = "Banana",
            ProductTeams = new(),
            Supervisors = new()
        };

        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>() { new() { GroupName = "Banana" } }.AsQueryable().BuildMock());

        _fixture.ProductGroupRepository.AddAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _engineerService.AddProductGroupAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
            result.Code.Should().Be((int)HttpStatusCode.BadRequest);
        }
    }

    [Fact]
    public async Task UpdateProductGroupAsync_Should_Update_ProductGroup_When_Id_And_Request_Are_Valid()
    {
        // Arrange
        var id = "validId";
        var request = new UpdateProductGroupRequest
        {
            /* Set properties here */
            ProductTeams = new(),
            Supervisors = new()
        };

        _fixture.ProductGroupRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((ProductGroup?)new ProductGroup());

        _fixture.ProductGroupRepository.UpdateAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>()).Returns(1);

        _fixture.ProductTeamRepository.GetQueryable() .Returns(new List<ProductTeam>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());
        
        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task UpdateProductGroupAsync_Should_Return_NotFound_When_ProductGroup_Does_Not_Exist()
    {
        var id = "not-found";
        var request = new UpdateProductGroupRequest { };

        _fixture.ProductGroupRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((ProductGroup?)null);

        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }
    
    
    [Fact]
    public async Task UpdateProductGroupAsync_Should_Not_Trigger_Duplicate_Check_When_GroupName_Is_Null_Or_Same()
    {
        var id = "validId";
        var groupName = "Banana";
        var request = new UpdateProductGroupRequest
        {
            GroupName = groupName
        };

        var existing = new ProductGroup { GroupName = groupName };

        _fixture.ProductGroupRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(existing);

        _fixture.ProductGroupRepository.UpdateAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>())
            .Returns(1);

        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task UpdateProductGroupAsync_Should_Return_BadRequest_When_GroupName_Already_Exists()
    {
        var id = "validId";
        var request = new UpdateProductGroupRequest
        {
            GroupName = "ExistingName"
        };

        var existing = new ProductGroup { GroupName = "OldName" };

        _fixture.ProductGroupRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(existing);

        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>
            {
                new ProductGroup { GroupName = "ExistingName" }
            }.AsQueryable().BuildMock());

        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Data.Should().BeFalse();
        }
    }
    
    
    [Fact]
    public async Task UpdateProductGroupAsync_Should_Return_Failure_When_Update_Count_Is_Zero_()
    {
        // Arrange
        var id = "validId";
        var request = new UpdateProductGroupRequest
        {
            GroupName = "UpdatedGroup",
            ProductTeams = new List<ProductTeamItemRequest> { new() { Id = "team1" } },
            Supervisors = new List<Supervisor> { new() { Id = "sup1" } }
        };

        var productGroup = new ProductGroup
        {
            Id = id,
            GroupName = "OldGroup",
            ProductTeams = new(),
            Supervisors = new()
        };

        _fixture.RepositoryContext.ProductGroupRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(productGroup);

        _fixture.RepositoryContext.ProductGroupRepository.UpdateAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>())
            .Returns(0); // Simulate failed update

        _fixture.RepositoryContext.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>
            {
                new ProductTeam
                {
                    Id = "team1",
                    Name = "Team 1",
                    Members = new(),
                    Repositories = new()
                }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>
            {
                new ProductGroup()
                {
                    Id = "team1",
                    GroupName = "UpdatedGroup",
                }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.EngineerRepository.GetQueryable()
            .Returns(new List<Engineer>
            {
                new Engineer { Id = "sup1", Name = "Supervisor One" }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
        }
    }
    
    
    [Fact]
    public async Task UpdateProductGroupAsync_Should_Return_Failure_When_Update_Count_Is_Zero()
    {
        // Arrange
        const string id = "validId";
        const string groupName = "SameGroupName";
        var request = new UpdateProductGroupRequest
        {
            GroupName = groupName,
            ProductTeams = new List<ProductTeamItemRequest> { new() { Id = "team1" } },
            Supervisors = new List<Supervisor> { new() { Id = "sup1" } }
        };

        var productGroup = new ProductGroup
        {
            Id = id,
            GroupName = groupName,
            ProductTeams = new(),
            Supervisors = new()
        };

        _fixture.ProductGroupRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(productGroup);

        _fixture.ProductGroupRepository.UpdateAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>())
            .Returns(0); // Simulate failure

        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>
            {
                new ProductTeam
                {
                    Id = "team1",
                    Name = "Team 1",
                    Members = new(),
                    Repositories = new()
                }
            }.AsQueryable().BuildMock());

        _fixture.EngineerRepository.GetQueryable()
            .Returns(new List<Engineer>
            {
                new Engineer { Id = "sup1", Name = "Supervisor One" }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because update count was 0, indicating a failed update");
            result.Code.Should().Be(StatusCodes.Status404NotFound, "because update failure should return NotFound");
            result.Message.Should().Contain("not found", "to validate conditional path for mutation detection");
        }
    }


    [Fact]
    public async Task DeleteProductGroupAsync_Should_Delete_ProductGroup_When_Id_Is_Valid()
    {
        // Arrange
        var id = "validId";
        var data = new ProductGroup()
        {
            ProductTeams = new(),
            Supervisors = new()
        };
        _fixture.ProductGroupRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(data);


        _fixture.ProductGroupRepository.DeleteAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>()).Returns(1);


        // Act
        var result = await _engineerService.DeleteProductGroupAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }

    [Fact]
    public async Task GetProductGroupsAsync_Should_Return_Empty_When_No_ProductGroups_Exist()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "test" };

        var data = new[] { new ProductGroup() { GroupName = "Baba" } }.AsQueryable().BuildMock();

        _fixture.ProductGroupRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductGroupsAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task GetProductGroupAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";

        var data = new[] { new ProductGroup() { GroupName = "Baba", Id = "valid" } }.AsQueryable().BuildMock();

        _fixture.ProductGroupRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductGroupAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task AddProductGroupAsync_Should_Return_Error_When_Request_Is_Invalid()
    {
        // Arrange
        var request = new CreateProductGroupRequest
        {
            /* Set invalid properties here */
        };

        _fixture.ProductGroupRepository.GetQueryable().Returns(new List<ProductGroup>().AsQueryable().BuildMock());

        _fixture.ProductTeamRepository.GetQueryable() .Returns(new List<ProductTeam>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());
        
        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.AddProductGroupAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task UpdateProductGroupAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";
        var request = new UpdateProductGroupRequest
        {
            /* Set properties here */
        };

        _fixture.ProductGroupRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((ProductGroup?)null);

        // Act
        var result = await _engineerService.UpdateProductGroupAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task DeleteProductGroupAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";

        // Act
        var result = await _engineerService.DeleteProductGroupAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }
    
    
    [Fact]
    public async Task DeleteProductGroupAsync_Should_Return_NotFound_When_Delete_Count_Is_Zero()
    {
        // Arrange
        var id = "validId";
        var data = new ProductGroup
        {
            Id = id,
            GroupName = "Test Group",
            ProductTeams = new(),
            Supervisors = new()
        };

        _fixture.ProductGroupRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(data);

        _fixture.ProductGroupRepository.DeleteAsync(Arg.Any<ProductGroup>(), Arg.Any<CancellationToken>())
            .Returns(0); // Simulate failure to delete

        // Act
        var result = await _engineerService.DeleteProductGroupAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because delete returned zero indicating failure");
            result.Code.Should().Be(StatusCodes.Status404NotFound, "because the product group was not deleted");
            result.Message.Should().Contain("not found", "to validate mutation resistance on ternary and conditionals");
        }
    }


    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_ReturnsProductGroup_WhenRepositoryIdExists()
    {
        // Arrange
        var repositoryId = "existing-repo-id";
        var productGroup = new ProductGroup { Id = "1", GroupName = "Test Group" };
        var productTeam = new ProductTeamItem
        {
            Id = "1", Name = "Test Team",
            Repositories = new Data.Entities.Repositories
            {
                Items = new List<RepositoryItem>
                    { new RepositoryItem { Type = "Frontend", Url = "https://www.google.co", Id = repositoryId } }
            }
        };
        productGroup.ProductTeams.Items.Add(productTeam);
        var data = new[]
        {
            new Repository()
            {
                Id = repositoryId, Name = "test", Description = "hello", Type = "Frontend",
                Url = "https://www.google.co"
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);
        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup> { productGroup }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(Arg.Any<string>(), Arg.Any<string>(),
                Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(new ProductGroupDetails()
            {
                ProductGroupId = productGroup.Id, ProductGroupName = productGroup.GroupName,
                ProductTeams = productGroup.ProductTeams,
                RepositoryDetails = new RepositoryDetails
                    { Url = productTeam.Repositories.Items[0].Url, Type = productTeam.Repositories.Items[0].Type }
            });

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, null);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.ProductGroupId.Should().Be(productGroup.Id);
            result.Data?.ProductGroupName.Should().Be(productGroup.GroupName);
            result.Data?.ProductTeamId.Should().Be(productTeam.Id);
            result.Data?.ProductTeamName.Should().Be(productTeam.Name);
            result.Data?.RepositoryType.Should().Be(productTeam.Repositories.Items[0].Type);
            result.Data?.RepositoryUrl.Should().Be(productTeam.Repositories.Items[0].Url);
        }
    }

    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_ReturnsProductGroup_WhenSonarQubeKeyExists()
    {
        // Arrange
        var sonarQubeKey = "existing-sonarqube-key";
        var productGroup = new ProductGroup { Id = "1", GroupName = "Test Group" };
        var productTeam = new ProductTeamItem
        {
            Id = "1", Name = "Test Team",
            Repositories = new Data.Entities.Repositories
            {
                Items = new List<RepositoryItem>
                {
                    new RepositoryItem { SonarQubeKey = sonarQubeKey, Type = "Backend", Url = "https://www.google.com" }
                }
            }
        };
        productGroup.ProductTeams.Items.Add(productTeam);

        var data = new[]
        {
            new Repository()
            {
                Id = "asdf", SonarQubeKey = sonarQubeKey, Name = "test", Description = "hello", Type = "Backend",
                Url = "https://www.google.com"
            }
        }.AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);
        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup> { productGroup }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(Arg.Any<string>(), Arg.Any<string>(),
                Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(new ProductGroupDetails()
            {
                ProductGroupId = productGroup.Id, ProductGroupName = productGroup.GroupName,
                ProductTeams = productGroup.ProductTeams,
                RepositoryDetails = new RepositoryDetails
                    { Url = productTeam.Repositories.Items[0].Url, Type = productTeam.Repositories.Items[0].Type }
            });

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(null, sonarQubeKey);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.ProductGroupId.Should().Be(productGroup.Id);
            result.Data?.ProductGroupName.Should().Be(productGroup.GroupName);
            result.Data?.ProductTeamId.Should().Be(productTeam.Id);
            result.Data?.ProductTeamName.Should().Be(productTeam.Name);
            result.Data?.RepositoryType.Should().Be(productTeam.Repositories.Items[0].Type);
            result.Data?.RepositoryUrl.Should().Be(productTeam.Repositories.Items[0].Url);
        }
    }

    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_ReturnsNotFound_WhenRepositoryIdDoesNotExist()
    {
        // Arrange
        var repositoryId = "non-existing-repo-id";

        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>().AsQueryable().BuildMock());

        var data = new[] { new Repository() { Id = "hello", Name = "test", Description = "hello" } }.AsQueryable()
            .BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, null);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
            result.Code.Should().Be((int)HttpStatusCode.NotFound);
        }
    }

    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_ReturnsNotFound_WhenSonarQubeKeyDoesNotExist()
    {
        // Arrange
        var sonarQubeKey = "non-existing-sonarqube-key";

        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>().AsQueryable().BuildMock());

        _fixture.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>().AsQueryable().BuildMock());

        var data = new[]
                { new Repository() { Id = "hello", SonarQubeKey = "parrot", Name = "test", Description = "hello" } }
            .AsQueryable().BuildMock();

        _fixture.RepositoryRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(null, sonarQubeKey);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
            result.Code.Should().Be((int)HttpStatusCode.NotFound);
        }
    }
    
    
    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Return_NotFound_When_Params_Are_Empty()
    {
        // Arrange
        string? repositoryId = null;
        string? sonarQubeKey = null;

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(Enumerable.Empty<Repository>().AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, sonarQubeKey, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Message.ToLower().Should().Contain("not found");
        }
    }
    
    
    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Return_NotFound_When_Repository_Not_Found()
    {
        // Arrange
        var sonarKey = "sonar-key";

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(new List<Repository>().AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(null, sonarKey, CancellationToken.None);

        // Assert
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }
    
    
    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Return_NotFound_When_ProductGroup_Is_Null()
    {
        // Arrange
        var repo = new Repository { Id = "repo1", SonarQubeKey = "sonar-key" };

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(new[] { repo }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns((ProductGroupDetails?)null);

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey("repo1", null, CancellationToken.None);

        // Assert
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }
    
    
    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Return_Success_When_Valid()
    {
        // Arrange
        var repositoryId = "repo1";
        var repo = new Repository { Id = repositoryId, SonarQubeKey = "sonar-key" };

        var productGroup = new ProductGroupDetails
        {
            ProductGroupId = "pg-id",
            ProductGroupName = "PG",
            ProductTeams = new ProductTeams()
            {
                Items =
                [
                    new ProductTeamItem()
                    {
                        Id = "pt-id",
                        Name = "PT",
                        Repositories = new Data.Entities.Repositories()
                        {
                            Items = [new RepositoryItem { Id = repositoryId }]
                        }
                    }
                ]
            },
            RepositoryDetails = new RepositoryDetails() { Url = "http://url", Type = "backend" }
        };

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(new[] { repo }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(repositoryId, Arg.Any<string>(), repo, Arg.Any<CancellationToken>())
            .Returns(productGroup);

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, null, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data.Should().NotBeNull();
            result.Data?.ProductGroupId.Should().Be(productGroup.ProductGroupId);
        }
    }

    
    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Return_NotFound_When_Only_RepositoryId_Is_Set_And_Does_Not_Match()
    {
        // Arrange
        var repositoryId = "repo-id";
        var sonarQubeKey = null as string;

        var repoInDb = new Repository { Id = "another-id", SonarQubeKey = "some-key" };

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(new[] { repoInDb }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, sonarQubeKey, CancellationToken.None);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }


    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Return_NotFound_When_Only_SonarQubeKey_Is_Set_And_Does_Not_Match()
    {
        // Arrange
        var sonarQubeKey = "sonar-id";
        var repoInDb = new Repository { Id = "repo-id", SonarQubeKey = "other-key" };

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(new[] { repoInDb }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(null, sonarQubeKey, CancellationToken.None);

        // Assert
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }

    
    [Fact]
    public async Task GetProductGroupByRepositoryIdOrSonarQubeKey_Should_Match_Repository_When_Id_And_Key_Valid()
    {
        // Arrange
        var repositoryId = "repo-id";
        var sonarQubeKey = "sonar-key";

        var matchingRepo = new Repository { Id = repositoryId, SonarQubeKey = sonarQubeKey };

        var productGroup = new ProductGroupDetails
        {
            ProductGroupId = "pg-id",
            ProductGroupName = "Product Group",
            ProductTeams = new ProductTeams
            {
                Items = new List<ProductTeamItem>
                {
                    new ProductTeamItem()
                    {
                        Id = "pt-id",
                        Name = "Team",
                        Repositories = new Data.Entities.Repositories()
                        {
                            Items = new List<RepositoryItem>
                            {
                                new RepositoryItem { Id = repositoryId, SonarQubeKey = sonarQubeKey }
                            }
                        }
                    }
                }
            },
            RepositoryDetails = new RepositoryDetails() { Url = "http://repo", Type = "Git" }
        };

        _fixture.RepositoryRepository
            .GetQueryable()
            .Returns(new[] { matchingRepo }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.GetProductGroupByRepositoryFilterAsync(repositoryId, Arg.Any<string>(), Arg.Any<Repository>(), Arg.Any<CancellationToken>())
            .Returns(productGroup);

        // Act
        var result = await _engineerService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, sonarQubeKey, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Code.Should().Be(StatusCodes.Status200OK);
            result.Data?.ProductTeamId.Should().Be("pt-id");
        }
    }



}