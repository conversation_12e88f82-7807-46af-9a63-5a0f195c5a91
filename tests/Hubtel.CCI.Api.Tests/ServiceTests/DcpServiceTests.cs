using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Xunit;
using Service = Hubtel.CCI.Api.Data.Entities.Service;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class DcpServiceTests
{
    private readonly DcpService _dcpService;
    private readonly DiFixture _fixture;

    public DcpServiceTests()
    {
        _fixture = new DiFixture();
        _dcpService = new DcpService(Substitute.For<ILogger<DcpService>>(), _fixture.RepositoryContext);
    }
    
    [Fact]
    public async Task CreateDcpRequestAsync_ShouldReturnCreatedApiResponse_WhenRequestIsValid()
    {
        // Arrange
        var dcpRequest = new CreateDcpRequest()
        {
            // Populate properties as needed
        };

        _fixture.RepositoryContext.EngineerRepository.GetQueryable()
            .Returns(new List<Engineer>().AsQueryable().BuildMock());
        _fixture.RepositoryContext.DeploymentRequestRepository.AddAsync(Arg.Any<DeploymentRequest>()).Returns(1);

        // Act
        var response = await _dcpService.CreateDcpRequestAsync(dcpRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(201,response.Code);
        Assert.Equal("Deployment Request Added Successfully", response.Message);
    }
    
    [Fact]
    public async Task CreateDcpRequestAsync_ShouldReturnStatusCodeOf500_WhenInternalServerErrorOccurs()
    {
        // Arrange
        var dcpRequest = new CreateDcpRequest()
        {
            // Populate properties as needed
        };
        
      

        _fixture.RepositoryContext.DeploymentRequestRepository.AddAsync(Arg.Any<DeploymentRequest>())
            .Throws(new Exception());

        // Act
        var response = await _dcpService.CreateDcpRequestAsync(dcpRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(500,response.Code);
    }

    [Fact]
    public async Task UpdateDcpRequestAsync_ShouldReturnUpdatedApiResponse_WhenRequestIsValid()
    {
        // Arrange
        var id = "validId";
        var dcpRequest = new DcpRequest();

        var existingRequest = new DeploymentRequest
        {
            Id = id,
            Status = DeploymentRequestStatus.NotStarted
        };
        
        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>())
            .Returns(existingRequest);

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>()).Returns(existingRequest);

        _fixture.DeploymentRequestRepository.UpdateAsync(Arg.Any<DeploymentRequest>()).Returns(1);

        // Act
        var response = await _dcpService.UpdateDcpRequestAsync(id, dcpRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(200,response.Code);
        Assert.Equal("Deployment Request Updated Successfully", response.Message);
    }

    [Fact]
    public async Task UpdateDcpRequestAsync_ShouldReturnNotFoundApiResponse_WhenRequestDoesNotExist()
    {
        // Arrange
        var id = "invalidId";
        var dcpRequest = new DcpRequest
        {
            // Populate properties as needed
        };

        
        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>()).Returns((DeploymentRequest?)null);

        // Act
        var response = await _dcpService.UpdateDcpRequestAsync(id, dcpRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(404,response.Code);
        Assert.Equal("Deployment Request not found", response.Message);
    }
    
    [Fact]
    public async Task UpdateDcpRequestAsync_ShouldReturnStatusCodeOf500_WhenInternalServerErrorOccurs()
    {
        // Arrange
        var id = "invalidId";
        var dcpRequest = new DcpRequest
        {
            // Populate properties as needed
        };

        
        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>()).Throws(new Exception());

        // Act
        var response = await _dcpService.UpdateDcpRequestAsync(id, dcpRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(500,response.Code);
    }


    
    [Fact]
    public async Task CancelDcpRequestAsync_ShouldReturnCancelledApiResponse_WhenRequestIsValid()
    {
        // Arrange
        var id = "validId";
        var cancelRequest = new CancelDcpRequest
        {
            CancellationReason = "Reason for cancellation"
        };

        var existingRequest = new DeploymentRequest
        {
            Id = id,
            Status = DeploymentRequestStatus.NotStarted
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>()).Returns(existingRequest);

        _fixture.RepositoryContext.DeploymentRequestRepository.UpdateAsync(Arg.Any<DeploymentRequest>()).Returns(1);
        

        // Act
        var response = await _dcpService.CancelDcpRequestAsync(id, cancelRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(200,response.Code);
        Assert.Equal("Deployment Request Cancelled Successfully", response.Message);
        Assert.Equal(DeploymentRequestStatus.Cancelled, existingRequest.Status);
        Assert.Equal(cancelRequest.CancellationReason, existingRequest.CancellationReason);
    }

    [Fact]
    public async Task CancelDcpRequestAsync_ShouldReturnNotFoundApiResponse_WhenRequestDoesNotExist()
    {
        // Arrange
        var id = "invalidId";
        var cancelRequest = new CancelDcpRequest
        {
            CancellationReason = "Reason for cancellation"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>()).Returns((DeploymentRequest?)null);
        // Act
        var response = await _dcpService.CancelDcpRequestAsync(id, cancelRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(404,response.Code);
    }

    [Fact]
    public async Task CancelDcpRequestAsync_ShouldReturnServerErrorApiResponse_WhenExceptionIsThrown()
    {
        // Arrange
        var id = "validId";
        var cancelRequest = new CancelDcpRequest
        {
            CancellationReason = "Reason for cancellation"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>()).Throws(new Exception());
        // Act
        var response = await _dcpService.CancelDcpRequestAsync(id, cancelRequest);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(500,response.Code);
    }

    
    [Fact]
    public async Task UpdateServiceToBeDeployedAsync_ShouldUpdateAndReturnOkResponse_WhenDataIsValid()
    {
        // Arrange
        var requestId = "req-123";
        var serviceId = "svc-456";

        var serviceToBeDeployed = new ServiceToBeDeployedInput 
        {
            ServiceId = serviceId
        };

        var existingRequest = new DeploymentRequest
        {
            Id = requestId,
            ServicesToBeDeployed =
            [
                new ServiceToBeDeployed
                {
                    ServiceId = serviceId,
                    ServiceName = "Old Service",
                    RepositoryId = "repo-001",
                    RepositoryName = "old-repo"
                }
            ],
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };

        var serviceInfo = new Service
        {
            Id = serviceId,
            Name = "Updated Service",
            RepositoryId = "repo-123",
            RepositoryName = "updated-repo"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>())
            .Returns(existingRequest);

        _fixture.RepositoryContext.ServiceRepository.GetByIdAsync(Arg.Any<string>())
            .Returns(serviceInfo);

        _fixture.RepositoryContext.DeploymentRequestRepository.UpdateAsync(Arg.Any<DeploymentRequest>())
            .Returns(1);

        // Act
        var result = await _dcpService.UpdateServiceToBeDeployedAsync(requestId, serviceToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(200, result.Code);
        Assert.Equal("Service to be deployed updated successfully", result.Message);
        Assert.Equal(serviceId, result.Data!.ServicesToBeDeployed.First().ServiceId);
        Assert.Equal("Updated Service", result.Data.ServicesToBeDeployed.First().ServiceName);
        Assert.Equal("repo-123", result.Data.ServicesToBeDeployed.First().RepositoryId);
        Assert.Equal("updated-repo", result.Data.ServicesToBeDeployed.First().RepositoryName);
    }
    
    [Fact]
    public async Task UpdateServiceToBeDeployedAsync_ShouldReturnNotFound_WhenDeploymentRequestDoesNotExist()
    {
        // Arrange
        var requestId = "invalid-request-id";
        var serviceToBeDeployed = new ServiceToBeDeployedInput 
        {
            ServiceId = "svc-001"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId)
            .Returns((DeploymentRequest)null!); // Simulate not found

        // Act
        var result = await _dcpService.UpdateServiceToBeDeployedAsync(requestId, serviceToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(404, result.Code);
        Assert.Equal("Deployment Request not found", result.Message);
    }
    
    
    [Fact]
    public async Task UpdateServiceToBeDeployedAsync_ShouldReturnServerError_WhenExceptionIsThrown()
    {
        // Arrange
        var requestId = "req-500";
        var serviceToBeDeployed = new ServiceToBeDeployedInput 
        {
            ServiceId = "svc-001"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId)
            .Throws(new Exception("Database failure"));

        // Act
        var result = await _dcpService.UpdateServiceToBeDeployedAsync(requestId, serviceToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(500, result.Code);
        Assert.Equal("Sorry,something went wrong", result.Message);
    }
    
    [Fact]
    public async Task RemoveServiceToBeDeployedAsync_ShouldRemoveServiceAndReturnOk_WhenServiceExists()
    {
        // Arrange
        var requestId = "req-001";
        var serviceId = "svc-123";

        var deploymentRequest = new DeploymentRequest
        {
            Id = requestId,
            ServicesToBeDeployed =
            [
                new ServiceToBeDeployed
                {
                    ServiceId = serviceId,
                    ServiceName = "Target Service"
                }
            ]
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>())
            .Returns(deploymentRequest);

        _fixture.RepositoryContext.DeploymentRequestRepository.UpdateAsync(Arg.Any<DeploymentRequest>())
            .Returns(1);

        // Act
        var result = await _dcpService.RemoveServiceToBeDeployedAsync(requestId, serviceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(200, result.Code);
        Assert.Equal("Service to be deployed removed successfully", result.Message);
        Assert.DoesNotContain(result.Data!.ServicesToBeDeployed, s => s.ServiceId == serviceId);
    }
    
    [Fact]
    public async Task RemoveServiceToBeDeployedAsync_ShouldReturnNotFound_WhenDeploymentRequestDoesNotExist()
    {
        // Arrange
        var requestId = "nonexistent";
        var serviceId = "svc-999";

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>())
            .Returns((DeploymentRequest)null!);

        // Act
        var result = await _dcpService.RemoveServiceToBeDeployedAsync(requestId, serviceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(404, result.Code);
        Assert.Equal("Deployment Request not found", result.Message);
    }
    
    [Fact]
    public async Task RemoveServiceToBeDeployedAsync_ShouldReturnOk_WhenServiceDoesNotExistInRequest()
    {
        // Arrange
        var requestId = "req-002";
        var serviceId = "svc-missing";

        var deploymentRequest = new DeploymentRequest
        {
            Id = requestId,
            ServicesToBeDeployed = [] // Empty list
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(Arg.Any<string>())
            .Returns(deploymentRequest);

        // Act
        var result = await _dcpService.RemoveServiceToBeDeployedAsync(requestId, serviceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(200, result.Code);
        Assert.Empty(result.Data!.ServicesToBeDeployed);
    }
    
    
    [Fact]
    public async Task UpdateBulkServicesToBeDeployedAsync_ShouldReturnOk_WhenRequestAndServicesAreValid()
    {
        // Arrange
        var requestId = "req-123";
        var serviceId1 = "svc-1";
        var serviceId2 = "svc-2";

        var servicesToBeDeployed = new List<ServiceToBeDeployedInput >
        {
            new ServiceToBeDeployedInput  { ServiceId = serviceId1 },
            new ServiceToBeDeployedInput  { ServiceId = serviceId2 }
        };

        var deploymentRequest = new DeploymentRequest
        {
            Id = requestId,
            ServicesToBeDeployed = new List<ServiceToBeDeployed>
            {
                new ServiceToBeDeployed { ServiceId = serviceId1, ServiceName = "Old Service" }
            }
        };

        var servicesInfo = new List<Service>
        {
            new Service { Id = serviceId1, Name = "New Service 1", RepositoryId = "repo-1", RepositoryName = "repo-name-1" },
            new Service { Id = serviceId2, Name = "New Service 2", RepositoryId = "repo-2", RepositoryName = "repo-name-2" }
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId)
            .Returns(deploymentRequest);

        _fixture.RepositoryContext.ServiceRepository.GetQueryable()
            .Returns(servicesInfo.AsQueryable().BuildMock());

        _fixture.RepositoryContext.DeploymentRequestRepository.UpdateAsync(Arg.Any<DeploymentRequest>())
            .Returns(1);

        // Act
        var result = await _dcpService.UpdateBulkServicesToBeDeployedAsync(requestId, servicesToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(200, result.Code);
        Assert.Equal("Bulk services to be deployed updated successfully", result.Message);
        Assert.Equal(2, result.Data!.ServicesToBeDeployed.Count);
    }
    
    [Fact]
    public async Task UpdateBulkServicesToBeDeployedAsync_ShouldReturnNotFound_WhenDeploymentRequestDoesNotExist()
    {
        // Arrange
        var requestId = "invalid-id";
        var servicesToBeDeployed = new List<ServiceToBeDeployedInput >
        {
            new ServiceToBeDeployedInput  { ServiceId = "svc-1" }
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId)
            .Returns((DeploymentRequest)null!);

        // Act
        var result = await _dcpService.UpdateBulkServicesToBeDeployedAsync(requestId, servicesToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(404, result.Code);
        Assert.Equal("Deployment Request not found", result.Message);
    }
    
    [Fact]
    public async Task UpdateBulkServicesToBeDeployedAsync_ShouldReturnNotFound_WhenSomeServicesNotFound()
    {
        // Arrange
        var requestId = "req-999";
        var servicesToBeDeployed = new List<ServiceToBeDeployedInput >
        {
            new() { ServiceId = "svc-1" },
            new() { ServiceId = "svc-2" }
        };

        var deploymentRequest = new DeploymentRequest
        {
            Id = requestId,
            ServicesToBeDeployed = new List<ServiceToBeDeployed>()
        };

        var onlyOneServiceInfo = new List<Service>
        {
            new Service { Id = "svc-1", Name = "Valid Service", RepositoryId = "repo-1", RepositoryName = "repo-name-1" }
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId)
            .Returns(deploymentRequest);

        _fixture.RepositoryContext.ServiceRepository.GetQueryable()
            .Returns(onlyOneServiceInfo.AsQueryable().BuildMock());

        // Act
        var result = await _dcpService.UpdateBulkServicesToBeDeployedAsync(requestId, servicesToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(404, result.Code);
        Assert.Equal("Some services not found", result.Message);
    }
    
    [Fact]
    public async Task UpdateBulkServicesToBeDeployedAsync_ShouldReturnServerError_WhenExceptionIsThrown()
    {
        // Arrange
        var requestId = "req-ex";
        var servicesToBeDeployed = new List<ServiceToBeDeployedInput >
        {
            new ServiceToBeDeployedInput  { ServiceId = "svc-1" }
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId)
            .Throws(new Exception("Boom"));

        // Act
        var result = await _dcpService.UpdateBulkServicesToBeDeployedAsync(requestId, servicesToBeDeployed);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(500, result.Code);
        Assert.Equal("Sorry,something went wrong", result.Message);
    }

    #region  Services Deployment Trails

    [Fact]
    public async Task AddServiceDeploymentTrailAsync_ShouldReturnCreated_WhenRequestIsValid()
    {
        // Arrange
        var request = new ServiceDeploymentTrailRequest
        {
            DeploymentRequestId = "req-001",
            RepositoryServiceId = "svc-001",
            Status = "Started",
            // Add other required properties...
        };

        var deploymentRequest = new DeploymentRequest
        {
            Id = request.DeploymentRequestId,
            ServicesToBeDeployed = [new ServiceToBeDeployed { ServiceId = request.RepositoryServiceId }]
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(request.DeploymentRequestId)
            .Returns(deploymentRequest);

        _fixture.RepositoryContext.ServiceDeploymentTrailRepository.AddAsync(Arg.Any<ServiceDeploymentTrail>())
            .Returns(1);

        // Act
        var result = await _dcpService.AddServiceDeploymentTrailAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(201, result.Code);
        Assert.Equal("Service deployment trail added successfully", result.Message);
        Assert.NotNull(result.Data!.Id);
    }
    
    [Fact]
    public async Task AddServiceDeploymentTrailAsync_ShouldReturnNotFound_WhenDeploymentRequestNotFound()
    {
        // Arrange
        var request = new ServiceDeploymentTrailRequest
        {
            DeploymentRequestId = "missing-id",
            RepositoryServiceId = "svc-001"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(request.DeploymentRequestId)
            .Returns((DeploymentRequest)null!);

        // Act
        var result = await _dcpService.AddServiceDeploymentTrailAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(404, result.Code);
        Assert.Equal("Deployment Request not found", result.Message);
    }
    
    [Fact]
    public async Task AddServiceDeploymentTrailAsync_ShouldReturnNotFound_WhenServiceNotFoundInDeploymentRequest()
    {
        // Arrange
        var request = new ServiceDeploymentTrailRequest
        {
            DeploymentRequestId = "req-001",
            RepositoryServiceId = "svc-missing"
        };

        var deploymentRequest = new DeploymentRequest
        {
            Id = request.DeploymentRequestId,
            ServicesToBeDeployed = [new ServiceToBeDeployed { ServiceId = "svc-001" }]
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(request.DeploymentRequestId)
            .Returns(deploymentRequest);

        // Act
        var result = await _dcpService.AddServiceDeploymentTrailAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(404, result.Code);
        Assert.Equal("Service to be deployed not found in the request", result.Message);
    }
    
    [Fact]
    public async Task AddServiceDeploymentTrailAsync_ShouldReturnServerError_WhenExceptionOccurs()
    {
        // Arrange
        var request = new ServiceDeploymentTrailRequest
        {
            DeploymentRequestId = "req-error",
            RepositoryServiceId = "svc-error"
        };

        _fixture.RepositoryContext.DeploymentRequestRepository.GetByIdAsync(request.DeploymentRequestId)
            .Throws(new Exception("Unexpected failure"));

        // Act
        var result = await _dcpService.AddServiceDeploymentTrailAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(500, result.Code);
        Assert.Equal("Sorry,something went wrong", result.Message);
    }

    #endregion
    
    #region Delete Dcp Requests
   
    [Fact]
    public async Task DeleteDcpRequestByIdsAsync_ShouldReturnOkApiResponse_WhenDeleteSucceeds()
    {
        // Arrange
        var request = new DeleteDcpRequest { RequestIds = new List<string> { "id1", "id2" } };
        _fixture.RepositoryContext.DeploymentRequestRepository.DeleteBulkByIdsAsync(Arg.Any<List<string>>()).Returns(2);

        // Act
        var response = await _dcpService.DeleteDcpRequestByIdsAsync(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(200, response.Code);
        Assert.Equal(2, response.Data);
        Assert.Equal("DCP requests deleted successfully", response.Message);
    }

    [Fact]
    public async Task DeleteDcpRequestByIdsAsync_ShouldThrowException_WhenRepositoryThrows()
    {
        // Arrange
        var request = new DeleteDcpRequest { RequestIds = ["id1", "id2"] };
        _fixture.RepositoryContext.DeploymentRequestRepository.DeleteBulkByIdsAsync(request.RequestIds)
            .Throws(new Exception("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _dcpService.DeleteDcpRequestByIdsAsync(request));
    }
  
    
    #endregion
    
}