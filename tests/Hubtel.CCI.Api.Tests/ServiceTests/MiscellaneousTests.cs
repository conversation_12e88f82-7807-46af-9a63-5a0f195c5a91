using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Helpers;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class MiscellaneousTests
{
    
    [Fact]
    public void GetPreviousPublicationWeekStartAndEndDates_ShouldReturnPreviousMondayToFriday()
    {
        // Arrange
        var today = DateTime.Today;
        var currentDayOfWeek = (int)today.DayOfWeek;
        var currentWeekMonday = today.AddDays(-(currentDayOfWeek - 1));
        var expectedStart = currentWeekMonday.AddDays(-7).Date;
        var expectedEnd = expectedStart.AddDays(4).Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        // Act
        var result = Miscellaneous.GetPreviousPublicationWeekStartAndEndDates();

        // Assert
        using var scope = new AssertionScope();
        result.StartDate.Should().Be(expectedStart);
        result.EndDate.Should().Be(expectedEnd);
    }
    
    
    [Fact]
    public void GetPreviousPublicationDay_ShouldReturnCorrectPreviousDayWePublishedFor()
    {
        // Arrange
        var today = DateTime.Today;

        DateTime GetPreviousWeekday(DateTime from)
        {
            var day = from.AddDays(-1);
            return day.DayOfWeek switch
            {
                DayOfWeek.Sunday => day.AddDays(-2),
                DayOfWeek.Saturday => day.AddDays(-1),
                _ => day
            };
        }

        var forDay = GetPreviousWeekday(today);
        var expected = GetPreviousWeekday(forDay);

        // Act
        var result = Miscellaneous.GetPreviousPublicationDay();

        // Assert
        result.Should().Be(expected);
    }
    
    
    [Fact]
    public void GetCurrentPublicationTargetDay_ShouldReturnCorrectTargetDay()
    {
        // Arrange
        var today = DateTime.Today;

        var expected = today.DayOfWeek switch
        {
            DayOfWeek.Monday => today.AddDays(-3),    // Publishing Friday’s data
            DayOfWeek.Sunday => today.AddDays(-2),    // Publishing Friday’s data
            DayOfWeek.Saturday => today.AddDays(-1),  // Publishing Friday’s data
            _ => today.AddDays(-1)                    // Publishing yesterday’s data
        };

        // Act
        var result = Miscellaneous.GetCurrentPublicationTargetDay();

        // Assert
        result.Should().Be(expected);
    }
}