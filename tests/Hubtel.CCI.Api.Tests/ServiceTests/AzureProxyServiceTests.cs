using System.Net;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Models.Azure;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Providers;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class AzureProxyServiceTests
{
    private readonly AzureProxyService _azureProxyService;
    private readonly IMemoryCache _cache;
    private readonly AzureOrgConfig _azureConfig;

    public AzureProxyServiceTests()
    {
        var logger = Substitute.For<ILogger<AzureProxyService>>();
        _cache = Substitute.For<IMemoryCache>();

        var azureConfig = Substitute.For<IOptions<AzureOrgConfig>>();
        azureConfig.Value.Returns(new AzureOrgConfig
        {
            Organization = "test-org",
            AccessToken = "test-token",
            DevUrl = "https://dev.azure.com",
            ReleaseUrl = "https://vsrm.dev.azure.com",
            ApiVersion = "7.0",
            DefaultPageSize = 100,
            RequestTimeoutSeconds = 60,
            WorkItemToken = "work-item-token"
        });
        _azureConfig = azureConfig.Value;
        azureConfig.Value.Returns(new AzureOrgConfig
        {
            Organization = "test-org",
            AccessToken = "test-token",
            DevUrl = "https://dev.azure.com",
            ReleaseUrl = "https://vsrm.dev.azure.com",
            ApiVersion = "7.0",
            DefaultPageSize = 100,
            RequestTimeoutSeconds = 60,
            WorkItemToken = "work-item-token"
        });
        _azureProxyService = new AzureProxyService(azureConfig, logger, _cache);
    }

    #region GetRepositoryDetailAsync Tests

    [Fact]
    public async Task GetRepositoryDetailAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryName = "test-repo";
        var expectedResponse = new AzureRepositoryResponse
        {
            Id = "repo-id-123",
            Name = repositoryName,
            Url = "https://dev.azure.com/test-org/test-project/_git/test-repo",
            Project = new ProjectInfo
            {
                Id = "project-id-123",
                Name = project
            }
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedResponse);

        // Act
        var result = await _azureProxyService.GetRepositoryDetailAsync(project, repositoryName);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be("repo-id-123");
            result.Data.Name.Should().Be(repositoryName);
        }

        httpTest.ShouldHaveCalled(
                $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryName}")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .WithHeader(ValidationConstants.AzureProxyRelated.Authorization,
                $"Basic {Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($":{_azureConfig.AccessToken}"))}")
            .Times(1);
    }

    [Fact]
    public async Task GetRepositoryDetailAsync_WithCachedData_ReturnsCachedResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryName = "test-repo";
        var cacheKey = $"repo:{project}:{repositoryName}";
        var cachedResponse = new AzureRepositoryResponse
        {
            Id = "cached-repo-id",
            Name = repositoryName
        };

        _cache.TryGetValue(cacheKey, out Arg.Any<AzureRepositoryResponse?>())
            .Returns(x =>
            {
                x[1] = cachedResponse;
                return true;
            });

        using var httpTest = new HttpTest();

        // Act
        var result = await _azureProxyService.GetRepositoryDetailAsync(project, repositoryName);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be("cached-repo-id");
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    [Fact]
    public async Task GetRepositoryDetailAsync_WithHttpException_ReturnsNotFoundResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryName = "non-existent-repo";

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Repository not found", 404);

        // Act
        var result = await _azureProxyService.GetRepositoryDetailAsync(project, repositoryName);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(404);
            result.Data.Should().BeNull();
        }
    }

    #endregion

    #region GetWorkItemsAsync Tests

    [Fact]
    public async Task GetWorkItemsAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var pullRequestId = 456;
        var expectedWorkItems = new List<AzureWorkItemListItemInfo>
        {
            new() { Id = "1", Url = "https://dev.azure.com/test-org/_apis/wit/workItems/1" },
            new() { Id = "2", Url = "https://dev.azure.com/test-org/_apis/wit/workItems/2" }
        };
        var expectedResponse = new AzureListResponse<AzureWorkItemListItemInfo>
        {
            Count = 2,
            Value = expectedWorkItems!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedResponse);

        // Act
        var result = await _azureProxyService.GetWorkItemsAsync(project, repositoryId, pullRequestId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(2);
            result.Data.First().Id.Should().Be("1");
        }

        httpTest.ShouldHaveCalled(
                $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryId}/pullRequests/{pullRequestId}/workitems")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .Times(1);
    }

    [Fact]
    public async Task GetWorkItemsAsync_WithCachedData_ReturnsCachedResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var pullRequestId = 456;
        var cacheKey = $"workitems:{project}:{repositoryId}:{pullRequestId}";
        var cachedWorkItems = new List<AzureWorkItemListItemInfo>
        {
            new() { Id = "cached-1", Url = "cached-url" }
        };

        _cache.TryGetValue(cacheKey, out Arg.Any<List<AzureWorkItemListItemInfo>?>())
            .Returns(x =>
            {
                x[1] = cachedWorkItems;
                return true;
            });

        using var httpTest = new HttpTest();

        // Act
        var result = await _azureProxyService.GetWorkItemsAsync(project, repositoryId, pullRequestId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(1);
            result.Data.First().Id.Should().Be("cached-1");
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    [Fact]
    public async Task GetWorkItemsAsync_WithHttpException_ReturnsFailedDependencyResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var pullRequestId = 456;

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal server error", 500);

        // Act
        var result = await _azureProxyService.GetWorkItemsAsync(project, repositoryId, pullRequestId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(424); // FailedDependency
            result.Data.Should().BeNull();
            // result.Data!.Count.Should().Be(0);
        }
    }

    #endregion

    #region GetBuildChangesAsync Tests

    [Fact]
    public async Task GetBuildChangesAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var buildId = "build-123";
        var expectedChanges = new List<AzureBuildChangeListItemInfo>
        {
            new() { Id = "commit-1" },
            new() { Id = "commit-2" }
        };
        var expectedResponse = new AzureListResponse<AzureBuildChangeListItemInfo>
        {
            Count = 2,
            Value = expectedChanges!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedResponse);

        // Act
        var result = await _azureProxyService.GetBuildChangesAsync(project, buildId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(2);
            result.Data.First().Id.Should().Be("commit-1");
        }

        httpTest.ShouldHaveCalled(
                $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/build/builds/{buildId}/changes")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .Times(1);
    }

    #endregion

    #region ExtractProjectAndRepository Tests

    [Theory]
    [InlineData("https://dev.azure.com/hubtel/Back-End/_git/test-repo", "Back-End", "test-repo")]
    [InlineData("https://dev.azure.com/organization/My%20Project/_git/My%20Repo", "My Project", "My Repo")]
    [InlineData("invalid-url", "", "")]
    public void ExtractProjectAndRepository_WithVariousUrls_ReturnsExpectedResults(string azureUrl,
        string expectedProject, string expectedRepo)
    {
        // Act
        var (project, repository) = _azureProxyService.ExtractProjectAndRepository(azureUrl);

        // Assert
        using (new AssertionScope())
        {
            project.Should().Be(expectedProject);
            repository.Should().Be(expectedRepo);
        }
    }

    #endregion

    #region GetPullRequestsAsync Tests

    [Fact]
    public async Task GetPullRequestsAsync_WithValidParameters_ReturnsAllPullRequests()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var status = "completed";

        var expectedPrs = new List<AzurePullRequestInfo>
        {
            new()
            {
                PullRequestId = 1,
                Title = "First PR",
                Status = "completed",
                CreationDate = DateTime.UtcNow.AddDays(-1),
                TargetRefName = "refs/heads/main",
                LastMergeCommit = new CommitRef { CommitId = "commit-1" },
                Reviewers = new List<Reviewer>
                {
                    new() { Vote = 10 }, // Approved
                    new() { Vote = 10 } // Approved
                }
            },
            new()
            {
                PullRequestId = 2,
                Title = "Second PR",
                Status = "completed",
                CreationDate = DateTime.UtcNow.AddDays(-2),
                TargetRefName = "refs/heads/main",
                LastMergeCommit = new CommitRef { CommitId = "commit-2" },
                Reviewers = new List<Reviewer>
                {
                    new() { Vote = 5 }, // Approved with suggestions
                    new() { Vote = 0 } // No vote
                }
            }
        };

        var azureResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 2,
            Value = expectedPrs!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(azureResponse);

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId, status))
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(2);
            results.First().PullRequestId.Should().Be(1);
            results.First().Title.Should().Be("First PR");
            results.Last().PullRequestId.Should().Be(2);
            results.Last().Title.Should().Be("Second PR");
        }

        httpTest.ShouldHaveCalled(
                $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryId}/pullrequests")
            .WithQueryParam("searchCriteria.status", status)
            .WithQueryParam("$top", _azureConfig.DefaultPageSize)
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, "7.1")
            .WithHeader(ValidationConstants.AzureProxyRelated.Authorization,
                $"Basic {Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($":{_azureConfig.AccessToken}"))}")
            .Times(1);
    }

    [Fact]
    public async Task GetPullRequestsAsync_WithCachedData_ReturnsCachedResults()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var status = "completed";
        var cacheKey = $"prs:{project}:{repositoryId}:{status}";

        var cachedPrs = new List<AzurePullRequestInfo>
        {
            new()
            {
                PullRequestId = 999,
                Title = "Cached PR",
                Status = "completed"
            }
        };

        _cache.TryGetValue(cacheKey, out Arg.Any<List<AzurePullRequestInfo>?>())
            .Returns(x =>
            {
                x[1] = cachedPrs;
                return true;
            });

        using var httpTest = new HttpTest();

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId, status))
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(1);
            results.First().PullRequestId.Should().Be(999);
            results.First().Title.Should().Be("Cached PR");
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    [Fact]
    public async Task GetPullRequestsAsync_WithPagination_ReturnsAllPages()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var status = "completed";

        var firstPagePrs = new List<AzurePullRequestInfo>
        {
            new() { PullRequestId = 1, Title = "PR 1" },
            new() { PullRequestId = 2, Title = "PR 2" }
        };

        var secondPagePrs = new List<AzurePullRequestInfo>
        {
            new() { PullRequestId = 3, Title = "PR 3" },
            new() { PullRequestId = 4, Title = "PR 4" }
        };

        var firstPageResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 2,
            Value = firstPagePrs!
        };

        var secondPageResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 2,
            Value = secondPagePrs!
        };

        using var httpTest = new HttpTest();

        // First call returns data with continuation token
        httpTest.RespondWithJson(firstPageResponse , headers: new Dictionary<string, string> { { "x-ms-continuationtoken", "next-page" } });

        // Second call returns data without continuation token
        httpTest.RespondWithJson(secondPageResponse);

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId, status))
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(4);
            results.Select(pr => pr.PullRequestId).Should().BeEquivalentTo([1, 2, 3, 4]);
        }

        httpTest.ShouldHaveMadeACall()
            .Times(2);
    }

    [Fact]
    public async Task GetPullRequestsAsync_WithHttpError_StopsEnumerationGracefully()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var status = "completed";

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal Server Error", 500);

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId, status))
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(0); // Should return empty when error occurs
        }

        httpTest.ShouldHaveMadeACall()
            .Times(1);
    }

    [Fact]
    public async Task GetPullRequestsAsync_WithEmptyResponse_ReturnsEmptyEnumerable()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var status = "completed";

        var emptyResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 0,
            Value = new List<AzurePullRequestInfo>()!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(emptyResponse);

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId, status))
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(0);
        }

        httpTest.ShouldHaveMadeACall()
            .Times(1);
    }

    [Fact]
    public async Task GetPullRequestsAsync_WithNullPullRequestsInResponse_FiltersOutNulls()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var status = "completed";

        var responseWithNulls = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 3,
            Value = new List<AzurePullRequestInfo?>
            {
                new AzurePullRequestInfo { PullRequestId = 1, Title = "Valid PR" },
                null, // This should be filtered out
                new AzurePullRequestInfo { PullRequestId = 2, Title = "Another Valid PR" }
            }!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(responseWithNulls);

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId, status))
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(2); // Only non-null items should be returned
            results.All(pr => pr != null).Should().BeTrue();
            results.Select(pr => pr.PullRequestId).Should().BeEquivalentTo([1, 2]);
        }
    }

    [Fact]
    public async Task GetPullRequestsAsync_WithDefaultStatus_UsesCompletedStatus()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        // Not specifying status parameter to test default value

        var expectedPrs = new List<AzurePullRequestInfo>
        {
            new() { PullRequestId = 1, Title = "Completed PR", Status = "completed" }
        };

        var azureResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 1,
            Value = expectedPrs!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(azureResponse);

        // Act
        var results = new List<AzurePullRequestInfo>();
        await foreach (var pr in _azureProxyService.GetPullRequestsAsync(project, repositoryId)) // Using default status
        {
            results.Add(pr);
        }

        // Assert
        using (new AssertionScope())
        {
            results.Should().NotBeNull();
            results.Count.Should().Be(1);
        }

        httpTest.ShouldHaveCalled(
                $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryId}/pullrequests")
            .WithQueryParam("searchCriteria.status", "completed") // Should use default "completed" status
            .Times(1);
    }

    #endregion

    #region GetRepositoryServiceDeploymentMetricsSummaryAsync Tests

    [Fact]
    public async Task GetRepositoryServiceDeploymentMetricsSummaryAsync_WithValidParameters_ReturnsMetricsSummary()
    {
        // Arrange
        var projectName = "test-project";
        var releaseDefinitionId = 123;
        var top = 5;

        var releaseInfos = new List<AzureReleaseInfo>
        {
            new() { Id = 1, Name = "Release 1" },
            new() { Id = 2, Name = "Release 2" }
        };

        var releaseListResponse = new AzureListResponse<AzureReleaseInfo>
        {
            Count = 2,
            Value = releaseInfos!
        };

        var releaseDetail1 = new AzureReleaseDetail
        {
            Id = 1,
            Name = "Release 1",
            Environments = new List<ReleaseEnvironment>
            {
                new() { Status = "succeeded" },
                new() { Status = "succeeded" },
                new() { Status = "rejected" }
            }
        };

        var releaseDetail2 = new AzureReleaseDetail
        {
            Id = 2,
            Name = "Release 2",
            Environments = new List<ReleaseEnvironment>
            {
                new() { Status = "succeeded" },
                new() { Status = "rejected" }
            }
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(releaseListResponse)
                .RespondWithJson(releaseDetail1)
                .RespondWithJson(releaseDetail2);

        // Act
        var result = await _azureProxyService.GetRepositoryServiceDeploymentMetricsSummaryAsync(projectName, releaseDefinitionId, top);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result!.TotalSuccessfulDeployments.Should().Be(3); // 2 + 1
            result.TotalFailedDeployments.Should().Be(2); // 1 + 1
            result.TotalDeployments.Should().Be(5); // 3 + 2
        }

        httpTest.ShouldHaveCalled($"{_azureConfig.ReleaseUrl}/{_azureConfig.Organization}/{projectName}/_apis/release/releases")
            .WithQueryParam("definitionId", releaseDefinitionId)
            .WithQueryParam("$top", top)
            .Times(1);
    }

    [Fact]
    public async Task GetRepositoryServiceDeploymentMetricsSummaryAsync_WithHttpException_ReturnsNull()
    {
        // Arrange
        var projectName = "test-project";
        var releaseDefinitionId = 123;

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal Server Error", 500);

        // Act
        var result = await _azureProxyService.GetRepositoryServiceDeploymentMetricsSummaryAsync(projectName, releaseDefinitionId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetRepositoryServiceDeploymentMetricsSummaryAsync_WithEmptyReleases_ReturnsEmptyMetrics()
    {
        // Arrange
        var projectName = "test-project";
        var releaseDefinitionId = 123;

        var emptyReleaseListResponse = new AzureListResponse<AzureReleaseInfo>
        {
            Count = 0,
            Value = new List<AzureReleaseInfo>()!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(emptyReleaseListResponse);

        // Act
        var result = await _azureProxyService.GetRepositoryServiceDeploymentMetricsSummaryAsync(projectName, releaseDefinitionId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result!.TotalSuccessfulDeployments.Should().Be(0);
            result.TotalFailedDeployments.Should().Be(0);
            result.TotalDeployments.Should().Be(0);
        }
    }

    #endregion

    #region GetRecentReleasesAsync Tests

    [Fact]
    public async Task GetRecentReleasesAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var releaseDefinitionId = 123;
        var top = 3;

        var releaseInfos = new List<AzureReleaseInfo>
        {
            new() { Id = 1, Name = "Release 1" },
            new() { Id = 2, Name = "Release 2" }
        };

        var releaseListResponse = new AzureListResponse<AzureReleaseInfo>
        {
            Count = 2,
            Value = releaseInfos!
        };

        var releaseDetail1 = new AzureReleaseDetail
        {
            Id = 1,
            Name = "Release 1",
            CreatedOn = DateTime.UtcNow.AddDays(-1)
        };

        var releaseDetail2 = new AzureReleaseDetail
        {
            Id = 2,
            Name = "Release 2",
            CreatedOn = DateTime.UtcNow.AddDays(-2)
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(releaseListResponse)
                .RespondWithJson(releaseDetail1)
                .RespondWithJson(releaseDetail2);

        // Act
        var result = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, top);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(2);
            result.Data.First().Id.Should().Be(1);
            result.Data.Last().Id.Should().Be(2);
        }

        httpTest.ShouldHaveCalled($"{_azureConfig.ReleaseUrl}/{_azureConfig.Organization}/{project}/_apis/release/releases")
            .WithQueryParam("definitionId", releaseDefinitionId)
            .WithQueryParam("$top", top)
            .Times(1);
    }

    [Fact]
    public async Task GetRecentReleasesAsync_WithCachedData_ReturnsCachedResponse()
    {
        // Arrange
        var project = "test-project";
        var releaseDefinitionId = 123;
        var top = 5;
        var cacheKey = $"releases:{project}:{releaseDefinitionId}:{top}";

        var cachedReleases = new List<AzureReleaseDetail>
        {
            new() { Id = 999, Name = "Cached Release" }
        };

        _cache.TryGetValue(cacheKey, out Arg.Any<List<AzureReleaseDetail>?>())
            .Returns(x =>
            {
                x[1] = cachedReleases;
                return true;
            });

        using var httpTest = new HttpTest();

        // Act
        var result = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, top);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(1);
            result.Data.First().Id.Should().Be(999);
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    #endregion

    #region GetReleaseDetailAsync Tests

    [Fact]
    public async Task GetReleaseDetailAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var releaseId = 123;

        var expectedReleaseDetail = new AzureReleaseDetail
        {
            Id = releaseId,
            Name = "Test Release",
            CreatedOn = DateTime.UtcNow.AddDays(-1),
            Environments = new List<ReleaseEnvironment>
            {
                new() { Name = "Development", Status = "succeeded" },
                new() { Name = "Production", Status = "succeeded" }
            }
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedReleaseDetail);

        // Act
        var result = await _azureProxyService.GetReleaseDetailAsync(project, releaseId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be(releaseId);
            result.Data.Name.Should().Be("Test Release");
            result.Data.Environments!.Count.Should().Be(2);
        }

        httpTest.ShouldHaveCalled($"{_azureConfig.ReleaseUrl}/{_azureConfig.Organization}/{project}/_apis/release/releases/{releaseId}")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .Times(1);
    }

    [Fact]
    public async Task GetReleaseDetailAsync_WithCachedData_ReturnsCachedResponse()
    {
        // Arrange
        var project = "test-project";
        var releaseId = 123;
        var cacheKey = $"releasedetail:{project}:{releaseId}";

        var cachedReleaseDetail = new AzureReleaseDetail
        {
            Id = releaseId,
            Name = "Cached Release"
        };

        _cache.TryGetValue(cacheKey, out Arg.Any<AzureReleaseDetail?>())
            .Returns(x =>
            {
                x[1] = cachedReleaseDetail;
                return true;
            });

        using var httpTest = new HttpTest();

        // Act
        var result = await _azureProxyService.GetReleaseDetailAsync(project, releaseId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Name.Should().Be("Cached Release");
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    [Fact]
    public async Task GetReleaseDetailAsync_WithHttpException_ReturnsFailedDependencyResponse()
    {
        // Arrange
        var project = "test-project";
        var releaseId = 999;

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Release not found", 404);

        // Act
        var result = await _azureProxyService.GetReleaseDetailAsync(project, releaseId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(424); // FailedDependency
            result.Data.Should().BeNull();
        }
    }

    #endregion

    #region GetPullRequestCreatedTimeByCommitAsync Tests

    [Fact]
    public async Task GetPullRequestCreatedTimeByCommitAsync_WithMatchingCommit_ReturnsCreationTime()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var commitSha = "abc123def456";
        var expectedCreationDate = DateTime.UtcNow.AddDays(-1);

        var pullRequests = new List<AzurePullRequestInfo>
        {
            new()
            {
                PullRequestId = 1,
                CreationDate = expectedCreationDate,
                TargetRefName = "refs/heads/main",
                LastMergeCommit = new CommitRef { CommitId = commitSha }
            },
            new()
            {
                PullRequestId = 2,
                CreationDate = DateTime.UtcNow.AddDays(-2),
                TargetRefName = "refs/heads/feature",
                LastMergeCommit = new CommitRef { CommitId = "different-commit" }
            }
        };

        var azureResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 2,
            Value = pullRequests!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(azureResponse);

        // Act
        var result = await _azureProxyService.GetPullRequestCreatedTimeByCommitAsync(project, repositoryId, commitSha);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data.Should().BeCloseTo(expectedCreationDate, TimeSpan.FromSeconds(1));
        }
    }

    [Fact]
    public async Task GetPullRequestCreatedTimeByCommitAsync_WithCachedData_ReturnsCachedTime()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var commitSha = "abc123def456";
        var cacheKey = $"prtime:{project}:{repositoryId}:{commitSha}";
        var cachedTime = DateTime.UtcNow.AddDays(-1);

        DateTime? _;
        _cache.TryGetValue(cacheKey, out _)
            .Returns(x =>
            {
                x[1] = cachedTime;
                return true;
            });


        using var httpTest = new HttpTest();

        // Act
        var result = await _azureProxyService.GetPullRequestCreatedTimeByCommitAsync(project, repositoryId, commitSha);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().Be(cachedTime);
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    #endregion

    #region CalculateDeploymentMetricsAsync Tests

    [Fact]
    public async Task CalculateDeploymentMetricsAsync_WithValidRequest_ReturnsCalculatedMetrics()
    {
        // Arrange
        var request = new CalculateDeploymentMetricsRequest
        {
            Project = "test-project",
            ReleaseDefinitionId = 123,
            ServiceName = "test-service",
            RepositoryId = "repo-123",
            Top = 5
        };

        var releases = new List<AzureReleaseDetail>
        {
            new()
            {
                Id = 1,
                Name = "Release 1",
                Environments = new List<ReleaseEnvironment>
                {
                    new()
                    {
                        Name = "Production",
                        Status = "succeeded",
                        DeploySteps = new List<DeployStep>
                        {
                            new()
                            {
                                QueuedOn = DateTime.UtcNow.AddHours(-2),
                                ReleaseDeployPhases = new List<ReleaseDeployPhase>
                                {
                                    new()
                                    {
                                        DeploymentJobs = new List<DeploymentJobContainer>
                                        {
                                            new()
                                            {
                                                Tasks = new List<DeploymentTaskItem>
                                                {
                                                    new() { FinishTime = DateTime.UtcNow.AddHours(-1) }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                Artifacts = new List<Artifact>
                {
                    new()
                    {
                        Type = "Build",
                        DefinitionReference = new Dictionary<string, DefinitionReferenceItem>
                        {
                            ["version"] = new() { Id = "build-123" },
                            ["sourceVersion"] = new() { Id = "commit-abc123" }
                        }
                    }
                }
            }
        };

        // Mock the GetRecentReleasesAsync call
        var recentReleasesResponse = releases.ToOkApiResponse();

        // Mock build changes
        var buildChanges = new List<AzureBuildChangeListItemInfo>
        {
            new() { Id = "commit-abc123" }
        };
        var buildChangesResponse = buildChanges.ToOkApiResponse();

        // Mock PR analysis score
        var prAnalysisScore = 0.85m.ToOkApiResponse();

        // Mock post-deployment issue score
        var issueScore = 0.9m.ToOkApiResponse();

        using var httpTest = new HttpTest();

        // Setup responses for the various HTTP calls that will be made
        httpTest.RespondWithJson(new AzureListResponse<AzureReleaseInfo>
            {
                Count = 1,
                Value = new List<AzureReleaseInfo> { new() { Id = 1 } }!
            })
            .RespondWithJson(releases.First()) // GetReleaseDetailAsync
            .RespondWithJson(new AzureListResponse<AzureBuildChangeListItemInfo>
            {
                Count = 1,
                Value = buildChanges!
            }) // GetBuildChangesAsync
            .RespondWithJson(new AzureListResponse<AzurePullRequestInfo>
            {
                Count = 1,
                Value = new List<AzurePullRequestInfo>
                {
                    new()
                    {
                        PullRequestId = 1,
                        TargetRefName = "refs/heads/main",
                        LastMergeCommit = new CommitRef { CommitId = "commit-abc123" },
                        Reviewers = new List<Reviewer>
                        {
                            new() { Vote = 10 },
                            new() { Vote = 10 }
                        }
                    }
                }!
            }) // GetPullRequestsAsync
            .RespondWithJson(new AzureListResponse<AzureWorkItemListItemInfo>
            {
                Count = 1,
                Value = new List<AzureWorkItemListItemInfo>
                {
                    new() { Id = "1" }
                }!
            }) // GetWorkItemsAsync
            .RespondWithJson(new AzureWorkItemQuery
            {
                WorkItems = new List<AzureWorkItemRef>
                {
                    new() { Id = 1 }
                }
            }) // QueryWorkItemsAsync for issue score
            .RespondWithJson(new AzureListResponse<AzureWorkItemDetailsListItemInfo>
            {
                Count = 1,
                Value = new List<AzureWorkItemDetailsListItemInfo>
                {
                    new()
                    {
                        Id = 1,
                        Fields = new AzureWorkItemFields
                        {
                            SystemTitle = "Test Issue",
                            VstsCommonPriority = 1
                        }
                    }
                }!
            }); // Work item details for issue score

        // Act
        var result = await _azureProxyService.CalculateDeploymentMetricsAsync(request);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.SuccessRate.Should().BeGreaterThan(0);
            result.Data.StabilityScore.Should().BeGreaterThan(0);
            result.Data.SpeedScore.Should().BeGreaterOrEqualTo(0);
            result.Data.ProcessComplianceScore.Should().BeGreaterOrEqualTo(0);
            result.Data.IssueSeverityScore.Should().BeGreaterOrEqualTo(0);
        }
    }

    [Fact]
    public async Task CalculateDeploymentMetricsAsync_WithCachedData_ReturnsCachedMetrics()
    {
        // Arrange
        var request = new CalculateDeploymentMetricsRequest
        {
            Project = "test-project",
            ReleaseDefinitionId = 123,
            ServiceName = "test-service",
            RepositoryId = "repo-123",
            Top = 5
        };

        var cacheKey = $"metrics:{request.Project}:{request.ReleaseDefinitionId}:{request.ServiceName}:{request.Top}";
        var cachedMetrics = new DeploymentMetrics
        {
            SuccessRate = 0.95m,
            StabilityScore = 0.90m,
            SpeedScore = 0.85m,
            ProcessComplianceScore = 0.80m,
            IssueSeverityScore = 0.75m
        };

        _cache.TryGetValue(cacheKey, out Arg.Any<DeploymentMetrics?>())
            .Returns(x =>
            {
                x[1] = cachedMetrics;
                return true;
            });

        using var httpTest = new HttpTest();

        // Act
        var result = await _azureProxyService.CalculateDeploymentMetricsAsync(request);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.SuccessRate.Should().Be(0.95m);
            result.Data.StabilityScore.Should().Be(0.90m);
            result.Data.SpeedScore.Should().Be(0.85m);
            result.Data.ProcessComplianceScore.Should().Be(0.80m);
            result.Data.IssueSeverityScore.Should().Be(0.75m);
        }

        httpTest.ShouldNotHaveMadeACall();
    }

    [Fact]
    public async Task CalculateDeploymentMetricsAsync_WithNoReleases_ReturnsEmptyMetrics()
    {
        // Arrange
        var request = new CalculateDeploymentMetricsRequest
        {
            Project = "test-project",
            ReleaseDefinitionId = 123,
            ServiceName = "test-service",
            RepositoryId = "repo-123",
            Top = 5
        };

        var emptyReleases = new List<AzureReleaseDetail>().ToFailedDependencyApiResponse<List<AzureReleaseDetail>>("No releases found");

        using var httpTest = new HttpTest();
        httpTest.RespondWith("No releases found", 404);

        // Act
        var result = await _azureProxyService.CalculateDeploymentMetricsAsync(request);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.SuccessRate.Should().Be(0);
            result.Data.StabilityScore.Should().Be(0);
            result.Data.SpeedScore.Should().Be(0);
            result.Data.ProcessComplianceScore.Should().Be(0);
            result.Data.IssueSeverityScore.Should().Be(0);
        }
    }

    [Fact]
    public async Task CalculateDeploymentMetricsAsync_WithException_ReturnsFailedDependencyResponse()
    {
        // Arrange
        var request = new CalculateDeploymentMetricsRequest
        {
            Project = "test-project",
            ReleaseDefinitionId = 123,
            ServiceName = "test-service",
            RepositoryId = "repo-123",
            Top = 5
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal Server Error", 500);

        // Act
        var result = await _azureProxyService.CalculateDeploymentMetricsAsync(request);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200); // FailedDependency
            result.Data.Should().NotBeNull();
        }
    }

    #endregion

    #region Additional Edge Case Tests

    [Fact]
    public async Task GetPullRequestCreatedTimeByCommitAsync_WithNonMainBranch_ReturnsNull()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var commitSha = "abc123def456";

        var pullRequests = new List<AzurePullRequestInfo>
        {
            new()
            {
                PullRequestId = 1,
                CreationDate = DateTime.UtcNow.AddDays(-1),
                TargetRefName = "refs/heads/feature-branch", // Not main or master
                LastMergeCommit = new CommitRef { CommitId = commitSha }
            }
        };

        var azureResponse = new AzureListResponse<AzurePullRequestInfo>
        {
            Count = 1,
            Value = pullRequests!
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(azureResponse);

        // Act
        var result = await _azureProxyService.GetPullRequestCreatedTimeByCommitAsync(project, repositoryId, commitSha);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetPullRequestCreatedTimeByCommitAsync_WithException_ReturnsFailedDependencyResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var commitSha = "abc123def456";

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal Server Error", 500);

        // Act
        var result = await _azureProxyService.GetPullRequestCreatedTimeByCommitAsync(project, repositoryId, commitSha);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200); // FailedDependency
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetRecentReleasesAsync_WithHttpException_ReturnsFailedDependencyResponse()
    {
        // Arrange
        var project = "test-project";
        var releaseDefinitionId = 123;

        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal Server Error", 500);

        // Act
        var result = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(424); // FailedDependency
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetRecentReleasesAsync_WithNullReleaseIds_FiltersOutNulls()
    {
        // Arrange
        var project = "test-project";
        var releaseDefinitionId = 123;

        var releaseInfos = new List<AzureReleaseInfo>
        {
            new() { Id = 1, Name = "Release 1" },
            new() { Id = null, Name = "Invalid Release" }, // This should be filtered out
            new() { Id = 2, Name = "Release 2" }
        };

        var releaseListResponse = new AzureListResponse<AzureReleaseInfo>
        {
            Count = 3,
            Value = releaseInfos!
        };

        var releaseDetail1 = new AzureReleaseDetail { Id = 1, Name = "Release 1" };
        var releaseDetail2 = new AzureReleaseDetail { Id = 2, Name = "Release 2" };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(releaseListResponse)
                .RespondWithJson(releaseDetail1)
                .RespondWithJson(releaseDetail2);

        // Act
        var result = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(2); // Only valid releases should be returned
            result.Data.All(r => r.Id != null).Should().BeTrue();
        }
    }

    #endregion
}