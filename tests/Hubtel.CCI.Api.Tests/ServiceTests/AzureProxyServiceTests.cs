using System.Net;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Models.Azure;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Providers;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class AzureProxyServiceTests
{
    private readonly AzureProxyService _azureProxyService;
    private readonly IMemoryCache _cache;
    private readonly AzureOrgConfig _azureConfig;

    public AzureProxyServiceTests()
    {
        var logger = Substitute.For<ILogger<AzureProxyService>>();
        _cache = Substitute.For<IMemoryCache>();
        
        var azureConfig = Substitute.For<IOptions<AzureOrgConfig>>();
        azureConfig.Value.Returns(new AzureOrgConfig
        {
            Organization = "test-org",
            AccessToken = "test-token",
            DevUrl = "https://dev.azure.com",
            ReleaseUrl = "https://vsrm.dev.azure.com",
            ApiVersion = "7.0",
            DefaultPageSize = 100,
            RequestTimeoutSeconds = 60,
            WorkItemToken = "work-item-token"
        });
        _azureConfig = azureConfig.Value;
        azureConfig.Value.Returns(new AzureOrgConfig
        {
            Organization = "test-org",
            AccessToken = "test-token",
            DevUrl = "https://dev.azure.com",
            ReleaseUrl = "https://vsrm.dev.azure.com",
            ApiVersion = "7.0",
            DefaultPageSize = 100,
            RequestTimeoutSeconds = 60,
            WorkItemToken = "work-item-token"
        });
        _azureProxyService = new AzureProxyService(azureConfig, logger, _cache);
    }

    #region GetRepositoryDetailAsync Tests

    [Fact]
    public async Task GetRepositoryDetailAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryName = "test-repo";
        var expectedResponse = new AzureRepositoryResponse
        {
            Id = "repo-id-123",
            Name = repositoryName,
            Url = "https://dev.azure.com/test-org/test-project/_git/test-repo",
            Project = new ProjectInfo
            {
                Id = "project-id-123",
                Name = project
            }
        };

        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedResponse);

        // Act
        var result = await _azureProxyService.GetRepositoryDetailAsync(project, repositoryName);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be("repo-id-123");
            result.Data.Name.Should().Be(repositoryName);
        }

        httpTest.ShouldHaveCalled($"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryName}")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($":{_azureConfig.AccessToken}"))}")
            .Times(1);
    }

    [Fact]
    public async Task GetRepositoryDetailAsync_WithCachedData_ReturnsCachedResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryName = "test-repo";
        var cacheKey = $"repo:{project}:{repositoryName}";
        var cachedResponse = new AzureRepositoryResponse
        {
            Id = "cached-repo-id",
            Name = repositoryName
        };
    
        _cache.TryGetValue(cacheKey, out Arg.Any<AzureRepositoryResponse?>())
            .Returns(x =>
            {
                x[1] = cachedResponse;
                return true;
            });
    
        using var httpTest = new HttpTest();
    
        // Act
        var result = await _azureProxyService.GetRepositoryDetailAsync(project, repositoryName);
    
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be("cached-repo-id");
        }
    
        httpTest.ShouldNotHaveMadeACall();
    }
    
    [Fact]
    public async Task GetRepositoryDetailAsync_WithHttpException_ReturnsNotFoundResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryName = "non-existent-repo";
    
        using var httpTest = new HttpTest();
        httpTest.RespondWith("Repository not found", 404);
    
        // Act
        var result = await _azureProxyService.GetRepositoryDetailAsync(project, repositoryName);
    
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(404);
            result.Data.Should().BeNull();
        }
    }
    
    #endregion
    
    #region GetWorkItemsAsync Tests
    
    [Fact]
    public async Task GetWorkItemsAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var pullRequestId = 456;
        var expectedWorkItems = new List<AzureWorkItemListItemInfo>
        {
            new() { Id = "1", Url = "https://dev.azure.com/test-org/_apis/wit/workItems/1" },
            new() { Id = "2", Url = "https://dev.azure.com/test-org/_apis/wit/workItems/2" }
        };
        var expectedResponse = new AzureListResponse<AzureWorkItemListItemInfo>
        {
            Count = 2,
            Value = expectedWorkItems!
        };
    
        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedResponse);
    
        // Act
        var result = await _azureProxyService.GetWorkItemsAsync(project, repositoryId, pullRequestId);
    
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(2);
            result.Data.First().Id.Should().Be("1");
        }
    
        httpTest.ShouldHaveCalled($"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryId}/pullRequests/{pullRequestId}/workitems")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .Times(1);
    }
    
    [Fact]
    public async Task GetWorkItemsAsync_WithCachedData_ReturnsCachedResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var pullRequestId = 456;
        var cacheKey = $"workitems:{project}:{repositoryId}:{pullRequestId}";
        var cachedWorkItems = new List<AzureWorkItemListItemInfo>
        {
            new() { Id = "cached-1", Url = "cached-url" }
        };
    
        _cache.TryGetValue(cacheKey, out Arg.Any<List<AzureWorkItemListItemInfo>?>())
            .Returns(x =>
            {
                x[1] = cachedWorkItems;
                return true;
            });
    
        using var httpTest = new HttpTest();
    
        // Act
        var result = await _azureProxyService.GetWorkItemsAsync(project, repositoryId, pullRequestId);
    
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(1);
            result.Data.First().Id.Should().Be("cached-1");
        }
    
        httpTest.ShouldNotHaveMadeACall();
    }
    
    [Fact]
    public async Task GetWorkItemsAsync_WithHttpException_ReturnsFailedDependencyResponse()
    {
        // Arrange
        var project = "test-project";
        var repositoryId = "repo-123";
        var pullRequestId = 456;
    
        using var httpTest = new HttpTest();
        httpTest.RespondWith("Internal server error", 500);
    
        // Act
        var result = await _azureProxyService.GetWorkItemsAsync(project, repositoryId, pullRequestId);
    
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(424); // FailedDependency
            result.Data.Should().BeNull();
            // result.Data!.Count.Should().Be(0);
        }
    }
    
    #endregion
    
    #region GetBuildChangesAsync Tests
    
    [Fact]
    public async Task GetBuildChangesAsync_WithValidParameters_ReturnsSuccessResponse()
    {
        // Arrange
        var project = "test-project";
        var buildId = "build-123";
        var expectedChanges = new List<AzureBuildChangeListItemInfo>
        {
            new() { Id = "commit-1" },
            new() { Id = "commit-2" }
        };
        var expectedResponse = new AzureListResponse<AzureBuildChangeListItemInfo>
        {
            Count = 2,
            Value = expectedChanges!
        };
    
        using var httpTest = new HttpTest();
        httpTest.RespondWithJson(expectedResponse);
    
        // Act
        var result = await _azureProxyService.GetBuildChangesAsync(project, buildId);
    
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be(200);
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(2);
            result.Data.First().Id.Should().Be("commit-1");
        }
    
        httpTest.ShouldHaveCalled($"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/build/builds/{buildId}/changes")
            .WithQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
            .Times(1);
    }
    
    #endregion
    
    #region ExtractProjectAndRepository Tests
    
    [Theory]
    [InlineData("https://dev.azure.com/hubtel/Back-End/_git/test-repo", "Back-End", "test-repo")]
    [InlineData("https://dev.azure.com/organization/My%20Project/_git/My%20Repo", "My Project", "My Repo")]
    [InlineData("invalid-url", "", "")]
    public void ExtractProjectAndRepository_WithVariousUrls_ReturnsExpectedResults(string azureUrl, string expectedProject, string expectedRepo)
    {
        // Act
        var (project, repository) = _azureProxyService.ExtractProjectAndRepository(azureUrl);
    
        // Assert
        using (new AssertionScope())
        {
            project.Should().Be(expectedProject);
            repository.Should().Be(expectedRepo);
        }
    }
    
    #endregion
}
