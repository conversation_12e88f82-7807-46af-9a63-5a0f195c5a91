using System.Net;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ProductTeamServiceTests
{
    private readonly ProductTeamService _engineerService;
    private readonly DiFixture _fixture;

    public ProductTeamServiceTests()
    {
        _fixture = new DiFixture();
        _engineerService =
            new ProductTeamService(Substitute.For<ILogger<ProductTeamService>>(), _fixture.RepositoryContext,
                _fixture.MainActorService);
    }

    [Fact]
    public async Task GetProductTeamsAsync_Should_Return_PagedResult_When_Filter_Is_Valid()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "test" };

        var data = new[] { new ProductTeam() { Name = "test", Repositories = new(), Members = new() } }.AsQueryable()
            .BuildMock();

        _fixture.ProductTeamRepository.GetQueryable().Returns(data);
        // Act
        var result = await _engineerService.GetProductTeamsAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().NotBeEmpty();
            result.Data?.Results.Count.Should().Be(1);
        }
    }

    [Fact]
    public async Task GetProductTeamAsync_Should_Return_ProductTeam_When_Id_Is_Valid()
    {
        // Arrange
        var id = "validId";


        var data = new[] { new ProductTeam() { Name = "t", Id = id, Repositories = new(), Members = new() } }
            .AsQueryable().BuildMock();

        _fixture.ProductTeamRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
        }
    }
    
    
    [Fact]
    public async Task GetProductTeamsAsync_Should_Return_Results_In_Descending_Order_By_CreatedAt()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10 };

        var older = new ProductTeam
        {
            Name = "Team A",
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            Repositories = new(),
            Members = new()
        };

        var newer = new ProductTeam
        {
            Name = "Team B",
            CreatedAt = DateTime.UtcNow,
            Repositories = new(),
            Members = new()
        };

        var data = new[] { older, newer }.AsQueryable().BuildMock();

        _fixture.ProductTeamRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductTeamsAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().HaveCount(2);
            result.Data?.Results.First().Name.Should().Be("Team B", "because it has the latest CreatedAt and should be first");
        }
    }


    [Fact]
    public async Task AddProductTeamAsync_Should_Add_ProductTeam_When_Request_Is_Valid()
    {
        // Arrange
        var request = new CreateProductTeamRequest
        {
            /* Set properties here */
            Repositories = new(), Members = new()
        };

        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        _fixture.ProductTeamRepository.AddAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>()).Returns(1);

        _fixture.RepositoryRepository.GetQueryable() .Returns(new List<Repository>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());
        
        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        
        // Act
        var result = await _engineerService.AddProductTeamAsync(request, CancellationToken.None);
        
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
        }
    }

    [Fact]
    public async Task AddProductTeamAsync_Should_Fail_To_Add_ProductTeam_When_Product_Name_Already_Exists()
    {
        // Arrange
        var request = new CreateProductTeamRequest
        {
            Name = "Banana",
        };

        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        _fixture.ProductTeamRepository.AddAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _engineerService.AddProductTeamAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
            result.Code.Should().Be((int)HttpStatusCode.BadRequest);
        }
    }

    [Fact]
    public async Task UpdateProductTeamAsync_Should_Update_ProductTeam_When_Id_And_Request_Are_Valid()
    {
        // Arrange
        var id = "validId";
        var request = new UpdateProductTeamRequest
        {
            Repositories = new(), Members = new()
        };

        _fixture.ProductTeamRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((ProductTeam?)new ProductTeam());
        
        _fixture.RepositoryRepository.GetQueryable() .Returns(new List<Repository>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());
        
        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        _fixture.ProductTeamRepository.UpdateAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task UpdateProductTeamAsync_Should_Check_For_Duplicate_When_Name_Changed()
    {
        // Arrange
        var id = "team1";
        var request = new UpdateProductTeamRequest
        {
            Name = "NewName"
        };

        var existingTeam = new ProductTeam
        {
            Id = id,
            Name = "OldName",
            Repositories = new(),
            Members = new()
        };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingTeam);

        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>
            {
                new ProductTeam { Id = "another", Name = "NewName" }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
            result.Code.Should().Be(StatusCodes.Status400BadRequest);
            result.Message.Should().Contain("already exists");
        }
    }
    
    
    [Fact]
    public async Task UpdateProductTeamAsync_Should_Return_NotFound_When_Update_Fails()
    {
        // Arrange
        var id = "team1";
        var request = new UpdateProductTeamRequest { Name = "NewName" };

        var team = new ProductTeam
        {
            Id = id,
            Name = "OldName",
            Repositories = new(),
            Members = new()
        };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(team);

        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>().AsQueryable().BuildMock()); // no duplicate

        _fixture.ProductTeamRepository.UpdateAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>()).Returns(0); // simulate failure

        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>().AsQueryable().BuildMock());
        _fixture.RepositoryRepository.GetQueryable().Returns(new List<Repository>().AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because update count was 0");
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }
    
    
    [Fact]
    public async Task UpdateProductTeamAsync_Should_Skip_DuplicateCheck_When_Name_Unchanged()
    {
        // Arrange
        const string id = "team1";
        const string unchangedName = "SameName";

        var request = new UpdateProductTeamRequest
        {
            Name = unchangedName
        };

        var existingTeam = new ProductTeam
        {
            Id = id,
            Name = unchangedName,
            Repositories = new(),
            Members = new()
        };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(existingTeam);

        // Ensure no duplicate is found (this should not be called if mutation is absent)
        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(Enumerable.Empty<ProductTeam>().AsQueryable().BuildMock());

        _fixture.ProductTeamRepository.UpdateAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>())
            .Returns(1);

        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>().AsQueryable().BuildMock());
        _fixture.RepositoryRepository.GetQueryable().Returns(new List<Repository>().AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue("because name wasn't changed, so no conflict or error");
        }
    }
    
    [Fact]
    public async Task UpdateProductTeamAsync_Should_Return_NotFound_When_Team_Not_Exist()
    {
        // Arrange
        var id = "nonexistentId";
        var request = new UpdateProductTeamRequest { Name = "SomeName" };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns((ProductTeam?)null); // simulate not found

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because the team was not found");
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Message.ToLower().Should().Contain("not found", "ensuring proper failure context");
        }
    }
    
    
    [Fact]
    public async Task UpdateProductTeamAsync_Should_Not_Check_For_Duplicate_Name_When_Name_Has_Not_Changed()
    {
        // Arrange
        const string id = "someId";
        const string name = "SameName";

        var request = new UpdateProductTeamRequest
        {
            Name = name
        };

        var existingProductTeam = new ProductTeam
        {
            Id = id,
            Name = name, // same name to prevent the inner block from running
            Repositories = new(),
            Members = new()
        };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(existingProductTeam);

        _fixture.ProductTeamRepository.UpdateAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }

        // Verify that the duplicate-name check was not even triggered
        _fixture.ProductTeamRepository.Received(0)
            .GetQueryable(); // it should NOT query for duplicate names when name hasn’t changed
    }






    [Fact]
    public async Task DeleteProductTeamAsync_Should_Delete_ProductTeam_When_Id_Is_Valid()
    {
        // Arrange
        var id = "validId";
        var data = new ProductTeam()
        {
            Repositories = new(), Members = new()
        };
        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(data);


        _fixture.ProductTeamRepository.DeleteAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>()).Returns(1);


        // Act
        var result = await _engineerService.DeleteProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
        }
    }
    
    
    [Fact]
    public async Task DeleteProductTeamAsync_Should_Return_NotFound_When_Team_Does_Not_Exist()
    {
        // Arrange
        var id = "nonexistent-team-id";

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns((ProductTeam?)null); // Simulate not found

        // Act
        var result = await _engineerService.DeleteProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because the team doesn't exist");
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Message.ToLower().Should().Contain("not found");
        }
    }
    
    
    [Fact]
    public async Task DeleteProductTeamAsync_Should_Return_NotFound_When_Delete_Count_Is_Zero()
    {
        // Arrange
        var id = "team-id";
        var team = new ProductTeam { Id = id, IsDeleted = false, Repositories = new(), Members = new() };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(team);

        _fixture.ProductTeamRepository.DeleteAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>())
            .Returns(0); // Simulate failure

        // Act
        var result = await _engineerService.DeleteProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because no records were deleted");
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Message.ToLower().Should().Contain("not found");
        }
    }
    
    
    [Fact]
    public async Task DeleteProductTeamAsync_Should_Return_Success_When_Deleted()
    {
        // Arrange
        var id = "valid-team-id";
        var team = new ProductTeam { Id = id, IsDeleted = false, Repositories = new(), Members = new() };

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .Returns(team);

        _fixture.ProductTeamRepository.DeleteAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>())
            .Returns(1); // Simulate successful delete

        // Act
        var result = await _engineerService.DeleteProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue("because the deletion was successful");
            result.Code.Should().Be(StatusCodes.Status200OK);
        }
    }




    [Fact]
    public async Task GetProductTeamsAsync_Should_Return_Empty_When_No_ProductTeams_Exist()
    {
        // Arrange
        var filter = new SearchFilter { PageIndex = 1, PageSize = 10, SearchTerm = "test" };

        var data = new[] { new ProductTeam() { Name = "t", } }.AsQueryable().BuildMock();

        _fixture.ProductTeamRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductTeamsAsync(filter, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.Results.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task GetProductTeamAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";

        var data = new[] { new ProductTeam() { Name = "t", Id = "valid" } }.AsQueryable().BuildMock();

        _fixture.ProductTeamRepository.GetQueryable().Returns(data);

        // Act
        var result = await _engineerService.GetProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task AddProductTeamAsync_Should_Return_Error_When_Request_Is_Invalid()
    {
        // Arrange
        var request = new CreateProductTeamRequest
        {
            /* Set invalid properties here */
        };
        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        _fixture.RepositoryRepository.GetQueryable() .Returns(new List<Repository>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());
        
        _fixture.EngineerRepository.GetQueryable().Returns(new List<Engineer>() { new() { Name = "Banana" } }.AsQueryable().BuildMock());

        // Act
        var result = await _engineerService.AddProductTeamAsync(request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task UpdateProductTeamAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";
        var request = new UpdateProductTeamRequest
        {
            /* Set properties here */
        };

        _fixture.ProductTeamRepository.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((ProductTeam?)null);

        // Act
        var result = await _engineerService.UpdateProductTeamAsync(id, request, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }
    
    
    
    [Fact]
    public async Task UpdateProductTeamRepositoriesAsync_Should_Return_NotFound_When_Team_Does_Not_Exist()
    {
        // Arrange
        const string id = "nonexistent-team-id";

        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>())
            .ReturnsNull();

        // Act
        var result = await _engineerService.UpdateProductTeamRepositoriesAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse("because the team was not found");
            result.Code.Should().Be(StatusCodes.Status404NotFound);
            result.Message.ToLower().Should().Contain("not found");
        }
    }


    [Fact]
    public async Task DeleteProductTeamAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        var id = "invalidId";

        // Act
        var result = await _engineerService.DeleteProductTeamAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
        }
    }


    #region Update Product Team Repositories Async

    [Fact]
    public async Task UpdateProductTeamRepositoriesAsync_Should_Return_Not_Found_Api_Response_When_Product_Team_Is_Not_Found()
    {
        // Arrange
        const string id = "invalidId";
        
        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns((ProductTeam?)null);

        // Act
        var result = await _engineerService.UpdateProductTeamRepositoriesAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeFalse();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }
    
    
    [Fact]
    public async Task UpdateProductTeamRepositoriesAsync_Should_Return_Success_Api_Response_When_Product_Team_Is_Found()
    {
        // Arrange
        const string id = "invalidId";
        
        _fixture.ProductTeamRepository.GetByIdAsync(id, Arg.Any<CancellationToken>()).Returns(new ProductTeam(){Id="1", Status = "Active"});
        _fixture.RepositoryRepository.GetQueryable()
            .Returns(new List<Repository>() { new() { Id= "1",Name = "Banana", Status = "Retired"} }.
                AsQueryable().BuildMock());
        _fixture.ProductTeamRepository.UpdateAsync(Arg.Any<ProductTeam>(), Arg.Any<CancellationToken>()).Returns(1);

        // Act
        var result = await _engineerService.UpdateProductTeamRepositoriesAsync(id, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
            result.Code.Should().Be(StatusCodes.Status200OK);
        }
    }

    #endregion


    #region Update Product Teams Repositories Async

    [Fact]
    public async Task UpdateProductTeamsRepositoriesAsync_Should_Return_Success_Response()
    {
        // Arrange
        _fixture.ProductTeamRepository.GetQueryable()
            .Returns(new List<ProductTeam>()
                { new() { Id = "1", Status = "Active" } }.AsQueryable().BuildMock());
        
        // Act
        var result = await _engineerService.UpdateProductTeamsRepositoriesAsync(CancellationToken.None);
        
        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeTrue();
            result.Code.Should().Be(StatusCodes.Status200OK);
        }
    }

    #endregion


    #region GetProductTeamPublicationAsync

    [Fact]
    public async Task GetProductTeamPublicationAsync_Should_Return_NotFound_When_ProductTeam_Does_Not_Exist()
    {
        // Arrange
        const string id = "nonexistent-team-id";

        _fixture.ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .Returns(new List<ProductTeam>().BuildMock());
        
        var publicationDay = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDay);

        // Act
        var result = await _engineerService.GetProductTeamPublicationAsync(id, publicationWeek, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }
    
    
    [Fact]
    public async Task GetProductTeamPublicationAsync_Should_Return_ProductTeam_Publication_When_ProductTeam_Exists()
    {
        // Arrange
        const string id = "team1";
        const string name = "Test Team";
        var publicationDay = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDay);

        var productTeam = new ProductTeam
        {
            Id = id,
            Name = name,
            Repositories = new Data.Entities.Repositories()
            {
                Items = [
                    new RepositoryItem()
                {
                    Id = "repo1",
                    Name = "Test Repo",
                    Type = "Backend",
                }]
            },
            Members = new()
        };
        
        var cciRepositoryScore = new CciRepositoryScore
        {
            Id = "score1",
            RepositoryId = "repo1",
            FinalAverage = 85,
            PublicationDate = publicationDay,
            PublicationWeek = publicationWeek,
            Bugs = 30,
            CodeSmells = 20,
            Vulnerabilities = 3,
            Coverage = 75,
            DuplicatedLinesDensity = 4.5m,
            NonCommentedLinesOfCode = 13400
        };

        _fixture.ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .Returns(new List<ProductTeam> { productTeam }.BuildMock());
        
        _fixture.RepositoryContext.CciRepositoryScoreRepository.GetQueryable()
            .AsNoTracking()
            .Returns(new List<CciRepositoryScore> { cciRepositoryScore }.BuildMock());
        
        _fixture.RepositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(new List<CciProductsRanking>()
            {
                new CciProductsRanking()
                {
                    PublicationWeek = publicationWeek,
                    PublicationDate = publicationDay,
                    Rankings = new Rankings()
                    {
                        Backend = new List<RankingItem>()
                        {
                            new RankingItem()
                            {
                                ProductTeamId = id,
                                ProductTeamName = name,
                                Status = "Outstanding"
                                
                            }
                        }
                    }
                }
            }.BuildMock());

        // Act
        var result = await _engineerService.GetProductTeamPublicationAsync(id, publicationWeek, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().NotBeNull();
            result.Data?.ProductTeamId.Should().Be(id);
            result.Data?.ProductTeamName.Should().Be(name);
        }
    }
    
    
    [Fact]
    public async Task GetProductTeamPublicationAsync_Should_Return_NotFound_When_Publication_Week_Is_Invalid()
    {
        // Arrange
        const string id = "team1";
        const string name = "Test Team";
        var publicationDay = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDay);

        var productTeam = new ProductTeam
        {
            Id = id,
            Name = name,
            Repositories = new Data.Entities.Repositories()
            {
                Items = [
                    new RepositoryItem()
                    {
                        Id = "repo1",
                        Name = "Test Repo",
                        Type = "Backend",
                    }]
            },
            Members = new()
        };
        
        var cciRepositoryScore = new CciRepositoryScore
        {
            Id = "score1",
            RepositoryId = "repo1",
            FinalAverage = 85,
            PublicationWeek = publicationWeek,
            Bugs = 30,
            CodeSmells = 20,
            Vulnerabilities = 3,
            Coverage = 75,
            DuplicatedLinesDensity = 4.5m,
            NonCommentedLinesOfCode = 13400
        };
        
        
        _fixture.ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .Returns(new List<ProductTeam> { productTeam }.BuildMock());
        
        _fixture.RepositoryContext.CciRepositoryScoreRepository.GetQueryable()
            .AsNoTracking()
            .Returns(new List<CciRepositoryScore> { cciRepositoryScore }.BuildMock());
        
        _fixture.RepositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Returns(new List<CciProductsRanking>()
            {
                new CciProductsRanking()
                {
                    PublicationWeek = publicationWeek,
                    PublicationDate = publicationDay,
                    Rankings = new Rankings()
                    {
                        Backend = new List<RankingItem>()
                        {
                            new RankingItem()
                            {
                                ProductTeamId = id,
                                ProductTeamName = name,
                                Status = "Outstanding"
                                
                            }
                        }
                    }
                }
            }.BuildMock());


        // Act
        var result = await _engineerService.GetProductTeamPublicationAsync(id, publicationWeek, CancellationToken.None);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Data.Should().BeNull();
            result.Code.Should().Be(StatusCodes.Status404NotFound);
        }
    }

    #endregion
}