using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ReportDeliveryRule;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Tests.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Hubtel.CCI.Api.Tests.ServiceTests;

public class ReportDeliveryRuleServiceTests
{
    private readonly ReportDeliveryRuleService _sut;
    private readonly DiFixture _fixture;
    
    public ReportDeliveryRuleServiceTests()
    {
        _fixture = new DiFixture();
        _sut = new ReportDeliveryRuleService(
            Substitute.For<ILogger<ReportDeliveryRuleService>>(),
            _fixture.RepositoryContext);
    }

    #region GetReportDeliveryRuleAsync

    [Fact]
    public async Task GetReportDeliveryRuleAsync_Should_Return_ReportDeliveryRule_When_Id_Is_Valid()
    {
        // Arrange
        const string ruleId = "valid-rule-id";
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>
            {
                new ReportDeliveryRule { Id = ruleId }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _sut.GetReportDeliveryRuleAsync(ruleId);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Id.Should().Be(ruleId);
    }
    
    
    [Fact]
    public async Task GetReportDeliveryRuleAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        const string invalidRuleId = "invalid-rule-id";
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>().AsQueryable().BuildMock());

        // Act
        var result = await _sut.GetReportDeliveryRuleAsync(invalidRuleId);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().BeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }

    #endregion


    #region CreateReportDeliveryRuleAsync

    [Fact]
    public async Task CreateReportDeliveryRuleAsync_Should_Create_ReportDeliveryRule_When_Valid()
    {
        // Arrange
        var createRule = new CreateReportDeliveryRule()
        {
            ProductGroupId = "valid-product-group-id",
            RecipientEmail = "<EMAIL>"
        };

        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>
            {
                new ProductGroup { Id = createRule.ProductGroupId, GroupName = "Valid Product Group" }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.AddAsync(Arg.Any<ReportDeliveryRule>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _sut.CreateReportDeliveryRuleAsync(createRule);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Code.Should().Be(StatusCodes.Status201Created);
    }


    [Fact]
    public async Task
        CreateReportDeliveryRuleAsync_Should_Fail_To_Create_ReportDeliveryRule_When_ProductGroup_Not_Found()
    {
        // Arrange
        var createRule = new CreateReportDeliveryRule()
        {
            ProductGroupId = "invalid-product-group-id",
            RecipientEmail = "<EMAIL>"
        };

        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>
            {
                new ProductGroup { Id = "valid-product-group-id", GroupName = "Valid Product Group" }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _sut.CreateReportDeliveryRuleAsync(createRule);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().BeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }
    
    
    [Fact]
    public async Task
        CreateReportDeliveryRuleAsync_Should_Fail_To_Create_ReportDeliveryRule_When_Report_Fails_To_Create()
    {
        // Arrange
        var createRule = new CreateReportDeliveryRule()
        {
            ProductGroupId = "valid-product-group-id",
            RecipientEmail = "<EMAIL>"
        };

        _fixture.RepositoryContext.ProductGroupRepository.GetQueryable()
            .Returns(new List<ProductGroup>
            {
                new ProductGroup { Id = createRule.ProductGroupId, GroupName = "Valid Product Group" }
            }.AsQueryable().BuildMock());
        
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.AddAsync(Arg.Any<ReportDeliveryRule>(), Arg.Any<CancellationToken>())
            .Returns(0);

        // Act
        var result = await _sut.CreateReportDeliveryRuleAsync(createRule);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().BeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    }

    #endregion

    #region GetReportDeliveryRulesAsync

    [Fact]
    public async Task GetReportDeliveryRulesAsync_Should_Return_All_ReportDeliveryRules()
    {
        // Arrange
        var filter = new SearchFilter() { PageIndex = 1, PageSize = 10, SearchTerm = "" };
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>
            {
                new ReportDeliveryRule { Id = "rule-1" },
                new ReportDeliveryRule { Id = "rule-2" }
            }.AsQueryable().BuildMock());

        // Act
        var result = await _sut.GetReportDeliveryRulesAsync(filter);

        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetReportDeliveryRulesAsync_Should_Return_Results_Based_On_Search_Filter()
    {
        // Arrange
        var filter = new SearchFilter() { PageIndex = 1, PageSize = 10, SearchTerm = "<EMAIL>" };
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>
            {
                new ReportDeliveryRule { Id = "rule-1", RecipientEmail = "<EMAIL>"},
                new ReportDeliveryRule { Id = "rule-2", RecipientEmail = "<EMAIL>"}
            }.AsQueryable().BuildMock());
        
        // Act
        var result = await _sut.GetReportDeliveryRulesAsync(filter);
        
        // Assert
        using var scope = new AssertionScope();
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Results.Should().HaveCount(1);
    }

    #endregion


    #region DeleteReportDeliveryRuleAsync

    [Fact]
    public async Task DeleteReportDeliveryRuleAsync_Should_Delete_ReportDeliveryRule_When_Id_Is_Valid()
    {
        // Arrange
        const string ruleId = "valid-rule-id";
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>
            {
                new ReportDeliveryRule { Id = ruleId }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.ReportDeliveryRuleRepository.DeleteAsync(Arg.Any<ReportDeliveryRule>(), Arg.Any<CancellationToken>())
            .Returns(1);

        // Act
        var result = await _sut.DeleteReportDeliveryRuleAsync(ruleId);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data?.Id.Should().Be(ruleId);
        result.Code.Should().Be(StatusCodes.Status200OK);
    }
    
    [Fact]
    public async Task DeleteReportDeliveryRuleAsync_Should_Return_NotFound_When_Id_Is_Invalid()
    {
        // Arrange
        const string invalidRuleId = "invalid-rule-id";
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>().AsQueryable().BuildMock());

        // Act
        var result = await _sut.DeleteReportDeliveryRuleAsync(invalidRuleId);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().BeNull();
        result.Code.Should().Be(StatusCodes.Status404NotFound);
    }
    
    [Fact]
    public async Task DeleteReportDeliveryRuleAsync_Should_Return_FailedDependency_When_Delete_Fails()
    {
        // Arrange
        const string ruleId = "valid-rule-id";
        _fixture.RepositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .Returns(new List<ReportDeliveryRule>
            {
                new ReportDeliveryRule { Id = ruleId }
            }.AsQueryable().BuildMock());

        _fixture.RepositoryContext.ReportDeliveryRuleRepository.DeleteAsync(Arg.Any<ReportDeliveryRule>(), Arg.Any<CancellationToken>())
            .Returns(0);

        // Act
        var result = await _sut.DeleteReportDeliveryRuleAsync(ruleId);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().BeNull();
        result.Code.Should().Be(StatusCodes.Status424FailedDependency);
    }

    #endregion
}