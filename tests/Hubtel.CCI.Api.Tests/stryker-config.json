{
  "$schema": "https://raw.githubusercontent.com/stryker-mutator/stryker-net/master/docs/stryker-schema.json",
  "stryker-config": {
    "mutate": [
      // "**/Services/Cascaders/EngineerCascader.cs",
      "**/*.cs",
      "!**/*.Actor*.cs",
      "!**/*Dto*.cs",
      "!**/*DTO*.cs",
      "!**/*.Options*.cs",
      "!**/*.Controller*.cs",
      "!**/*.Config*.cs",
      "!**/*.Attribute*.cs",
      "!**/*.Configuration*.cs",
      "!**/*.Validator*.cs",
      "!**/*.Message*.cs",
      "!**/*.Models*.cs",
      "!**/*.ViewModels*.cs",
      "!**/*.Views*.cs",
      "!**/*.ViewModels*.cs",
      "!**/*Views*.cs",
      "!**/*Extensions*.cs",
      "!**/*Handlers*.cs",
      "!**/*Mapping*.cs",
      "!**/*Repository*.cs",
      "!**/*Repositories*.cs",
      "!**/HubtelControllerBase*.cs",
      "!**/Startup.cs",
      "!**/*DbContext*.cs",
      "!**/*Filter*.cs",
      "!**/*Middleware*.cs",
      "!**/Program.cs",
      "!**/*Config*.cs",
      "!**/*Mapping*.cs",
      "!**/*Entities*.cs",
      "!**/Migrations/**/*.cs",
      "!**/Dtos/**/*.cs",
      "!**/Actors/**/*.cs",
      "!**/Controllers/**/*.cs",
      "!**/Helpers/**/*.cs",
      "!**/Handlers/**/*.cs",
      "!**/Filters/**/*.cs",
      "!**/Extensions/**/*.cs",
      "!**/*Constants*.cs",
      "!**/*Component*.cs",
      "!**/*Storage*.cs",
      "!**/*Request*.cs",
      "!**/*Response*.cs",
      "!**/*Setting*.cs",
      "!**/*Dependenc*.cs",
      "!**/*.py",
      "!**/*.cshtml",
      "!**/*Consumer*.cs",
      "!**/*Consumers*.cs",
      "!**/*Job*.cs",
      "!**/*Jobs*.cs",
      "!**/*Worker*.cs",
      "!**/*Workers*.cs",
      "!**/*Trigger*.cs",
      "!**/Data/**/*.cs",
      "!**/Authentication/**/*.cs",
      "!**/Views/**/*.cs",
      "!**/ViewModels/**/*.cs",
      "!**/View/**/*.cs",
      "!**/ViewModel/**/*.cs",
      "!**/Models/*.cs",
      "!**/Models/**/*.cs",
      "!**/Model/*.cs",
      "!**/Model/**/*.cs",
      "!**/Trigger.cs",
      "!**/*Actor*.cs"
    ],
    "ignore-mutations": [
      "string",
      "statement",
      "Initializer",
      "NullCoalescing",
      "Block",
      "Conditional",
      "Update",
      "Arithmetic"
    ],
    "ignore-methods": ["*logger."]
  }
}
