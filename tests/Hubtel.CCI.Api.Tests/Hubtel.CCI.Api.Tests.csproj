<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <IsPackable>true</IsPackable>
        <Optimize>true</Optimize>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.3.2"/>

        <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.1"/>

        <PackageReference Include="NSubstitute" Version="5.1.0"/>

        <PackageReference Include="System.Linq.Async" Version="6.0.1" />
        <PackageReference Include="xunit" Version="2.4.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="3.1.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>

        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0"/>


        <!--<PackageReference Include="Npgsql" Version="7.0.2" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="7.0.3" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Moq.EntityFrameworkCore" Version="6.0.1.4" />-->

        <PackageReference Include="StackExchange.Redis" Version="2.8.0"/>
        <PackageReference Include="Mapster" Version="7.4.0"/>

        <PackageReference Include="Hubtel.Redis.Sdk" Version="8.0.4"/>
        <!--<PackageReference Include="Hubtel.ElasticSearch.Sdk" Version="6.0.1" />-->

        <PackageReference Include="Bogus" Version="34.0.2"/>
        <PackageReference Include="FluentAssertions" Version="6.10.0"/>


    </ItemGroup>

    <ItemGroup>
        <Content Include="appsettings.json">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\Hubtel.CCI.Api\Hubtel.CCI.Api.csproj"/>
    </ItemGroup>

</Project>
