<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <IsPackable>true</IsPackable>
        <Optimize>true</Optimize>
        <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.1" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
        <PackageReference Include="Testcontainers" Version="3.10.0" />
        <PackageReference Include="Testcontainers.Kafka" Version="3.9.0" />
        <PackageReference Include="Testcontainers.PostgreSql" Version="3.9.0" />
        <PackageReference Include="Testcontainers.Redis" Version="3.9.0" />
        <PackageReference Include="FluentAssertions" Version="6.12.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.7" />
        <PackageReference Include="xunit" Version="2.4.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
        <PackageReference Include="NSubstitute" Version="5.1.0" />
        <PackageReference Include="coverlet.collector" Version="6.0.0">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Reqnroll.xUnit" Version="2.3.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\Hubtel.CCI.Api\Hubtel.CCI.Api.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="**/*.feature" />
    </ItemGroup>

</Project>
