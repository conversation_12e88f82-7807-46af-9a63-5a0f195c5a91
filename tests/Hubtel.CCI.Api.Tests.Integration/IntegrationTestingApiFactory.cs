using Hubtel.CCI.Api.Data;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Services.Interfaces;
using Hubtel.Kafka.Producer.Sdk.Extensions;
using Hubtel.Kafka.Producer.Sdk.Options;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NSubstitute;
using Testcontainers.Kafka;
using Testcontainers.PostgreSql;
using Testcontainers.Redis;
using Xunit;
using RedisConfiguration = Testcontainers.Redis.RedisConfiguration;
using Flurl.Http.Testing;

namespace Hubtel.CCI.Api.Tests.Integration;

public class IntegrationTestingApiFactory : WebApplicationFactory<IApiMarker>, IAsyncLifetime
{
    private readonly HttpTest _httpTest;
    private ApplicationDbContext _dbContext;
    private IServiceScope _scope;
    private readonly PostgreSqlContainer _postgreSqlContainer;
    
    public IntegrationTestingApiFactory()
    {
        _postgreSqlContainer = new PostgreSqlBuilder()
            .WithImage("postgres:16")
            .WithPortBinding(5432)
            .WithDatabase("db")
            .WithUsername("postgres")
            .WithPassword("postgres")
            .Build();
        _httpTest = new HttpTest();
        SetupHttpMocks();
        Server.PreserveExecutionContext = true;
        
        
    }

    private void SetupHttpMocks()
    {
        _httpTest
            .ForCallsTo("https://staffportal-boapi.hubtel.com/api/account/lookup/dev-docs/*")
            .RespondWithJson(new
            {
                code = "200",
                message = "Success",
                data = new
                {
                    tokenData = new
                    {
                        email = "<EMAIL>",
                        role = "{\"id\":\"5a37d61c36fe410492e4b27c0ef75971\",\"name\":\"User Care Team & Dev-Docs Executive\",\"description\":\"User Care Team & Dev-Docs Executive\",\"permissions\":[\"dev-docs.read\",\"dev-docs.cci.read\",\"dev-docs.cci.publish\",\"dev-docs.cci.write\",\"dev-docs.cci.delete\"]}",
                        userName = "Whitson"
                    }
                }
            });
    }



    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("Testing");

        var connectionString = _postgreSqlContainer.GetConnectionString();
        builder.ConfigureServices(c =>
        {
            c.RemoveAll(typeof(ApplicationDbContext));
            c.AddDbContext<ApplicationDbContext>(options =>
                options.UseNpgsql(connectionString));

            c.RemoveAll(typeof(KafkaProducerConfig));
            c.RemoveAll(typeof(RedisConfiguration));
        });
    }

    public async Task InitializeAsync()
    {
        _postgreSqlContainer.StartAsync().Wait();
        
        // Create a scope to get the DbContext
        _scope = Services.CreateScope();
        _dbContext = _scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Ensure database is created and migrations are applied
        await _dbContext.Database.EnsureDeletedAsync();
        await _dbContext.Database.MigrateAsync();
    }

    public new async Task DisposeAsync()
    {
        // Clean up database
        await _dbContext.Database.EnsureDeletedAsync();
        await _dbContext.DisposeAsync();

        // Dispose scope
        _scope.Dispose();

        // Stop and dispose container
        await _postgreSqlContainer.StopAsync();
        await _postgreSqlContainer.DisposeAsync();

        _httpTest.Dispose();
        
        await base.DisposeAsync();
    }

    // Helper method to get a clean DbContext for tests
    public ApplicationDbContext GetDbContext()
    {
        return _dbContext;
    }
}