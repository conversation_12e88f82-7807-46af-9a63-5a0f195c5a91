using System.Net;
using System.Net.Http.Json;
using Bogus;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductGroup;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Dtos.Responses.ProductGroup;
using Newtonsoft.Json;
using Xunit;

namespace Hubtel.CCI.Api.Tests.Integration.Tests;

public class ProductGroupsControllerTests : IClassFixture<IntegrationTestingApiFactory>
{
    private readonly HttpClient _client;

    private const string Token =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jQaMDeaVaZcFzy6gNw5424EAf4-UqXdRhjKwzkqKzME";


    private static readonly Faker<Member> MemberFaker = new Faker<Member>()
        .RuleFor(r => r.Name, f => f.Person.FullName)
        .RuleFor(r => r.Id, _ => Guid.NewGuid().ToString("N"))
        .RuleFor(r => r.Email, f => f.Person.Email)
        .RuleFor(e => e.Domain, _ => ["Backend"])
        .RuleFor(e => e.JobLevel, _ => "Team Lead");

    private static readonly Faker<Supervisor> SupervisorFaker = new Faker<Supervisor>()
        .RuleFor(r => r.Name, f => f.Person.FullName)
        .RuleFor(r => r.Id, _ => Guid.NewGuid().ToString("N"))
        .RuleFor(r => r.Email, f => f.Person.Email)
        .RuleFor(e => e.Domain, _ => ["Backend"])
        .RuleFor(e => e.JobLevel, _ => "Team Lead");

    private static readonly Faker<RepositoryItem> RepositoryFaker = new Faker<RepositoryItem>()
        .RuleFor(r => r.Name, f => f.Company.CompanyName())
        .RuleFor(r => r.Description, f => f.Lorem.Sentence())
        .RuleFor(r => r.Url, f => f.Internet.Url())
        .RuleFor(r => r.Type, "Frontend")
        .RuleFor(r => r.Id, _ => Guid.NewGuid().ToString("N"))
        .RuleFor(r => r.SonarQubeKey, f => f.Random.AlphaNumeric(10));


    private static readonly Faker<ProductTeamItemRequest> ProductTeamFaker = new Faker<ProductTeamItemRequest>()
        .RuleFor(r => r.Name, f => f.Company.CompanyName())
        .RuleFor(r => r.Members, _ => MemberFaker.Generate(3))
        .RuleFor(r => r.Repositories, _ => RepositoryFaker.Generate(3));

    private readonly Faker<CreateProductGroupRequest> _productGroupFaker = new Faker<CreateProductGroupRequest>()
        .RuleFor(r => r.GroupName, f => f.Company.CompanyName())
        .RuleFor(r => r.ProductTeams, _ => ProductTeamFaker.Generate(3))
        .RuleFor(r => r.Supervisors, _ => SupervisorFaker.Generate(3));


    public ProductGroupsControllerTests(IntegrationTestingApiFactory applicationFactory)
    {
        _client = applicationFactory.CreateClient();
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Token}");
    }

    // #region GetProductGroup
    //
    // [Fact]
    // public async Task GetProductGroup_Should_Return_NotFound_When_ProductGroup_Does_Not_Exist()
    // {
    //     // Arrange
    //     var productGroupId = "non-existent-product-group-id";
    //
    //     // Act
    //     var productGroup = await _client.GetAsync($"/api/v1/ProductGroups/{productGroupId}");
    //     var response = await productGroup.Content.ReadFromJsonAsync<ApiResponse<GetProductGroupResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productGroup.Should().NotBeNull();
    //         productGroup.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeNull();
    //     }
    // }
    //
    // [Fact]
    // public async Task GetProductGroup_Should_Return_ProductGroup_When_ProductGroup_Exists()
    // {
    //     // Arrange
    //     var createProductGroupRequest = _productGroupFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //     var createdProductGroup =
    //         await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductGroupResponse>>();
    //
    //     // Act
    //     var productGroup = await _client.GetAsync($"/api/v1/ProductGroups/{createdProductGroup?.Data?.Id}");
    //     var response = await productGroup.Content.ReadFromJsonAsync<ApiResponse<GetProductGroupResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productGroup.Should().NotBeNull();
    //         productGroup.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Id.Should().Be(createdProductGroup?.Data?.Id);
    //         response.Data.GroupName.Should().Be(createProductGroupRequest.GroupName);
    //     }
    // }
    //
    // #endregion
    //
    // #region GetProductGroups
    //
    // [Fact]
    // public async Task GetProductGroups_Should_Return_ProductGroups_When_ProductGroups_Exist()
    // {
    //     // Arrange
    //     var createProductGroupRequest = _productGroupFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //     var createdProductGroup =
    //         await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductGroupResponse>>();
    //
    //     // Act
    //     var productGroups = await _client.GetAsync("/api/v1/ProductGroups");
    //     var responseAsString = await productGroups.Content.ReadAsStringAsync();
    //     var response =
    //         JsonConvert.DeserializeObject<ApiResponse<PagedResult<GetProductGroupResponse>>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productGroups.Should().NotBeNull();
    //         productGroups.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Results.Should().NotBeEmpty();
    //         response.Data.Results.Should().Contain(x =>
    //             createdProductGroup != null && createdProductGroup.Data != null && x.Id == createdProductGroup.Data.Id);
    //     }
    // }
    //
    // [Fact]
    // public async Task GetProductGroups_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createProductGroupRequest = _productGroupFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //
    //     // Act
    //     var productGroups = await _client.GetAsync("/api/v1/ProductGroups?pageSize=invalid");
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productGroups.Should().NotBeNull();
    //         productGroups.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region CreateProductGroup
    //
    // [Fact]
    // public async Task CreateProductGroup_Should_Return_Created_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createProductGroupRequest = _productGroupFaker.Generate();
    //
    //     // Act
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //     var responseAsString = await createResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<GetProductGroupResponse>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         createResponse.Should().NotBeNull();
    //         createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.Created);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.GroupName.Should().Be(createProductGroupRequest.GroupName);
    //     }
    // }
    //
    // [Fact]
    // public async Task CreateProductGroup_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createProductGroupRequest = new CreateProductGroupRequest();
    //
    //     // Act
    //     var response = await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         response.Should().NotBeNull();
    //         response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region UpdateProductGroup
    //
    // [Fact]
    // public async Task UpdateProductGroup_Should_Return_Ok_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createProductGroupRequest = _productGroupFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //     var createdProductGroup =
    //         await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductGroupResponse>>();
    //
    //     var updateProductGroupRequest = new UpdateProductGroupRequest
    //     {
    //         GroupName = "Updated Name 290",
    //     };
    //
    //     // Act
    //     var updateResponse = await _client.PutAsJsonAsync($"/api/v1/ProductGroups/{createdProductGroup?.Data?.Id}",
    //         updateProductGroupRequest);
    //     var response = await updateResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updateResponse.Should().NotBeNull();
    //         updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task UpdateProductGroup_Should_Return_NotFound_When_ProductGroup_Does_Not_Exist()
    // {
    //     // Arrange
    //     var updateProductGroupRequest = new UpdateProductGroupRequest
    //     {
    //         GroupName = "Non-existent Product Group",
    //     };
    //
    //     // Act
    //     var updatedResponse = await _client.PutAsJsonAsync($"/api/v1/ProductGroups/non-existent-product-group-id",
    //         updateProductGroupRequest);
    //     var response = await updatedResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updatedResponse.Should().NotBeNull();
    //         updatedResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
    //
    // #region DeleteProductGroup
    //
    // [Fact]
    // public async Task DeleteProductGroup_Should_Return_Ok_When_ProductGroup_Exists()
    // {
    //     // Arrange
    //     var createProductGroupRequest = _productGroupFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductGroups", createProductGroupRequest);
    //     var createdProductGroup =
    //         await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductGroupResponse>>();
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/ProductGroups/{createdProductGroup?.Data?.Id}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task DeleteProductGroup_Should_Return_NotFound_When_ProductGroup_Does_Not_Exist()
    // {
    //     // Arrange
    //     var invalidProductGroupId = "non-existent-product-group-id";
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/ProductGroups/{invalidProductGroupId}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
}