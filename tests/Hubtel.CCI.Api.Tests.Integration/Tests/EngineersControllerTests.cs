using System.Net;
using System.Net.Http.Json;
using Bogus;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Engineer;
using Hubtel.CCI.Api.Dtos.Responses.Engineer;
using Newtonsoft.Json;
using Xunit;

namespace Hubtel.CCI.Api.Tests.Integration.Tests;

public class EngineersControllerTests : IClassFixture<IntegrationTestingApiFactory>
{
    private readonly HttpClient _client;

    private const string Token =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jQaMDeaVaZcFzy6gNw5424EAf4-UqXdRhjKwzkqKzME";


    private readonly Faker<CreateEngineerRequest> _engineerFaker = new Faker<CreateEngineerRequest>()
        .RuleFor(e => e.Name, f => f.Person.FullName)
        .RuleFor(e => e.Email, f => f.Person.Email)
        .RuleFor(e => e.Domain, _ => ["Backend"])
        .RuleFor(e => e.JobLevel, _ => "Team Lead");

    public EngineersControllerTests(IntegrationTestingApiFactory applicationFactory)
    {
        _client = applicationFactory.CreateClient();
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Token}");
    }

    // #region GetEngineer
    //
    // [Fact]
    // public async Task GetEngineer_Should_Return_NotFound_When_Engineer_Does_Not_Exist()
    // {
    //     // Arrange
    //     var engineerId = "non-existent-engineer-id";
    //
    //     // Act
    //     var engineer = await _client.GetAsync($"/api/v1/engineers/{engineerId}");
    //     var response = await engineer.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         engineer.Should().NotBeNull();
    //         engineer.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeNull();
    //     }
    // }
    //
    // [Fact]
    // public async Task GetEngineer_Should_Return_Engineer_When_Engineer_Exists()
    // {
    //     // Arrange
    //     var createEngineerRequest = _engineerFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //     var createdEngineer = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Act
    //     var engineer = await _client.GetAsync($"/api/v1/engineers/{createdEngineer?.Data?.Id}");
    //     var response = await engineer.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         engineer.Should().NotBeNull();
    //         engineer.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Id.Should().Be(createdEngineer?.Data?.Id);
    //         response.Data.Name.Should().Be(createEngineerRequest.Name);
    //         response.Data.Email.Should().Be(createEngineerRequest.Email);
    //     }
    // }
    //
    // #endregion
    //
    // #region GetEngineers
    //
    // [Fact]
    // public async Task GetEngineers_Should_Return_Engineers_When_Engineers_Exist()
    // {
    //     // Arrange
    //     var createEngineerRequest = _engineerFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //     var createdEngineer = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Act
    //     var engineers = await _client.GetAsync("/api/v1/engineers");
    //     var responseAsString = await engineers.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<PagedResult<GetEngineerResponse>>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         engineers.Should().NotBeNull();
    //         engineers.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Results.Should().NotBeEmpty();
    //         response.Data.Results.Should().Contain(x =>
    //             createdEngineer != null && createdEngineer.Data != null && x.Id == createdEngineer.Data.Id);
    //     }
    // }
    //
    // [Fact]
    // public async Task GetEngineers_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createEngineerRequest = _engineerFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //
    //     // Act
    //     var engineers = await _client.GetAsync("/api/v1/engineers?pageSize=invalid");
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         engineers.Should().NotBeNull();
    //         engineers.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region CreateEngineer
    //
    // [Fact]
    // public async Task CreateEngineer_Should_Return_Created_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createEngineerRequest = _engineerFaker.Generate();
    //
    //     // Act
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //     var response = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         createResponse.Should().NotBeNull();
    //         createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.Created);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Name.Should().Be(createEngineerRequest.Name);
    //         response.Data.Email.Should().Be(createEngineerRequest.Email);
    //     }
    // }
    //
    // [Fact]
    // public async Task CreateEngineer_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createEngineerRequest = new CreateEngineerRequest();
    //
    //     // Act
    //     var response = await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         response.Should().NotBeNull();
    //         response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region UpdateEngineer
    //
    // [Fact]
    // public async Task UpdateEngineer_Should_Return_Ok_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createEngineerRequest = _engineerFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //     var createdEngineer = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     var updateEngineerRequest = new UpdateEngineerRequest
    //     {
    //         Name = "Updated Name",
    //     };
    //
    //     // Act
    //     var updateResponse =
    //         await _client.PutAsJsonAsync($"/api/v1/engineers/{createdEngineer?.Data?.Id}", updateEngineerRequest);
    //     var response = await updateResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updateResponse.Should().NotBeNull();
    //         updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task UpdateEngineer_Should_Return_NotFound_When_Engineer_Does_Not_Exist()
    // {
    //     // Arrange
    //     var updateEngineerRequest = new UpdateEngineerRequest
    //     {
    //         Name = "Non-existent Engineer",
    //     };
    //
    //     // Act
    //     var updatedResponse =
    //         await _client.PutAsJsonAsync($"/api/v1/engineers/non-existent-engineer-id", updateEngineerRequest);
    //     var response = await updatedResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updatedResponse.Should().NotBeNull();
    //         updatedResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
    //
    // [Fact]
    // public async Task CreateEngineer_Should_Return_BadRequest_When_Email_Already_Exists()
    // {
    //     // Arrange
    //     var existingEngineerRequest = _engineerFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/engineers", existingEngineerRequest);
    //
    //     var duplicateEngineerRequest = _engineerFaker.Clone()
    //         .RuleFor(e => e.Email, _ => existingEngineerRequest.Email)
    //         .Generate();
    //
    //     // Act
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", duplicateEngineerRequest);
    //     var response = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         createResponse.Should().NotBeNull();
    //         createResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.BadRequest);
    //         response.Message.Should().Be("Engineer with email already exists");
    //     }
    // }
    //
    // [Fact]
    // public async Task UpdateEngineer_Should_Return_BadRequest_When_Email_Already_Exists()
    // {
    //     // Arrange
    //     var existingEngineerRequest = _engineerFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", existingEngineerRequest);
    //     var createdEngineer = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     var anotherEngineerRequest = _engineerFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/engineers", anotherEngineerRequest);
    //
    //     var updateEngineerRequest = new UpdateEngineerRequest
    //     {
    //         Name = "Updated Name",
    //         Email = anotherEngineerRequest.Email, // Duplicate email
    //         Domain = ["Backend"],
    //         JobLevel = "Team Lead"
    //     };
    //
    //     // Act
    //     var updateResponse =
    //         await _client.PutAsJsonAsync($"/api/v1/engineers/{createdEngineer?.Data?.Id}", updateEngineerRequest);
    //     var responseAsString = await updateResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<bool>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updateResponse.Should().NotBeNull();
    //         updateResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.BadRequest);
    //         response.Message.Should().Be("Engineer with email already exists");
    //     }
    // }
    //
    // #region DeleteEngineer
    //
    // [Fact]
    // public async Task DeleteEngineer_Should_Return_Ok_When_Engineer_Exists()
    // {
    //     // Arrange
    //     var createEngineerRequest = _engineerFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/engineers", createEngineerRequest);
    //     var createdEngineer = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/engineers/{createdEngineer?.Data?.Id}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task DeleteEngineer_Should_Return_NotFound_When_Engineer_Does_Not_Exist()
    // {
    //     // Arrange
    //     var invalidEngineerId = "non-existent-engineer-id";
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/engineers/{invalidEngineerId}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
}