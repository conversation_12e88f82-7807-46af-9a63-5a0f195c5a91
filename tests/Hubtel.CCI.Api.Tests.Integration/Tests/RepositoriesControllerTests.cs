using System.Net;
using System.Net.Http.Json;
using Bogus;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Repository;
using Hubtel.CCI.Api.Dtos.Responses.Repository;
using Newtonsoft.Json;
using Xunit;

namespace Hubtel.CCI.Api.Tests.Integration.Tests;

public class RepositoriesControllerTests : IClassFixture<IntegrationTestingApiFactory>
{
    private readonly HttpClient _client;

    private const string Token =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jQaMDeaVaZcFzy6gNw5424EAf4-UqXdRhjKwzkqKzME";


    private readonly Faker<CreateRepositoryRequest> _repositoryFaker = new Faker<CreateRepositoryRequest>()
        .RuleFor(r => r.Name, f => f.Company.CompanyName())
        .RuleFor(r => r.Description, f => f.Lorem.Sentence())
        .RuleFor(r => r.FrameworkUpgradeComputation, 50)
        .RuleFor(r => r.SemanticScoreComputation, 50)
        .RuleFor(r => r.Url, f => f.Internet.Url())
        .RuleFor(r => r.Type, "Frontend")
        .RuleFor(r => r.SonarQubeKey, f => f.Random.AlphaNumeric(10));

    public RepositoriesControllerTests(IntegrationTestingApiFactory applicationFactory)
    {
        _client = applicationFactory.CreateClient();
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Token}");
    }

    // #region GetRepository
    //
    // [Fact]
    // public async Task GetRepository_Should_Return_NotFound_When_Repository_Does_Not_Exist()
    // {
    //     // Arrange
    //     var repositoryId = "non-existent-repository-id";
    //
    //     // Act
    //     var repository = await _client.GetAsync($"/api/v1/repositories/{repositoryId}");
    //     var response = await repository.Content.ReadFromJsonAsync<ApiResponse<GetRepositoryResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         repository.Should().NotBeNull();
    //         repository.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeNull();
    //     }
    // }
    //
    // [Fact]
    // public async Task GetRepository_Should_Return_Repository_When_Repository_Exists()
    // {
    //     // Arrange
    //     var createRepositoryRequest = _repositoryFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //     var createdRepository = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetRepositoryResponse>>();
    //
    //     // Act
    //     var repository = await _client.GetAsync($"/api/v1/repositories/{createdRepository?.Data?.Id}");
    //     var response = await repository.Content.ReadFromJsonAsync<ApiResponse<GetRepositoryResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         repository.Should().NotBeNull();
    //         repository.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Id.Should().Be(createdRepository?.Data?.Id);
    //         response.Data.Name.Should().Be(createRepositoryRequest.Name);
    //         response.Data.Description.Should().Be(createRepositoryRequest.Description);
    //     }
    // }
    //
    // #endregion
    //
    // #region GetRepositories
    //
    // [Fact]
    // public async Task GetRepositories_Should_Return_Repositories_When_Repositories_Exist()
    // {
    //     // Arrange
    //     var createRepositoryRequest = _repositoryFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //     var createdRepository = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetRepositoryResponse>>();
    //
    //     // Act
    //     var repositories = await _client.GetAsync("/api/v1/repositories");
    //     var responseAsString = await repositories.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<PagedResult<GetRepositoryResponse>>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         repositories.Should().NotBeNull();
    //         repositories.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Results.Should().NotBeEmpty();
    //         response.Data.Results.Should().Contain(x =>
    //             createdRepository != null && createdRepository.Data != null && x.Id == createdRepository.Data.Id);
    //     }
    // }
    //
    // [Fact]
    // public async Task GetRepositories_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createRepositoryRequest = _repositoryFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //
    //     // Act
    //     var repositories = await _client.GetAsync("/api/v1/repositories?pageSize=invalid");
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         repositories.Should().NotBeNull();
    //         repositories.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region CreateRepository
    //
    // [Fact]
    // public async Task CreateRepository_Should_Return_Created_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createRepositoryRequest = _repositoryFaker.Generate();
    //
    //     // Act
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //     var responseAsString = await createResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<GetRepositoryResponse>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         createResponse.Should().NotBeNull();
    //         createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.Created);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Name.Should().Be(createRepositoryRequest.Name);
    //         response.Data.Description.Should().Be(createRepositoryRequest.Description);
    //     }
    // }
    //
    // [Fact]
    // public async Task CreateRepository_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createRepositoryRequest = new CreateRepositoryRequest();
    //
    //     // Act
    //     var response = await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         response.Should().NotBeNull();
    //         response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region UpdateRepository
    //
    // [Fact]
    // public async Task UpdateRepository_Should_Return_Ok_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createRepositoryRequest = _repositoryFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //     var createdRepository = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetRepositoryResponse>>();
    //
    //     var updateRepositoryRequest = new UpdateRepositoryRequest
    //     {
    //         Name = "Updated Name",
    //     };
    //
    //     // Act
    //     var updateResponse = await _client.PutAsJsonAsync($"/api/v1/repositories/{createdRepository?.Data?.Id}",
    //         updateRepositoryRequest);
    //     var response = await updateResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updateResponse.Should().NotBeNull();
    //         updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task UpdateRepository_Should_Return_NotFound_When_Repository_Does_Not_Exist()
    // {
    //     // Arrange
    //     var updateRepositoryRequest = new UpdateRepositoryRequest
    //     {
    //         Name = "Non-existent Repository",
    //     };
    //
    //     // Act
    //     var updatedResponse = await _client.PutAsJsonAsync($"/api/v1/repositories/non-existent-repository-id",
    //         updateRepositoryRequest);
    //     var response = await updatedResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updatedResponse.Should().NotBeNull();
    //         updatedResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
    //
    // #region DeleteRepository
    //
    // [Fact]
    // public async Task DeleteRepository_Should_Return_Ok_When_Repository_Exists()
    // {
    //     // Arrange
    //     var createRepositoryRequest = _repositoryFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/repositories", createRepositoryRequest);
    //     var createdRepository = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetRepositoryResponse>>();
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/repositories/{createdRepository?.Data?.Id}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task DeleteRepository_Should_Return_NotFound_When_Repository_Does_Not_Exist()
    // {
    //     // Arrange
    //     var invalidRepositoryId = "non-existent-repository-id";
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/repositories/{invalidRepositoryId}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
}