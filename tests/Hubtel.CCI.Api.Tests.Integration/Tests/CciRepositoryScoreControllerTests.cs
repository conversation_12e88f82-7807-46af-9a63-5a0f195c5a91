using System.Net;
using System.Net.Http.Json;
using Bogus;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Responses.CciRepositoryScore;
using Newtonsoft.Json;
using Xunit;

namespace Hubtel.CCI.Api.Tests.Integration.Tests;

public class CciRepositoryScoreControllerTests : IClassFixture<IntegrationTestingApiFactory>
{
    private readonly HttpClient _client;

    private const string Token =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jQaMDeaVaZcFzy6gNw5424EAf4-UqXdRhjKwzkqKzME";

    private readonly Faker<CreateCciRepositoryScoreRequest> _scoreFaker = new Faker<CreateCciRepositoryScoreRequest>()
        .RuleFor(s => s.RepositorySonarQubeKey, f => f.Random.String2(10))
        .RuleFor(s => s.SonarQubeMetricsAcquired, f => f.Random.Bool())
        .RuleFor(s => s.FinalAverage, f => f.Random.Decimal(0, 100))
        .RuleFor(s => s.Average, f => f.Random.Decimal(0, 100))
        .RuleFor(s => s.Bugs, f => f.Random.Int(0, 100))
        .RuleFor(s => s.RepositoryId, f => f.Random.String2(10));

    public CciRepositoryScoreControllerTests(IntegrationTestingApiFactory applicationFactory)
    {
        _client = applicationFactory.CreateClient();
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Token}");
    }

    // #region GetCciRepositoryScore

    // [Fact]
    // public async Task GetCciRepositoryScore_Should_Return_NotFound_When_Score_Does_Not_Exist()
    // {
    //     // Arrange
    //     var scoreId = "non-existent-score-id";
    //     // Act
    //     var score = await _client.GetAsync($"/api/v1/ccirepositoryscores/{scoreId}");
    //     var responseAsString = await score.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         score.Should().NotBeNull();
    //         score.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeNull();
    //     }
    // }
    //
    // [Fact]
    // public async Task GetCciRepositoryScore_Should_Return_Score_When_Score_Exists()
    // {
    //     // Arrange
    //     var createScoreRequest = _scoreFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //     var createdScoreString = await createResponse.Content.ReadAsStringAsync();
    //     var createdScore =
    //         JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(createdScoreString);
    //     // Act
    //     var score = await _client.GetAsync($"/api/v1/ccirepositoryscores/{createdScore?.Data?.Id}");
    //     var scoreString = await score.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(scoreString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         score.Should().NotBeNull();
    //         score.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Id.Should().Be(createdScore?.Data?.Id);
    //     }
    // }
    //
    // #endregion
    //
    // #region GetCciRepositoryScores
    //
    // [Fact]
    // public async Task GetCciRepositoryScores_Should_Return_Scores_When_Scores_Exist()
    // {
    //     // Arrange
    //     var createScoreRequest = _scoreFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //     var createdScoreString = await createResponse.Content.ReadAsStringAsync();
    //     var createdScore =
    //         JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(createdScoreString);
    //     // Act
    //     var scores = await _client.GetAsync("/api/v1/ccirepositoryscores");
    //     var responseAsString = await scores.Content.ReadAsStringAsync();
    //     var response =
    //         JsonConvert.DeserializeObject<ApiResponse<PagedResult<GetCciRepositoryScoreResponse>>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         scores.Should().NotBeNull();
    //         scores.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Results.Should().NotBeEmpty();
    //         response.Data.Results.Should().Contain(x =>
    //             createdScore != null && createdScore.Data != null && x.Id == createdScore.Data.Id);
    //     }
    // }
    //
    // [Fact]
    // public async Task GetCciRepositoryScores_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createScoreRequest = _scoreFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //
    //     // Act
    //     var scores = await _client.GetAsync("/api/v1/ccirepositoryscores?pageSize=invalid");
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         scores.Should().NotBeNull();
    //         scores.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region CreateCciRepositoryScore
    //
    // [Fact]
    // public async Task CreateCciRepositoryScore_Should_Return_Created_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createScoreRequest = _scoreFaker.Generate();
    //
    //     // Act
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //     var createdScoreString = await createResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(createdScoreString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         createResponse.Should().NotBeNull();
    //         createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.Created);
    //         response.Data.Should().NotBeNull();
    //     }
    // }
    //
    // [Fact]
    // public async Task CreateCciRepositoryScore_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createScoreRequest = new CreateCciRepositoryScoreRequest() { };
    //
    //     // Act
    //     var response = await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         response.Should().NotBeNull();
    //         response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region UpdateCciRepositoryScore
    //
    // [Fact]
    // public async Task UpdateCciRepositoryScore_Should_Return_Ok_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createScoreRequest = _scoreFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //     var createdScoreString = await createResponse.Content.ReadAsStringAsync();
    //     var createdScore =
    //         JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(createdScoreString);
    //
    //     var updateScoreRequest = new UpdateCciRepositoryScoreRequest
    //     {
    //         FinalAverage = 90,
    //     };
    //
    //     // Act
    //     var updateResponse = await _client.PutAsJsonAsync($"/api/v1/ccirepositoryscores/{createdScore?.Data?.Id}",
    //         updateScoreRequest);
    //     var createdScoreString2 = await updateResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<bool>>(createdScoreString2);
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updateResponse.Should().NotBeNull();
    //         updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task UpdateCciRepositoryScore_Should_Return_NotFound_When_Score_Does_Not_Exist()
    // {
    //     // Arrange
    //     var updateScoreRequest = new UpdateCciRepositoryScoreRequest
    //     {
    //         FinalAverage = 90,
    //     };
    //
    //     // Act
    //     var updatedResponse =
    //         await _client.PutAsJsonAsync($"/api/v1/ccirepositoryscores/non-existent-score-id", updateScoreRequest);
    //     var createdScoreString2 = await updatedResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<bool>>(createdScoreString2);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updatedResponse.Should().NotBeNull();
    //         updatedResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
    //
    // #region DeleteCciRepositoryScore
    //
    // [Fact]
    // public async Task DeleteCciRepositoryScore_Should_Return_Ok_When_Score_Exists()
    // {
    //     // Arrange
    //     var createScoreRequest = _scoreFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ccirepositoryscores", createScoreRequest);
    //     var createdScoreString = await createResponse.Content.ReadAsStringAsync();
    //     var createdScore =
    //         JsonConvert.DeserializeObject<ApiResponse<GetCciRepositoryScoreResponse>>(createdScoreString);
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/ccirepositoryscores/{createdScore?.Data?.Id}");
    //     var createdScoreString2 = await deleteResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<bool>>(createdScoreString2);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task DeleteCciRepositoryScore_Should_Return_NotFound_When_Score_Does_Not_Exist()
    // {
    //     // Arrange
    //     var invalidScoreId = "non-existent-score-id";
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/ccirepositoryscores/{invalidScoreId}");
    //     var createdScoreString2 = await deleteResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<bool>>(createdScoreString2);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
}