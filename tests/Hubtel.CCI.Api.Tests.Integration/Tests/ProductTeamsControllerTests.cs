using System.Net;
using System.Net.Http.Json;
using Bogus;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Dtos.Responses.ProductTeam;
using Newtonsoft.Json;
using Xunit;

namespace Hubtel.CCI.Api.Tests.Integration.Tests;

public class ProductTeamsControllerTests : IClassFixture<IntegrationTestingApiFactory>
{
    private readonly HttpClient _client;

    private const string Token =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jQaMDeaVaZcFzy6gNw5424EAf4-UqXdRhjKwzkqKzME";


    private static readonly Faker<Member> MemberFaker = new Faker<Member>()
        .RuleFor(r => r.Name, f => f.Person.FullName)
        .RuleFor(r => r.Id, _ => Guid.NewGuid().ToString("N"))
        .RuleFor(r => r.Email, f => f.Person.Email)
        .RuleFor(e => e.Domain, _ => ["Backend"])
        .RuleFor(e => e.JobLevel, _ => "Team Lead");

    private static readonly Faker<RepositoryItem> RepositoryFaker = new Faker<RepositoryItem>()
        .RuleFor(r => r.Name, f => f.Company.CompanyName())
        .RuleFor(r => r.Description, f => f.Lorem.Sentence())
        .RuleFor(r => r.Url, f => f.Internet.Url())
        .RuleFor(r => r.Type, "Frontend")
        .RuleFor(r => r.Id, _ => Guid.NewGuid().ToString("N"))
        .RuleFor(r => r.SonarQubeKey, f => f.Random.AlphaNumeric(10));

    private readonly Faker<CreateProductTeamRequest> _productTeamFaker = new Faker<CreateProductTeamRequest>()
        .RuleFor(r => r.Name, f => f.Company.CompanyName())
        .RuleFor(r => r.Members, _ => MemberFaker.Generate(3))
        .RuleFor(r => r.Repositories, _ => RepositoryFaker.Generate(3));

    public ProductTeamsControllerTests(IntegrationTestingApiFactory applicationFactory)
    {
        _client = applicationFactory.CreateClient();
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Token}");
    }

    // #region GetProductTeam
    //
    // [Fact]
    // public async Task GetProductTeam_Should_Return_NotFound_When_ProductTeam_Does_Not_Exist()
    // {
    //     // Arrange
    //     var productTeamId = "non-existent-product-team-id";
    //
    //     // Act
    //     var productTeam = await _client.GetAsync($"/api/v1/ProductTeams/{productTeamId}");
    //     var response = await productTeam.Content.ReadFromJsonAsync<ApiResponse<GetProductTeamResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productTeam.Should().NotBeNull();
    //         productTeam.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeNull();
    //     }
    // }
    //
    // [Fact]
    // public async Task GetProductTeam_Should_Return_ProductTeam_When_ProductTeam_Exists()
    // {
    //     // Arrange
    //     var createProductTeamRequest = _productTeamFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //     var createdProductTeam = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductTeamResponse>>();
    //
    //     // Act
    //     var productTeam = await _client.GetAsync($"/api/v1/ProductTeams/{createdProductTeam?.Data?.Id}");
    //     var response = await productTeam.Content.ReadFromJsonAsync<ApiResponse<GetProductTeamResponse>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productTeam.Should().NotBeNull();
    //         productTeam.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Id.Should().Be(createdProductTeam?.Data?.Id);
    //         response.Data.Name.Should().Be(createProductTeamRequest.Name);
    //     }
    // }
    //
    // #endregion
    //
    // #region GetProductTeams
    //
    // [Fact]
    // public async Task GetProductTeams_Should_Return_ProductTeams_When_ProductTeams_Exist()
    // {
    //     // Arrange
    //     var createProductTeamRequest = _productTeamFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //     var createdProductTeam = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductTeamResponse>>();
    //
    //     // Act
    //     var productTeams = await _client.GetAsync("/api/v1/ProductTeams");
    //     var responseAsString = await productTeams.Content.ReadAsStringAsync();
    //     var response =
    //         JsonConvert.DeserializeObject<ApiResponse<PagedResult<GetProductTeamResponse>>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productTeams.Should().NotBeNull();
    //         productTeams.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response?.Code.Should().Be((int)HttpStatusCode.OK);
    //         response?.Data.Should().NotBeNull();
    //         response?.Data?.Results.Should().NotBeEmpty();
    //         response?.Data?.Results.Should().Contain(x =>
    //             createdProductTeam != null && createdProductTeam.Data != null && x.Id == createdProductTeam.Data.Id);
    //     }
    // }
    //
    // [Fact]
    // public async Task GetProductTeams_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createProductTeamRequest = _productTeamFaker.Generate();
    //     await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //
    //     // Act
    //     var productTeams = await _client.GetAsync("/api/v1/ProductTeams?pageSize=invalid");
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         productTeams.Should().NotBeNull();
    //         productTeams.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region CreateProductTeam
    //
    // [Fact]
    // public async Task CreateProductTeam_Should_Return_Created_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createProductTeamRequest = _productTeamFaker.Generate();
    //
    //     // Act
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //     var responseAsString = await createResponse.Content.ReadAsStringAsync();
    //     var response = JsonConvert.DeserializeObject<ApiResponse<GetProductTeamResponse>>(responseAsString);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         createResponse.Should().NotBeNull();
    //         createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.Created);
    //         response.Data.Should().NotBeNull();
    //         response.Data!.Name.Should().Be(createProductTeamRequest.Name);
    //     }
    // }
    //
    // [Fact]
    // public async Task CreateProductTeam_Should_Return_BadRequest_When_Request_Is_Invalid()
    // {
    //     // Arrange
    //     var createProductTeamRequest = new CreateProductTeamRequest();
    //
    //     // Act
    //     var response = await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         response.Should().NotBeNull();
    //         response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    //     }
    // }
    //
    // #endregion
    //
    // #region UpdateProductTeam
    //
    // [Fact]
    // public async Task UpdateProductTeam_Should_Return_Ok_When_Request_Is_Valid()
    // {
    //     // Arrange
    //     var createProductTeamRequest = _productTeamFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //     var createdProductTeam = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductTeamResponse>>();
    //
    //     var updateProductTeamRequest = new UpdateProductTeamRequest
    //     {
    //         Name = "Updated Name 90",
    //     };
    //
    //     // Act
    //     var updateResponse = await _client.PutAsJsonAsync($"/api/v1/ProductTeams/{createdProductTeam?.Data?.Id}",
    //         updateProductTeamRequest);
    //     var response = await updateResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updateResponse.Should().NotBeNull();
    //         updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task UpdateProductTeam_Should_Return_NotFound_When_ProductTeam_Does_Not_Exist()
    // {
    //     // Arrange
    //     var updateProductTeamRequest = new UpdateProductTeamRequest
    //     {
    //         Name = "Non-existent Product Team",
    //     };
    //
    //     // Act
    //     var updatedResponse = await _client.PutAsJsonAsync($"/api/v1/ProductTeams/non-existent-product-team-id",
    //         updateProductTeamRequest);
    //     var response = await updatedResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         updatedResponse.Should().NotBeNull();
    //         updatedResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
    //
    // #region DeleteProductTeam
    //
    // [Fact]
    // public async Task DeleteProductTeam_Should_Return_Ok_When_ProductTeam_Exists()
    // {
    //     // Arrange
    //     var createProductTeamRequest = _productTeamFaker.Generate();
    //     var createResponse = await _client.PostAsJsonAsync("/api/v1/ProductTeams", createProductTeamRequest);
    //     var createdProductTeam = await createResponse.Content.ReadFromJsonAsync<ApiResponse<GetProductTeamResponse>>();
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/ProductTeams/{createdProductTeam?.Data?.Id}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.OK);
    //         response.Data.Should().BeTrue();
    //     }
    // }
    //
    // [Fact]
    // public async Task DeleteProductTeam_Should_Return_NotFound_When_ProductTeam_Does_Not_Exist()
    // {
    //     // Arrange
    //     var invalidProductTeamId = "non-existent-product-team-id";
    //
    //     // Act
    //     var deleteResponse = await _client.DeleteAsync($"/api/v1/ProductTeams/{invalidProductTeamId}");
    //     var response = await deleteResponse.Content.ReadFromJsonAsync<ApiResponse<bool>>();
    //
    //     // Assert
    //     using (new AssertionScope())
    //     {
    //         deleteResponse.Should().NotBeNull();
    //         deleteResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    //         response.Should().NotBeNull();
    //         response!.Code.Should().Be((int)HttpStatusCode.NotFound);
    //         response.Data.Should().BeFalse();
    //     }
    // }
    //
    // #endregion
}