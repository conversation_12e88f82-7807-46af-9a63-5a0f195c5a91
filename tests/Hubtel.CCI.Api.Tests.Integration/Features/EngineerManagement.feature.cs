// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
namespace Hubtel.CCI.Api.Tests.Integration.Features
{
    using Reqnroll;
    using System;
    using System.Linq;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public partial class EngineerManagementFeature : object, Xunit.IClassFixture<EngineerManagementFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new System.Globalization.CultureInfo("en-US"), "Features", "Engineer Management", "  As a user with engineer management permissions\r\n  I want to manage engineers in" +
                " the CCI application\r\n  So that I can maintain the engineering team roster and d" +
                "etails", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "EngineerManagement.feature"
#line hidden
        
        public EngineerManagementFeature(EngineerManagementFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            if (((testRunner.FeatureContext != null) 
                        && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
            {
                await testRunner.OnFeatureEndAsync();
            }
            if ((testRunner.FeatureContext == null))
            {
                await testRunner.OnFeatureStartAsync(featureInfo);
            }
        }
        
        public async System.Threading.Tasks.Task TestTearDownAsync()
        {
            await testRunner.OnScenarioEndAsync();
            global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("I have a valid Hubtel Auth token with engineer management permissions", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("I am authenticated with the CCI application", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            await this.TestInitializeAsync();
        }
        
        async System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Successfully create a new engineer")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Successfully create a new engineer")]
        public async System.Threading.Tasks.Task SuccessfullyCreateANewEngineer()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully create a new engineer", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 11
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table1 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table1.AddRow(new string[] {
                            "John Doe",
                            "<EMAIL>",
                            "Backend",
                            "Team Lead"});
#line 12
    await testRunner.WhenAsync("I create an engineer with the following details:", ((string)(null)), table1, "When ");
#line hidden
#line 15
    await testRunner.ThenAsync("the response status code should be 201", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 16
    await testRunner.AndAsync("the engineer should be created successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 17
    await testRunner.AndAsync("the response should contain the engineer details", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
    await testRunner.AndAsync("the engineer should have a unique identifier", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Attempt to create engineer with existing email")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Attempt to create engineer with existing email")]
        public async System.Threading.Tasks.Task AttemptToCreateEngineerWithExistingEmail()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Attempt to create engineer with existing email", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 20
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 21
    await testRunner.GivenAsync("an engineer exists with email \"<EMAIL>\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
                global::Reqnroll.Table table2 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table2.AddRow(new string[] {
                            "Another Dev",
                            "<EMAIL>",
                            "Frontend",
                            "Engineer"});
#line 22
    await testRunner.WhenAsync("I create an engineer with the following details:", ((string)(null)), table2, "When ");
#line hidden
#line 25
    await testRunner.ThenAsync("the response status code should be 400", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 26
    await testRunner.AndAsync("the response should contain error message \"Engineer with email already exists\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Successfully update engineer details")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Successfully update engineer details")]
        public async System.Threading.Tasks.Task SuccessfullyUpdateEngineerDetails()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully update engineer details", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 28
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table3 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table3.AddRow(new string[] {
                            "Original Name",
                            "<EMAIL>",
                            "Backend",
                            "Engineer"});
#line 29
    await testRunner.GivenAsync("an engineer exists with the following details:", ((string)(null)), table3, "Given ");
#line hidden
                global::Reqnroll.Table table4 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table4.AddRow(new string[] {
                            "Updated Name",
                            "<EMAIL>",
                            "Frontend",
                            "Senior Engineer"});
#line 32
    await testRunner.WhenAsync("I update the engineer with the following details:", ((string)(null)), table4, "When ");
#line hidden
#line 35
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 36
    await testRunner.AndAsync("the engineer details should be updated successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Attempt to update engineer with duplicate email")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Attempt to update engineer with duplicate email")]
        public async System.Threading.Tasks.Task AttemptToUpdateEngineerWithDuplicateEmail()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Attempt to update engineer with duplicate email", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 38
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table5 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table5.AddRow(new string[] {
                            "First Dev",
                            "<EMAIL>",
                            "Backend",
                            "Engineer"});
                table5.AddRow(new string[] {
                            "Second Dev",
                            "<EMAIL>",
                            "Frontend",
                            "Team Lead"});
#line 39
    await testRunner.GivenAsync("the following engineers exist:", ((string)(null)), table5, "Given ");
#line hidden
#line 43
    await testRunner.WhenAsync("I update \"First Dev\" with email \"<EMAIL>\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 44
    await testRunner.ThenAsync("the response status code should be 400", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 45
    await testRunner.AndAsync("the response should contain error message \"Engineer with email already exists\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Successfully delete an engineer")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Successfully delete an engineer")]
        public async System.Threading.Tasks.Task SuccessfullyDeleteAnEngineer()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully delete an engineer", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 47
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 48
    await testRunner.GivenAsync("an engineer exists with email \"<EMAIL>\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 49
    await testRunner.WhenAsync("I delete the engineer", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 50
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 51
    await testRunner.AndAsync("the engineer should be deleted successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Attempt to delete non-existent engineer")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Attempt to delete non-existent engineer")]
        public async System.Threading.Tasks.Task AttemptToDeleteNon_ExistentEngineer()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Attempt to delete non-existent engineer", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 53
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 54
    await testRunner.WhenAsync("I attempt to delete an engineer with id \"non-existent-id\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 55
    await testRunner.ThenAsync("the response status code should be 404", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Successfully retrieve a specific engineer")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Successfully retrieve a specific engineer")]
        public async System.Threading.Tasks.Task SuccessfullyRetrieveASpecificEngineer()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully retrieve a specific engineer", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 57
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 58
    await testRunner.GivenAsync("an engineer exists with email \"<EMAIL>\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 59
    await testRunner.WhenAsync("I request the engineer details", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 60
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 61
    await testRunner.AndAsync("the response should contain the complete engineer details", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Attempt to retrieve non-existent engineer")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Attempt to retrieve non-existent engineer")]
        public async System.Threading.Tasks.Task AttemptToRetrieveNon_ExistentEngineer()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Attempt to retrieve non-existent engineer", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 63
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 64
    await testRunner.WhenAsync("I request engineer details with id \"non-existent-id\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 65
    await testRunner.ThenAsync("the response status code should be 404", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Successfully retrieve all engineers")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Successfully retrieve all engineers")]
        public async System.Threading.Tasks.Task SuccessfullyRetrieveAllEngineers()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully retrieve all engineers", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 67
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 68
    await testRunner.GivenAsync("multiple engineers exist in the system", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 69
    await testRunner.WhenAsync("I request all engineers", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 70
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 71
    await testRunner.AndAsync("the response should contain a paged list of engineers", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 72
    await testRunner.AndAsync("each engineer should have complete details", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Successfully retrieve engineers with pagination")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Successfully retrieve engineers with pagination")]
        public async System.Threading.Tasks.Task SuccessfullyRetrieveEngineersWithPagination()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully retrieve engineers with pagination", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 74
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 75
    await testRunner.GivenAsync("there are more than 10 engineers in the system", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 76
    await testRunner.WhenAsync("I request page 1 with page size 5", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 77
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 78
    await testRunner.AndAsync("the response should contain 5 engineers", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 79
    await testRunner.AndAsync("the response should include pagination metadata", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Filter engineers by domain")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Filter engineers by domain")]
        public async System.Threading.Tasks.Task FilterEngineersByDomain()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Filter engineers by domain", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 81
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table6 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table6.AddRow(new string[] {
                            "Backend Dev",
                            "<EMAIL>",
                            "Backend",
                            "Engineer"});
                table6.AddRow(new string[] {
                            "Frontend Dev",
                            "<EMAIL>",
                            "Frontend",
                            "Engineer"});
#line 82
    await testRunner.GivenAsync("engineers exist with different domains:", ((string)(null)), table6, "Given ");
#line hidden
#line 86
    await testRunner.WhenAsync("I filter engineers by domain \"Backend\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 87
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 88
    await testRunner.AndAsync("the response should only contain Backend engineers", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Filter engineers by job level")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Filter engineers by job level")]
        public async System.Threading.Tasks.Task FilterEngineersByJobLevel()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Filter engineers by job level", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 90
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table7 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Email",
                            "Domain",
                            "JobLevel"});
                table7.AddRow(new string[] {
                            "Team Lead",
                            "<EMAIL>",
                            "Backend",
                            "Team Lead"});
                table7.AddRow(new string[] {
                            "Junior Dev",
                            "<EMAIL>",
                            "Backend",
                            "Engineer"});
#line 91
    await testRunner.GivenAsync("engineers exist with different job levels:", ((string)(null)), table7, "Given ");
#line hidden
#line 95
    await testRunner.WhenAsync("I filter engineers by job level \"Team Lead\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 96
    await testRunner.ThenAsync("the response status code should be 200", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 97
    await testRunner.AndAsync("the response should only contain Team Lead engineers", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Attempt operations without authentication")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Attempt operations without authentication")]
        public async System.Threading.Tasks.Task AttemptOperationsWithoutAuthentication()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Attempt operations without authentication", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 99
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 100
    await testRunner.GivenAsync("I don\'t have an authentication token", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 101
    await testRunner.WhenAsync("I attempt to access the engineers endpoint", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 102
    await testRunner.ThenAsync("the response status code should be 401", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Attempt operations with invalid authentication")]
        [Xunit.TraitAttribute("FeatureTitle", "Engineer Management")]
        [Xunit.TraitAttribute("Description", "Attempt operations with invalid authentication")]
        public async System.Threading.Tasks.Task AttemptOperationsWithInvalidAuthentication()
        {
            string[] tagsOfScenario = ((string[])(null));
            System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Attempt operations with invalid authentication", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 104
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 105
    await testRunner.GivenAsync("I have an invalid authentication token", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 106
    await testRunner.WhenAsync("I attempt to access the engineers endpoint", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 107
    await testRunner.ThenAsync("the response status code should be 401", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
        [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await EngineerManagementFeature.FeatureSetupAsync();
            }
            
            async System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await EngineerManagementFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
