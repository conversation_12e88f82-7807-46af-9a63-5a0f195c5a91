
Feature: Engineer Management
  As a user with engineer management permissions
  I want to manage engineers in the CCI application
  So that I can maintain the engineering team roster and details

  Background:
    Given I have a valid Hubtel Auth token with engineer management permissions
    And I am authenticated with the CCI application

  Scenario: Successfully create a new engineer
    When I create an engineer with the following details:
      | Name           | Email                  | Domain   | JobLevel    |
      | <PERSON>       | <EMAIL>    | Backend  | Team Lead   |
    Then the response status code should be 201
    And the engineer should be created successfully
    And the response should contain the engineer details
    And the engineer should have a unique identifier

  <PERSON><PERSON><PERSON>: Attempt to create engineer with existing email
    Given an engineer exists with email "<EMAIL>"
    When I create an engineer with the following details:
      | Name           | Email                  | Domain   | JobLevel    |
      | Another Dev    | <EMAIL>    | Frontend | Engineer    |
    Then the response status code should be 400
    And the response should contain error message "Engineer with email already exists"

  Scenario: Successfully update engineer details
    Given an engineer exists with the following details:
      | Name           | Email                  | Domain   | JobLevel    |
      | Original Name  | <EMAIL>    | Backend  | Engineer    |
    When I update the engineer with the following details:
      | Name           | Email                  | Domain   | JobLevel        |
      | Updated Name   | <EMAIL>     | Frontend | Senior Engineer |
    Then the response status code should be 200
    And the engineer details should be updated successfully

  Scenario: Attempt to update engineer with duplicate email
    Given the following engineers exist:
      | Name           | Email                  | Domain   | JobLevel    |
      | First Dev      | <EMAIL>      | Backend  | Engineer    |
      | Second Dev     | <EMAIL>     | Frontend | Team Lead   |
    When I update "First Dev" with email "<EMAIL>"
    Then the response status code should be 400
    And the response should contain error message "Engineer with email already exists"

  Scenario: Successfully delete an engineer
    Given an engineer exists with email "<EMAIL>"
    When I delete the engineer
    Then the response status code should be 200
    And the engineer should be deleted successfully

  Scenario: Attempt to delete non-existent engineer
    When I attempt to delete an engineer with id "non-existent-id"
    Then the response status code should be 404

  Scenario: Successfully retrieve a specific engineer
    Given an engineer exists with email "<EMAIL>"
    When I request the engineer details
    Then the response status code should be 200
    And the response should contain the complete engineer details

  Scenario: Attempt to retrieve non-existent engineer
    When I request engineer details with id "non-existent-id"
    Then the response status code should be 404

  Scenario: Successfully retrieve all engineers
    Given multiple engineers exist in the system
    When I request all engineers
    Then the response status code should be 200
    And the response should contain a paged list of engineers
    And each engineer should have complete details

  Scenario: Successfully retrieve engineers with pagination
    Given there are more than 10 engineers in the system
    When I request page 1 with page size 5
    Then the response status code should be 200
    And the response should contain 5 engineers
    And the response should include pagination metadata

  Scenario: Filter engineers by domain
    Given engineers exist with different domains:
      | Name           | Email                  | Domain   | JobLevel    |
      | Backend Dev    | <EMAIL>     | Backend  | Engineer    |
      | Frontend Dev   | <EMAIL>    | Frontend | Engineer    |
    When I filter engineers by domain "Backend"
    Then the response status code should be 200
    And the response should only contain Backend engineers

  Scenario: Filter engineers by job level
    Given engineers exist with different job levels:
      | Name           | Email                  | Domain   | JobLevel      |
      | Team Lead      | <EMAIL>        | Backend  | Team Lead     |
      | Junior Dev     | <EMAIL>      | Backend  | Engineer      |
    When I filter engineers by job level "Team Lead"
    Then the response status code should be 200
    And the response should only contain Team Lead engineers

  Scenario: Attempt operations without authentication
    Given I don't have an authentication token
    When I attempt to access the engineers endpoint
    Then the response status code should be 401

  Scenario: Attempt operations with invalid authentication
    Given I have an invalid authentication token
    When I attempt to access the engineers endpoint
    Then the response status code should be 401
