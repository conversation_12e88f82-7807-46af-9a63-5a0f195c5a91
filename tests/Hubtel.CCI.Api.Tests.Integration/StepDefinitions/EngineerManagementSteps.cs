using System.Net;
using System.Net.Http.Json;
using Bogus;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Engineer;
using Hubtel.CCI.Api.Dtos.Responses.Engineer;
using Newtonsoft.Json;
using Reqnroll;
using Xunit;

namespace Hubtel.CCI.Api.Tests.Integration.StepDefinitions;

[Binding]
public class EngineerManagementSteps : IClassFixture<IntegrationTestingApiFactory>
{
    private readonly HttpClient _client;
    private HttpResponseMessage? _lastResponse;
    private string? _createdEngineerId;
    private const string Token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ccYqPp80X2KVGiUQlb0rJfc_IMTKmjNfdBhdU00-RJs";
    private readonly Dictionary<string, GetEngineerResponse> _existingEngineers = new();
    private readonly Faker<CreateEngineerRequest> _engineerFaker = new Faker<CreateEngineerRequest>()
        .RuleFor(e => e.Name, f => f.Person.FullName)
        .RuleFor(e => e.Email, f => f.Person.Email)
        .RuleFor(e => e.Domain, _ => ["Backend"])
        .RuleFor(e => e.JobLevel, _ => "Team Lead");

    public EngineerManagementSteps(IntegrationTestingApiFactory factory)
    {
        _client = factory.CreateClient();
    }

    #region Background
    [Given(@"I have a valid Hubtel Auth token with engineer management permissions")]
    public void GivenIHaveAValidHubtelAuthTokenWithEngineerManagementPermissions()
    {
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Token}");
    }

    [Given(@"I am authenticated with the CCI application")]
    public void GivenIAmAuthenticatedWithTheCciApplication()
    {
        _client.DefaultRequestHeaders.Should().ContainKey("Authorization");
        
        // make a request to the look
    }
    #endregion
    
    #region Common Steps
    [When(@"I create an engineer with the following details:")]
    public async Task WhenICreateAnEngineerWithTheFollowingDetails(Table table)
    {
        var row = table.Rows[0];
        var request = new CreateEngineerRequest
        {
            Name = row["Name"],
            Email = row["Email"],
            Domain = [row["Domain"]],
            JobLevel = row["JobLevel"]
        };

        _lastResponse = await _client.PostAsJsonAsync("/api/v1/engineers", request);
        
        // read the actual content response from the api
        var content = await _lastResponse.Content.ReadAsStringAsync();
        var response = JsonConvert.DeserializeObject<ApiResponse<GetEngineerResponse>>(content);
        if (_lastResponse.IsSuccessStatusCode)
        {
            _createdEngineerId = response?.Data?.Id;
        }
    }
    [Then(@"the response status code should be (.*)")]
    public void ThenTheResponseStatusCodeShouldBe(int statusCode)
    {
        _lastResponse!.StatusCode.Should().Be((HttpStatusCode)statusCode);
    }
    [Then(@"the response should contain error message ""(.*)""")]
    public async Task ThenTheResponseShouldContainErrorMessage(string errorMessage)
    {
        var content = await _lastResponse!.Content.ReadAsStringAsync();
        var response = JsonConvert.DeserializeObject<ApiResponse<object>>(content);
        response!.Message.Should().Contain(errorMessage);
    }
    [Given(@"an engineer exists with email ""(.*)""")]
    public async Task GivenAnEngineerExistsWithEmail(string email)
    {
        var request = new CreateEngineerRequest
        {
            Name = "Engineer To Delete",
            Email = email,
            Domain = ["Backend"],
            JobLevel = "Engineer"
        };

        var response = await _client.PostAsJsonAsync("/api/v1/engineers", request);
        var engineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
        _existingEngineers[email] = engineer!.Data!;
    }
    [When(@"I attempt to access the engineers endpoint")]
    public async Task WhenIAttemptToAccessTheEngineersEndpoint()
    {
        _lastResponse = await _client.GetAsync("/api/v1/engineers");
    }
    #endregion

    #region Successfully Create New Engineer Scenario
    [Then(@"the engineer should be created successfully")]
    public async Task ThenTheEngineerShouldBeCreatedSuccessfully()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
        response.Should().NotBeNull();
        response!.Data.Should().NotBeNull();
    }

    [Then(@"the response should contain the engineer details")]
    public async Task ThenTheResponseShouldContainTheEngineerDetails()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
        using (new AssertionScope())
        {
            response!.Data.Should().NotBeNull();
            response.Data!.Name.Should().NotBeNullOrEmpty();
            response.Data.Email.Should().NotBeNullOrEmpty();
            response.Data.Domain.Should().NotBeEmpty();
            response.Data.JobLevel.Should().NotBeNullOrEmpty();
        }
    }

    [Then(@"the engineer should have a unique identifier")]
    public async Task ThenTheEngineerShouldHaveAUniqueIdentifier()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
        response!.Data!.Id.Should().NotBeNullOrEmpty();
    }
    #endregion

    #region Attempt Create Engineer With Existing Email Scenario
    
    #endregion

    #region Successfully Update Engineer Details Scenario
    [Given(@"an engineer exists with the following details:")]
    public async Task GivenAnEngineerExistsWithTheFollowingDetails(Table table)
    {
        var row = table.Rows[0];
        var request = new CreateEngineerRequest
        {
            Name = row["Name"],
            Email = row["Email"],
            Domain = [row["Domain"]],
            JobLevel = row["JobLevel"]
        };

        var response = await _client.PostAsJsonAsync("/api/v1/engineers", request);
        var engineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
        _existingEngineers[request.Email] = engineer!.Data!;
    }

    [When(@"I update the engineer with the following details:")]
    public async Task WhenIUpdateTheEngineerWithTheFollowingDetails(Table table)
    {
        var row = table.Rows[0];
        var request = new UpdateEngineerRequest
        {
            Name = row["Name"],
            Email = row["Email"],
            Domain = [row["Domain"]],
            JobLevel = row["JobLevel"]
        };

        var engineerId = _existingEngineers.First().Value.Id;
        _lastResponse = await _client.PutAsJsonAsync($"/api/v1/engineers/{engineerId}", request);
    }

    [Then(@"the engineer details should be updated successfully")]
    public async Task ThenTheEngineerDetailsShouldBeUpdatedSuccessfully()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<bool>>();
        response!.Data.Should().BeTrue();
    }
    #endregion

    #region Attempt Update Engineer With Duplicate Email Scenario
    [Given(@"the following engineers exist:")]
    public async Task GivenTheFollowingEngineersExist(Table table)
    {
        foreach (var row in table.Rows)
        {
            var request = new CreateEngineerRequest
            {
                Name = row["Name"],
                Email = row["Email"],
                Domain = [row["Domain"]],
                JobLevel = row["JobLevel"]
            };

            var response = await _client.PostAsJsonAsync("/api/v1/engineers", request);
            var engineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
            _existingEngineers[row["Name"]] = engineer!.Data!;
        }
    }

    [When(@"I update ""(.*)"" with email ""(.*)""")]
    public async Task WhenIUpdateEngineerWithEmail(string engineerName, string newEmail)
    {
        var engineer = _existingEngineers[engineerName];
        var request = new UpdateEngineerRequest
        {
            Name = engineer.Name,
            Email = newEmail,
            Domain = engineer.Domain,
            JobLevel = engineer.JobLevel
        };

        _lastResponse = await _client.PutAsJsonAsync($"/api/v1/engineers/{engineer.Id}", request);
    }
    #endregion

    #region Successfully Delete Engineer Scenario
    [When(@"I delete the engineer")]
    public async Task WhenIDeleteTheEngineer()
    {
        var engineer = _existingEngineers.First().Value;
        _lastResponse = await _client.DeleteAsync($"/api/v1/engineers/{engineer.Id}");
    }

    [Then(@"the engineer should be deleted successfully")]
    public async Task ThenTheEngineerShouldBeDeletedSuccessfully()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<bool>>();
        using (new AssertionScope())
        {
            response.Should().NotBeNull();
            response!.Data.Should().BeTrue();
            
            // Verify the engineer no longer exists
            var engineer = _existingEngineers.First().Value;
            var getResponse = await _client.GetAsync($"/api/v1/engineers/{engineer.Id}");
            getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }
    }
    #endregion

    #region Attempt Delete Non-existent Engineer Scenario
    [When(@"I attempt to delete an engineer with id ""(.*)""")]
    public async Task WhenIAttemptToDeleteAnEngineerWithId(string engineerId)
    {
        _lastResponse = await _client.DeleteAsync($"/api/v1/engineers/{engineerId}");
    }
    #endregion

    #region Successfully Retrieve Specific Engineer Scenario

    [When(@"I request the engineer details")]
    public async Task WhenIRequestTheEngineerDetails()
    {
        var engineer = _existingEngineers.First().Value;
        _lastResponse = await _client.GetAsync($"/api/v1/engineers/{engineer.Id}");
    }

    [Then(@"the response should contain the complete engineer details")]
    public async Task ThenTheResponseShouldContainTheCompleteEngineerDetails()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
        var originalEngineer = _existingEngineers.First().Value;

        using (new AssertionScope())
        {
            response!.Data.Should().NotBeNull();
            response.Data!.Id.Should().Be(originalEngineer.Id);
            response.Data.Name.Should().Be(originalEngineer.Name);
            response.Data.Email.Should().Be(originalEngineer.Email);
            response.Data.Domain.Should().BeEquivalentTo(originalEngineer.Domain);
            response.Data.JobLevel.Should().Be(originalEngineer.JobLevel);
        }
    }
    #endregion

    #region Attempt Retrieve Non-existent Engineer Scenario
    [When(@"I request engineer details with id ""(.*)""")]
    public async Task WhenIRequestEngineerDetailsWithId(string engineerId)
    {
        _lastResponse = await _client.GetAsync($"/api/v1/engineers/{engineerId}");
    }
    #endregion

    #region Successfully Retrieve All Engineers Scenario
    [Given(@"multiple engineers exist in the system")]
    public async Task GivenMultipleEngineersExistInTheSystem()
    {
        var engineers = _engineerFaker.Generate(3);
        foreach (var engineer in engineers)
        {
            var response = await _client.PostAsJsonAsync("/api/v1/engineers", engineer);
            var createdEngineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
            _existingEngineers[engineer.Email] = createdEngineer!.Data!;
        }
    }

    [When(@"I request all engineers")]
    public async Task WhenIRequestAllEngineers()
    {
        _lastResponse = await _client.GetAsync("/api/v1/engineers");
    }

    [Then(@"the response should contain a paged list of engineers")]
    public async Task ThenTheResponseShouldContainAPagedListOfEngineers()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<PagedResult<GetEngineerResponse>>>();
        using (new AssertionScope())
        {
            response.Should().NotBeNull();
            response!.Data.Should().NotBeNull();
            response.Data!.Results.Should().NotBeEmpty();
            response.Data.TotalCount.Should().BeGreaterThan(0);
        }
    }

    [Then(@"each engineer should have complete details")]
    public async Task ThenEachEngineerShouldHaveCompleteDetails()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<PagedResult<GetEngineerResponse>>>();
        using (new AssertionScope())
        {
            foreach (var engineer in response!.Data!.Results)
            {
                engineer.Id.Should().NotBeNullOrEmpty();
                engineer.Name.Should().NotBeNullOrEmpty();
                engineer.Email.Should().NotBeNullOrEmpty();
                engineer.Domain.Should().NotBeEmpty();
                engineer.JobLevel.Should().NotBeNullOrEmpty();
            }
        }
    }
    #endregion

    #region Successfully Retrieve Engineers With Pagination Scenario
    [Given(@"there are more than (.*) engineers in the system")]
    public async Task GivenThereAreMoreThanEngineersInTheSystem(int count)
    {
        var engineers = _engineerFaker.Generate(count + 2); // Generate more than requested count
        foreach (var engineer in engineers)
        {
            var response = await _client.PostAsJsonAsync("/api/v1/engineers", engineer);
            var createdEngineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
            _existingEngineers[engineer.Email] = createdEngineer!.Data!;
        }
    }

    [When(@"I request page (.*) with page size (.*)")]
    public async Task WhenIRequestPageWithPageSize(int pageNumber, int pageSize)
    {
        _lastResponse = await _client.GetAsync($"/api/v1/engineers?pageIndex={pageNumber}&pageSize={pageSize}");
    }

    [Then(@"the response should contain (.*) engineers")]
    public async Task ThenTheResponseShouldContainEngineers(int expectedCount)
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<PagedResult<GetEngineerResponse>>>();
        response!.Data!.Results.Count.Should().Be(expectedCount);
    }

    [Then(@"the response should include pagination metadata")]
    public async Task ThenTheResponseShouldIncludePaginationMetadata()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<PagedResult<GetEngineerResponse>>>();
        using (new AssertionScope())
        {
            response!.Data.Should().NotBeNull();
            response.Data!.PageIndex.Should().Be(1);
            response.Data.PageSize.Should().Be(5);
            response.Data.TotalCount.Should().BeGreaterThan(10);
            response.Data.TotalPages.Should().BeGreaterThan(2);
        }
    }
    #endregion

    #region Filter Engineers By Domain Scenario
    [Given(@"engineers exist with different domains:")]
    public async Task GivenEngineersExistWithDifferentDomains(Table table)
    {
        foreach (var row in table.Rows)
        {
            var request = new CreateEngineerRequest
            {
                Name = row["Name"],
                Email = row["Email"],
                Domain = [row["Domain"]],
                JobLevel = row["JobLevel"]
            };

            var response = await _client.PostAsJsonAsync("/api/v1/engineers", request);
            var engineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
            _existingEngineers[row["Email"]] = engineer!.Data!;
        }
    }

    [When(@"I filter engineers by domain ""(.*)""")]
    public async Task WhenIFilterEngineersByDomain(string domain)
    {
        _lastResponse = await _client.GetAsync($"/api/v1/engineers?domain={domain}");
    }

    [Then(@"the response should only contain Backend engineers")]
    public async Task ThenTheResponseShouldOnlyContainBackendEngineers()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<PagedResult<GetEngineerResponse>>>();
        using (new AssertionScope())
        {
            response!.Data.Should().NotBeNull();
            response.Data!.Results.Should().NotBeEmpty();
            response.Data.Results.Should().AllSatisfy(engineer => 
                engineer.Domain.Should().Contain("Backend"));
        }
    }
    #endregion

    #region Filter Engineers By Job Level Scenario
    [Given(@"engineers exist with different job levels:")]
    public async Task GivenEngineersExistWithDifferentJobLevels(Table table)
    {
        foreach (var row in table.Rows)
        {
            var request = new CreateEngineerRequest
            {
                Name = row["Name"],
                Email = row["Email"],
                Domain = [row["Domain"]],
                JobLevel = row["JobLevel"]
            };

            var response = await _client.PostAsJsonAsync("/api/v1/engineers", request);
            var engineer = await response.Content.ReadFromJsonAsync<ApiResponse<GetEngineerResponse>>();
            _existingEngineers[row["Email"]] = engineer!.Data!;
        }
    }

    [When(@"I filter engineers by job level ""(.*)""")]
    public async Task WhenIFilterEngineersByJobLevel(string jobLevel)
    {
        _lastResponse = await _client.GetAsync($"/api/v1/engineers?jobLevel={jobLevel}");
    }

    [Then(@"the response should only contain Team Lead engineers")]
    public async Task ThenTheResponseShouldOnlyContainTeamLeadEngineers()
    {
        var response = await _lastResponse!.Content.ReadFromJsonAsync<ApiResponse<PagedResult<GetEngineerResponse>>>();
        using (new AssertionScope())
        {
            response!.Data.Should().NotBeNull();
            response.Data!.Results.Should().NotBeEmpty();
            response.Data.Results.Should().AllSatisfy(engineer => 
                engineer.JobLevel.Should().Be("Team Lead"));
        }
    }
    #endregion

    #region Attempt Operations Without Authentication Scenario
    [Given(@"I don't have an authentication token")]
    public void GivenIDontHaveAnAuthenticationToken()
    {
        _client.DefaultRequestHeaders.Remove("Authorization");
    }
    #endregion

    #region Attempt Operations With Invalid Authentication Scenario
    [Given(@"I have an invalid authentication token")]
    public void GivenIHaveAnInvalidAuthenticationToken()
    {
        _client.DefaultRequestHeaders.Remove("Authorization");
        _client.DefaultRequestHeaders.Add("Authorization", "Bearer invalid_token_here");
    }
    #endregion

}
