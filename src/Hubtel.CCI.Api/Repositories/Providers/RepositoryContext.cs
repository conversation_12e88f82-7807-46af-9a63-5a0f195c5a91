using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Data;

namespace Hubtel.CCI.Api.Repositories.Providers;

/// <summary>
/// Provides a context for accessing repositories in the application.
/// </summary>
public class RepositoryContext : IRepositoryContext
{
    /// <summary>
    ///     Gets the repository for accessing CciProductsRankingRepository entities.
    /// </summary>
    public IBaseRepository<CciProductsRanking> CciProductsRankingRepository { get; init; }

    /// <summary>
    ///     Gets the repository for accessing CciRepositoryScore entities.
    /// </summary>
    public IBaseRepository<CciRepositoryScore> CciRepositoryScoreRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing Engineer entities.
    /// </summary>
    public IBaseRepository<Engineer> EngineerRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing ProductTeam entities.
    /// </summary>
    public IBaseRepository<ProductTeam> ProductTeamRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing ProductGroup entities.
    /// </summary>
    public IBaseRepository<ProductGroup> ProductGroupRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing Repository entities.
    /// </summary>
    public IBaseRepository<Repository> RepositoryRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing CciRepositoryScore entities.
    /// </summary>
    public IBaseRepository<CciProductTeamsScoreTrend> CciProductTeamsScoreTrendRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing CciRepositoryScoreStatistic entities.
    /// </summary>
    public IBaseRepository<CciRepositoryScoreStatistic> CciRepositoryScoreStatisticRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing CciRoadMap entities.
    /// </summary>
    public IBaseRepository<CciRoadMap> CciRoadMapRepository { get; init; }
    
    /// <summary>
    ///     Gets the repository for accessing CciRoadMapMetricUpdateRecords entities.
    /// </summary>
    public IBaseRepository<CciRoadMapMetricUpdateRecord> CciRoadMapMetricUpdateRecordRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing the BypassedPr entities.
    /// </summary>
    public IBaseRepository<BypassedPr> BypassedPrRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing the RepositorySyncLogBp entities.
    /// </summary>
    public IBaseRepository<RepositorySyncLogBp> RepositorySyncLogBpRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing the EngineerSonarQubeMetrics entities.
    /// </summary>
    public IBaseRepository<EngineerSonarQubeMetrics> EngineerSonarQubeMetricsRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing the CciEngineersScoreStatistic entities.
    /// </summary>
    public IBaseRepository<CciEngineerScore> CciEngineerScoreRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing the CciEngineersScoreStatistic entities.
    /// </summary>
    public IBaseRepository<CciEngineersScoreStatistic> CciEngineersScoreStatisticRepository { get; init; }

    public IBaseRepository<CciRoadMapRecord> CciRoadMapRecordRepository { get; init; }
    public IBaseRepository<AzureReleaseDefinition> AzureReleaseDefinitionRepository { get; init; }
    public IBaseRepository<AzureReleaseDefinitionDeployment> AzureReleaseDefinitionDeploymentRepository { get; init; }
    public IBaseRepository<CciEngineerRanking> CciEngineerRankingRepository { get; init; }
    public IBaseRepository<RepositoryLanguageDistribution> RepositoryLanguageDistributionRepository { get; init; }
    
    public IBaseRepository<RepositoryLanguageStatistic> RepositoryLanguageStatisticRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing the Service entities.
    /// </summary>
    public IBaseRepository<Service> ServiceRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing the DCIServiceScore entities.
    /// </summary>
    public IBaseRepository<DciServiceScore> DciServiceScoreRepository { get; init; }

    public IBaseRepository<ServiceStatistic> ServiceStatisticRepository { get; init; }

    public IBaseRepository<MsTeamsBotToken> MsTeamsBotTokenRepository
    {
        get; init; }

    public IBaseRepository<DeploymentRequest> DeploymentRequestRepository { get; init; }
    public IBaseRepository<ServiceDeploymentTrail> ServiceDeploymentTrailRepository { get; init; }
    public IBaseRepository<ReportDeliveryRule> ReportDeliveryRuleRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing the DciProductsRanking entities.
    /// </summary>
    public IBaseRepository<DciProductsRanking> DciProductsRankingRepository { get; init; }
    public IBaseRepository<IssueTracker> IssueTrackerRepository { get; init; }
    public IBaseRepository<ToolingTool> ToolingToolsRepository { get; init; }
    public IBaseRepository<ToolingReport> ToolingReportRepository { get; init; }


    /// <summary>
    /// Initializes a new instance of the RepositoryContext class.
    /// </summary>
    /// <param name="dbContext">The application's database context.</param>
    public RepositoryContext(ApplicationDbContext dbContext)
    {
        EngineerRepository = new BaseRepository<Engineer>(dbContext);
        ProductGroupRepository = new BaseRepository<ProductGroup>(dbContext);
        ProductTeamRepository = new BaseRepository<ProductTeam>(dbContext);
        RepositoryRepository = new BaseRepository<Repository>(dbContext);
        CciRepositoryScoreRepository = new BaseRepository<CciRepositoryScore>(dbContext);
        CciProductsRankingRepository = new BaseRepository<CciProductsRanking>(dbContext);
        CciProductTeamsScoreTrendRepository = new BaseRepository<CciProductTeamsScoreTrend>(dbContext);
        CciRepositoryScoreStatisticRepository = new BaseRepository<CciRepositoryScoreStatistic>(dbContext);
        CciRoadMapRepository = new BaseRepository<CciRoadMap>(dbContext);
        CciRoadMapMetricUpdateRecordRepository = new BaseRepository<CciRoadMapMetricUpdateRecord>(dbContext);
        BypassedPrRepository = new BaseRepository<BypassedPr>(dbContext);
        RepositorySyncLogBpRepository = new BaseRepository<RepositorySyncLogBp>(dbContext);
        EngineerSonarQubeMetricsRepository = new BaseRepository<EngineerSonarQubeMetrics>(dbContext);
        CciEngineerScoreRepository = new BaseRepository<CciEngineerScore>(dbContext);
        CciEngineersScoreStatisticRepository = new BaseRepository<CciEngineersScoreStatistic>(dbContext);
        CciRoadMapRecordRepository = new BaseRepository<CciRoadMapRecord>(dbContext);
        AzureReleaseDefinitionRepository = new BaseRepository<AzureReleaseDefinition>(dbContext);
        AzureReleaseDefinitionDeploymentRepository = new BaseRepository<AzureReleaseDefinitionDeployment>(dbContext);
        CciEngineerRankingRepository = new BaseRepository<CciEngineerRanking>(dbContext);
        RepositoryLanguageDistributionRepository = new BaseRepository<RepositoryLanguageDistribution>(dbContext);
        RepositoryLanguageStatisticRepository = new BaseRepository<RepositoryLanguageStatistic>(dbContext);
        MsTeamsBotTokenRepository = new BaseRepository<MsTeamsBotToken>(dbContext);
        ServiceRepository = new BaseRepository<Service>(dbContext);
        DeploymentRequestRepository = new BaseRepository<DeploymentRequest>(dbContext);
        ReportDeliveryRuleRepository = new BaseRepository<ReportDeliveryRule>(dbContext);
        ServiceDeploymentTrailRepository = new BaseRepository<ServiceDeploymentTrail>(dbContext);
        DciServiceScoreRepository = new BaseRepository<DciServiceScore>(dbContext);
        DciProductsRankingRepository = new BaseRepository<DciProductsRanking>(dbContext);
        ServiceStatisticRepository = new BaseRepository<ServiceStatistic>(dbContext);
        IssueTrackerRepository = new BaseRepository<IssueTracker>(dbContext);
        ToolingToolsRepository = new BaseRepository<ToolingTool>(dbContext);
        ToolingReportRepository = new BaseRepository<ToolingReport>(dbContext);
    }

    /// <summary>
    /// Asynchronously retrieves a list of product groups associated with a specific product team.
    /// </summary>
    /// <param name="productTeamId">The ID of the product team.</param>
    /// <returns>A list of product groups associated with the specified product team.</returns>
    public async Task<List<ProductGroup>> GetProductGroupsForProductTeamAsync(string productTeamId)
    {
        var productTeamFilterJson = $"{{\"Items\": [{{\"Id\": \"{productTeamId}\"}}]}}";

        return await ProductGroupRepository.GetQueryable()
            .Where(pg =>
                EF.Functions.JsonContains(
                    pg.ProductTeams,
                    productTeamFilterJson
                )
            )
            .ToListAsync();
    }
    /// <summary>
    /// Asynchronously retrieves a list of product teams associated with a specific engineer.
    /// </summary>
    /// <param name="engineerId">The ID of the engineer.</param>
    /// <returns>A list of product teams associated with the specified engineer.</returns>
    public async Task<List<ProductTeam>> GetProductTeamsForEngineerAsync(string engineerId)
    {
        var memberFilterJson = $"{{\"Items\": [{{\"Id\": \"{engineerId}\"}}]}}";

        return await ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .Where(pt =>
                EF.Functions.JsonContains(
                    pt.Members,
                    memberFilterJson
                )
            )
            .ToListAsync();
    }

    /// <summary>
    /// Asynchronously retrieves a list of product teams associated with a specific repository.
    /// </summary>
    /// <param name="repositoryId">The ID of the repository.</param>
    /// <returns>A list of product teams associated with the specified repository.</returns>
    public async Task<List<ProductTeam>> GetProductTeamsForRepositoryAsync(string repositoryId)
    {
        var repoFilterJson = $"{{\"Items\": [{{\"Id\": \"{repositoryId}\"}}]}}";

        return await ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .Where(pt =>
                EF.Functions.JsonContains(
                    pt.Repositories,
                    repoFilterJson
                )
            )
            .ToListAsync();
    }

    /// <summary>
    /// Asynchronously retrieves a list of product groups associated with a specific repository.
    /// </summary>
    /// <param name="repositoryId">The ID of the repository.</param>
    /// <returns>A list of product groups associated with the specified repository.</returns>
    public async Task<List<ProductGroup>> GetProductGroupsForRepositoryAsync(string repositoryId)
    {
        var repositoryFilterJson =
            $"{{\"Items\": [{{\"Repositories\": {{\"Items\": [{{\"Id\": \"{repositoryId}\"}}]}}}}]}}";

        return await ProductGroupRepository.GetQueryable()
            .AsNoTracking()
            .Where(pg =>
                EF.Functions.JsonContains(
                    pg.ProductTeams,
                    repositoryFilterJson
                )
            )
            .ToListAsync();
    }

    /// <summary>
    /// Asynchronously retrieves a list of product groups associated with a specific engineer.
    /// </summary>
    /// <param name="engineerId">The ID of the engineer.</param>
    /// <returns>A list of product groups associated with the specified engineer.</returns>
    public async Task<List<ProductGroup>> GetProductGroupsForEngineerAsync(string engineerId)
    {
        var supervisorFilterJson = $"{{\"Items\": [{{\"Id\": \"{engineerId}\"}}]}}";
        var memberFilterJson = $"{{\"Items\": [{{\"Members\": {{\"Items\": [{{\"Id\": \"{engineerId}\"}}]}}}}]}}";

        return await ProductGroupRepository.GetQueryable()
            .AsNoTracking()
            .Where(pg =>
                EF.Functions.JsonContains(
                    pg.Supervisors,
                    supervisorFilterJson
                ) ||
                EF.Functions.JsonContains(
                    pg.ProductTeams,
                    memberFilterJson
                )
            )
            .ToListAsync();
    }


    /// <summary>
    /// Asynchronously gets the product group by repository ID.
    /// </summary>
    /// <param name="repositoryId">Optional repository key</param>
    /// <param name="sonarqubeKey">Optional sonarqube key.</param>
    /// <param name="repositoryItem">The repository item.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>The details of the product group.</returns>
    public async Task<ProductGroupDetails?> GetProductGroupByRepositoryFilterAsync(string repositoryId,
        string sonarqubeKey, Repository repositoryItem, CancellationToken ct = default)
    {
        var repositoryFilterJson = !string.IsNullOrEmpty(repositoryId)
            ? $"{{\"Items\": [{{\"Repositories\": {{\"Items\": [{{\"Id\": \"{repositoryId}\"}}]}}}}]}}"
            : $"{{\"Items\": [{{\"Repositories\": {{\"Items\": [{{\"SonarQubeKey\": \"{sonarqubeKey}\"}}]}}}}]}}";
        var productGroup = await ProductGroupRepository.GetQueryable()
            .AsNoTracking()
            .Where(pg => EF.Functions.JsonContains(pg.ProductTeams, repositoryFilterJson))
            .Select(pg => new ProductGroupDetails
            {
                ProductGroupId = pg.Id,
                ProductGroupName = pg.GroupName,
                ProductTeams = pg.ProductTeams,
                RepositoryDetails = new RepositoryDetails
                {
                    Url = repositoryItem.Url,
                    Type = repositoryItem.Type,
                    SemanticScoreComputation = repositoryItem.SemanticScoreComputation,
                    FrameworkUpgradeComputation = repositoryItem.FrameworkUpgradeComputation
                }
            })
            .FirstOrDefaultAsync(ct);
        return productGroup;
    }
}