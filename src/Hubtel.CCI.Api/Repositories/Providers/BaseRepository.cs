using System.Linq.Expressions;
using Hubtel.CCI.Api.Data;
using Hubtel.CCI.Api.Data.Entities.Common;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Repositories.Providers;

/// <summary>
/// Represents a base repository for performing CRUD operations.
/// </summary>
/// <typeparam name="T">The type of object this repository works with.</typeparam>
public class BaseRepository<T> : IBaseRepository<T>
    where T : BaseModel
{
    /// <summary>
    /// The set of objects of type T in the database.
    /// </summary>
    private readonly DbSet<T> _dbSet;

    /// <summary>
    /// The application's database context.
    /// </summary>
    private readonly ApplicationDbContext _dbContext;

    /// <summary>
    /// Initializes a new instance of the BaseRepository class.
    /// </summary>
    /// <param name="dbContext">The application's database context.</param>
    public BaseRepository(ApplicationDbContext dbContext)
    {
        _dbSet = dbContext.Set<T>();
        _dbContext = dbContext;
    }

    /// <summary>
    /// Retrieves an entity of type T by its ID.
    /// </summary>
    public async Task<T?> GetByIdAsync(string id, CancellationToken ct = default)
    {
        return await _dbSet.FindAsync(new object?[] { id, ct }, cancellationToken: ct);
    }

    /// <summary>
    /// Retrieves all entities of type T.
    /// </summary>
    public async Task<IEnumerable<T>> GetAllAsync(CancellationToken ct = default)
    {
        return await _dbSet.ToListAsync(ct);
    }

    /// <summary>
    /// Finds entities of type T based on a predicate.
    /// </summary>
    public async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken ct = default)
    {
        return await _dbSet.Where(predicate).ToListAsync(ct);
    }

    /// <summary>
    /// Finds a single entity of type T based on a predicate.
    /// </summary>
    public async Task<T?> FindOneAsync(Expression<Func<T, bool>> predicate, CancellationToken ct = default)
    {
        return await _dbSet.FirstOrDefaultAsync(predicate, ct);
    }

    /// <summary>
    /// Finds a single entity of type T based on a predicate without tracking it in the DbContext.
    /// </summary>
    public async Task<T?> FindOneAsNoTrackingAsync(Expression<Func<T, bool>> predicate, CancellationToken ct = default)
    {
        return await _dbSet.AsNoTracking().FirstOrDefaultAsync(predicate, ct);
    }

    /// <summary>
    /// Adds a new entity of type T.
    /// </summary>
    public async Task<int> AddAsync(T entity, CancellationToken ct = default)
    {
        await _dbSet.AddAsync(entity, ct);
        return await _dbContext.SaveChangesAsync(ct);
    }

    /// <summary>
    /// Adds a range of new entities of type T.
    /// </summary>
    public async Task<int> AddRangeAsync(List<T> entities, CancellationToken ct = default)
    {
        _dbSet.AddRange(entities);
        return await _dbContext.SaveChangesAsync(ct);
    }

    /// <summary>
    /// Updates an existing entity of type T.
    /// </summary>
    public async Task<int> UpdateAsync(T entity, CancellationToken ct = default)
    {
        _dbSet.Update(entity);
        return await _dbContext.SaveChangesAsync(ct);
    }

    /// <summary>
    /// Updates a range of existing entities of type T.
    /// </summary>
    public async Task<int> UpdateRangeAsync(List<T> entities, CancellationToken ct = default)
    {
        _dbSet.UpdateRange(entities);
        return await _dbContext.SaveChangesAsync(ct);
    }

    /// <summary>
    /// Deletes an existing entity of type T.
    /// </summary>
    public async Task<int> DeleteAsync(T entity, CancellationToken ct = default)
    {
        _dbSet.Remove(entity);
        return await _dbContext.SaveChangesAsync(ct);
    }

    /// <summary>
    /// Deletes existing entities of type T.
    /// </summary>
    public async Task<int> DeleteRangeAsync(List<T> entities, CancellationToken ct = default)
    {
        _dbSet.RemoveRange(entities);
        return await _dbContext.SaveChangesAsync(ct);
    }

    public async Task<int> RemoveRangeAsync(IQueryable<T> entities, CancellationToken ct = default)
    {
        _dbSet.RemoveRange(entities);
        return await _dbContext.SaveChangesAsync(ct);
    }

    public async Task<int> DeleteBulkByIdsAsync(List<string> ids, CancellationToken ct = default)
    {
        var res = await _dbSet.AsQueryable()
            .AsNoTracking()
            .Where(item => ids.Contains(item.Id))
            .ExecuteUpdateAsync(setter => setter
                .SetProperty(s => s.IsDeleted, value => true), ct);

        return res;
    }

    /// <summary>
    /// Returns a queryable of all entities of type T without tracking them in the DbContext.
    /// </summary>
    public IQueryable<T> GetQueryable() => _dbSet.AsNoTracking().AsQueryable();
    
    
}