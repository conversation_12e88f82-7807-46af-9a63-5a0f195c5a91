using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Models;

namespace Hubtel.CCI.Api.Repositories.Interfaces;

/// <summary>
/// Provides an interface for accessing repositories in the application.
/// </summary>
public interface IRepositoryContext
{
    /// <summary>
    ///     Gets the repository for accessing CciRepositoryScore entities.
    /// </summary>
    IBaseRepository<CciRepositoryScore> CciRepositoryScoreRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing Engineer entities.
    /// </summary>
    IBaseRepository<Engineer> EngineerRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing ProductTeam entities.
    /// </summary>
    IBaseRepository<ProductTeam> ProductTeamRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing ProductGroup entities.
    /// </summary>
    IBaseRepository<ProductGroup> ProductGroupRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing Repository entities.
    /// </summary>
    IBaseRepository<Repository> RepositoryRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing CciRepositoryScore entities.
    /// </summary>
    IBaseRepository<CciProductsRanking> CciProductsRankingRepository { get; init; }

    /// <summary>
    ///   Gets the repository for accessing CciProductTeamsScoreTrend entities.
    /// </summary>
    IBaseRepository<CciProductTeamsScoreTrend> CciProductTeamsScoreTrendRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing CciRepositoryScoreStatistic entities.
    /// </summary>
    IBaseRepository<CciRepositoryScoreStatistic> CciRepositoryScoreStatisticRepository { get; init; }

    /// <summary>
    /// Asynchronously retrieves a list of product teams associated with a specific engineer.
    /// </summary>
    /// <param name="engineerId">The ID of the engineer.</param>
    /// <returns>A list of product teams associated with the specified engineer.</returns>
    Task<List<ProductTeam>> GetProductTeamsForEngineerAsync(string engineerId);

    /// <summary>
    /// Asynchronously retrieves a list of product groups associated with a specific repository.
    /// </summary>
    /// <param name="repositoryId">The ID of the repository.</param>
    /// <returns>A list of product groups associated with the specified repository.</returns>
    Task<List<ProductGroup>> GetProductGroupsForRepositoryAsync(string repositoryId);


    /// <summary>
    /// Asynchronously retrieves a list of product teams associated with a specific repository.
    /// </summary>
    /// <param name="repositoryId">The ID of the repository.</param>
    /// <returns>A list of product teams associated with the specified repository.</returns>
    Task<List<ProductTeam>> GetProductTeamsForRepositoryAsync(string repositoryId);

    /// <summary>
    /// Asynchronously gets the product group by repository ID.
    /// </summary>
    /// <param name="repositoryId">Optional repository key</param>
    /// <param name="sonarqubeKey">Optional sonarqube key.</param>
    /// <param name="repositoryItem">The repository item.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>The details of the product group.</returns>
    Task<ProductGroupDetails?> GetProductGroupByRepositoryFilterAsync(string repositoryId, string sonarqubeKey,
        Repository repositoryItem, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously retrieves a list of product groups associated with a specific engineer.
    /// </summary>
    /// <param name="engineerId">The ID of the engineer.</param>
    /// <returns>A list of product groups associated with the specified engineer.</returns>
    Task<List<ProductGroup>> GetProductGroupsForEngineerAsync(string engineerId);


    /// <summary>
    /// Asynchronously retrieves a list of product groups associated with a specific product team.
    /// </summary>
    /// <param name="productTeamId">The ID of the product team.</param>
    /// <returns>A list of product groups associated with the specified product team.</returns>
    Task<List<ProductGroup>> GetProductGroupsForProductTeamAsync(string productTeamId);


    /// <summary>
    ///     Gets the repository for accessing CciRoadMap entities.
    /// </summary>
    IBaseRepository<CciRoadMap> CciRoadMapRepository { get; init; }


    /// <summary>
    ///     Gets the repository for accessing CciRoadMapMetricUpdateRecords entities.
    /// </summary>
    IBaseRepository<CciRoadMapMetricUpdateRecord> CciRoadMapMetricUpdateRecordRepository { get; init; }


    /// <summary>
    /// Gets the repository for accessing the BypassedPr entities.
    /// </summary>
    IBaseRepository<BypassedPr> BypassedPrRepository { get; init; }


    /// <summary>
    /// Gets the repository for accessing the RepositorySyncLogBp entities.
    /// </summary>
    IBaseRepository<RepositorySyncLogBp> RepositorySyncLogBpRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing the EngineerSonarQubeMetrics entities.
    /// </summary>
    IBaseRepository<EngineerSonarQubeMetrics> EngineerSonarQubeMetricsRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing the CciEngineersScoreStatistic entities.
    /// </summary>
    IBaseRepository<CciEngineerScore> CciEngineerScoreRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing the CciEngineersScoreStatistic entities.
    /// </summary>
    IBaseRepository<CciEngineersScoreStatistic> CciEngineersScoreStatisticRepository { get; init; }


    /// <summary>
    ///     Gets the repository for accessing CciRoadMapRecords entities.
    /// </summary>
    IBaseRepository<CciRoadMapRecord> CciRoadMapRecordRepository { get; init; }


    /// <summary>
    ///   Gets the repository for accessing AzureReleaseDefinition entities.
    /// </summary>
    IBaseRepository<AzureReleaseDefinition> AzureReleaseDefinitionRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing AzureReleaseDefinitionDeployment entities.
    /// </summary>
    IBaseRepository<AzureReleaseDefinitionDeployment> AzureReleaseDefinitionDeploymentRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing CciEngineerRanking entities.
    /// </summary>
    IBaseRepository<CciEngineerRanking> CciEngineerRankingRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing RepositoryLanguageDistribution entities.
    /// </summary>
    IBaseRepository<RepositoryLanguageDistribution> RepositoryLanguageDistributionRepository { get; init; }

    /// <summary>
    /// Gets the repository for accessing RepositoryLanguageStatistic entities.
    /// </summary>
    IBaseRepository<RepositoryLanguageStatistic> RepositoryLanguageStatisticRepository { get; init; }
    

    /// <summary>
    /// Gets the repository for accessing MsTeamsBotToken entities.
    /// </summary>
    IBaseRepository<MsTeamsBotToken> MsTeamsBotTokenRepository { get; init; }
    
    /// <summary>
    ///  Gets the repository for accessing Service entities.
    ///  </summary>
    IBaseRepository<Service> ServiceRepository { get; init; }

    /// <summary>
    /// Deployment Confidence Process (DCP) Request Repository
    /// </summary>
    IBaseRepository<DeploymentRequest> DeploymentRequestRepository { get; init; }
    IBaseRepository<ServiceDeploymentTrail> ServiceDeploymentTrailRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing ReportDeliveryRule entities.
    /// </summary>
    IBaseRepository<ReportDeliveryRule> ReportDeliveryRuleRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing DCIServiceScore entities.
    /// </summary>
    IBaseRepository<DciServiceScore> DciServiceScoreRepository { get; init; }
    
    /// <summary>
    /// Gets the repository for accessing DciProductsRanking entities.
    /// </summary>
    IBaseRepository<DciProductsRanking> DciProductsRankingRepository { get; init; }
    IBaseRepository<ServiceStatistic> ServiceStatisticRepository { get; init; }
    IBaseRepository<IssueTracker> IssueTrackerRepository { get; init; }
    IBaseRepository<ToolingTool> ToolingToolsRepository { get; init; }
    IBaseRepository<ToolingReport> ToolingReportRepository { get; init; }
}