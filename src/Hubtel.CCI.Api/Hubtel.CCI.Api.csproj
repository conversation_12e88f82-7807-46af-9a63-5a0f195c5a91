<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <IsPackable>true</IsPackable>
        <Optimize>true</Optimize>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Akka.Hosting" Version="1.5.22" />
        <PackageReference Include="Confluent.Kafka" Version="2.4.0" />

        <PackageReference Include="Confluent.Kafka.Extensions.Diagnostics" Version="0.4.0" />

        <PackageReference Include="FluentValidation" Version="11.9.1" />

        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.1" />

        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />

        <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="9.0.7" />

        <PackageReference Include="Microsoft.EntityFrameworkCore.UnitOfWork" Version="3.1.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
        <PackageReference Include="Microsoft.Graph" Version="5.39.0" />
        <PackageReference Include="Microsoft.Identity.Client" Version="4.73.1" />
        <PackageReference Include="Microsoft.Identity.Web" Version="2.16.1" />
        <PackageReference Include="PuppeteerSharp" Version="20.2.0" />
        <PackageReference Include="SharpGrip.FluentValidation.AutoValidation.Mvc" Version="1.4.0" />
        <PackageReference Include="SharpGrip.FluentValidation.AutoValidation.Shared" Version="1.4.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.5.2" />

        <PackageReference Include="Npgsql" Version="8.0.3" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
        <PackageReference Include="Npgsql.Json.NET" Version="8.0.3" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>

        <PackageReference Include="Akka" Version="1.5.24" />
        <PackageReference Include="Akka.DependencyInjection" Version="1.5.24" />

        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="JustEat.StatsD" Version="5.0.1" />
        <PackageReference Include="Gelf.Extensions.Logging" Version="2.6.0" />
        <PackageReference Include="Flurl.Http" Version="4.0.2" />
        <PackageReference Include="Pluralize.NET" Version="1.0.2" />
        <PackageReference Include="StackExchange.Redis" Version="2.7.33" />

        <PackageReference Include="Hubtel.Redis.Sdk" Version="8.0.4" />
        <PackageReference Include="Hubtel.PhoneNumbers" Version="8.0.4" />
        <PackageReference Include="Hubtel.Instrumentation" Version="8.0.4" />
        <PackageReference Include="Hubtel.Otel.Instrumentation" Version="8.0.20" />
        <PackageReference Include="Hubtel.Kafka.Producer.Sdk" Version="8.0.4" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="6.6.2" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2" />
		<PackageReference Include="RazorLight" Version="2.3.1" />
    </ItemGroup>

	<ItemGroup>
		<None Update="README.md">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<Content Include="Templates\cci_report.template.html">
			<!-- ensures the file goes into bin/Debug/net6.0/Templates/cci.html -->
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<!--<Content Include="Templates\ToolingReports.cshtml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>-->
	</ItemGroup>
</Project>
