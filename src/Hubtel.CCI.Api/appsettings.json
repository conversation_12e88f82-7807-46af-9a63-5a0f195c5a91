{"AllowedHosts": "*", "ApiDocsConfig": {"ShowSwaggerUi": true, "ShowRedocUi": true, "EnableSwaggerTryIt": true}, "ApplicationInsights": {"InstrumentationKey": ""}, "BearerTokenConfig": {"Issuer": "", "Audience": "", "SigningKey": ""}, "ConnectionStrings": {"DbConnection": ""}, "LookupConfig": {"Url": ""}, "KafkaProducerConfig": {"Hosts": [{"Alias": "", "BootstrapServers": ""}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Akka": "Debug"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.CCI.Api", "Facility": "Hubtel.CCI.Api", "Environment": "Development", "LogLevel": {"Default": "Debug"}}}, "LogRequestResponse": false, "RedisConfiguration": {"Setup": [{"Host": "127.0.0.1", "Name": "localRedis", "Port": "6379", "Databases": [{"alias": "", "db": 1}]}]}, "StatsdConfig": {"Server": "127.0.0.1", "Port": "6379", "Prefix": ""}, "ActorConfig": {"SendCallbackActorConfig": {"NumberOfInstances": 10, "UpperBound": 100}}, "BasicAuthConfigs": {"AuthConfigs": [{"Username": "<EMAIL>", "Password": "", "Role": "Admin"}, {"Username": "<EMAIL>", "Password": "", "Role": "Admin"}]}, "OpenTelemetryConfig": {"ServiceName": "hubtel.cci.api", "Host": "localhost", "Port": 4317, "Protocol": "http", "ShowConsoleMetrics": false, "ShowConsoleTrace": true}, "SonarQubeConfig": {"Host": "https://sonar.hubtel.com", "AlmSetting": "", "Token": ""}, "AzureOrgConfig": {"Organization": "hubtel", "DevUrl": "https://dev.azure.com", "ReleaseUrl": "https://vsrm.dev.azure.com", "AccessToken": "", "Projects": ["Back-End", "Inventory", "AI Lab", "Back-Office", "Consumer", "Front-End", "Gov", "<PERSON><PERSON><PERSON>", "Notifications", "Orders", "Payments", "Producer"]}, "TeamsBotAppConfig": {"ClientId": "", "ClientSecret": "", "TenantId": "", "RedirectUri": "", "Scopes": ["User.Read", "Chat.ReadWrite", "ChatMessage.Send", "User.Read.All", "Directory.Read.All"]}, "ToolingReportPAT": "", "BackendPackagesList": ["Hubtel.PhoneNumbers", "Hubtel.Instrumentation", "Hubtel.Redis.Sdk", "Hubtel.ProgrammableServices.Sdk", "Hubtel.Kafka.Host", "Hubtel.BusinessInfo.Sdk", "Hubtel.Kafka.Payload.Model", "Hubtel.Inventory.Sdk", "Hubtel.UssdAdmin.Sdk", "Hubtel.Sat.Insights.Sdk", "Hubtel.Sat.Coupons.Sdk", "Hubtel.Obsidian.Sdk", "Hubtel.Sat.InventoryTags.Sdk", "Hubtel.Cart.Sdk", "Hubtel.Survey.Sdk", "Hubtel.VisaDirect.Sdk", "Hubtel.SearchTerm.Sdk", "Hubtel.VisaDirect.Sdk.UnitTests", "Hubtel.FirebaseManager.Sdk", "Hubtel.Ecommerce.OrderManagers.Sdk", "Hubtel.AwsS3.Sdk", "Hubtel.Profile.Sdk", "Hubtel.FileUpload.Sdk", "Hubtel.ElasticSearch.Sdk", "Hubtel.Kafka.Producer.Sdk", "Hubtel.Callback.Sdk", "Hubtel.AwsSecretsManager.Sdk", "Hubtel.Ai.GptDb.Pg.Sdk", "Hubtel.Ai.GptDb.Es.Sdk", "Hubtel.SemanticKernel.Sdk", "Hubtel.MongoDb.Sdk", "Hubtel.OpenSearch.Sdk", "Hubtel.CoreModels.Sdk", "Hubtel.Otel.Instrumentation"], "BackendProductGroupRepositories": [{"GroupName": "ML", "RepositoryUrls": ["https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.ML.Octopus", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.ML.CreditScore", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.Gov.ML.Proxy", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.ML.Sms", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.ML.QuickCommerce", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.Payment.Fraud", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.Internal.Audit.Gatekeeper", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.ML.CreditScore.Report", "https://dev.azure.com/hubtel/AI%20Lab/_git/Hubtel.ML.AgenticChatbot"]}, {"GroupName": "UX and Marketing", "RepositoryUrls": ["https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel-Web", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/blog.hubtel.com", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/about.hubtel.com", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/CreditScore%20website", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel%20Certificates", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel-Brand-Partners", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/HubtelAcademy", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Marketing%20Portal", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel-Package-Delivery", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/URL%20Shortener", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel%20Designs%20Portal%20v4"]}, {"GroupName": "Internal Back offices", "RepositoryUrls": ["https://dev.azure.com/hubtel/Back-Office/_git/Hubtel.BackOfficeEvent", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.StaffPortalBackoffice", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.BackOfficeEvent", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.UserCareBackOffice", "https://dev.azure.com/hubtel/Back-Office/_git/Support-BackOffice-App", "https://dev.azure.com/hubtel/Back-Office/_git/Finance-BackOffice-App", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.PartnerComplianceBackoffice"]}, {"GroupName": "Community-Based Products", "RepositoryUrls": ["https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Education", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Health", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.SocialGroupV2", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.PartnerCustomerInsight"]}, {"GroupName": "Significant Sectors", "RepositoryUrls": ["https://dev.azure.com/hubtel/Gov/_git/ECG-Webapp", "https://dev.azure.com/hubtel/Gov/_git/ECG-OLTPV-BACKOFFICE", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MtnEcgProxy", "https://dev.azure.com/hubtel/Gov/_git/ECG-Backend", "https://dev.azure.com/hubtel/Gov/_git/ECG-TRANSACTIONS-BACKOFFICE", "https://dev.azure.com/hubtel/Gov/_git/Gov-ECG-Frontend", "https://dev.azure.com/hubtel/Gov/_git/ECG-Staff-Portal", "https://dev.azure.com/hubtel/Gov/_git/ECG-BackOffice-FrontEnd", "https://dev.azure.com/hubtel/Gov/_git/ECG-Backend", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MtnEcgProxy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InstantServices.Insurance", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InsuranceMerchantPlatform"]}, {"GroupName": "Special Projects", "RepositoryUrls": ["https://dev.azure.com/hubtel/Gov/_git/Hubtel.Gov.Transport", "https://dev.azure.com/hubtel/Notifications/_git/Ghana-Gov-Invoice-Processor", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MtnPortal", "https://dev.azure.com/hubtel/Gov/_git/Citizens%20App-Agencies", "https://dev.azure.com/hubtel/Gov/_git/GOV%20Citizens%20APIs", "https://dev.azure.com/hubtel/Gov/_git/Notifications%20-%20Frontend", "https://dev.azure.com/hubtel/Gov/_git/Citizens-app-web", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Events.Monetization.Platform", "https://dev.azure.com/hubtel/Gov/_git/Hubtel.Gov.DataExchangeService"]}, {"GroupName": "Government and Socially Impactful Projects", "RepositoryUrls": ["https://dev.azure.com/hubtel/Gov/_git/MyAssembly", "https://dev.azure.com/hubtel/Gov/_git/MyAssembly.Gov", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.LendScore", "https://dev.azure.com/hubtel/Gov/_git/Hubtel.Gov.MyCreditScore", "https://dev.azure.com/hubtel/Gov/_git/Gov-Citizens-Creditscore-Portal", "https://dev.azure.com/hubtel/Gov/_git/Hubtel.SME", "https://dev.azure.com/hubtel/Gov/_git/SME-GO-Admin-Portal", "https://dev.azure.com/hubtel/Gov/_git/SME-GO"]}, {"GroupName": "Merchant Tools", "RepositoryUrls": ["https://dev.azure.com/hubtel/Back-End/_git/Hubtel.SupplyChain", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.com%20-%20Profile.Wallets", "https://dev.azure.com/hubtel/Consumer/_git/Hubtel.Consumer.Auth", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.com%20-%20Auth", "https://dev.azure.com/hubtel/Consumer/_git/Gratis-Api-ConsumerData", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.com%20-%20Transactions", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ProductAnalytics", "https://dev.azure.com/hubtel/Consumer/_git/Gratis-ConsumerProxy-Api", "https://dev.azure.com/hubtel/Consumer/_git/Hubtel.ConsumerPurchaseHistory", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ReportStream", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ZeePay", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.Kyc.Verification", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Chenosis", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.UnifiedCheckout", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.Reconciliation.Refunds", "https://dev.azure.com/hubtel/Orders/_git/Order-Consumer-OrderPaid", "https://dev.azure.com/hubtel/Payments/_git/ReceiveMoney-StatusCheckApi", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.GMoney.Proxy", "https://dev.azure.com/hubtel/Back-Office/_git/Finance-Api", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Gatekeeper", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.GeneralServiceFullment", "https://dev.azure.com/hubtel/Back-End/_git/ConsumerSendReceiveMoney", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MerchantCardProxy", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.Accounting", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Accounting.Processor", "https://dev.azure.com/hubtel/Payments/_git/Payment-Consumer-ReceiveMoney-Accounting-Provider", "https://dev.azure.com/hubtel/Orders/_git/Unified%20Sales", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ReceiveMoney", "https://dev.azure.com/hubtel/Payments/_git/ReceiveMoney-Consumers", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.SupportBO", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.CardWhitelist", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.TaxWithHolding", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.CardVerification", "https://dev.azure.com/hubtel/Payments/_git/ReceiveMoney-ApiProxy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.FinanceBOCashFlash", "https://dev.azure.com/hubtel/Payments/_git/Hubtel-BusinessPayments-Api", "https://dev.azure.com/hubtel/Orders/_git/Unified%20Sales", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MerchantPreApproval", "https://dev.azure.com/hubtel/Orders/_git/Order-Scheduler", "https://dev.azure.com/hubtel/Payments/_git/Hubtel-InterFineractTransfer-Consumer", "https://dev.azure.com/hubtel/Payments/_git/SendMoney-CoreApps", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.PartnerSettlements", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ELevy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.PaySmallSmallRefunds", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.PayIn4", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.UserCarePPEBackOffice", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.TelcoProcessor", "https://dev.azure.com/hubtel/Payments/_git/Hubtel-BusinessPayments-Consumer", "https://dev.azure.com/hubtel/Consumer/_git/GeneralService-Consumer-Fulfillment", "https://dev.azure.com/hubtel/Payments/_git/ReceiveMoney-CallbackNotifier", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ReceiveMoney.DirectSettlement", "https://dev.azure.com/hubtel/Orders/_git/Orders-Caching-Consumers", "https://dev.azure.com/hubtel/Payments/_git/Hubtel-BusinessPayments-Consumer", "https://dev.azure.com/hubtel/Payments/_git/Hubtel.Accounting.Transactions", "https://dev.azure.com/hubtel/Back-End/_git/DevPortalProxy", "https://dev.azure.com/hubtel/Payments/_git/RecurringInvoices-CoreApp", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MerchantPayment", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ProducerMessagingV2", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.CoreMessaging", "https://dev.azure.com/hubtel/Notifications/_git/Hubtel.ProducerMessaging.Consumers", "https://dev.azure.com/hubtel/Notifications/_git/Notification-Consumer-DefaultSystem", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Automated.Notifications", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.PaymentNotifications", "https://dev.azure.com/hubtel/Back-End/_git/TransactionMonitoringInsights", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Notifications.ReceiptOnly", "https://dev.azure.com/hubtel/Back-End/_git/MTN%20MADAPI%20Proxy", "https://dev.azure.com/hubtel/Back-End/_git/ProducerPaylinkInvoicing", "https://dev.azure.com/hubtel/Notifications/_git/Messaging", "https://dev.azure.com/hubtel/Notifications/_git/Notification-Consumer-ReceiptsOnly", "https://dev.azure.com/hubtel/Notifications/_git/Producer%20Messaging", "https://dev.azure.com/hubtel/Inventory/_git/USSD", "https://dev.azure.com/hubtel/Producer/_git/Downloads-Consumer-Main", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.XeroPayment", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InternalProducerCheckoutProxy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MerchantDeveloperPortalProxy", "https://dev.azure.com/hubtel/Consumer/_git/Checkout-Api-Proxy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MaPortal%20-%20Auxiliaries", "https://dev.azure.com/hubtel/Inventory/_git/Inventory-CoreApps", "https://dev.azure.com/hubtel/Consumer/_git/Checkout-Api-Main", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MerchantInvoicing", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MAPortalProxy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InternalProducerCheckoutProxy", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Merchant.Notification", "https://dev.azure.com/hubtel/Producer/_git/Downloads-Consumer-Main", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.MerchantPayment", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.CheckoutAppPartners", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.BankPartners", "https://dev.azure.com/hubtel/Consumer/_git/POS-Proxy"]}, {"GroupName": "Directs To Consumers", "RepositoryUrls": ["https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Riders", "https://dev.azure.com/hubtel/Inventory/_git/GeneralServices-CoreApps", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ConsumerServices.ProductAnalytics", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InstantServices.Orders.Api", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.BettingAndLotto", "https://dev.azure.com/hubtel/Inventory/_git/CloudNetwork", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InstantServices", "https://dev.azure.com/hubtel/Inventory/_git/GeneralServices-ImplementationApps", "https://dev.azure.com/hubtel/Inventory/_git/ProgrammableServiceReports", "https://dev.azure.com/hubtel/Consumer/_git/Ussd-Consumer-Payment", "https://dev.azure.com/hubtel/Consumer/_git/Hubtel.ConsumerApp.Messaging", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.com%20-%20Messaging", "https://dev.azure.com/hubtel/Inventory/_git/ProgrammableServices-GHQR", "https://dev.azure.com/hubtel/Inventory/_git/ProgrammableServices-ConsumerUssd", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Events", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.Retail", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.QuickCommerce.Dashboard", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.QuickCommerce.SalesBackOffice", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.QuickCommerce", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InstantServices.Paylinks", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.TakePayments", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.QuickCommerce.BackOffice", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.ProgrammableServices.PaidPolls", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.QuickCommerce.ProductAnalytics", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.InstantServices.Paylinks.Consumer", "https://dev.azure.com/hubtel/Back-End/_git/Hubtel.QuickCommerce.BrandPartners"]}], "FrontedProductGroupRepositories": [{"GroupName": "Code Quality & Tooling", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Unified-Checkout-Web-SDK", "https://dev.azure.com/hubtel/Front-End/_git/Test-Project-1", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Authentication", "https://dev.azure.com/hubtel/Inventory/_git/ProgrammableServices-Simulator", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Frontend-CLI-Tool", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Tooling-Proxy-Server", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Events-Publisher", "https://dev.azure.com/hubtel/Front-End/_git/CCI-Frontend"]}, {"GroupName": "UX and Marketing", "RepositoryUrls": ["https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel-Web", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/blog.hubtel.com", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/about.hubtel.com", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/CreditScore%20website", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel%20Certificates", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel-Brand-Partners", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/HubtelAcademy", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Marketing%20Portal", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel-Package-Delivery", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/URL%20Shortener", "https://dev.azure.com/hubtel/UX%20and%20Marketing/_git/Hubtel%20Designs%20Portal%20v4"]}, {"GroupName": "Internal Back offices", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Admin-Internal-Backoffices", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Large-Nationwide-Businesses-Backoffice", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Compliance-Portal"]}, {"GroupName": "Community-Based Products", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Education-Client-Portal", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-School-Fees", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Education-Web-Parent", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Backoffice-Education", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Health-PatientPortal", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-For-Hospitals", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Health-Checkout", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-%20Health-Caregiver", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Health-Finance-Backoffice", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Social-Groups-Give", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Social-Groups", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-SMB-Payroll-Admin-Portal", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-SMB-Payroll-Staff-Portal", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-General-Backoffice"]}, {"GroupName": "Significant Sectors", "RepositoryUrls": ["https://dev.azure.com/hubtel/Gov/_git/ECG-Webapp", "https://dev.azure.com/hubtel/Gov/_git/ECG-OLTPV-BACKOFFICE", "https://dev.azure.com/hubtel/Gov/_git/ECG-TRANSACTIONS-BACKOFFICE", "https://dev.azure.com/hubtel/Gov/_git/Gov-ECG-Frontend", "https://dev.azure.com/hubtel/Gov/_git/ECG-Staff-Portal", "https://dev.azure.com/hubtel/Gov/_git/ECG-BackOffice-FrontEnd", "https://dev.azure.com/hubtel/Front-End/_git/Insurance-Merchant-Platform"]}, {"GroupName": "Special Projects", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/NTPS-Admin-Portal", "https://dev.azure.com/hubtel/Front-End/_git/NTPS-Agency-Portal", "https://dev.azure.com/hubtel/Gov/_git/Citizens%20App-Agencies", "https://dev.azure.com/hubtel/Gov/_git/Notifications%20-%20Frontend", "https://dev.azure.com/hubtel/Gov/_git/Citizens-app-web", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel.EventManagement.Attendee", "https://dev.azure.com/hubtel/Gov/_git/MyAssembly", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Lenders-Financier-Portal", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-LendScore", "https://dev.azure.com/hubtel/Gov/_git/Gov-Citizens-Creditscore-Portal", "https://dev.azure.com/hubtel/Gov/_git/SME-GO-Admin-Portal", "https://dev.azure.com/hubtel/Gov/_git/SME-GO"]}, {"GroupName": "Merchant Tools", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/Hubtel-mPos-Checkout", "https://dev.azure.com/hubtel/Front-End/_git/Checkout-Web", "https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Merchant-Invoice-Pay", "https://dev.azure.com/hubtel/Front-End/_git/Hu<PERSON><PERSON>-Major-Accounts-Portal-V2"]}, {"GroupName": "Directs To Consumers", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Quick-Commerce-Backoffice"]}, {"GroupName": "Hubtel for Retailer Payment Partnerships(All-in-one POS & Partner Portal)", "RepositoryUrls": ["https://dev.azure.com/hubtel/Front-End/_git/Hubtel-Bank-Pos-Partner"]}]}