using System.ComponentModel.DataAnnotations;

namespace Hubtel.CCI.Api.Attributes;


[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public class IsAllowedValueAttribute(params string[] allowedValues) : ValidationAttribute
{
    //receive list of allowed values and check if the value is in the list

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null || !allowedValues.Contains(value.ToString()))
        {
            return new ValidationResult($"The value '{value}' is not allowed. Allowed values are: {string.Join(", ", allowedValues)}");
        }
        return ValidationResult.Success;
    }
    
}