using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Migrations
{
    /// <inheritdoc />
    public partial class Added_Cci_RoadMap_Entity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciRoadMaps",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RepositoryId = table.Column<string>(type: "text", nullable: false),
                    RepositoryName = table.Column<string>(type: "text", nullable: true),
                    ProductTeamId = table.Column<string>(type: "text", nullable: false),
                    ProductTeamName = table.Column<string>(type: "text", nullable: true),
                    EngineerAssignedId = table.Column<string>(type: "text", nullable: false),
                    EngineerName = table.Column<string>(type: "text", nullable: true),
                    StartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    TargetDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Bugs = table.Column<decimal>(type: "numeric", nullable: true),
                    CodeSmells = table.Column<decimal>(type: "numeric", nullable: true),
                    Coverage = table.Column<decimal>(type: "numeric", nullable: true),
                    DuplicatedLines = table.Column<decimal>(type: "numeric", nullable: true),
                    Vulnerabilities = table.Column<decimal>(type: "numeric", nullable: true),
                    SecurityHotspots = table.Column<decimal>(type: "numeric", nullable: true),
                    TargetBugs = table.Column<decimal>(type: "numeric", nullable: true),
                    TargetCodeSmells = table.Column<decimal>(type: "numeric", nullable: true),
                    TargetCoverage = table.Column<decimal>(type: "numeric", nullable: true),
                    TargetDuplicatedLines = table.Column<decimal>(type: "numeric", nullable: true),
                    TargetVulnerabilities = table.Column<decimal>(type: "numeric", nullable: true),
                    TargetSecurityHotspots = table.Column<decimal>(type: "numeric", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciRoadMaps", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciRoadMaps");
        }
    }
}
