using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddedDeploymentTrailsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ServiceDeploymentTrails",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DeploymentRequestId = table.Column<string>(type: "text", nullable: true),
                    RepositoryServiceId = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceDeploymentTrails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ServiceDeploymentTrails_DeploymentRequests_DeploymentReques~",
                        column: x => x.DeploymentRequestId,
                        principalTable: "DeploymentRequests",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ServiceDeploymentTrails_Services_RepositoryServiceId",
                        column: x => x.RepositoryServiceId,
                        principalTable: "Services",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ServiceDeploymentTrails_DeploymentRequestId",
                table: "ServiceDeploymentTrails",
                column: "DeploymentRequestId");

            migrationBuilder.CreateIndex(
                name: "IX_ServiceDeploymentTrails_RepositoryServiceId",
                table: "ServiceDeploymentTrails",
                column: "RepositoryServiceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ServiceDeploymentTrails");
        }
    }
}
