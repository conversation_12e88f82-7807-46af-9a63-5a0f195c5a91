using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddedTableForDeploymentRequests : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DeploymentRequests",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    ProductGroupId = table.Column<string>(type: "text", nullable: true),
                    ProductGroupName = table.Column<string>(type: "text", nullable: true),
                    ProductTeamName = table.Column<string>(type: "text", nullable: true),
                    ProductTeamId = table.Column<string>(type: "text", nullable: true),
                    EngineerRequestId = table.Column<string>(type: "text", nullable: true),
                    EngineerRequestName = table.Column<string>(type: "text", nullable: true),
                    AreaOfImpact = table.Column<string>(type: "text", nullable: true),
                    SystemBehaviorOfChange = table.Column<string>(type: "text", nullable: true),
                    HasControlledEnv = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<string>(type: "text", nullable: true),
                    CicdEngineerResponsibleId = table.Column<string>(type: "text", nullable: true),
                    CicdEngineerResponsibleName = table.Column<string>(type: "text", nullable: true),
                    DcpPlanningMeetingStartTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpPlanningMeetingEndTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpWorkingSessionStartTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpWorkingSessionEndTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpWorkingSessionReviewStartTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpWorkingSessionReviewEndTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpConfidenceReadinessReviewStartTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DcpConfidenceReadinessReviewEndTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DeploymentConfidenceReportLink = table.Column<string>(type: "text", nullable: true),
                    CancellationReason = table.Column<string>(type: "text", nullable: true),
                    DeltaDiscretion = table.Column<string>(type: "text", nullable: true),
                    AffectedRepositories = table.Column<List<AffectedRepository>>(type: "jsonb", nullable: false),
                    ServicesToBeDeployed = table.Column<List<ServiceToBeDeployed>>(type: "jsonb", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeploymentRequests", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DeploymentRequests");
        }
    }
}
