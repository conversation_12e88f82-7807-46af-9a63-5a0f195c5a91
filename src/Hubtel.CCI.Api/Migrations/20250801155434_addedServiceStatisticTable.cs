using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Migrations
{
    /// <inheritdoc />
    public partial class addedServiceStatisticTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ServiceStatistics",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RepositoryName = table.Column<string>(type: "text", nullable: true),
                    RepositoryId = table.Column<string>(type: "text", nullable: true),
                    RepositoryUrl = table.Column<string>(type: "text", nullable: true),
                    RepositoryType = table.Column<string>(type: "text", nullable: true),
                    RepositorySonarQubeKey = table.Column<string>(type: "text", nullable: true),
                    ProductTeamName = table.Column<string>(type: "text", nullable: true),
                    ProductTeamId = table.Column<string>(type: "text", nullable: true),
                    ProductGroupName = table.Column<string>(type: "text", nullable: true),
                    ProductGroupId = table.Column<string>(type: "text", nullable: true),
                    ServiceId = table.Column<string>(type: "text", nullable: true),
                    ServiceName = table.Column<string>(type: "text", nullable: true),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationStartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DeploymentFrequency = table.Column<decimal>(type: "numeric", nullable: true),
                    NumberOfRollbacks = table.Column<decimal>(type: "numeric", nullable: true),
                    NumberOfDeployments = table.Column<decimal>(type: "numeric", nullable: true),
                    NumberOfSuccessfulDeployments = table.Column<decimal>(type: "numeric", nullable: true),
                    NumberOfFailedDeployments = table.Column<decimal>(type: "numeric", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceStatistics", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ServiceStatistics");
        }
    }
}
