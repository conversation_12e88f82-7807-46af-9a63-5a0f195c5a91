using System.Net.Mime;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{AuthScheme.Basic}")]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
[ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
public class DeploymentRequestsController : HubtelControllerBase
{
    private readonly IDcpService _dcpService;
    private readonly IRepositoryContext _repositoryContext;

    public DeploymentRequestsController(IDcpService dcpService, IRepositoryContext repositoryContext)
    {
        _dcpService = dcpService;
        _repositoryContext = repositoryContext;
    }


    [HttpPost,Route("trails")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<ServiceDeploymentTrailResponse>))]
    [SwaggerOperation(nameof(AddDeploymentRequestTrail), OperationId = nameof(AddDeploymentRequestTrail))]
    public async Task<IActionResult> AddDeploymentRequestTrail([FromBody] ServiceDeploymentTrailRequest model)
    {
        var response= await _dcpService.AddServiceDeploymentTrailAsync(model);
        return ToActionResult(response);
    }
    
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<DeploymentRequest>))]
    [SwaggerOperation(nameof(CreateDcpRequestAsync), OperationId = nameof(CreateDcpRequestAsync))]
    public async Task<IActionResult> CreateDcpRequestAsync([FromBody] CreateDcpRequest model)
    {
        var user =HttpContext.User.GetAccount();
        var engineer=await _repositoryContext.EngineerRepository.GetQueryable()
            .FirstOrDefaultAsync(item => string.Equals(item.Email, user.EmailAddress));
        if (engineer == null)
        {
            return ToActionResult(ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse("Engineer not found"));
        }

        List<string> nowAllowedEngineerLevel =
        [
            ValidationConstants.EngineerJobLevelTypes.Executive,
            ValidationConstants.EngineerJobLevelTypes.NationalServicePersonnel,
            ValidationConstants.EngineerJobLevelTypes.Intern
        ];
        if (nowAllowedEngineerLevel.Contains(engineer.JobLevel))
        {
            return ToActionResult(ApiResponse<DeploymentRequest>.Default.ToForbiddenApiResponse(
                "You are not allowed to create a deployment request"));
        }

        model.AuthData = user;
        var response = await _dcpService.CreateDcpRequestAsync(model);
        return ToActionResult(response);
    }
    
    [HttpPatch,Route("{requestId}/services")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<DeploymentRequest>))]
    [SwaggerOperation(nameof(UpdateServiceToBeDeployedAsync), OperationId = nameof(UpdateServiceToBeDeployedAsync))]
    public async Task<IActionResult> UpdateServiceToBeDeployedAsync(string requestId, [FromBody] ServiceToBeDeployedInput  serviceToBeDeployed)
    {
        var response = await _dcpService.UpdateServiceToBeDeployedAsync(requestId, serviceToBeDeployed);
        return ToActionResult(response);
    }
    
    [HttpDelete,Route("{requestId}/services/{serviceId}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<DeploymentRequest>))]
    [SwaggerOperation(nameof(RemoveServiceToBeDeployedAsync), OperationId = nameof(RemoveServiceToBeDeployedAsync))]
    public async Task<IActionResult> RemoveServiceToBeDeployedAsync(string requestId, string serviceId)
    {
        var response = await _dcpService.RemoveServiceToBeDeployedAsync(requestId, serviceId);
        return ToActionResult(response);
    }
    
    [HttpPatch,Route("{requestId}/services/bulk")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<DeploymentRequest>))]
    [SwaggerOperation(nameof(UpdateBulkServicesToBeDeployedAsync), OperationId = nameof(UpdateBulkServicesToBeDeployedAsync))]
    public async Task<IActionResult> UpdateBulkServicesToBeDeployedAsync(string requestId, [FromBody] List<ServiceToBeDeployedInput > servicesToBeDeployed)
    {
        var response = await _dcpService.UpdateBulkServicesToBeDeployedAsync(requestId, servicesToBeDeployed);
        return ToActionResult(response);
    }
    
    [HttpPatch,Route("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<DeploymentRequest>))]
    [SwaggerOperation(nameof(UpdateDcpRequestAsync), OperationId = nameof(UpdateDcpRequestAsync))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> UpdateDcpRequestAsync(string id, [FromBody] DcpRequest model)
    {
        var user = HttpContext.User.GetAccount();
        model.AuthData = user;
        var response = await _dcpService.UpdateDcpRequestAsync(id, model);
        return ToActionResult(response);
    }
    [HttpGet,Route("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<DeploymentRequest>))]
    public async Task<IActionResult> GetDcpRequestByIdAsync(string id)
    {
        var response = await _dcpService.GetDcpRequestByIdAsync(id);
        return ToActionResult(response);
    }
    
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK,Type=typeof(IApiResponse<PagedResult<DeploymentRequest>>))]
    public async Task<IActionResult> FilterDcpRequestsAsync([FromQuery] DcpRequestFilter filter)
    {
        var response = await _dcpService.FilterDcpRequestsAsync(filter);
        return ToActionResult(response);
    }
    
    [HttpPatch,Route("cancel/{id}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<DeploymentRequest>))]
    [SwaggerOperation(nameof(CancelDcpRequestAsync), OperationId = nameof(CancelDcpRequestAsync))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> CancelDcpRequestAsync(string id, [FromBody] CancelDcpRequest model)
    {
        var user = HttpContext.User.GetAccount();
        model.AuthData = user;
        var response = await _dcpService.CancelDcpRequestAsync(id,model);
        return ToActionResult(response);
    }
    
    [HttpDelete,Route("by-date-range")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<int>))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> DeleteDcpRequestByDateRangeAsync([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
    {
        var response = await _dcpService.DeleteDcpRequestByDateRangeAsync(startDate, endDate);
        return ToActionResult(response);
    }
    
    [HttpPatch,Route("delete-by-ids")]
    [Consumes(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<int>))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> DeleteDcpRequestByIdsAsync([FromBody] DeleteDcpRequest request)
    {
        var response = await _dcpService.DeleteDcpRequestByIdsAsync(request);
        return ToActionResult(response);
    }
}