using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Repository;
using Hubtel.CCI.Api.Dtos.Responses.Repository;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the repositories model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
public class RepositoriesController : HubtelControllerBase
{
    private readonly IArsenalService _arsenalsService;

    public RepositoriesController(IArsenalService arsenalsService)
    {
        _arsenalsService = arsenalsService;
    }

    /// <summary>
    /// Gets a single repositories
    /// </summary>
    /// <param name="id">id of repositories</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetRepositoryResponse>))]
    [SwaggerOperation(nameof(GetRepository), OperationId = nameof(GetRepository))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetRepository(string id, CancellationToken ct)
    {
        var repositories = await _arsenalsService.GetRepositoryAsync(id, ct);
        return ToActionResult(repositories);
    }

    /// <summary>
    /// Retrieves paginated list of repositories
    /// </summary>
    /// <param name="filter">filter for querying repositories documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan respositories</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetRepositoryResponse>>))]
    [SwaggerOperation(nameof(GetRepositoriesAsync), OperationId = nameof(GetRepositoriesAsync))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetRepositoriesAsync([FromQuery] SearchFilter filter, CancellationToken ct)
    {
        var repositories = await _arsenalsService.GetRepositoriesAsync(filter, ct);
        return ToActionResult(repositories);
    }

    /// <summary>
    /// Creates a repositories
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetRepositoryResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CreateRepository), OperationId = nameof(CreateRepository))]
    [Validate<CreateRepositoryRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> CreateRepository([FromBody] CreateRepositoryRequest model, CancellationToken ct)
    {
        var added = await _arsenalsService.AddRepositoryAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Updates an repositories
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetRepositoryResponse>))]
    [SwaggerOperation(nameof(UpdateRepository), OperationId = nameof(UpdateRepository))]
    [Validate<UpdateRepositoryRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> UpdateRepository(string id, [FromBody] UpdateRepositoryRequest model,
        CancellationToken ct)
    {
        var updated = await _arsenalsService.UpdateRepositoryAsync(id, model, ct);

        return ToActionResult(updated);
    }


    /// <summary>
    /// Deletes an repositories
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteRepository), OperationId = nameof(DeleteRepository))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsDeletePolicy")]
    public async Task<IActionResult> DeleteRepository(string id, CancellationToken ct)
    {
        var deleted = await _arsenalsService.DeleteRepositoryAsync(id, ct);
        return ToActionResult(deleted);
    }
}