using System.Net.Mime;
using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Responses.CciRepositoryScore;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the cci repository score model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class CciRepositoryScoresController(
    ICciArsenalScoreService cciArsenalScoreService,
    IActorService<MainActor> mainActor)
    : HubtelControllerBase
{
    /// <summary>
    /// Gets a single cciRepositoryScore
    /// </summary>
    /// <param name="id">id of cciRepositoryScore</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetCciRepositoryScoreResponse>))]
    [SwaggerOperation(nameof(GetCciRepositoryScore), OperationId = nameof(GetCciRepositoryScore))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRepositoryScore(string id, CancellationToken ct)
    {
        var cciRepositoryScore = await cciArsenalScoreService.GetCciRepositoryScoreAsync(id, ct);
        return ToActionResult(cciRepositoryScore);
    }

    /// <summary>
    /// Retrieves paginated list oan cciRepositoryScores
    /// </summary>
    /// <param name="filter">filter for querying cciRepositoryScore documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan cciRepositoryScores</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRepositoryScoreResponse>>))]
    [SwaggerOperation(nameof(GetCciRepositoryScores), OperationId = nameof(GetCciRepositoryScores))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRepositoryScores([FromQuery] SearchCciRepositoryScoresRequest filter,
        CancellationToken ct)
    {
        var cciRepositoryScores = await cciArsenalScoreService.GetCciRepositoryScoresAsync(filter, ct);
        return ToActionResult(cciRepositoryScores);
    }

    /// <summary>
    /// Creates an cciRepositoryScore
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetCciRepositoryScoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CreateCciRepositoryScore), OperationId = nameof(CreateCciRepositoryScore))]
    [Validate<CreateCciRepositoryScoreRequest>("model")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> CreateCciRepositoryScore([FromBody] CreateCciRepositoryScoreRequest model,
        CancellationToken ct)
    {
        var added = await cciArsenalScoreService.AddCciRepositoryScoreAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// bulk inserts cciRepositoryScores
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost("bulk-create")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<int>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(AddBulkCciRepositoryScoresAsync), OperationId = nameof(AddBulkCciRepositoryScoresAsync))]
    [Validate<CreateBulkCciRepositoryScoresRequest>("model")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> AddBulkCciRepositoryScoresAsync(
        [FromBody] CreateBulkCciRepositoryScoresRequest model,
        CancellationToken ct)
    {
        var added = await cciArsenalScoreService.AddBulkCciRepositoryScoresAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Updates an cciRepositoryScore
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetCciRepositoryScoreResponse>))]
    [SwaggerOperation(nameof(UpdateCciRepositoryScore), OperationId = nameof(UpdateCciRepositoryScore))]
    [Validate<UpdateCciRepositoryScoreRequest>("model")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> UpdateCciRepositoryScore(string id,
        [FromBody] UpdateCciRepositoryScoreRequest model, CancellationToken ct)
    {
        var updated = await cciArsenalScoreService.UpdateCciRepositoryScoreAsync(id, model, ct);

        return ToActionResult(updated);
    }

    /// <summary>
    /// Deletes an cciRepositoryScore
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteCciRepositoryScore), OperationId = nameof(DeleteCciRepositoryScore))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> DeleteCciRepositoryScore(string id, CancellationToken ct)
    {
        var deleted = await cciArsenalScoreService.DeleteCciRepositoryScoreAsync(id, ct);
        return ToActionResult(deleted);
    }

    /// <summary>
    /// Deletes an cciRepositoryScore
    /// </summary>
    /// <param name="publicationWeek">Publication week to be deleted</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("delete-publication-scores/{publicationWeek}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteCciRepositoryScoreAsync), OperationId = nameof(DeleteCciRepositoryScoreAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> DeleteCciRepositoryScoreAsync(string publicationWeek, CancellationToken ct)
    {
        var deleted = await cciArsenalScoreService.DeleteCciScoresByWeekAsync(publicationWeek, ct);
        if (!deleted.Data) return ToActionResult(deleted);
        await mainActor.Tell(new DeleteCciPublicationRankingByWeekMessage(publicationWeek), ActorRefs.Nobody);
        await mainActor.Tell(new DeleteCciPublicationTrendByWeekMessage(publicationWeek), ActorRefs.Nobody);
        await mainActor.Tell(new DeleteCciPublicationStatisticByWeekMessage(publicationWeek), ActorRefs.Nobody);
        return ToActionResult(deleted);
    }

    /// <summary>
    /// Retrieves CCI repository table scores based on products.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the scores.</param>
    /// <param name="ct">Cancellation token to cancel the request.</param>
    /// <returns>
    /// An IActionResult containing a dictionary with product team names as keys and lists of CCI repository scores as values.
    /// </returns>
    [HttpGet("product-table-scores")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<Dictionary<string, List<GetCciRepositoryScoreResponse>>>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetCciRepositoryTableScoresBasedOnProductsAsync),
        OperationId = nameof(GetCciRepositoryTableScoresBasedOnProductsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRepositoryTableScoresBasedOnProductsAsync(
        [FromQuery] GetCciRepositoryTableScoresBasedOnProductsRequest request, CancellationToken ct)
    {
        var response = await cciArsenalScoreService.GetCciRepositoryTableScoresBasedOnProductsAsync(request, ct);
        return ToActionResult(response);
    }
    
    
    /// <summary>
    /// Retrieves CCI repository table scores based on products, paginated
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the scores.</param>
    /// <param name="ct">Cancellation token to cancel the request.</param>
    /// <returns>
    /// An IActionResult containing a dictionary with product team names as keys and lists of CCI repository scores as values.
    /// </returns>
    [HttpGet("product-table-scores-updated")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<Dictionary<string, List<GetCciRepositoryScoreResponse>>>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync),
        OperationId = nameof(GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync(
        [FromQuery] GetCciRepositoryTableScoresBasedOnProductsRequest request, CancellationToken ct)
    {
        var response = await cciArsenalScoreService.GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync(request, ct);
        return ToActionResult(response);
    }

    /// <summary>
    /// Retrieves a statistics based on given criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the score stats.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// An IActionResult containing a single GetCciRepositoryScoreStatisticResponse object.
    /// </returns>
    [HttpGet("publication-dashboard-stats")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<Dictionary<string, List<GetCciRepositoryScoreStatisticResponse>>>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetCciRepositoryScoreStatisticsAsync),
        OperationId = nameof(GetCciRepositoryScoreStatisticsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRepositoryScoreStatisticsAsync(
        [FromQuery] GetCciRepositoryScoreStatisticsRequest request,
        CancellationToken ct)
    {
        var response = await cciArsenalScoreService.GetCciRepositoryScoreStatisticsAsync(request, ct);
        return ToActionResult(response);
    }


    /// <summary>
    /// Retrieves product team score trends based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the score trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// An IActionResult containing a dictionary with product team names as keys and lists of product team score trend responses as values.
    /// </returns>
    [HttpGet("product-teams-score-trends")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetProductTrendsAsync), OperationId = nameof(GetProductTrendsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetProductTrendsAsync([FromQuery] GetProductTrendsRequest request,
        CancellationToken ct)
    {
        var response = await cciArsenalScoreService.GetProductTrendsAsync(request, ct);
        return ToActionResult(response);
    }
    
    
    /// <summary>
    /// Retrieves product team score trends based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the score trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// An IActionResult containing a dictionary with product team names as keys and lists of product team score trend responses as values.
    /// </returns>
    [HttpGet("product-teams-score-trends-recent")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetProductTrendsRecentAsync), OperationId = nameof(GetProductTrendsRecentAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetProductTrendsRecentAsync([FromQuery] GetProductTrendsRequest request,
        CancellationToken ct)
    {
        var response = await cciArsenalScoreService.GetProductTrendsRecentAsync(request, ct);
        return ToActionResult(response);
    }

    /// <summary>
    /// Retrieves the CCI products rankings based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the rankings.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// An IActionResult containing a dictionary with product team names as keys and lists of CCI repository scores as values.
    /// </returns>
    [HttpGet("products-rankings")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<GetCciProductsRankingResponse>>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetCciProductsRankingsAsync), OperationId = nameof(GetCciProductsRankingsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciProductsRankingsAsync([FromQuery] GetCciProductsRankingsRequest request,
        CancellationToken ct)
    {
        var response = await cciArsenalScoreService.GetCciProductsRankingsAsync(request, ct);
        return ToActionResult(response);
    }

    /// <summary>
    /// Creates the latest publication CCI products ranking.
    /// </summary>
    /// <param name="ct">Cancellation token to cancel the request.</param>
    /// <param name="request">Publication request.</param>
    /// <returns>
    /// An IActionResult indicating the result of the operation.
    /// </returns>
    [HttpPost("latest-publication-cci-products-ranking")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<bool>))]
    [SwaggerOperation(nameof(CreateLatestPublicationCciProductsRankingAsync),
        OperationId = nameof(CreateLatestPublicationCciProductsRankingAsync))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Validate<PublishCciProductsRankingRequest>("request")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> CreateLatestPublicationCciProductsRankingAsync(
        [FromBody] PublishCciProductsRankingRequest request, CancellationToken ct)
    {
        var added = await cciArsenalScoreService.PublishCciProductsRankingAsync(request, ct);
        return ToActionResult(added);
    }
    
    
    /// <summary>
    /// Updates cc repository statistics
    /// </summary>
    /// <param name="request">request for updating cciRepositoryStatistics document</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan cciRepositoryScores</returns>
    [HttpPut("update-cci-repository-statistics")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRepositoryScoreResponse>>))]
    [SwaggerOperation(nameof(GetCciRepositoryScores), OperationId = nameof(GetCciRepositoryScores))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> UpdateCciRepositoryScoresStatistics([FromBody] UpdateCciRepositoryStatisticsRequest request,
        CancellationToken ct)
    {
        var cciRepositoryScores = await cciArsenalScoreService.UpdateCciRepositoryStatisticsNewAsync(request, ct);
        return ToActionResult(cciRepositoryScores);
    }


    /// <summary>
    /// Gets CCI trends by publication date range.
    /// </summary>
    /// <param name="request">request for fetching cci trends by range</param>
    /// <returns>Paginated list oan cciRepositoryScores</returns>
    [HttpGet("cci-trends/by-range")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRepositoryScoreResponse>>))]
    [SwaggerOperation(nameof(GetCciTrendsByRange), OperationId = nameof(GetCciTrendsByRange))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciTrendsByRange([FromQuery] CciTrendRequest request)
    {
        var response = await cciArsenalScoreService.GetCciTrendsByPublicationDateRangeAsync(request);
        return ToActionResult(response);
    }



    /// <summary>
    /// Triggers the publication of CCI repository scores.
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpPost("trigger-cci-repository-score-publication")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<bool>))]
    [SwaggerOperation(nameof(TriggerCciRepositoryScorePublication),
        OperationId = nameof(TriggerCciRepositoryScorePublication))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> TriggerCciRepositoryScorePublication(CancellationToken ct)
    {
        var response = await cciArsenalScoreService.TriggerCciRepositoryScorePublishAsync(ct);
        return ToActionResult(response);
    }
}