using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.RepositoryService;
using Hubtel.CCI.Api.Dtos.Responses.RepositoryService;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     Contains all endpoints for the repository services model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
public class RepositoryServicesController : HubtelControllerBase
{
    private readonly IArsenalServiceService _arsenalServiceService;

    public RepositoryServicesController(IArsenalServiceService arsenalServiceService)
    {
        _arsenalServiceService = arsenalServiceService;
    }

    /// <summary>
    /// Gets a single repository service
    /// </summary>
    /// <param name="id">id of repository service</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetRepositoryServiceResponse>))]
    [SwaggerOperation(nameof(GetRepositoryService), OperationId = nameof(GetRepositoryService))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetRepositoryService(string id, CancellationToken ct)
    {
        var service = await _arsenalServiceService.GetRepositoryServiceAsync(id, ct);
        return ToActionResult(service);
    }

    /// <summary>
    /// Retrieves paginated list of repository services
    /// </summary>
    /// <param name="filter">filter for querying repository services</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list of repository services</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetRepositoryServiceResponse>>))]
    [SwaggerOperation(nameof(GetRepositoryServicesAsync), OperationId = nameof(GetRepositoryServicesAsync))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetRepositoryServicesAsync([FromQuery] RepositoryServiceSearchFilter filter, CancellationToken ct)
    {
        var services = await _arsenalServiceService.GetRepositoryServicesAsync(filter, ct);
        return ToActionResult(services);
    }

    /// <summary>
    /// Creates a repository service
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetRepositoryServiceResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CreateRepositoryService), OperationId = nameof(CreateRepositoryService))]
    [Validate<CreateRepositoryServiceRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> CreateRepositoryService([FromBody] CreateRepositoryServiceRequest model, CancellationToken ct)
    {
        var added = await _arsenalServiceService.AddRepositoryServiceAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Updates a repository service
    /// </summary>
    /// <param name="id">id of repository service to update</param>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetRepositoryServiceResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(UpdateRepositoryService), OperationId = nameof(UpdateRepositoryService))]
    [Validate<UpdateRepositoryServiceRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> UpdateRepositoryService(string id, [FromBody] UpdateRepositoryServiceRequest model,
        CancellationToken ct)
    {
        var updated = await _arsenalServiceService.UpdateRepositoryServiceAsync(id, model, ct);
        return ToActionResult(updated);
    }

    /// <summary>
    /// Deletes a repository service
    /// </summary>
    /// <param name="id">id of repository service to delete</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteRepositoryService), OperationId = nameof(DeleteRepositoryService))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> DeleteRepositoryService(string id, CancellationToken ct)
    {
        var deleted = await _arsenalServiceService.DeleteRepositoryServiceAsync(id, ct);
        return ToActionResult(deleted);
    }
}