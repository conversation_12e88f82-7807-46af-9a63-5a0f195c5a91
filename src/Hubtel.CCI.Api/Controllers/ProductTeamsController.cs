using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Dtos.Responses.ProductTeam;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the productTeams model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class ProductTeamsController : HubtelControllerBase
{
    private readonly IProductTeamService _productTeamsService;

    public ProductTeamsController(IProductTeamService productTeamsService)
    {
        _productTeamsService = productTeamsService;
    }

    /// <summary>
    /// Gets a single productTeams
    /// </summary>
    /// <param name="id">id of productTeams</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetProductTeamResponse>))]
    [SwaggerOperation(nameof(GetProductTeam), OperationId = nameof(GetProductTeam))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> GetProductTeam(string id, CancellationToken ct)
    {
        var productTeams = await _productTeamsService.GetProductTeamAsync(id, ct);
        return ToActionResult(productTeams);
    }

    /// <summary>
    /// Retrieves paginated list oan productTeams
    /// </summary>
    /// <param name="filter">filter for querying productTeams documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan productTeams</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetProductTeamResponse>>))]
    [SwaggerOperation(nameof(GetProductTeams), OperationId = nameof(GetProductTeams))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> GetProductTeams([FromQuery] SearchFilter filter, CancellationToken ct)
    {
        var productTeams = await _productTeamsService.GetProductTeamsAsync(filter, ct);
        return ToActionResult(productTeams);
    }

    /// <summary>
    /// Creates an productTeams
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetProductTeamResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CreateProductTeam), OperationId = nameof(CreateProductTeam))]
    [Validate<CreateProductTeamRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> CreateProductTeam([FromBody] CreateProductTeamRequest model, CancellationToken ct)
    {
        var added = await _productTeamsService.AddProductTeamAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Updates an productTeams
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetProductTeamResponse>))]
    [SwaggerOperation(nameof(UpdateProductTeam), OperationId = nameof(UpdateProductTeam))]
    [Validate<UpdateProductTeamRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> UpdateProductTeam(string id, [FromBody] UpdateProductTeamRequest model,
        CancellationToken ct)
    {
        var updated = await _productTeamsService.UpdateProductTeamAsync(id, model, ct);

        return ToActionResult(updated);
    }

    /// <summary>
    /// Deletes an productTeams
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteProductTeam), OperationId = nameof(DeleteProductTeam))]
    [Authorize(Policy = "DevDocsDeletePolicy")]
    public async Task<IActionResult> DeleteProductTeam(string id, CancellationToken ct)
    {
        var deleted = await _productTeamsService.DeleteProductTeamAsync(id, ct);
        return ToActionResult(deleted);
    }


    /// <summary>
    /// Updates a productTeam's repository status with that of the productTeam
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("repositories/{id}")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetProductTeamResponse>))]
    [SwaggerOperation(nameof(UpdateProductTeamRepositoryStatus), OperationId = nameof(UpdateProductTeamRepositoryStatus))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> UpdateProductTeamRepositoryStatus(string id, CancellationToken ct)
    {
        var updated = await _productTeamsService.UpdateProductTeamRepositoriesAsync(id, ct);

        return ToActionResult(updated);
    }


    /// <summary>
    /// Updates productTeams repositories status with that of the productTeam
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("repositories")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetProductTeamResponse>))]
    [SwaggerOperation(nameof(UpdateProductTeamsRepositoryStatus), OperationId = nameof(UpdateProductTeamsRepositoryStatus))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> UpdateProductTeamsRepositoryStatus(CancellationToken ct)
    {
        var updated = await _productTeamsService.UpdateProductTeamsRepositoriesAsync(ct);

        return ToActionResult(updated);
    }
    

    /// <summary>
    /// Retrieves publication information for product team in a given week
    /// </summary>
    /// <param name="productTeamId">filter for querying productTeams documents</param>
    /// <param name="week">filter for querying productTeams documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan productTeams</returns>
    [HttpGet("publication")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetProductTeamResponse>>))]
    [SwaggerOperation(nameof(GetProductTeams), OperationId = nameof(GetProductTeams))]
    // [Authorize(Policy = "DevDocsReadPolicy")]
    // [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    public async Task<IActionResult> GetProductTeamPublication([FromQuery] string productTeamId, [FromQuery] string week, CancellationToken ct)
    {
        var productTeams = await _productTeamsService.GetProductTeamPublicationAsync(productTeamId, week, ct);
        return ToActionResult(productTeams);
    }

}