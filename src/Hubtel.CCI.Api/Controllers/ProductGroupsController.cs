using System.Net.Mime;
using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductGroup;
using Hubtel.CCI.Api.Dtos.Responses.ProductGroup;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the productGroups model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class ProductGroupsController : HubtelControllerBase
{
    private readonly IProductGroupService _productGroupsService;
    public ProductGroupsController(IProductGroupService productGroupsService)
    {
        _productGroupsService = productGroupsService;
    }

    /// <summary>
    /// Gets a single productGroups
    /// </summary>
    /// <param name="id">id of productGroups</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetProductGroupResponse>))]
    [SwaggerOperation(nameof(GetProductGroup), OperationId = nameof(GetProductGroup))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetProductGroup(string id, CancellationToken ct)
    {
        var productGroups = await _productGroupsService.GetProductGroupAsync(id, ct);
        return ToActionResult(productGroups);
    }

    /// <summary>
    /// Retrieves paginated list oan productGroups
    /// </summary>
    /// <param name="filter">filter for querying productGroups documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan productGroups</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetProductGroupResponse>>))]
    [SwaggerOperation(nameof(GetProductGroups), OperationId = nameof(GetProductGroups))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetProductGroups([FromQuery] SearchFilter filter, CancellationToken ct)
    {
        var productGroups = await _productGroupsService.GetProductGroupsAsync(filter, ct);
        return ToActionResult(productGroups);
    }

    /// <summary>
    /// Creates an productGroups
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetProductGroupResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CreateProductGroup), OperationId = nameof(CreateProductGroup))]
    [Validate<CreateProductGroupRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> CreateProductGroup([FromBody] CreateProductGroupRequest model,
        CancellationToken ct)
    {
        var added = await _productGroupsService.AddProductGroupAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Updates an productGroups
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetProductGroupResponse>))]
    [SwaggerOperation(nameof(UpdateProductGroup), OperationId = nameof(UpdateProductGroup))]
    [Validate<UpdateProductGroupRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> UpdateProductGroup(string id, [FromBody] UpdateProductGroupRequest model,
        CancellationToken ct)
    {
        var updated = await _productGroupsService.UpdateProductGroupAsync(id, model, ct);

        return ToActionResult(updated);
    }

    /// <summary>
    /// Deletes an productGroups
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteProductGroup), OperationId = nameof(DeleteProductGroup))]
    [Authorize(Policy = "DevDocsDeletePolicy")]
    public async Task<IActionResult> DeleteProductGroup(string id, CancellationToken ct)
    {
        var deleted = await _productGroupsService.DeleteProductGroupAsync(id, ct);
        return ToActionResult(deleted);
    }

    /// <summary>
    /// Get the product group and team that a repository belongs to given an id or sonarqube key
    /// </summary>
    /// <param name="sonarqubeKey">sonarqube key</param>
    /// <param name="repositoryId">repository id</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpGet("repository-group")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetProductGroupByRepositoryIdOrSonarQubeKey),
        OperationId = nameof(GetProductGroupByRepositoryIdOrSonarQubeKey))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetProductGroupByRepositoryIdOrSonarQubeKey([FromQuery] string repositoryId,
        [FromQuery] string sonarqubeKey, CancellationToken ct)
    {
        var deleted =
            await _productGroupsService.GetProductGroupByRepositoryIdOrSonarQubeKey(repositoryId, sonarqubeKey, ct);
        return ToActionResult(deleted);
    }
}