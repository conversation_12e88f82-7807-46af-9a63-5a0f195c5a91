using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.MicrosoftTeams;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;



[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class TeamsNotificationController(ITeamsNotificationService teamsNotificationService) : HubtelControllerBase
{
    /// <summary>
    /// Creates a repositories
    /// </summary>
    /// <returns></returns>
    [HttpGet("auth/start")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(StartTeamsNotificationAuthAsync), OperationId = nameof(StartTeamsNotificationAuthAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> StartTeamsNotificationAuthAsync()
    {
        var result = await teamsNotificationService.StartMSALDelegateAuthFlowAsync();
        return ToActionResult(result);
    }


    /// <summary>
    /// Creates a repositories
    /// </summary>
    /// <returns></returns>
    [HttpPost("auth/complete")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CompleteTeamsNotificationAuthAsync), OperationId = nameof(CompleteTeamsNotificationAuthAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> CompleteTeamsNotificationAuthAsync([FromBody] RedirectPayloadRequest request)
    {
        var result = await teamsNotificationService.CompleteMSALDelegateAuthFlowAsync(request);
        return ToActionResult(result);
    }
    

        /// <summary>
    /// Creates a repositories
    /// </summary>
    /// <returns></returns>
    [HttpPost("trigger-message")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(SendTeamsMessageAsync), OperationId = nameof(SendTeamsMessageAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> SendTeamsMessageAsync([FromBody]  TeamsMessageRequest request)
    {
        var result = await teamsNotificationService.SendTeamsMessageAsync(request);
        return ToActionResult(result);
    }
        
    /// <summary>
    /// Sends a Teams message with multiple PDF attachments
    /// </summary>
    /// <returns></returns>
    [HttpPost("trigger-message-with-files"), DisableRequestSizeLimit,
     RequestFormLimits(MultipartBodyLengthLimit = int.MaxValue, ValueLengthLimit = int.MaxValue)]
    //[Consumes("multipart/form-data")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(SendTeamsMessageWithFilesAsync), OperationId = nameof(SendTeamsMessageWithFilesAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> SendTeamsMessageWithFilesAsync(
        [FromForm] string recipientEmail,
        [FromForm] string messageContent,
        [FromForm] List<IFormFile> uploadedFiles)
    {
        var result = await teamsNotificationService.SendTeamsMessageWithFilesAsync(recipientEmail, messageContent, uploadedFiles);
        return ToActionResult(result);
    }
    
    /// <summary>
    /// Creates a repositories
    /// </summary>
    /// <returns></returns>
    [HttpPost("trigger-report-automation")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(TriggerProductTeamReportNotifierFlowAsync), OperationId = nameof(TriggerProductTeamReportNotifierFlowAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> TriggerProductTeamReportNotifierFlowAsync()
    {
        var result = await teamsNotificationService.TriggerFlowToAutomateReportNotifierAsync();
        return ToActionResult(result);
    }

}