using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Azure;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the sonarqube integrations
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class AzureController(IAzureService azureService): HubtelControllerBase
{
    /// <summary>
    ///     Endpoint To Import Azure DevOps release definitions along with triggering Release Info Aggregation
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    [HttpPost("import-release-definitions")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [SwaggerOperation(nameof(ImportAzureDevOpsReleaseDefinitionsAsync), OperationId = nameof(ImportAzureDevOpsReleaseDefinitionsAsync))]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken ct = default)
    {
        var result = await azureService.ImportAzureDevOpsReleaseDefinitionsAsync(ct);
        return ToActionResult(result);
    }


    /// <summary>
    /// Fetch Azure DevOps release definitions
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("release-definitions")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetAzureReleaseDefinitionResponse>>))]
    [SwaggerOperation(nameof(FetchReleaseDefinitionsAsync), OperationId = nameof(FetchReleaseDefinitionsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchReleaseDefinitionsAsync([FromQuery] FetchAzureDefinitionRequest filter,CancellationToken ct)
    {
        var result = await azureService.FetchReleaseDefinitionsAsync(filter,ct);
        return ToActionResult(result);
    }
    
    /// <summary>
    /// Fetch Azure DevOps release deployments publications
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("release-deployments")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetAzureReleaseDeploymentRecords>>))]
    [SwaggerOperation(nameof(FetchReleaseDeploymentsAsync), OperationId = nameof(FetchReleaseDeploymentsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchReleaseDeploymentsAsync([FromQuery] FetchAzureDeploymentsRequest filter,CancellationToken ct)
    {
        var result = await azureService.FetchReleaseDeploymentsAsync(filter,ct);
        return ToActionResult(result);
    }
    
    
    /// <summary>
    /// Fetch Azure DevOps release deployments statistics
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("release-deployments-statistics")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetAzureReleaseDeploymentRecords>>))]
    [SwaggerOperation(nameof(FetchReleaseDeploymentsPublicationStatisticsAsync), OperationId = nameof(FetchReleaseDeploymentsPublicationStatisticsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchReleaseDeploymentsPublicationStatisticsAsync(
        [FromQuery] FetchAzureDeploymentStatisticsRequest filter,CancellationToken ct)
    {
        var result = await azureService.FetchReleaseDeploymentsPublicationStatisticsAsync(filter,ct);
        return ToActionResult(result);
    }


    /// <summary>
    /// Fetch Azure DevOps release deployments details
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("release-deployments-details")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetAzureReleaseDeploymentRecords>>))]
    [SwaggerOperation(nameof(FetchReleaseDeploymentsPublicationDetailsAsync), OperationId = nameof(FetchReleaseDeploymentsPublicationDetailsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchReleaseDeploymentsPublicationDetailsAsync(
        [FromQuery] FetchAzureDeploymentStatisticsRequest filter, CancellationToken ct)
    {
        var result = await azureService.FetchReleaseDeploymentsPublicationDetailsAsync(filter,ct);
        return ToActionResult(result);
    }
    
    
    /// <summary>
    ///     Updates repositories with azure repository fields.
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    [HttpPost("update-repositories")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [SwaggerOperation(nameof(UpdateRepositoriesAsync), OperationId = nameof(UpdateRepositoriesAsync))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> UpdateRepositoriesAsync(CancellationToken ct = default)
    {
        var result = await azureService.UpdateRepositoriesAsync(ct);
        return ToActionResult(result);
    }
    
    
    /// <summary>
    ///     Deletes a published Azure DevOps release deployment.
    /// </summary>
    /// <param name="publicationDate">The date of the publication to delete.</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    [HttpDelete("deployment-publication")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [SwaggerOperation(nameof(UpdateRepositoriesAsync), OperationId = nameof(UpdateRepositoriesAsync))]
    // [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> DeleteAzureDeploymentPublication([FromQuery] DateTime publicationDate,CancellationToken ct)
    {
        var result = await azureService.DeleteAzureDeploymentPublicationAsync(publicationDate,ct);
        return ToActionResult(result);
    }
}