using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Responses.DciServiceScore;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
/// Contains all endpoints for the DCI service score model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class DciServiceScoresController : HubtelControllerBase
{
    private readonly IDciServiceScoreService _dciServiceScoreService;

    public DciServiceScoresController(IDciServiceScoreService dciServiceScoreService)
    {
        _dciServiceScoreService = dciServiceScoreService;
    }

    /// <summary>
    /// Gets a single DCI service score
    /// </summary>
    /// <param name="id">id of DCI service score</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetDciServiceScoreResponse>))]
    [SwaggerOperation(nameof(GetDciServiceScore), OperationId = nameof(GetDciServiceScore))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetDciServiceScore(string id, CancellationToken ct)
    {
        var dciServiceScore = await _dciServiceScoreService.GetDciServiceScoreAsync(id, ct);
        return ToActionResult(dciServiceScore);
    }

    /// <summary>
    /// Retrieves paginated list of DCI service scores
    /// </summary>
    /// <param name="filter">filter for querying DCI service score documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list of DCI service scores</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetDciServiceScoreResponse>>))]
    [SwaggerOperation(nameof(GetDciServiceScores), OperationId = nameof(GetDciServiceScores))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetDciServiceScores([FromQuery] SearchFilter filter, CancellationToken ct)
    {
        var dciServiceScores = await _dciServiceScoreService.GetDciServiceScoresAsync(filter, ct);
        return ToActionResult(dciServiceScores);
    }
    
    /// <summary>
    /// Triggers the publication of Dci Service scores.
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpPost("trigger-dci-service-score-publication")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<bool>))]
    [SwaggerOperation(nameof(TriggerDciServiceScorePublicationAsync),
        OperationId = nameof(TriggerDciServiceScorePublicationAsync))]
    // [Authorize(Policy = "DevDocsPublishPolicy")]
    [AllowAnonymous]
    public async Task<IActionResult> TriggerDciServiceScorePublicationAsync(CancellationToken ct)
    {
        var response = await _dciServiceScoreService.TriggerDciServiceScorePublicationAsync(ct);
        return ToActionResult(response);
    }   
}