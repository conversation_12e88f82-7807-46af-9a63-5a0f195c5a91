using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.SonarQube;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the sonarqube integrations
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class SonarQubeController : HubtelControllerBase
{
    private readonly ISonarQubeService _sonarQubeService;

    public SonarQubeController(ISonarQubeService sonarQubeService)
    {
        _sonarQubeService = sonarQubeService;
    }


    /// <summary>
    /// Creates a repositories
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost("import-azure-repository")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<SonarQubeApiResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(ImportAzureRepositoryAsync), OperationId = nameof(ImportAzureRepositoryAsync))]
    [Validate<ImportAzureRepositoryRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> ImportAzureRepositoryAsync([FromBody] ImportAzureRepositoryRequest model,
        CancellationToken ct)
    {
        var added = await _sonarQubeService.ImportAzureDevOpsRepositoryAsync(model.ProjectName, model.RepositoryName,
            ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Gets new metrics for a pull request
    /// </summary>
    /// <param name="pullRequestId"></param>
    /// <param name="repositoryId"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("new-metrics-on-pr")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<SonarQubeMetricsResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetNewMetricsOnPr), OperationId = nameof(GetNewMetricsOnPr))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetNewMetricsOnPr([FromQuery] int pullRequestId, [FromQuery] string repositoryId,
        CancellationToken ct)
    {
        var response = await _sonarQubeService.GetNewMetricsOnPrAsync(pullRequestId, repositoryId, ct);
        return ToActionResult(response);
    }


    /// <summary>
    /// Triggers the Engineer SonarQube Metrics flow
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpPost("publish-engineer-sonarqube-metrics")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(FetchEngineerSonarQubeMetrics), OperationId = nameof(FetchEngineerSonarQubeMetrics))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> FetchEngineerSonarQubeMetrics(CancellationToken ct)
    {
        var response = await _sonarQubeService.FetchEngineersSonarQubeMetricsAsync(ct);
        return ToActionResult(response);
    }


    /// <summary>
    /// Triggers the Gather Repository Language Distribution flow
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpPost("gather-repository-language-distribution")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GatherRepositoryLanguageDistribution), OperationId = nameof(GatherRepositoryLanguageDistribution))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> GatherRepositoryLanguageDistribution(CancellationToken ct)
    {
        var response = await _sonarQubeService.FetchRepositoryLanguageDistributionAsync(ct);
        return ToActionResult(response);
    }
    
    
    /// <summary>
    /// Fetches the Published Repository Language Distribution Statistics
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("language-distribution-statistics")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetRepositoryLanguageDistributionStatistics), OperationId = nameof(GetRepositoryLanguageDistributionStatistics))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetRepositoryLanguageDistributionStatistics([FromQuery] GetLanguageDistributionStatisticsRequest filter,
        CancellationToken ct)
    {
        var response = await _sonarQubeService.GetLanguagePublicationStatisticsAsync(filter,ct);
        return ToActionResult(response);
    }
    
    
    /// <summary>
    /// Fetches the Published Repository Language Distribution Publication Table
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("language-distribution-table")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetRepositoryLanguageDistributionPublicationTable), OperationId = nameof(GetRepositoryLanguageDistributionPublicationTable))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetRepositoryLanguageDistributionPublicationTable([FromQuery] GetLanguageDistributionStatisticsRequest filter,
        CancellationToken ct)
    {
        var response = await _sonarQubeService.GetLanguagePublicationTableAsync(filter,ct);
        return ToActionResult(response);
    }
}