using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Net.Mime;

namespace Hubtel.CCI.Api.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [SwaggerTag("Tooling Tools")]
    public class ToolingToolsController : HubtelControllerBase
    {
        private readonly IToolingToolsService _toolingToolsService;
        public ToolingToolsController(IToolingToolsService toolingToolsService)
        {
            _toolingToolsService = toolingToolsService;
        }


        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CreateToolResponse>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<CreateToolResponse>))]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> CreateToolAsync([FromBody] CreateToolRequest request, CancellationToken ct)
        {
            var response = await _toolingToolsService.CreateToolAsync(request, ct);
            return ToActionResult(response);
        }
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<GetToolResponse>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<GetToolResponse>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<GetToolResponse>))]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(GetTool), OperationId = nameof(GetTool))]
        public async Task<IActionResult> GetTool([FromRoute] string id, CancellationToken ct)
        {
            var response = await _toolingToolsService.GetToolAsync(id, ct);
            return ToActionResult(response);
        }

        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<List<GetToolResponse>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<List<GetToolResponse>>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<List<GetToolResponse>>))]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(GetTools), OperationId = nameof(GetTools))]
        public async Task<IActionResult> GetTools([FromQuery]SearchToolFilter filter,CancellationToken ct) //add filters and pagination later
        {
            var response = await _toolingToolsService.GetToolsAsync(filter,ct);
            return ToActionResult(response);
        }


        [HttpPut]
        [Route("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<CreateToolResponse>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<UpdateToolRequest>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<CreateToolResponse>))]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(UpdateTool), OperationId = nameof(UpdateTool))]
        public async Task<IActionResult> UpdateTool([FromRoute] string id, [FromBody] UpdateToolRequest request, CancellationToken ct)
        {
            var response = await _toolingToolsService.UpdateToolAsync(id, request, ct);
            return ToActionResult(response);
        }

        [HttpDelete]
        [Route("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent, Type = typeof(IApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<object>))]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(DeleteTool), OperationId = nameof(DeleteTool))]
        public async Task<IActionResult> DeleteTool([FromRoute] string id, CancellationToken ct)
        {
            var response = await _toolingToolsService.DeleteToolAsync(id, ct);
            return ToActionResult(response);
        }
        
    }
}
