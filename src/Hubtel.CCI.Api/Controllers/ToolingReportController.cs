using System.Net.Mime;
using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;

namespace Hubtel.CCI.Api.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[SwaggerTag("Tooling Tools")]
public class ToolingReportController : HubtelControllerBase
{
    private readonly IToolingToolsService _toolingToolsService;

    public ToolingReportController(IToolingToolsService toolingToolsService)
    {
        _toolingToolsService = toolingToolsService;
    }

    [HttpPost("/trigger-tooling-report-data-aggregation")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<object>))]
    public IActionResult TriggerReportDataAggregation([FromServices] IActorService<MainActor> requiredActor)
    {
        requiredActor.Tell(new StartFrontendToolingReportGenerationMessage(), ActorRefs.Nobody);
        requiredActor.Tell(new StartBackendToolingReportGenerationMessage(), ActorRefs.Nobody);
        return Ok(new ApiResponse<object>("Report data aggregation triggered successfully", 200));
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<List<GetToolingReportsResponse>>))]
    public async Task<IActionResult> GetToolingReports([FromQuery] GetToolingReportsRequest request, CancellationToken token)
    {
        return Ok(await _toolingToolsService.GetToolingReportsAsync(request, token));
    }
    [HttpGet]
    [Route("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<GetToolingReportsResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<GetToolingReportsResponse>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<GetToolingReportsResponse>))]
    [Produces(MediaTypeNames.Application.Json)]
    [SwaggerOperation(nameof(GetToolingReport), OperationId = nameof(GetToolingReport))]
    public async Task<IActionResult> GetToolingReport([FromRoute] string id, CancellationToken ct)
    {
        var response = await _toolingToolsService.GetToolingReportAsync(id, ct);
        return ToActionResult(response);
    }

    [HttpPatch]
    [Consumes(MediaTypeNames.Application.JsonPatch)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<ToolingReport>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(UpdateToolingReports), OperationId = nameof(UpdateToolingReports))]
    public async Task<IActionResult> UpdateToolingReports(string id, [FromBody] JsonPatchDocument<ToolingReport> patchDoc
        , CancellationToken ct)
    {
        await Task.CompletedTask;
        return Ok();
    }

    [HttpPut]
    [Route("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<UpdateToolingReportResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<UpdateToolingReportResponse>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<UpdateToolingReportResponse>))]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [SwaggerOperation(nameof(UpdateToolingReport), OperationId = nameof(UpdateToolingReport))]
    public async Task<IActionResult> UpdateToolingReport([FromRoute] string id, [FromBody] UpdateToolingReportRequest request, CancellationToken ct)
    {
        var response = await _toolingToolsService.UpdateToolingReportAsync(id, request, ct);
        return ToActionResult(response);
    }

    [HttpPost("{id}")]
    [Route("generate-and-send-tooling-report")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<bool>))]
    [Consumes(MediaTypeNames.Application.Json)]
    [SwaggerOperation(nameof(GenerateAndSendToolingReport), OperationId = nameof(GenerateAndSendToolingReport))]
    public async Task<IActionResult> GenerateAndSendToolingReport(string id, CancellationToken ct)
    {
        var response = await _toolingToolsService.GenerateAndSendToolingReport(id,ct);
        return ToActionResult(response);
    }

    
}