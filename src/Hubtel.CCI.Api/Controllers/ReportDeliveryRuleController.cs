using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ReportDeliveryRule;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;


[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
 [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
public class ReportDeliveryRuleController(IReportDeliveryRuleService reportDeliveryRuleService): HubtelControllerBase
{
    
    /// <summary>
    /// Gets a single report delivery rule
    /// </summary>
    /// <param name="id">id of repositories</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetReportDeliveryRule), OperationId = nameof(GetReportDeliveryRule))]
     [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
     [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetReportDeliveryRule(string id, CancellationToken ct)
    {
        var reportDeliveryRule = await reportDeliveryRuleService.GetReportDeliveryRuleAsync(id, ct);
        return ToActionResult(reportDeliveryRule);
    }
    
    
    [HttpPost]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(AddReportDeliveryRule), OperationId = nameof(AddReportDeliveryRule))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> AddReportDeliveryRule([FromBody] CreateReportDeliveryRule rule, CancellationToken ct)
    {
        var reportDeliveryRule = await reportDeliveryRuleService.CreateReportDeliveryRuleAsync(rule, ct);
        return ToActionResult(reportDeliveryRule);
    }
    
    
    /// <summary>
    /// Gets a single report delivery rule
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(GetReportDeliveryRules), OperationId = nameof(GetReportDeliveryRules))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetReportDeliveryRules([FromQuery] SearchFilter filter,CancellationToken ct)
    {
        var reportDeliveryRule = await reportDeliveryRuleService.GetReportDeliveryRulesAsync(filter, ct);
        return ToActionResult(reportDeliveryRule);
    }
    
    
    /// <summary>
    /// Gets a single report delivery rule
    /// </summary>
    /// <param name="id">id of repositories</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteReportDeliveryRule), OperationId = nameof(DeleteReportDeliveryRule))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> DeleteReportDeliveryRule(string id, CancellationToken ct)
    {
        var reportDeliveryRule = await reportDeliveryRuleService.DeleteReportDeliveryRuleAsync(id, ct);
        return ToActionResult(reportDeliveryRule);
    }
}