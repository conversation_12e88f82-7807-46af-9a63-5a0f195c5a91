using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Engineer;
using Hubtel.CCI.Api.Dtos.Responses.Engineer;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     contains all endpoints for the engineer model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class EngineersController : HubtelControllerBase
{
    private readonly IEngineerService _engineerService;

    public EngineersController(IEngineerService engineerService)
    {
        _engineerService = engineerService;
    }

    /// <summary>
    /// Gets a single engineer by email
    /// </summary>
    /// <param name="email">email of engineer</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("email/{email}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetEngineerResponse>))]
    [SwaggerOperation(nameof(GetEngineerByEmailAsync), OperationId = nameof(GetEngineerByEmailAsync))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetEngineerByEmailAsync(string email, CancellationToken ct)
    {
        var engineer = await _engineerService.GetEngineerByEmailAsync(email, ct);
        return ToActionResult(engineer);
    }

    /// <summary>
    /// Gets a single engineer
    /// </summary>
    /// <param name="id">id of engineer</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetEngineerResponse>))]
    [SwaggerOperation(nameof(GetEngineer), OperationId = nameof(GetEngineer))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetEngineer(string id, CancellationToken ct)
    {
        var engineer = await _engineerService.GetEngineerAsync(id, ct);
        return ToActionResult(engineer);
    }

    /// <summary>
    /// Retrieves paginated list oan engineers
    /// </summary>
    /// <param name="filter">filter for querying engineer documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan engineers</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResult<GetEngineerResponse>>))]
    [SwaggerOperation(nameof(GetEngineers), OperationId = nameof(GetEngineers))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetEngineers([FromQuery] SearchFilter filter, CancellationToken ct)
    {
        var engineers = await _engineerService.GetEngineersAsync(filter, ct);
        return ToActionResult(engineers);
    }

    /// <summary>
    /// Creates an engineer
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetEngineerResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(CreateEngineer), OperationId = nameof(CreateEngineer))]
    [Validate<CreateEngineerRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> CreateEngineer([FromBody] CreateEngineerRequest model, CancellationToken ct)
    {
        var added = await _engineerService.AddEngineerAsync(model, ct);
        return ToActionResult(added);
    }

    /// <summary>
    /// Updates an engineer
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json), Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetEngineerResponse>))]
    [SwaggerOperation(nameof(UpdateEngineer), OperationId = nameof(UpdateEngineer))]
    [Validate<UpdateEngineerRequest>("model")]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> UpdateEngineer(string id, [FromBody] UpdateEngineerRequest model,
        CancellationToken ct)
    {
        var updated = await _engineerService.UpdateEngineerAsync(id, model, ct);

        return ToActionResult(updated);
    }

    /// <summary>
    /// Deletes an engineer
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteEngineer), OperationId = nameof(DeleteEngineer))]
    [Authorize(Policy = "DevDocsDeletePolicy")]
    public async Task<IActionResult> DeleteEngineer(string id, CancellationToken ct)
    {
        var deleted = await _engineerService.DeleteEngineerAsync(id, ct);
        return ToActionResult(deleted);
    }
}