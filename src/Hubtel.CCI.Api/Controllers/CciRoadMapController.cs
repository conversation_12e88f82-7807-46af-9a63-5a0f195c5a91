using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMapRecord;
using Hubtel.CCI.Api.Dtos.Responses.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Responses.CciRoadMapRecord;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;


/// <summary>
///     contains all endpoints for the cci roadmap model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class CciRoadMapController(ICciRoadMapService cciRoadMapService, 
    ICciRoadMapMetricUpdateRecordsService cciRoadMapMetricUpdateRecordsService) : HubtelControllerBase
{
    /// <summary>
    /// Creates a cci road map
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetCciRoadMapResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GetCciRoadMapResponse>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<GetCciRoadMapResponse>))]
    [SwaggerOperation(nameof(CreateCciRoadMap), OperationId = nameof(CreateCciRoadMap))]
    //[Validate<CreateCciRoadMapRequest>("model")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> CreateCciRoadMap([FromBody] CreateCciRoadMapRequest model,
        CancellationToken ct)
    {
        var added = await cciRoadMapService.AddCciRoadMapAsync(model, ct);
        return ToActionResult(added);
    }
    
    
    /// <summary>
    /// Retrieves paginated list oan cciRoadMaps
    /// </summary>
    /// <param name="filter">filter for querying cciRoadMaps documents</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Paginated list oan cciRoadMaps</returns>
    [HttpGet]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRoadMapResponse>>))]
    [SwaggerOperation(nameof(GetCciRoadMaps), OperationId = nameof(GetCciRoadMapResponse))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRoadMaps([FromQuery] SearchCciRoadMapRequest filter,
        CancellationToken ct)
    {
        var cciRepositoryScores = await cciRoadMapService.GetCciRoadMapsAsync(filter, ct);
        return ToActionResult(cciRepositoryScores);
    }
        
    /// <summary>
    /// Deletes a repository's cciRoadMaps
    /// </summary>
    /// <param name="id"> Id of RoadMap to delete</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>A boolean value indicating successful deletion of cciRoadMap</returns>
    [HttpDelete("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRoadMapResponse>>))]
    [SwaggerOperation(nameof(DeleteCciRoadMap), OperationId = nameof(GetCciRoadMapResponse))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> DeleteCciRoadMap([FromRoute] string id,
        CancellationToken ct)
    {
        var cciRoadMap = await cciRoadMapService.DeleteCciRoadMapAsync(id, ct);
        return ToActionResult(cciRoadMap);
    }
    
    
    /// <summary>
    /// Retrieves paginated list oan cciRoadMaps
    /// </summary>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Boolean value determining update result</returns>
    [HttpPost("update-scores")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRoadMapResponse>>))]
    [SwaggerOperation(nameof(UpdateCciRoadMapScores), OperationId = nameof(GetCciRoadMapResponse))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> UpdateCciRoadMapScores(
        CancellationToken ct)
    {
        var cciRepositoryScores = await cciRoadMapMetricUpdateRecordsService
            .ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime.Now, DateTime.Now,ct);
        return ToActionResult(cciRepositoryScores);
    }
    
    
    /// <summary>
    /// Creates a cci road map record
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpPost("record")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [SwaggerOperation(nameof(CreateCciRoadMapRecordV2), OperationId = nameof(CreateCciRoadMapRecordV2))]
    //[Validate<CreateCciRoadMapRequest>("model")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(Policy = "DevDocsPublishPolicy")]
    public async Task<IActionResult> CreateCciRoadMapRecordV2([FromBody] CreateCciRoadMapRecordRequest model,
        CancellationToken ct)
    {
        var added = await cciRoadMapService.AddCciRoadMapV2Async(model, ct);
        return ToActionResult(added);
    }
    
    
    /// <summary>
    /// Gets a cci road map record by id
    /// </summary>
    /// <param name="id">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpGet("record/{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [SwaggerOperation(nameof(GetCciRoadMapRecordV2), OperationId = nameof(GetCciRoadMapRecordV2))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRoadMapRecordV2([FromRoute] string id,
        CancellationToken ct)
    {
        var added = await cciRoadMapService.GetCciRoadMapRecordByIdAsync(id, ct);
        return ToActionResult(added);
    }
    
    
    /// <summary>
    /// Gets a cci road map record by id
    /// </summary>
    /// <param name="model">Must be valid and not null</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns></returns>
    [HttpGet("records")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<GetCciRoadMapRecordResponse>))]
    [SwaggerOperation(nameof(GetCciRoadMapRecords), OperationId = nameof(GetCciRoadMapRecords))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciRoadMapRecords([FromQuery] SearchCciRoadMapRequest model,
        CancellationToken ct)
    {
        var added = await cciRoadMapService.GetCciRoadMapRecordsV2Async(model, ct);
        return ToActionResult(added);
    }
    
    
    /// <summary>
    /// Deletes a repository's cciRoadMap Records
    /// </summary>
    /// <param name="id"> Id of RoadMap to delete</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>A boolean value indicating successful deletion of cciRoadMap</returns>
    [HttpDelete("record/{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK,
        Type = typeof(ApiResponse<PagedResult<GetCciRoadMapResponse>>))]
    [SwaggerOperation(nameof(DeleteCciRoadMapRecord), OperationId = nameof(GetCciRoadMapResponse))]
    [Authorize(Policy = "DevDocsDeletePolicy")]
    public async Task<IActionResult> DeleteCciRoadMapRecord([FromRoute] string id,
        CancellationToken ct)
    {
        var cciRoadMap = await cciRoadMapService.DeleteCciRoadMapRecordAsync(id, ct);
        return ToActionResult(cciRoadMap);
    }
}