using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;

/// <summary>
///     This service contains all endpoints for the repositories model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class AuthController : HubtelControllerBase
{
    private readonly IEngineerService _engineerService;

    public AuthController(IEngineerService lookupService)
    {
        _engineerService = lookupService;
    }

    /// <summary>
    /// Returns the information security details of a user by their email.
    /// </summary>
    /// <param name="email">email</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("lookup-user/{email}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<AuthClaim>))]
    [SwaggerOperation(nameof(LookupInfoSecUserAsync), OperationId = nameof(LookupInfoSecUserAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> LookupInfoSecUserAsync([Required] [EmailAddress] string email,
        CancellationToken ct)
    {
        var user = User.GetAccount();

        if (user.AuthenticationMethod is CommonConstants.AuthScheme.Bearer)
        {
            // later exceptions will come with permission checks
            email = user.EmailAddress;
        }

        var response = await _engineerService.GetEngineerByEmailAsync(email, ct);
        
        var engineer = response.Code == 200 ? response.Data : new Engineer()
        {
            Id = user.Id,
            Name = user.FullName,
            Email = user.EmailAddress,
            JobLevel = "Executive",
            Domain = ["Frontend"]
        };
        
        return ToActionResult(new { Claim = user, Engineer = engineer }.ToOkApiResponse());
    }
}