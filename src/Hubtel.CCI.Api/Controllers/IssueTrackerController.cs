using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Net.Mime;

namespace Hubtel.CCI.Api.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
    [Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
    [SwaggerTag("Tooling Tools")]
    public class IssueTrackerController : HubtelControllerBase
    {
        private readonly IIssueTrackerService _issueTrackerService;

        public IssueTrackerController(IIssueTrackerService issueTrackerService)
        {
            _issueTrackerService = issueTrackerService;
        }

        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CreateIssueTrackerResponse>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<CreateIssueTrackerResponse>))]
        public async Task<IActionResult> CreateIssue(CreateIssueTrackerRequest createIssue, CancellationToken ct)
        {
            return ToActionResult(await _issueTrackerService.CreateIssueAsync(createIssue, ct));
        }

        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<PagedResult<GetIssueTrackerResponse>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<PagedResult<GetIssueTrackerResponse>>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<PagedResult<GetIssueTrackerResponse>>))]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(GetIssues), OperationId = nameof(GetIssues))]
        public async Task<IActionResult> GetIssues([FromQuery] GetIssueTrackerRequest filter, CancellationToken ct)
        {
            var response = await _issueTrackerService.GetIssuesAsync(filter, ct);
            return ToActionResult(response);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<PagedResult<GetIssueTrackerResponse>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<PagedResult<GetIssueTrackerResponse>>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<PagedResult<GetIssueTrackerResponse>>))]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(GetIssuesById), OperationId = nameof(GetIssuesById))]
        public async Task<IActionResult> GetIssuesById([FromRoute] string id, CancellationToken ct)
        {
            var response = await _issueTrackerService.GetIssueByIdAsync(id, ct);
            return ToActionResult(response);
        }

        [HttpPut]
        [Route("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<CreateIssueTrackerResponse>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<CreateIssueTrackerResponse>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<CreateIssueTrackerResponse>))]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(UpdateIssue), OperationId = nameof(UpdateIssue))]
        public async Task<IActionResult> UpdateIssue([FromRoute] string id, [FromBody] UpdateIssueTrackerRequest model, CancellationToken ct)
        {
            var response = await _issueTrackerService.UpdateIssueAsync(id, model, ct);
            return ToActionResult(response);

        }

        [HttpPatch]
        [Route("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(IApiResponse<CreateIssueTrackerResponse>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(IApiResponse<CreateIssueTrackerResponse>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(IApiResponse<CreateIssueTrackerResponse>))]
        [Consumes(MediaTypeNames.Application.JsonPatch)]
        [Produces(MediaTypeNames.Application.Json)]
        [SwaggerOperation(nameof(PatchIssue), OperationId = nameof(PatchIssue))]
        public async Task<IActionResult> PatchIssue([FromRoute] string id, [FromBody] JsonPatchDocument<UpdateIssueTrackerRequest> patchDoc, CancellationToken ct)
        {
            if (patchDoc == null)
            {
                return ToActionResult(ApiResponse<CreateIssueTrackerResponse>.Default.ToBadRequestApiResponse("Patch document cannot be null."));
            }
            var updateIssue = new UpdateIssueTrackerRequest();
            patchDoc.ApplyTo(updateIssue);
            var response = await _issueTrackerService.PatchIssueAsync(id, updateIssue, ct);
            return ToActionResult(response);

        }
    }
}