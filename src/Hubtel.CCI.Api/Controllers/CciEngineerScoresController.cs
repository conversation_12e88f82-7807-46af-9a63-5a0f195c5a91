using System.Net.Mime;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciEngineerScore;
using Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Controllers;


/// <summary>
///    This class contains all endpoints for the cci engineers score model
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Bearer}")]
[Authorize(AuthenticationSchemes = $"{CommonConstants.AuthScheme.Basic}")]
public class CciEngineerScoresController(ICciEngineerScoreService cciEngineerScoreService): HubtelControllerBase
{
    
    /// <summary>
    /// Gets a single cciEngineerScore
    /// </summary>
    /// <param name="id">id of cciRepositoryScore</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CciEngineerScoreResponse>))]
    [SwaggerOperation(nameof(GetCciEngineerScore), OperationId = nameof(GetCciEngineerScore))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciEngineerScore(string id, CancellationToken ct)
    {
        var cciRepositoryScore = await cciEngineerScoreService.GetCciEngineerScoreByIdAsync(id, ct);
        return ToActionResult(cciRepositoryScore);
    }
    
    
    /// <summary>
    /// Gets a single cciEngineerSonarQubeMetrics record
    /// </summary>
    /// <param name="id">id of cciRepositoryScore</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("sonarqube-metrics/{id}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CciEngineerSonarQubeMetricsResponse>))]
    [SwaggerOperation(nameof(GetCciEngineerSonarQubeMetricsRecord), OperationId = nameof(GetCciEngineerSonarQubeMetricsRecord))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciEngineerSonarQubeMetricsRecord(string id, CancellationToken ct)
    {
        var cciRepositoryScore = await cciEngineerScoreService.GetCciEngineerSonarQubeMetricsByIdAsync(id, ct);
        return ToActionResult(cciRepositoryScore);
    }
    
    /// <summary>
    /// Gets cciEngineerScoreTable records, in a paginated view
    /// </summary>
    /// <param name="request">cciEngineerScore Table Filter</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("score-table")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CciEngineerScoreResponse>))]
    [SwaggerOperation(nameof(GetCciEngineerScoreTable), OperationId = nameof(GetCciEngineerScoreTable))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciEngineerScoreTable([FromQuery] GetCciEngineerTableScoresFilter request,CancellationToken ct)
    {
        var response = await cciEngineerScoreService.GetCciEngineerTableScoresAsync(request, ct);
        return ToActionResult(response);
    }
    
    
    /// <summary>
    /// Fetch Cci Engineer Publication statistics
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("publication-statistics")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(FetchCciEngineerPublicationStatisticsAsync), OperationId = nameof(FetchCciEngineerPublicationStatisticsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchCciEngineerPublicationStatisticsAsync(
        [FromQuery] FetchCciEngineerStatisticsRequest filter,CancellationToken ct)
    {
        var result = await cciEngineerScoreService.FetchEngineerCciPublicationStatisticsAsync(filter,ct);
        return ToActionResult(result);
    }
    
    
    /// <summary>
    /// Fetch Cci Engineer Publication Rankings
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("publication-rankings")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(FetchCciEngineerPublicationRankingsAsync), OperationId = nameof(FetchCciEngineerPublicationRankingsAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchCciEngineerPublicationRankingsAsync(
        [FromQuery] FetchCciEngineerStatisticsRequest filter,CancellationToken ct)
    {
        var result = await cciEngineerScoreService.FetchEngineerCciPublicationRankingsAsync(filter,ct);
        return ToActionResult(result);
    }
    
    
    
    /// <summary>
    /// Delete Cci Engineer Publication
    /// </summary>
    /// <param name="publicationDate"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpDelete("delete-publication")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(DeleteCciEngineerPublicationAsync), OperationId = nameof(DeleteCciEngineerPublicationAsync))]
    [Authorize(Policy = "DevDocsWritePolicy")]
    public async Task<IActionResult> DeleteCciEngineerPublicationAsync(
        [FromQuery] DateTime publicationDate,CancellationToken ct)
    {
        var result = await cciEngineerScoreService.DeleteEngineerCciPublicationAsync(publicationDate,ct);
        return ToActionResult(result);
    }
    
    
    
    /// <summary>
    /// Gets cciEngineerScoreTable records, in a paginated view
    /// </summary>
    /// <param name="request">cciEngineerScore Table Filter</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    [HttpGet("sq-score-table")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CciEngineerScoreResponse>))]
    [SwaggerOperation(nameof(GetCciEngineerSqTable), OperationId = nameof(GetCciEngineerSqTable))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> GetCciEngineerSqTable([FromQuery] GetCciEngineerSqMetricsTableScoresFilter request,CancellationToken ct)
    {
        var response = await cciEngineerScoreService.GetCciEngineerTableSqAsync(request, ct);
        return ToActionResult(response);
    }
    
    
    /// <summary>
    /// Fetch Cci Engineer Publication statistics
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    [HttpGet("sq-metrics-overview")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
    [SwaggerOperation(nameof(FetchCciEngineerPublicationSqMetricsOverviewAsync), OperationId = nameof(FetchCciEngineerPublicationSqMetricsOverviewAsync))]
    [Authorize(Policy = "DevDocsReadPolicy")]
    public async Task<IActionResult> FetchCciEngineerPublicationSqMetricsOverviewAsync(
        [FromQuery] GetCciEngineerSqMetricsTableScoresFilter filter,CancellationToken ct)
    {
        var result = await cciEngineerScoreService.FetchEngineerCciPublicationSqMetricsOverviewAsync(filter,ct);
        return ToActionResult(result);
    }
}