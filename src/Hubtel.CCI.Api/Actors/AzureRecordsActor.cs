using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Services.Interfaces;

namespace Hubtel.CCI.Api.Actors;

public class AzureRecordsActor: BaseActor
{
    public AzureRecordsActor(IServiceProvider serviceProvider)
    {
        ReceiveAsync<AzureByPassedPrMessage>(async x =>
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var bypassedPrService = services.GetRequiredService<IBypassedPrService>();
            await bypassedPrService.ExecuteBypassedPrsFlowAsync();
            await Task.CompletedTask;
        });
        
        ReceiveAsync<EngineerSonarQubeMetricsMessage>(async x  =>
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var sonarQubeService = services.GetRequiredService<ISonarQubeService>();
            await sonarQubeService.ExecuteEngineerSqMetricsFlowAsync(x);
            await Task.CompletedTask;
        });
        
        
        ReceiveAsync<AzureDeploymentRecordsMessage>(async x  =>
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var azureService = services.GetRequiredService<IAzureService>();
            await azureService.TriggerProcessDefinitionsAsync(x, CancellationToken.None);
            await Task.CompletedTask;
        });
        
        
        ReceiveAsync<RepositoryLanguageDistributionMessage>(async x  =>
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var sonarQubeService = services.GetRequiredService<ISonarQubeService>();
            await sonarQubeService.ExecuteFetchRepoLanguageDistributionFlowAsync(x, CancellationToken.None);
            await Task.CompletedTask;
        });
    }
}