using Hubtel.CCI.Api.Dtos.Models;

namespace Hubtel.CCI.Api.Actors;

public class CurateDciProductRankingMessage
{
    /// <summary>
    /// Gets the ranking data.
    /// </summary>
    public List<RepositoryDciScore> RankingData { get; set; }
    public DateTime PublicationDate { get; set; }
    public string PublicationWeek { get; set; }
    public DateTime PublicationStartDate { get; set; }
    public DateTime PublicationEndDate { get; set; }
    
    /// <summary>
    /// Initializes a new instance of the <see cref="CurateDciProductRankingMessage"/> struct.
    /// </summary>
    public CurateDciProductRankingMessage(List<RepositoryDciScore> rankingData, DateTime publicationDate, string publicationWeek, DateTime publicationStartDate, DateTime publicationEndDate)
    {
        RankingData = rankingData;
        PublicationDate = publicationDate;
        PublicationWeek = publicationWeek;
        PublicationStartDate = publicationStartDate;
        PublicationEndDate = publicationEndDate;
    }
}