namespace Hubtel.CCI.Api.Actors.Messages;

/// <summary>
/// Represents a message to publish DCI service scores.
/// </summary>
public struct PublishDciServiceScoresMessage
{
    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets the publication start date.
    /// </summary>
    public DateTime PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the publication end date.
    /// </summary>
    public DateTime PublicationEndDate { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="PublishDciServiceScoresMessage"/> struct.
    /// </summary>
    /// <param name="publicationDate">The publication date.</param>
    /// <param name="publicationWeek">The publication week.</param>
    /// <param name="publicationStartDate">The publication start date.</param>
    /// <param name="publicationEndDate">The publication end date.</param>
    public PublishDciServiceScoresMessage(DateTime publicationDate, string publicationWeek, DateTime publicationStartDate, DateTime publicationEndDate)
    {
        PublicationDate = publicationDate;
        PublicationWeek = publicationWeek;
        PublicationStartDate = publicationStartDate;
        PublicationEndDate = publicationEndDate;
    }
}