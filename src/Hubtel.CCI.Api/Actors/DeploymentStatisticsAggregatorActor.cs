using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Services.Interfaces;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Actors;

public class DeploymentStatisticsAggregatorActor:BaseActor
{
    private readonly ILogger<DeploymentStatisticsAggregatorActor> _logger;
    private readonly IServiceProvider _serviceProvider;

    public DeploymentStatisticsAggregatorActor(IServiceProvider serviceProvider,
        ILogger<DeploymentStatisticsAggregatorActor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        
        //Handle Events
        ReceiveAsync<DeploymentAggregatorActorMessage>(DoProcessDeploymentStatisticsAsync);
    }


    private async Task DoProcessDeploymentStatisticsAsync(DeploymentAggregatorActorMessage message)
    {
        try
        {
            const int pageSize = 10;
            var page = 1;
            var statisticsService = _serviceProvider.CreateScope().ServiceProvider.GetService<IServicesStatisticsService>()!;
            var hasMore = true;
            _logger.LogInformation("Processing deployment statistics for services");
            while (hasMore)
            {
                var data = await statisticsService.GetRepositoriesForStatisticsAggregation(page, pageSize);
                if (data.Results.Count < 1) break;

                hasMore = page * pageSize < data.TotalCount;
                page++;

                foreach (var repository in data.Results)
                {
                    var repositoryServices =
                        await statisticsService.GetServicesForRepository(repository.Id);
                    foreach (var service in repositoryServices)
                    {
                        var res = await statisticsService.AggregateServiceStatsAsync(service, repository, message);
                        _logger.LogInformation("Processed service {Service} with {Result} deployments",
                            JsonConvert.SerializeObject(service), res);
                    }
                }

            }

            _logger.LogInformation("Completed processing deployment statistics for services");
        }
        catch (Exception e)
        {
            _logger.LogError(e, " An error occurred while processing deployment statistics");
        }
    }
}