using Flurl.Http;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Hubtel.CCI.Api.Actors;

public class FrontendToolingReportGeneratorActor : BaseActor
{
    private const string UnkownString = "unknown";
    private readonly string _pat;
    private readonly ILogger<FrontendToolingReportGeneratorActor> _logger;
    private static readonly char[] Splitter = { '/', '\\' };
    public FrontendToolingReportGeneratorActor(ILogger<FrontendToolingReportGeneratorActor> logger,
        IServiceScopeFactory scopeFactory,
        IConfiguration configuration)
    {
        _logger = logger;
        _pat = configuration.GetValue<string>("ToolingReportPAT") ?? throw new ArgumentNullException(nameof(configuration), "Personal Access Token (PAT) is not configured.");
        ReceiveAsync<StartFrontendToolingReportGenerationMessage>(async message =>
        {
            logger.LogInformation("Received StartToolingReportGenerationMessage. Starting tooling report generation process for backend");
            using var scope = scopeFactory.CreateScope();
            var productGroupService = scope.ServiceProvider.GetRequiredService<IProductGroupService>();
            var allProductGroups = await productGroupService.GetProductGroupsAsync(new SearchFilter(), CancellationToken.None);

            if (allProductGroups.Data!.Results.Count == 0)
            {
                logger.LogWarning("No product groups found. Exiting tooling report generation process.");
                return;
            }
            ToolingReportCompressedData[] filteredData = allProductGroups.Data!.Results
                .Select(g => new ToolingReportCompressedData
                {
                    GroupName = g.GroupName,
                    RepositoryUrls = g.ProductTeams
                        .SelectMany(pt => pt.Repositories)
                        .Where(r => r.Type == "Frontend")
                        .Select(r => r.Url)
                        .ToArray()
                }).ToArray();

            var repoContext = scope.ServiceProvider.GetRequiredService<IRepositoryContext>();
            var reportRows = new List<ToolingReport>();
            var issuesService = scope.ServiceProvider.GetRequiredService<IIssueTrackerService>();


            foreach (var data in filteredData)
            {
                logger.LogInformation("Processing product group: {GroupName}", data.GroupName);
                var issueTrackerRequest = new GetIssueTrackerRequest
                {
                    Domain = "Frontend",
                    ProductGroup = [data.GroupName],
                    Status = "Open",
                };
                var issues = (await issuesService.GetIssuesAsync(issueTrackerRequest, CancellationToken.None)).Data;
                var numOfProjects =
                    await CountJavaScriptProjectsInRepoAsync(data.RepositoryUrls.ToList(), data.GroupName);
                reportRows.Add(new ToolingReport()
                {
                    ProductGroupName = data.GroupName,
                    CreatedAt = DateTime.Now,
                    Domain = Domain.Frontend,
                    GeneralOverview = new GeneralOverview
                    {
                        Repositories = data.RepositoryUrls.Length,
                        Projects = numOfProjects,
                        SecurityIssues = issues!.Results.Count(i => i.Severity == IncidentSeverity.Critical),
                        TotalIssues = issues!.Results.Count
                    },
                    KeyMetricsOverview = new KeyMetricsOverview
                    {
                        Repositories = data.RepositoryUrls.Length,
                        ActiveProjects = numOfProjects,
                        CriticalIssues = issues.Results.Count(i => i.Severity == IncidentSeverity.Critical)
                    },
                    DetailedProjectStatus = await ScanPackagesForUsageGroupedByVersion(data.RepositoryUrls.ToList(), _pat, data.GroupName),
                    IssuesSummary = new IssuesSummary
                    {
                        SecurityIssues = issues!.Results.Where(i => i.Status == IssueStatus.Open)
                            .Select(i => new SecurityIssues
                            {
                                Description = i.IncidentDescription,
                                AffectedServices = i.ServicesAffected,
                                CountOfAffectedServices = i.ServicesAffected.Count
                            })
                            .ToList()
                    }

                });
            }
            var savedCount = await repoContext.ToolingReportRepository.AddRangeAsync(reportRows);
            if (savedCount > 0)
            {
                logger.LogInformation("Frontend Tooling Report Generation Successful");
            }
        });
    }

    public async Task<int> CountJavaScriptProjectsInRepoAsync(List<string> repositoryUrls, string groupName)
    {
        _logger.LogInformation("Counting JavaScript projects in repositories for group: {GroupName}", groupName);
        if (repositoryUrls.Count <= 0)
        {
            return 0;
        }

        int totalProjectCount = 0;
        var projectCountingTasks = new List<Task<int>>();

        foreach (var repositoryUrl in repositoryUrls)
        {
            projectCountingTasks.Add(GetProjectCountForSingleRepoAsync(repositoryUrl, _pat));
        }

        var results = await Task.WhenAll(projectCountingTasks);
        totalProjectCount = results.Sum();

        return totalProjectCount;
    }

    private async Task<int> GetProjectCountForSingleRepoAsync(string repositoryUrl, string pat)
    {
        const string PackageJsonFileName = "package.json";
        string organization;
        string project;
        string repositoryName;
        var projects = new List<string>();
        try
        {
            var uri = new Uri(repositoryUrl);
            var parts = uri.AbsolutePath.Split('/');

            if (parts.Length < 5 || parts[3] != "_git")
            {
                throw new ArgumentException("Invalid Azure DevOps repository URL format. Expected format: https://dev.azure.com/organization/project/_git/repositoryName");
            }

            organization = parts[1];
            project = parts[2];
            repositoryName = parts[4];        
       
            var responseBody = await $"https://dev.azure.com/{organization}/{project}/_apis/git/repositories/{repositoryName}/items"
                .WithBasicAuth(string.Empty, pat)
                .SetQueryParams(new { recursionLevel = "Full", api_version = "6.0" })
                .GetStringAsync();

            var responseObject = JsonConvert.DeserializeObject<FrontendAzureSearchApiResponse>(responseBody);

            if (responseObject?.value != null)
            {
                foreach (var item in responseObject.value)
                {
                    if (!item.isFolder && item.path.EndsWith(PackageJsonFileName, StringComparison.OrdinalIgnoreCase))
                    {
                        string projectDirectory = Path.GetDirectoryName(item.path) ?? string.Empty;

                        if (!string.IsNullOrEmpty(projectDirectory) && projectDirectory != @"\")
                        {
                            projects.Add(projectDirectory);
                        }
                    }
                }
            }
        }
        catch (FlurlHttpException ex)
        {
            _logger.LogError(ex, "Flurl HTTP Error: {Message}", ex.Message);
            return 0;
        }

        return projects.Count;
    }

    public async Task<List<DetailedProjectStatus>> ScanPackagesForUsageGroupedByVersion(List<string> repositoryUrls, string pat, string groupName)
    {
        _logger.LogInformation("Scanning packages for usage grouped by version in {GroupName}", groupName);
        if (repositoryUrls == null || repositoryUrls.Count == 0)
        {
            return new List<DetailedProjectStatus>();
        }

        var allScanTasks = new List<Task<List<ScanResult>>>();
        foreach (var url in repositoryUrls)
        {
            allScanTasks.Add(ScanSingleRepoAsync(url, pat));
        }

        var resultsFromAllRepos = await Task.WhenAll(allScanTasks);

        var detailedProjectStatus = GenerateDetailedProjectStatus(resultsFromAllRepos.SelectMany(r => r).ToList());
        return detailedProjectStatus;
    }
    private async Task<List<ScanResult>> ScanSingleRepoAsync(string repositoryUrl, string pat)
    {
        _logger.LogInformation("Scanning projects in repository: {RepositoryUrl}", repositoryUrl);

        var uri = new Uri(repositoryUrl);
        var parts = uri.AbsolutePath.Split('/');

        if (parts.Length < 5 || parts[3] != "_git")
        {
            throw new ArgumentException("Invalid Azure DevOps repository URL format. Expected format: https://dev.azure.com/organization/project/_git/repositoryName");
        }

        var organization = parts[1];
        var project = parts[2];
        var repositoryName = parts[4];

        var filePaths = await GetRepositoryFilePathsAsync(organization, project, repositoryName, pat);
        var projectDirs = FindProjectDirectories(filePaths);

        var scanTasks = new List<Task<ScanResult>>();
        foreach (var projectDir in projectDirs)
        {
            _logger.LogInformation("Analyzing: {ProjectDir}", projectDir);
            scanTasks.Add(AnalyzeProjectAsync(projectDir, organization, project, repositoryName, pat, filePaths));
        }

        var results = (await Task.WhenAll(scanTasks)).Where(r => r != null).ToList();

        return results;
    }
    public async Task<List<string>> GetRepositoryFilePathsAsync(string organization, string project, string repositoryName, string pat)
    {
        try
        {
            var url = $"https://dev.azure.com/{organization}/{project}/_apis/git/repositories/{repositoryName}/items";
            var response = await url.WithBasicAuth(string.Empty, pat)
                .SetQueryParams(new { recursionLevel = "Full", api_version = "6.0" })
                .GetJsonAsync<GitItemsResponse>();

            return response.Value.Where(item => item.GitObjectType == "blob").Select(item => item.Path).ToList();
        }
        catch (FlurlHttpException ex)
        {
            _logger.LogError(ex, "Flurl HTTP Error: {Message}", ex.Message);
            return [];
        }
    }

    public static List<string> FindProjectDirectories(List<string> filePaths)
    {
        var projects = new List<string>();
        var projectPackageJsonPaths = filePaths.Where(p => p.EndsWith("package.json", StringComparison.OrdinalIgnoreCase)).ToList();

        foreach (var packageJsonPath in projectPackageJsonPaths)
        {
            string projectDir = Path.GetDirectoryName(packageJsonPath) ?? string.Empty;

            if (string.IsNullOrEmpty(projectDir) || projectDir == @"\")
            {
                continue;
            }
            
            string[] parts = projectDir.Split(Splitter, StringSplitOptions.RemoveEmptyEntries);
            if (!ShouldSkipDirectory(parts.LastOrDefault()!))
            {
                projects.Add(projectDir);
            }
        }
        return projects;
    }

    static bool ShouldSkipDirectory(string dirname)
    {
        var skipDirs = new[]
        {
                "node_modules", ".git", ".next", "dist", "build",
                ".nuxt", "coverage", ".vscode", ".idea", "tmp"
            };
        return skipDirs.Contains(dirname, StringComparer.OrdinalIgnoreCase) || dirname.StartsWith('.');
    }

    async Task<ScanResult> AnalyzeProjectAsync(string projectPath, string organization, string project, string repositoryName, string pat, List<string> filePaths)
    {
        try
        {
            string packageJsonPath = Path.Combine(projectPath, "package.json").Replace("\\", "/");
            string packageJsonContent = await FetchFileContentAsync(packageJsonPath, organization, project, repositoryName, pat);
            var packageJson = JsonConvert.DeserializeObject<PackageJson>(packageJsonContent);

            string projectName = packageJson!.Name ?? Path.GetFileName(projectPath);
            bool isMonorepo = DetectMonoRepo(projectPath, packageJson, filePaths);

            string repoUrl = ExtractRepositoryUrl(packageJson) ?? null!;

            var dependencies = await AnalyzeDependenciesAsync(packageJson);

            return new ScanResult
            {
                Title = projectName,
                Status = "Active",
                IsMonorepo = isMonorepo,
                Repositories = repoUrl != null ? new List<string> { repoUrl } : new List<string>(),
                Projects = new List<Project>
                    {
                        new Project
                        {
                            Name = projectName,
                            Description = packageJson.Description ?? $"{projectName} project",
                            Dependencies = dependencies
                        }
                    }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing {ProjectPath}: {Message}", projectPath, ex.Message);
            return null!;
        }
    }

    static bool DetectMonoRepo(string projectPath, PackageJson packageJson, List<string> filePaths)
    {
        if (packageJson?.Workspaces != null)
        {
            return true;
        }

        var indicators = new[]
        {
                "lerna.json", "nx.json", "rush.json", "pnpm-workspace.yaml"
            };

        _ = indicators.Where(indicatorFile =>
        {
            var fullPath = Path.Combine(projectPath, indicatorFile).Replace("\\", "/");
            return filePaths.Any(p => p.Equals(fullPath, StringComparison.OrdinalIgnoreCase));
        });

        if (filePaths.Any(p => p.Equals(Path.Combine(projectPath, "packages").Replace("\\", "/"), StringComparison.OrdinalIgnoreCase)) ||
            filePaths.Any(p => p.Equals(Path.Combine(projectPath, "apps").Replace("\\", "/"), StringComparison.OrdinalIgnoreCase)))
        {
            return true;
        }

        return false;
    }

    static string? ExtractRepositoryUrl(PackageJson packageJson)
    {
        if (packageJson.Repository == null) return null;

        if (packageJson.Repository is string repoString)
        {
            return repoString;
        }

        if (packageJson.Repository is JObject repoObject && repoObject.ContainsKey("url"))
        {
            var urlToken = repoObject["url"];
            return urlToken != null ? urlToken.ToString() : null;
        }

        return null;
    }

    static async Task<List<Dependency>> AnalyzeDependenciesAsync(PackageJson packageJson)
    {
        var allDeps = new Dictionary<string, string>();
        AddDependencies(allDeps, packageJson.Dependencies);
        AddDependencies(allDeps, packageJson.DevDependencies);
        AddDependencies(allDeps, packageJson.PeerDependencies);
        AddDependencies(allDeps, packageJson.OptionalDependencies);

        var dependencyTasks = new List<Task<Dependency>>();
        foreach (var dep in allDeps)
        {
            dependencyTasks.Add(AnalyzeDependencyAsync(dep.Key, dep.Value));
        }

        var dependencies = await Task.WhenAll(dependencyTasks);
        return dependencies.OrderBy(d => d.Name).ToList();
    }

    private static void AddDependencies(Dictionary<string, string> target, Dictionary<string, string> source)
    {
        if (source != null)
        {
            foreach (var dep in source)
            {
                if (!target.ContainsKey(dep.Key))
                {
                    target[dep.Key] = dep.Value;
                }
            }
        }
    }

    static async Task<Dependency> AnalyzeDependencyAsync(string name, string currentVersion)
    {
        try
        {
            string cleanedVersion = CleanVersion(currentVersion);
            string latestVersion = await GetLatestVersionAsync(name);
            bool isOutdated = latestVersion != UnkownString && IsVersionOutdated(cleanedVersion, latestVersion);

            return new Dependency
            {
                Id = name,
                Name = name,
                CurrentVersion = currentVersion,
                LatestVersion = latestVersion,
                IsOutdated = isOutdated
            };
        }
        catch (Exception)
        {
            return new Dependency
            {
                Id = name,
                Name = name,
                CurrentVersion = currentVersion,
                LatestVersion = UnkownString,
                IsOutdated = false
            };
        }
    }

    public static string CleanVersion(string version)
    {
        if (string.IsNullOrEmpty(version)) return version;

        if (version.StartsWith("npm:"))
        {
            var parts = version.Split('@');
            return parts.LastOrDefault() ?? string.Empty;
        }

        return version.TrimStart('^', '~', '>', '=', '<').Trim();
    }

    public static async Task<string> GetLatestVersionAsync(string packageName)
    {
        try
        {
            string url = $"https://registry.npmjs.org/{packageName.Replace("/", "%2F")}/latest";
            var response = await url.GetJsonAsync<NpmLatestResponse>();
            return response?.Version ?? UnkownString;
        }
        catch (FlurlHttpException)
        {
            return UnkownString;
        }
        catch (Exception)
        {
            return UnkownString;
        }
    }

    public static bool IsVersionOutdated(string current, string latest)
    {
        if (string.IsNullOrEmpty(current) || string.IsNullOrEmpty(latest)) return false;

        try
        {
            var currentParts = current.Split('.').Select(n => int.TryParse(n, out int num) ? num : 0).ToList();
            var latestParts = latest.Split('.').Select(n => int.TryParse(n, out int num) ? num : 0).ToList();

            int maxLength = Math.Max(currentParts.Count, latestParts.Count);
            while (currentParts.Count < maxLength) currentParts.Add(0);
            while (latestParts.Count < maxLength) latestParts.Add(0);

            for (int i = 0; i < maxLength; i++)
            {
                if (currentParts[i] < latestParts[i]) return true;
                if (currentParts[i] > latestParts[i]) return false;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    static List<DetailedProjectStatus> GenerateDetailedProjectStatus(List<ScanResult> scanResults)
    {
        var allDependencies = scanResults
            .SelectMany(r => r.Projects)
            .SelectMany(p => p.Dependencies)
            .ToList();

        var groupedDependencies = allDependencies
            .GroupBy(d => new { d.Name, d.CurrentVersion, d.IsOutdated })
            .Select(g => new DetailedProjectStatus
            {
                Tool = g.Key.Name,
                Version = g.Key.CurrentVersion,
                Projects = g.Count(),
                Status = g.Key.IsOutdated ? VersionStatus.Outdated : VersionStatus.Latest
            })
            .OrderBy(s => s.Tool)
            .ThenBy(s => s.Version)
            .ToList();

        return groupedDependencies;
    }
    private static async Task<string> FetchFileContentAsync(string filePath, string organization, string project, string repositoryName, string pat)
    {
        var url = $"https://dev.azure.com/{organization}/{project}/_apis/git/repositories/{repositoryName}/items";
        return await url.WithBasicAuth(string.Empty, pat)
            .SetQueryParams(new { path = filePath, api_version = "6.0" })
            .GetStringAsync();
    }
    class FrontendAzureSearchApiResponse
    {
        public List<GitItem> value { get; set; } = new();
    }

    class GitItem
    {
        public string path { get; set; } = null!;
        public bool isFolder { get; } = false;
    }
    class ScanResult
    {
        public string Title { get; set; } = null!;
        public string Status { get; set; } = null!;
        public bool IsMonorepo { get; set; }
        public List<string> Repositories { get; set; } = new();
        public List<Project> Projects { get; set; } = new List<Project>();
    }

    class Project
    {
        public string Name { get; set; } = null!;
        public string Description { get; set; } = null!;
        public List<Dependency> Dependencies { get; set; } = new List<Dependency>();
    }

    class Dependency
    {
        public string Id { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string CurrentVersion { get; set; } = null!;
        public string Category { get; set; } = "npm";
        public string LatestVersion { get; set; } = null!;
        public bool IsOutdated { get; set; }
    }

    class PackageJson
    {
        public string Name { get; set; } = null!;
        public string Description { get; set; } = null!;
        public object Repository { get; set; } = null!;
        public object Workspaces { get; set; } = null!;
        public Dictionary<string, string> Dependencies { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, string> DevDependencies { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, string> PeerDependencies { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, string> OptionalDependencies { get; set; } = new Dictionary<string, string>();
    }

    public class NpmLatestResponse
    {
        public string Version { get; set; } = null!;
    }

    class GitItemsResponse
    {
        public List<GitScanItem> Value { get; set; } = new();
    }

    public class GitScanItem
    {
        public string Path { get; set; } = null!;
        public string GitObjectType { get; set; } = null!;
    }

}