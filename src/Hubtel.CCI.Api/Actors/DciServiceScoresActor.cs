using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Services.Interfaces;

namespace Hubtel.CCI.Api.Actors;

public class DciServiceScoresActor: BaseActor
{
    public DciServiceScoresActor(IServiceProvider serviceProvider)
    {
        ReceiveAsync<PublishDciServiceScoresMessage>(async x =>
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var dciServiceScoreService = services.GetRequiredService<IDciServiceScoreService>();
            await dciServiceScoreService.PublishDciServiceScoresAsync(x);
            await Task.CompletedTask;
        });
        
        ReceiveAsync<CurateDciProductRankingMessage>(async x =>
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var dciServiceScoreService = services.GetRequiredService<IDciServiceScoreService>();
            await dciServiceScoreService.CurateDciProductRankingAsync(x.RankingData, x.PublicationDate, x.PublicationWeek, x.PublicationStartDate, x.PublicationEndDate);
            await Task.CompletedTask;
        });
    }
    
}