using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;

namespace Hubtel.CCI.Api.Actors
{
    public class BackendToolingReportGeneratorActor : BaseActor
    {
        private readonly string _pat;
        private readonly IConfiguration _configuration;
        private readonly ILogger<BackendToolingReportGeneratorActor> _logger;
        private static readonly TimeSpan RegexTimeOut = TimeSpan.FromSeconds(2);
        private static readonly JsonSerializerOptions JsonSerializerInstance = new() { WriteIndented = true };
        public BackendToolingReportGeneratorActor(ILogger<BackendToolingReportGeneratorActor> logger,
            IServiceScopeFactory scopeFactory,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _pat = configuration.GetValue<string>("ToolingReportPAT") ?? throw new ArgumentNullException(nameof(configuration), "Personal Access Token (PAT) is not configured.");
            ReceiveAsync<StartBackendToolingReportGenerationMessage>(async message =>
            {
                logger.LogInformation("Received StartToolingReportGenerationMessage. Starting tooling report generation process for backend");
                using var scope = scopeFactory.CreateScope();
                var productGroupService = scope.ServiceProvider.GetRequiredService<IProductGroupService>();
                var allProductGroups =await productGroupService.GetProductGroupsAsync(new SearchFilter(), CancellationToken.None);
                
                if (allProductGroups.Data!.Results.Count == 0)
                {
                    logger.LogWarning("No product groups found. Exiting tooling report generation process.");
                    return;
                }
                ToolingReportCompressedData[] filteredData = allProductGroups.Data!.Results
                    .Select(g => new ToolingReportCompressedData
                    {
                        GroupName = g.GroupName,
                        RepositoryUrls = g.ProductTeams
                            .SelectMany(pt => pt.Repositories)
                            .Where( r => r.Type == "Backend")
                            .Select(r => r.Url)
                            .ToArray()
                    }).ToArray();
                var repoContext = scope.ServiceProvider.GetRequiredService<IRepositoryContext>();
                var reportRows = new List<ToolingReport>();
                var issuesService = scope.ServiceProvider.GetRequiredService<IIssueTrackerService>();
               
                
                foreach (var data in filteredData)
                {
                    logger.LogInformation("Processing product group: {GroupName}", data.GroupName);
                    var issueTrackerRequest = new GetIssueTrackerRequest
                    {
                        Domain = "Backend",
                        ProductGroup = [data.GroupName],
                        Status = "Open",
                    };
                    var issues = (await issuesService.GetIssuesAsync(issueTrackerRequest, CancellationToken.None)).Data;
                    var numOfProjects =
                        await TotalNumberOfDotnetProjectsFromRepoUrlsAsync(data.RepositoryUrls.ToList());
                    reportRows.Add(new ToolingReport()
                    {
                        ProductGroupName = data.GroupName,
                        CreatedAt = DateTime.Now,
                        Domain = Domain.Backend,
                        GeneralOverview = new GeneralOverview
                        {
                            Repositories = data.RepositoryUrls.Length,
                            Projects = numOfProjects,
                            SecurityIssues = issues!.Results.Count(i => i.Severity == IncidentSeverity.Critical),
                            TotalIssues = issues!.Results.Count
                        },
                        KeyMetricsOverview = new KeyMetricsOverview
                        {
                            Repositories = data.RepositoryUrls.Length,
                            ActiveProjects = numOfProjects,
                            CriticalIssues =  issues.Results.Count(i => i.Severity == IncidentSeverity.Critical)
                        },
                        DetailedProjectStatus = await FindSdkUsageGroupedByVersion(data.RepositoryUrls.ToList(),data.GroupName),
                        IssuesSummary =  new IssuesSummary
                        {
                            SecurityIssues = issues!.Results.Where(i => i.Status == IssueStatus.Open)
                                .Select(i => new SecurityIssues
                                {
                                    Description = i.IncidentDescription,
                                    AffectedServices = i.ServicesAffected,
                                    CountOfAffectedServices = i.ServicesAffected.Count
                                })
                                .ToList()
                        }
                        
                    });
                }
                var savedCount = await repoContext.ToolingReportRepository.AddRangeAsync(reportRows);
                if (savedCount > 0)
                {
                    logger.LogInformation("Backend Tooling Report Generation Successful");
                }
            });
        }

        private async Task<int> TotalNumberOfDotnetProjectsFromRepoUrlsAsync(List<string> repoUrls)
        {
            _logger.LogInformation("Starting TotalNumberOfDotnetProjectsFromRepoUrlsAsync");
            int csprojCount = 0;
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
                Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($":{_pat}")));

            var repoTasks = new List<Task>();

            foreach (var repoUrl in repoUrls)
            {
                var match = Regex.Match(repoUrl, @"https://dev\.azure\.com/(?<org>[^/]+)/(?<project>[^/]+)/_git/(?<repo>[^/?#]+)", RegexOptions.None, RegexTimeOut);
                if (!match.Success)
                {
                    _logger.LogWarning(" Invalid repo URL: {RepoUrl}",repoUrl);
                    continue;
                }

                var organization = "hubtel";
                var project = match.Groups["project"].Value;
                var repoName = match.Groups["repo"].Value;

                _logger.LogInformation("Scanning repository: {Project}/{RepoName}",project,repoName);

                var getRepoUrl = $"https://dev.azure.com/{organization}/{Uri.EscapeDataString(project)}/_apis/git/repositories/{repoName}?api-version=7.0";
                var repoResp = await client.GetAsync(getRepoUrl);
                if (!repoResp.IsSuccessStatusCode)
                {
                    _logger.LogWarning(" StatusCode: {StatusCode} Failed to fetch repository metadata for {RepoUrl}, with reason {Reason}",repoResp.StatusCode,repoUrl,await repoResp.Content.ReadAsStringAsync());
                    continue;
                }

                var repoJson = JsonDocument.Parse(await repoResp.Content.ReadAsStringAsync());
                var repoId = repoJson.RootElement.GetProperty("id").GetString();

                repoTasks.Add(Task.Run(async () =>
                {
                    var itemsUrl = $"https://dev.azure.com/{organization}/{Uri.EscapeDataString(project)}/_apis/git/repositories/{repoId}/items?recursionLevel=Full&api-version=7.0";
                    var itemsResp = await client.GetAsync(itemsUrl);
                    if (!itemsResp.IsSuccessStatusCode)
                    {
                        _logger.LogWarning("StatusCode: {StatusCode}. Failed to list items in {RepoName} with reason {Reason}",itemsResp.StatusCode,repoName,await itemsResp.Content.ReadAsStringAsync());
                        return;
                    }

                    var paths = JsonDocument.Parse(await itemsResp.Content.ReadAsStringAsync())
                        .RootElement.GetProperty("value")
                        .EnumerateArray()
                        .Where(item => item.TryGetProperty("path", out _))
                        .Select(item => item.GetProperty("path").GetString())
                        .Where(path => path!.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    Interlocked.Add(ref csprojCount, paths.Count);
                }));
            }

            await Task.WhenAll(repoTasks);
            
            return csprojCount;
        }

        public async Task<List<DetailedProjectStatus>> FindSdkUsageGroupedByVersion(List<string> azureRepoUrls, string groupName)
        {
            _logger.LogInformation("Starting FindSdkUsageGroupedByVersion {GroupName}", groupName);

            var sdkList = _configuration.GetSection("BackendPackagesList").Get<string[]>()
                ?? throw new ArgumentNullException(nameof(groupName), message: "PackageSdksList is not configured.");

            var sdkUsageMap = new Dictionary<string, Dictionary<string, HashSet<string>>>(StringComparer.OrdinalIgnoreCase);

            using var client = BuildHttpClient();
            var repoTasks = azureRepoUrls.Select(url => ProcessRepositoryAsync(url, client, sdkList, sdkUsageMap)).ToList();

            await Task.WhenAll(repoTasks);

            return BuildDetailedStatusList(sdkUsageMap, groupName);
        }

        private HttpClient BuildHttpClient()
        {
            var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($":{_pat}"))
            );
            return client;
        }

        private async Task ProcessRepositoryAsync(string repoUrl, HttpClient client, string[] sdkList,
            Dictionary<string, Dictionary<string, HashSet<string>>> sdkUsageMap)
        {
            try
            {
                if (!TryParseRepoUrl(repoUrl, out var project, out var repoName))
                    return;

                _logger.LogInformation("Scanning: {Project}/{RepoName}", project, repoName);

                var repoId = await GetRepoIdAsync(client, project, repoName);
                if (repoId == null) return;

                var csprojPaths = await GetCsprojPathsAsync(client, project, repoId);
                foreach (var path in csprojPaths)
                {
                    var content = await TryReadFileContentAsync(client, project, repoId, path);
                    if (content == null) continue;

                    foreach (var pkg in ExtractPackageReferences(content).Where(pr => sdkList.Contains(pr.Name, StringComparer.OrdinalIgnoreCase)))
                    {
                        RecordUsage(sdkUsageMap, pkg.Name!, pkg.Version!, $"{project}/{repoName}{path}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing repo: {RepoUrl} - {Message}", repoUrl, ex.Message);
            }
        }

        private bool TryParseRepoUrl(string repoUrl, out string project, out string repoName)
        {
            project = repoName = string.Empty;

            var uri = new Uri(repoUrl);
            var segments = uri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);
            if (segments.Length < 4 || segments[2] != "_git")
            {
                _logger.LogWarning("Skipping invalid repo URL: {RepoUrl}", repoUrl);
                return false;
            }

            project = segments[1];
            repoName = segments[3];
            return true;
        }

        private async Task<string?> GetRepoIdAsync(HttpClient client, string project, string repoName)
        {
            var repoMetaUrl = $"https://dev.azure.com/hubtel/{Uri.EscapeDataString(project)}/_apis/git/repositories/{Uri.EscapeDataString(repoName)}?api-version=7.0";
            var resp = await client.GetAsync(repoMetaUrl);
            if (!resp.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to get repo ID for {Project}/{RepoName}", project, repoName);
                return null;
            }

            using var json = JsonDocument.Parse(await resp.Content.ReadAsStringAsync());
            return json.RootElement.GetProperty("id").GetString();
        }

        private static async Task<List<string>> GetCsprojPathsAsync(HttpClient client, string project, string repoId)
        {
            var itemsUrl = $"https://dev.azure.com/hubtel/{Uri.EscapeDataString(project)}/_apis/git/repositories/{repoId}/items?recursionLevel=Full&api-version=7.0";
            var resp = await client.GetAsync(itemsUrl);
            if (!resp.IsSuccessStatusCode) return new List<string>();

            return JsonDocument.Parse(await resp.Content.ReadAsStringAsync())
                .RootElement.GetProperty("value")
                .EnumerateArray()
                .Where(item => item.TryGetProperty("path", out _))
                .Select(item => item.GetProperty("path").GetString())
                .Where(path => path!.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase) &&
                               !Path.GetFileNameWithoutExtension(path)!
                                   .Contains(".Tests", StringComparison.OrdinalIgnoreCase))
                .ToList()!;
        }

        private static async Task<string?> TryReadFileContentAsync(HttpClient client, string project, string repoId, string path)
        {
            var url = $"https://dev.azure.com/hubtel/{Uri.EscapeDataString(project)}/_apis/git/repositories/{repoId}/items?path={Uri.EscapeDataString(path)}&api-version=7.0&includeContent=true";
            var resp = await client.GetAsync(url);
            if (!resp.IsSuccessStatusCode) return null;
            return await resp.Content.ReadAsStringAsync();
        }

        private IEnumerable<(string Name, string Version)> ExtractPackageReferences(string xmlContent)
        {
            try
            {
                return XDocument.Parse(xmlContent)
                    .Descendants("PackageReference")
                    .Where(pr => pr.Attribute("Include") != null)
                    .Select(pr => (
                        Name: pr.Attribute("Include")!.Value,
                        Version: pr.Attribute("Version")?.Value ?? pr.Element("Version")?.Value ?? "Unknown"));
            }
            catch (XmlException ex)
            {
                _logger.LogWarning(ex, "Skipping invalid XML");
                return Enumerable.Empty<(string, string)>();
            }
        }

        private static void RecordUsage(IDictionary<string, Dictionary<string, HashSet<string>>> map, string sdk, string version, string location)
        {
            lock (map)
            {
                map.TryAdd(sdk, new Dictionary<string, HashSet<string>>(StringComparer.OrdinalIgnoreCase));
                if (!map[sdk].ContainsKey(version))
                    map[sdk][version] = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                map[sdk][version].Add(location);
            }
        }

        private List<DetailedProjectStatus> BuildDetailedStatusList(Dictionary<string, Dictionary<string, HashSet<string>>> sdkUsageMap, string groupName)
        {
            var result = sdkUsageMap
                .OrderBy(k => k.Key)
                .SelectMany(sdk => sdk.Value.OrderBy(v => v.Key).Select(v => new DetailedProjectStatus
                {
                    Tool = sdk.Key,
                    Version = v.Key,
                    Projects = v.Value.Count,
                    Status = IsVersionBelow(v.Key, 8) ? VersionStatus.Outdated : VersionStatus.Latest
                }))
                .ToList();

            _logger.LogDebug("Tooling Report for {GroupName} : {ProjectDetails}", groupName,
                JsonSerializer.Serialize(result, JsonSerializerInstance));

            return result;
        }

        public static string ExtractFrameworkVersion(string csprojContent)
        {
            try
            {
                var xdoc = XDocument.Parse(csprojContent);

                var tf = xdoc.Descendants("TargetFramework").FirstOrDefault()?.Value ??
                         xdoc.Descendants("TargetFrameworks").FirstOrDefault()?.Value.Split(';').FirstOrDefault();

                if (tf != null && tf.StartsWith("net"))
                {
                    var numeric = tf.Substring(3);
                    if (numeric.Length == 2) numeric = $"{numeric[0]}.{numeric[1]}";
                    else if (numeric.Length == 3) numeric = $"{numeric[0]}.{numeric[1]}.{numeric[2]}";
                    return numeric;
                }

                var sdkAttr = xdoc.Root?.Attribute("Sdk")?.Value;
                return sdkAttr ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
        public static bool IsVersionBelow(string version,int latestVersionNumber)
        {
            if (string.IsNullOrWhiteSpace(version))
                return false;

            var cleaned = System.Text.RegularExpressions.Regex.Replace(version, @"\.{2,}", ".", RegexOptions.None, RegexTimeOut);

            if (Version.TryParse(cleaned, out var parsed))
            {
                return parsed.Major < 8;
            }

            return false;
        }
    }
    
}
