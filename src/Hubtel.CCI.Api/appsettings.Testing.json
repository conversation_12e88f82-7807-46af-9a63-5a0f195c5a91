{"AllowedHosts": "*", "ApiDocsConfig": {"ShowSwaggerUi": true, "ShowRedocUi": true, "EnableSwaggerTryIt": true}, "ApplicationInsights": {"InstrumentationKey": "tbd"}, "BearerTokenConfig": {"Issuer": "http://hubtel.com", "Audience": "http://hubtel.com", "SigningKey": "85415b491a1740a0aac3733a6f521e98"}, "ConnectionStrings": {"DbConnection": "Server=********;Port=5432;User Id=********;Password=********;Database=HubtelCCIDB;Pooling=true;CommandTimeout=120;Timeout=30"}, "LookupConfig": {"Url": "http://lookup-infosec-mock:8000/api/account/lookup/dev-docs"}, "KafkaProducerConfig": {"Hosts": [{"Alias": "default", "BootstrapServers": "127.0.0.1:9092"}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.CCI.Api", "Facility": "Hubtel.CCI.Api", "Environment": "Development", "LogLevel": {"Default": "Debug"}}}, "LogRequestResponse": false, "RedisConfiguration": {"Setup": [{"Host": "127.0.0.1", "Name": "localRedis", "Port": "6379", "Databases": [{"alias": "myapidb", "db": 1}]}]}, "StatsdConfig": {"Server": "127.0.0.1", "Port": "6379", "Prefix": "defaultstatsdprefix"}, "ActorConfig": {"SendCallbackActorConfig": {"NumberOfInstances": 10, "UpperBound": 100}}, "BasicAuthConfigs": {"AuthConfigs": [{"Username": "<EMAIL>", "Password": "oRtHeRXechENvalOGYNATiouGaBiniEloUsiVErIPlecArcyah", "Role": "Admin"}, {"Username": "<EMAIL>", "Password": "c9bb259fb3f94b7091eb4d142ab52ccc", "Role": "Admin"}]}, "OpenTelemetryConfig": {"ServiceName": "hubtel.cci.api", "Host": "localhost", "Port": 4317, "Protocol": "http", "ShowConsoleMetrics": false, "ShowConsoleTrace": true}, "SonarQubeConfig": {"Host": "http://sonarqube-mock:9000", "AlmSetting": "Automation-PAT_Azure", "Token": ""}, "AzureOrgConfig": {"Organization": "hubtel", "DevUrl": "https://dev.azure.com", "ReleaseUrl": "https://vsrm.dev.azure.com", "AccessToken": "DGeQMvieEhTaLyDsY65BMufRirlNC35k6HIsDPiOxWWUeKsZVz8AJQQJ99BEACAAAAAuqe42AAASAZDO75AG", "Projects": ["Back-End", "Inventory", "AI Lab", "Back-Office", "Consumer", "Front-End", "Gov", "<PERSON><PERSON><PERSON>", "Notifications", "Orders", "Payments", "Producer"]}, "TeamsBotAppConfig": {"ClientId": "cbbb9509-6087-4696-a4ef-", "ClientSecret": "jKd8Q~x5lgHfiCFfKUMeLv0T4G", "TenantId": "be5d35d8-e8e5-4021-a01a", "RedirectUri": "http://localhost", "MsGraphMeRequestUri": "", "MsGraphChatRequestUri": "", "SqBotUser": "<EMAIL>", "Scopes": ["User.Read", "Chat.ReadWrite", "ChatMessage.Send", "User.Read.All", "Directory.Read.All"]}}