using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Cascaders;

/// <summary>
/// Represents a cascader for handling product team updates and deletions.
/// </summary>
public class ProductTeamCascader : IProductTeamCascader
{
    private readonly ILogger<ProductTeamCascader> _logger;
    private readonly IRepositoryContext _repositoryContext;

    public ProductTeamCascader(ILogger<ProductTeamCascader> logger, IRepositoryContext repositoryContext)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
    }

    /// <summary>
    /// Asynchronously cascades the update of a product team for all associated product groups.
    /// </summary>
    /// <param name="message">The message containing the product team to be updated.</param>
    /// <returns>A task that represents the asynchronous operation, containing a boolean indicating success or failure.</returns>
    public async Task<bool> CascadeProductTeamUpdateForProductGroupsAsync(CascadeProductTeamUpdateMessage message)
    {
        var productGroups = await _repositoryContext.GetProductGroupsForProductTeamAsync(message.ProductTeam.Id);
        var anyUpdates = false;

        foreach (var productGroup in productGroups)
        {
            var productTeamItem = productGroup.ProductTeams.Items.Find(x => x.Id == message.ProductTeam.Id);

            if (productTeamItem == null) continue;
            _logger.LogInformation(
                "ProductTeam found in supervisors list for product group {GroupName} {ProductTeamId}",
                productGroup.GroupName, message.ProductTeam.Id);
            productTeamItem.Name = message.ProductTeam.Name;
            productTeamItem.Members = message.ProductTeam.Members;
            productTeamItem.Repositories = message.ProductTeam.Repositories;
            anyUpdates = true;
        }

        if (!anyUpdates)
        {
            _logger.LogWarning(
                "No updates found for product team {ProductTeamId} CascadeProductTeamUpdateForProductGroupsAsync",
                message.ProductTeam.Id);
            return true;
        }

        var updated = await _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups);

        if (updated > 0) return true;
        _logger.LogWarning("Failed to update product groups for product team {ProductTeamId}", message.ProductTeam.Id);
        return false;
    }

    /// <summary>
    /// Asynchronously cascades the deletion of a product team for all associated product groups.
    /// </summary>
    /// <param name="message">The message containing the product team to be deleted.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task<bool> CascadeProductTeamDeleteForProductGroupsAsync(CascadeProductTeamDeleteMessage message)
    {
        var productGroups = await _repositoryContext.GetProductGroupsForProductTeamAsync(message.ProductTeam.Id);

        var anyUpdates = false;
        foreach (var productGroup in productGroups)
        {
            var productTeamItem = productGroup.ProductTeams.Items.Find(x => x.Id == message.ProductTeam.Id);

            if (productTeamItem == null) continue;
            _logger.LogInformation(
                "ProductTeam found in product teams list for product group {GroupName} {ProductTeamId} CascadeProductTeamDeleteForProductGroupsAsync",
                productGroup.GroupName, message.ProductTeam.Id);
            productGroup.ProductTeams.Items.Remove(productTeamItem);
            anyUpdates = true;
        }

        if (!anyUpdates)
        {
            _logger.LogWarning("No updates found for product team {ProductTeamId}", message.ProductTeam.Id);
            return true;
        }

        var updated = await _repositoryContext.ProductGroupRepository.UpdateRangeAsync(productGroups);

        if (updated > 0) return true;
        _logger.LogWarning(
            "Failed to delete product groups for product team {ProductTeamId} CascadeProductTeamDeleteForProductGroupsAsync",
            message.ProductTeam.Id);
        return false;
    }

    /// <summary>
    /// Asynchronously cascades the update of product teams for all associated repositories.
    /// </summary>
    /// <param name="message">The message containing the product teams to be updated.</param>
    /// <returns>A task that represents the asynchronous operation, containing a boolean indicating success or failure.</returns>
    public async Task<bool> CascadeProductTeamsUpdateForRepositoriesAsync(CascadeProductTeamsUpdateRepositoriesMessage message)
    {
        Dictionary<string, string> repositoriesToStatuses = new();
        foreach( var productTeam in message.ProductTeams)
        {
            productTeam.Repositories.Items.ForEach(r =>
            {
                repositoriesToStatuses.Add(r.Id, productTeam.Status);

            });
        }
        
        
        var repositories = await _repositoryContext.RepositoryRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(r=> repositoriesToStatuses.Keys.ToList().Contains(r.Id))
            .ToListAsync();

        foreach (var repository in repositories)
        {
            repository.Status = repositoriesToStatuses[repository.Id];
        }
        
        var updated = await _repositoryContext.RepositoryRepository.UpdateRangeAsync(repositories);
        
        if (updated > 0) return true;
        
        _logger.LogError("Failed to update repositories for product teams {ProductTeamIds}", message.ProductTeams.Select(x=>x.Id).ToList());
        
        return false;
        
    }
}