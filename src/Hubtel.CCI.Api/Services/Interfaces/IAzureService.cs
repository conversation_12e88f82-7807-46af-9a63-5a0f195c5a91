using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Azure;
using Hubtel.CCI.Api.Dtos.Responses.Azure;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IAzureService
{
    /// <summary>
    /// Imports Azure DevOps release definitions.
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    Task<IApiResponse<bool>> ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken ct = default);

    /// <summary>
    /// Triggers the process definitions in Azure DevOps.
    /// </summary>
    /// <param name="message"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<bool>> TriggerProcessDefinitionsAsync(AzureDeploymentRecordsMessage message,CancellationToken ct = default);
    
    /// <summary>
    /// Fetches Azure DevOps release definitions based on the provided filter.
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<PagedResult<GetAzureReleaseDefinitionResponse>>> 
        FetchReleaseDefinitionsAsync(FetchAzureDefinitionRequest filter,CancellationToken ct = default);


    /// <summary>
    /// Fetches Azure DevOps release deployments based on the provided filter.
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<PagedResult<GetAzureReleaseDeploymentRecords>>>
        FetchReleaseDeploymentsAsync(FetchAzureDeploymentsRequest filter, CancellationToken ct = default);
    
    
    /// <summary>
    /// Fetches Azure DevOps release deployments publication statistics.
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<GetDeploymentStatistics>>
        FetchReleaseDeploymentsPublicationStatisticsAsync(FetchAzureDeploymentStatisticsRequest filter, CancellationToken ct = default);
    
    
    /// <summary>
    /// Fetches Azure DevOps release deployments publication statistics.
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<GetDeploymentMetricDetails>>
        FetchReleaseDeploymentsPublicationDetailsAsync(FetchAzureDeploymentStatisticsRequest filter, CancellationToken ct = default);
    
    
    /// <summary>
    /// Update Repositories with Azure Repo fields
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    Task<IApiResponse<AzureRepositoryUpdateSummary>> UpdateRepositoriesAsync(CancellationToken ct = default);
    
    
    /// <summary>
    /// Deletes an Azure deployment publication based on the provided publication date.
    /// </summary>
    /// <param name="publicationDate"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<bool>> 
        DeleteAzureDeploymentPublicationAsync(DateTime publicationDate,
            CancellationToken ct);



    IAsyncEnumerable<ReleaseDeployment> FetchAzureReleaseDeployments(
        string project,
        int definitionId,
        CancellationToken token);

    IAsyncEnumerable<ReleaseDefinition> FetchAzureReleaseDefinitions(
        string project,
        CancellationToken token);

    void CalculateRollbacks(List<AzureReleaseDeployment> deployments, List<AzureReleaseDeployment> deploymentsToday);
}