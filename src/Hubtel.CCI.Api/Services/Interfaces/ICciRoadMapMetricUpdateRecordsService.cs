using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface ICciRoadMapMetricUpdateRecordsService
{
    /// <summary>
    /// Asynchronously gets the recent road map metric update record.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of cciRoadMaps.</returns>
    Task<IApiResponse<CciRoadMapMetricUpdateRecord?>> GetRecentCciRoadMapMetricUpdateRecordAsync(
        CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously processes and updates a new cciRoadMap and creates a new cciRoadMapMetricUpdateRecord.
    /// </summary>
    /// <param name="runDate">The request to add a new cciRepositoryScore.</param>
    /// <param name="runStartTime">The request to add a new cciRepositoryScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the added cciRepositoryScore.</returns>
    Task<IApiResponse<bool>> ProcessCciRoadMapMetricUpdateRecordAsync(
        DateTime runDate, DateTime runStartTime, CancellationToken ct = default);

    Task<IApiResponse<bool>> ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime runDate, DateTime runStartTime,
        CancellationToken ct = default);
}