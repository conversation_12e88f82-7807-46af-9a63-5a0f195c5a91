using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciEngineerScore;
using Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface ICciEngineerScoreService
{
    Task<IApiResponse<CciEngineerScoreResponse>> GetCciEngineerScoreByIdAsync(string id, CancellationToken ct);
    
    Task<IApiResponse<CciEngineerSonarQubeMetricsResponse>> GetCciEngineerSonarQubeMetricsByIdAsync(string id, CancellationToken ct);


    Task<IApiResponse<PagedResult<CciEngineerScoreResponse>>> GetCciEngineerTableScoresAsync(GetCciEngineerTableScoresFilter request,
        CancellationToken ct);
    
    Task<IApiResponse<GetCciEngineerScoreStatisticResponse>> 
        FetchEngineerCciPublicationStatisticsAsync(FetchCciEngineerStatisticsRequest request,
            CancellationToken ct);
    
    Task<IApiResponse<CciEngineerRankingsResponse>> 
        FetchEngineerCciPublicationRankingsAsync(FetchCciEngineerStatisticsRequest request,
            CancellationToken ct);
    
    
    Task<IApiResponse<bool>> 
        DeleteEngineerCciPublicationAsync(DateTime publicationDate,
            CancellationToken ct);
    
    Task<IApiResponse<PagedResult<CciEngineerSonarQubeMetricsResponse>>> GetCciEngineerTableSqAsync(GetCciEngineerSqMetricsTableScoresFilter request,
        CancellationToken ct);
    
    
    Task<IApiResponse<CciEngineerSqMetricsOverview>> 
        FetchEngineerCciPublicationSqMetricsOverviewAsync(GetCciEngineerSqMetricsTableScoresFilter request,
            CancellationToken ct);

}