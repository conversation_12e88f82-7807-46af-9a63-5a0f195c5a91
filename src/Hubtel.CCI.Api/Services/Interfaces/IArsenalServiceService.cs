using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.RepositoryService;
using Hubtel.CCI.Api.Dtos.Responses.RepositoryService;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IArsenalServiceService
{
    /// <summary>
    /// Asynchronously gets a list of services.
    /// </summary>
    /// <param name="filter">The filter to apply to the service list.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of services.</returns>
    Task<IApiResponse<PagedResult<GetRepositoryServiceResponse>>> GetRepositoryServicesAsync(RepositoryServiceSearchFilter filter,
        CancellationToken ct = default);
        
    /// <summary>
    /// Asynchronously gets a service by its ID.
    /// </summary>
    /// <param name="id">The ID of the service to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the service.</returns>
    Task<IApiResponse<GetRepositoryServiceResponse>> GetRepositoryServiceAsync(string id, CancellationToken ct = default);
    
    /// <summary>
    /// Asynchronously adds a new service.
    /// </summary>
    /// <param name="request">The request containing the service details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the created service.</returns>
    Task<IApiResponse<GetRepositoryServiceResponse>> AddRepositoryServiceAsync(CreateRepositoryServiceRequest request, 
        CancellationToken ct = default);
    
    /// <summary>
    /// Asynchronously updates an existing service.
    /// </summary>
    /// <param name="id">The ID of the service to update.</param>
    /// <param name="request">The request containing the updated service details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the updated service.</returns>
    Task<IApiResponse<bool>> UpdateRepositoryServiceAsync(string id, 
        UpdateRepositoryServiceRequest request, CancellationToken ct = default);
    
    /// <summary>
    /// Asynchronously deletes a service.
    /// </summary>
    /// <param name="id">The ID of the service to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating success or failure.</returns>
    Task<IApiResponse<bool>> DeleteRepositoryServiceAsync(string id, CancellationToken ct = default);
}