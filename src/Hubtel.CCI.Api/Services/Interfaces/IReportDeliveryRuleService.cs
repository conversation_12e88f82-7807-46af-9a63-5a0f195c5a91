using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ReportDeliveryRule;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IReportDeliveryRuleService
{
    /// <summary>
    /// Gets a report delivery rule by its ID.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<ReportDeliveryRule>> GetReportDeliveryRuleAsync(string id, CancellationToken ct = default);
    
    /// <summary>
    /// Creates a new report delivery rule.
    /// </summary>
    /// <param name="rule"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<ReportDeliveryRule>> CreateReportDeliveryRuleAsync(CreateReportDeliveryRule rule, CancellationToken ct = default);


    /// <summary>
    /// Gets report delivery rules
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<PagedResult<ReportDeliveryRule>>> GetReportDeliveryRulesAsync(SearchFilter filter,
        CancellationToken ct = default);
    
    
    /// <summary>
    /// Deletes a report delivery rule by its ID.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<ReportDeliveryRule>> DeleteReportDeliveryRuleAsync(string id, CancellationToken ct = default);
}