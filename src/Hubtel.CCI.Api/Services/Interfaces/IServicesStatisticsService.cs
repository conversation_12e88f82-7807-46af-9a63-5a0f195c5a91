using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IServicesStatisticsService
{
    Task<int> AggregateServiceStatsAsync(Service service,Repository repository, DeploymentAggregatorActorMessage message);
    Task<PagedResult<Service>> GetServicesForStatisticsAggregation(int page, int pageSize);
    Task<PagedResult<Repository>> GetRepositoriesForStatisticsAggregation(int page, int pageSize);
    Task<List<Service>> GetServicesForRepository(string repositoryId);
}