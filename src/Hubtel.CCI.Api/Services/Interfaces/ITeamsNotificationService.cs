using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.MicrosoftTeams;

namespace Hubtel.CCI.Api.Services.Interfaces;


public interface ITeamsNotificationService
{
    /// <summary>
    /// Starts the Auth Process Via MSAL To Obtain The Delegate Token For Sending Messages
    /// </summary>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    Task<IApiResponse<string>> StartMSALDelegateAuthFlowAsync();


    /// <summary>
    /// Completes the Auth Process Via MSAL To Obtain The Delegate Token For Sending Messages
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<IApiResponse<string>> CompleteMSALDelegateAuthFlowAsync(RedirectPayloadRequest request);


    /// <summary>
    /// Triggers a teams message of the messageContent to the recipientEmail 
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<IApiResponse<string>> SendTeamsMessageAsync(TeamsMessageRequest request);


    /// <summary>
    /// Triggers a teams message with upload files to the recipientEmail
    /// </summary>
    /// <param name="recipientEmail"></param>
    /// <param name="messageContent"></param>
    /// <param name="uploadedFiles"></param>
    /// <returns></returns>
    Task<IApiResponse<string>> SendTeamsMessageWithFilesAsync(string recipientEmail, string messageContent,
        List<IFormFile> uploadedFiles);
    
    
    /// <summary>
    /// Triggers the flow to automate report notifier for CQT
    /// </summary>
    /// <returns></returns>
    Task<IApiResponse<string>> TriggerFlowToAutomateReportNotifierAsync();
}