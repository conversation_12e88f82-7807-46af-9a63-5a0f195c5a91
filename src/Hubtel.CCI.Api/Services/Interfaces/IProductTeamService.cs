using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Dtos.Responses.ProductTeam;

namespace Hubtel.CCI.Api.Services.Interfaces;

/// <summary>
/// Provides an interface for the ProductTeam service.
/// </summary>
public interface IProductTeamService
{
    /// <summary>
    /// Asynchronously gets a list of product teams.
    /// </summary>
    /// <param name="filter">The filter to apply to the product team list.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of product teams.</returns>
    Task<IApiResponse<PagedResult<GetProductTeamResponse>>> GetProductTeamsAsync(SearchFilter filter,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously gets a product team by ID.
    /// </summary>
    /// <param name="id">The ID of the product team to get.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the product team details.</returns>
    Task<IApiResponse<GetProductTeamResponse>> GetProductTeamAsync(string id, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously adds a new product team.
    /// </summary>
    /// <param name="request">The request to add a new product team.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the added product team.</returns>
    Task<IApiResponse<GetProductTeamResponse>> AddProductTeamAsync(CreateProductTeamRequest request,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously updates a product team by ID.
    /// </summary>
    /// <param name="id">The ID of the product team to update.</param>
    /// <param name="request">The request to update the product team.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the update was successful.</returns>
    Task<IApiResponse<bool>> UpdateProductTeamAsync(string id, UpdateProductTeamRequest request,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously deletes a product team by ID.
    /// </summary>
    /// <param name="id">The ID of the product team to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the deletion was successful.</returns>
    Task<IApiResponse<bool>> DeleteProductTeamAsync(string id, CancellationToken ct = default);


    /// <summary>
    /// Asynchronously updates a product team's repositories by ID.
    /// </summary>
    /// <param name="id">The ID of the product team to update.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the update was successful.</returns>
    Task<IApiResponse<bool>> UpdateProductTeamRepositoriesAsync(string id,
        CancellationToken ct = default);


    /// <summary>
    /// Asynchronously updates product teams repositories
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the update was successful.</returns>
    Task<IApiResponse<bool>> UpdateProductTeamsRepositoriesAsync(
        CancellationToken ct = default);
        

    /// <summary>
    /// Asynchronously gets a production teams publication
    /// </summary>
    /// <param name="productTeamId">filter for querying productTeams documents</param>
    /// <param name="week">The week to fetch.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of product teams.</returns>
    Task<IApiResponse<ProductTeamPublicationResponse>> GetProductTeamPublicationAsync(string productTeamId, string week,
        CancellationToken ct = default);
}