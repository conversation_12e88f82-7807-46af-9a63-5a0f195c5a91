using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.DciServiceScore;
using Hubtel.CCI.Api.Dtos.Responses.DciServiceScore;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IDciServiceScoreService
{
        /// <summary>
    /// Asynchronously gets a list of dciServiceScores.
    /// </summary>
    /// <param name="filter">The filter to apply to the dciServiceScore list.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of dciServiceScores.</returns>
    Task<IApiResponse<PagedResult<GetDciServiceScoreResponse>>> GetDciServiceScoresAsync(SearchFilter filter,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously gets an dciServiceScore by ID.
    /// </summary>
    /// <param name="id">The ID of the dciServiceScore to get.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the dciServiceScore details.</returns>
    Task<IApiResponse<GetDciServiceScoreResponse>> GetDciServiceScoreAsync(string id, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously updates an dciServiceScore by ID.
    /// </summary>
    /// <param name="id">The ID of the dciServiceScore to update.</param>
    /// <param name="request">The request to update the dciServiceScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the update was successful.</returns>
    Task<IApiResponse<bool>> UpdateDciServiceScoreAsync(string id, UpdateDciServiceScoreRequest request,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously deletes an dciServiceScore by ID.
    /// </summary>
    /// <param name="id">The ID of the dciServiceScore to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the deletion was successful.</returns>
    Task<IApiResponse<bool>> DeleteDciServiceScoreAsync(string id, CancellationToken ct = default);

    /// <summary>
    /// Triggers the publication of Dci Service scores.
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<bool>> TriggerDciServiceScorePublicationAsync(CancellationToken ct = default);


    /// <summary>
    ///  Triggers the calculation of Dci Service scores.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<IApiResponse<bool>> PublishDciServiceScoresAsync(
        PublishDciServiceScoresMessage request,
        CancellationToken ct = default);
    
    /// <summary>
    /// Curates the Dci Product Ranking.
    /// </summary>
    /// <param name="rankingData">The ranking data to curate.</param>
    /// <param name="publicationDate">The date of publication.</param>
    /// <param name="publicationWeek">The publication week identifier.</param>
    /// <param name="publicationStartDate">The start date of the publication period.</param>
    /// <param name="publicationEndDate">The end date of the publication period.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result indicates whether the curation was successful.</returns>
    Task<bool> CurateDciProductRankingAsync(List<RepositoryDciScore> rankingData, DateTime publicationDate,
        string publicationWeek, DateTime publicationStartDate, DateTime publicationEndDate,
        CancellationToken ct = default);
}
