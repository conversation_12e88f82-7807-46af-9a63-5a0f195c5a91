using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMapRecord;
using Hubtel.CCI.Api.Dtos.Responses.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Responses.CciRoadMapRecord;

namespace Hubtel.CCI.Api.Services.Interfaces;


/// <summary>
/// Provides an interface for the Cci Road Map service.
/// </summary>
public interface ICciRoadMapService
{
    /// <summary>
    /// Asynchronously adds a new cciRoadMap.
    /// </summary>
    /// <param name="request">The request to add a new cciRepositoryScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the added cciRepositoryScore.</returns>
    Task<IApiResponse<GetCciRoadMapResponse>> AddCciRoadMapAsync(
        CreateCciRoadMapRequest request, CancellationToken ct = default);
    
    /// <summary>
    /// Asynchronously gets a list of cciRoadMaps.
    /// </summary>
    /// <param name="filter">The filter to apply to the cciRoadMaps list.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of cciRoadMaps.</returns>
    Task<IApiResponse<PagedResult<GetCciRoadMapResponse>>> GetCciRoadMapsAsync(
        SearchCciRoadMapRequest filter, CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously deletes a cciRoadMap.
    /// </summary>
    /// <param name="id">Id of the roadmap to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a boolean value indicating successful deletion of a cciRoadMap.</returns>
    Task<IApiResponse<bool>> DeleteCciRoadMapAsync(
        string id, CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously adds a new cciRoadMap.
    /// </summary>
    /// <param name="request">The request to add a new cciRepositoryScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the added cciRepositoryScore.</returns>
    Task<IApiResponse<GetCciRoadMapRecordResponse>> AddCciRoadMapV2Async(
        CreateCciRoadMapRecordRequest request, CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously gets a new cciRoadMap record by id.
    /// </summary>
    /// <param name="id">The id of the cci road map</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the added cciRoadMapRecord.</returns>
    Task<IApiResponse<GetCciRoadMapRecordResponse>> GetCciRoadMapRecordByIdAsync(
        string id, CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously gets a list of cciRoadMapRecords.
    /// </summary>
    /// <param name="filter">The filter to apply to the cciRoadMaps list.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of cciRoadMaps.</returns>
    Task<IApiResponse<PagedResult<GetCciRoadMapRecordResponse>>> GetCciRoadMapRecordsV2Async(
        SearchCciRoadMapRequest filter, CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously deletes a cciRoadMap Record.
    /// </summary>
    /// <param name="id">Id of the roadmap to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a boolean value indicating successful deletion of a cciRoadMap.</returns>
    Task<IApiResponse<bool>> DeleteCciRoadMapRecordAsync(
        string id, CancellationToken ct = default);
}