using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IDcpService
{
    Task<IApiResponse<DeploymentRequest>> CreateDcpRequestAsync(CreateDcpRequest dcpRequest);
    Task<IApiResponse<DeploymentRequest>> UpdateDcpRequestAsync(string id, DcpRequest dcpRequest);
    Task<IApiResponse<DeploymentRequest>> GetDcpRequestByIdAsync(string id);
    Task<IApiResponse<PagedResult<DeploymentRequest>>> FilterDcpRequestsAsync(DcpRequestFilter filter);
    Task<IApiResponse<DeploymentRequest>> CancelDcpRequestAsync(string id, CancelDcpRequest cancelDcpRequest);
    Task<IApiResponse<DeploymentRequest>> UpdateServiceToBeDeployedAsync(string requestId,
        ServiceToBeDeployedInput request);
    Task<IApiResponse<DeploymentRequest>> RemoveServiceToBeDeployedAsync(string requestId, string serviceId);
    Task<IApiResponse<DeploymentRequest>> UpdateBulkServicesToBeDeployedAsync(string requestId,
        List<ServiceToBeDeployedInput> requests);

    Task<IApiResponse<ServiceDeploymentTrailResponse>> AddServiceDeploymentTrailAsync(
        ServiceDeploymentTrailRequest request);
    
    Task<IApiResponse<int>> DeleteDcpRequestByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IApiResponse<int>> DeleteDcpRequestByIdsAsync(DeleteDcpRequest request);

}