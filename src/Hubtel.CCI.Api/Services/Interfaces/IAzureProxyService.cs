using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Models.Azure;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IAzureProxyService
{
    Task<IApiResponse<AzureRepositoryResponse>> GetRepositoryDetailAsync(string project, string repositoryName, CancellationToken ct = default);

    Task<RepositoryServiceDeploymentMetricsSummary?> GetRepositoryServiceDeploymentMetricsSummaryAsync(
        string projectName,
        int releaseDefinitionId,
        int top = 5,
        CancellationToken ct = default);
    IAsyncEnumerable<AzurePullRequestInfo> GetPullRequestsAsync(string project, string repositoryId, string status = "completed", CancellationToken ct = default);
    
    Task<IApiResponse<List<AzureWorkItemListItemInfo>>> GetWorkItemsAsync(string project, string repositoryId, int pullRequestId, CancellationToken ct = default);
    
    Task<IApiResponse<List<AzureBuildChangeListItemInfo>>> GetBuildChangesAsync(string project, string buildId, CancellationToken ct = default);
    
    Task<IApiResponse<List<WorkItemQueryResult>>> QueryWorkItemsAsync(string project, string wiqlQuery, CancellationToken ct = default);
    
    Task<IApiResponse<decimal>> CalculatePullRequestAnalysisScoreAsync(string project, string repositoryId, List<string> commitIds, CancellationToken ct = default);
    
    Task<IApiResponse<decimal>> CalculatePostDeploymentIssueScoreAsync(string serviceName, DateTime startTime, string project = "Hubtel", CancellationToken ct = default);
    
    (string Project, string Repository) ExtractProjectAndRepository(string azureUrl);
    
    Task<IApiResponse<List<AzureReleaseDetail>>> GetRecentReleasesAsync(string project, int releaseDefinitionId, int top = 5, CancellationToken ct = default);
    
    Task<IApiResponse<AzureReleaseDetail>> GetReleaseDetailAsync(string project, int releaseId, CancellationToken ct = default);
    
    Task<IApiResponse<DateTime?>> GetPullRequestCreatedTimeByCommitAsync(string project, string repositoryId, string commitSha, CancellationToken ct = default);
    
    Task<IApiResponse<DeploymentMetrics>> CalculateDeploymentMetricsAsync(CalculateDeploymentMetricsRequest request, CancellationToken ct = default);
}
