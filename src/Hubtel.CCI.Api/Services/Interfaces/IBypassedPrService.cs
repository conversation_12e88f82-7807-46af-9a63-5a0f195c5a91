using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Dtos.Responses.BypassedPr;
using Hubtel.CCI.Api.Dtos.Responses.RepositoryBprSyncLogs;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface IBypassedPrService
{
    public Task<IApiResponse<BypassedPr>> GetByIdAsync(string id, CancellationToken ct = default);
    
    public Task<IApiResponse<bool>> TriggerBypassedPrsAsync(CancellationToken ct = default);
    
    Task<bool> ExecuteBypassedPrsFlowAsync(CancellationToken ct = default);

    Task<bool> ProcessRepositoryAsync(string projectName, AzureRepositoryDto repo, CancellationToken ct);
    
    Task<IApiResponse<BypassedPrsOverviewDto>> GetBypassedPrsOverviewAsync(CancellationToken ct = default);
    
    Task<IApiResponse<SyncBPrLogOverviewDto>> GetBypassedPrsSyncLogsOverviewAsync(CancellationToken ct = default);
    
    Task<IApiResponse<bool>> ExecuteClearBypassedPrRecordsAsync(CancellationToken ct = default);

    Task<Dictionary<string, List<AzureRepositoryDto>>> FetchAllRepositoriesAsync();


    IAsyncEnumerable<PullRequest> FetchCompletedPullRequests(
        DateTime from,
        DateTime to,
        string project,
        string repoId, CancellationToken token,
        bool onlyMainBranches=false);


}