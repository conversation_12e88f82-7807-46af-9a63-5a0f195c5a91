using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;

namespace Hubtel.CCI.Api.Services.Interfaces
{
    public interface IIssueTrackerService
    {
        //create issue tracker
        Task<IApiResponse<CreateIssueTrackerResponse>> CreateIssueAsync(CreateIssueTrackerRequest createIssue, CancellationToken ct = default);
        Task<IApiResponse<PagedResult<GetIssueTrackerResponse>>> GetIssuesAsync(GetIssueTrackerRequest filter, CancellationToken ct);
        
        //update issue tracker
        Task<IApiResponse<CreateIssueTrackerResponse>> UpdateIssueAsync(string id, UpdateIssueTrackerRequest updateIssue, CancellationToken ct = default);
        Task<IApiResponse<CreateIssueTrackerResponse>> PatchIssueAsync(string id, UpdateIssueTrackerRequest updateIssue, CancellationToken ct = default);
        //get issue by id
        Task<IApiResponse<GetIssueTrackerResponse>> GetIssueByIdAsync(string id, CancellationToken ct = default);

    }
}
