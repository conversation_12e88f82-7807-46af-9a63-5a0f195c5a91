using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Responses.CciRepositoryScore;

namespace Hubtel.CCI.Api.Services.Interfaces;

/// <summary>
/// Provides an interface for the Cci Repository Score service.
/// </summary>
public interface ICciArsenalScoreService
{
    /// <summary>
    /// Asynchronously deletes CCI publication statistics for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI publication stats.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI publication stats were marked as deleted successfully.</returns>
    Task<IApiResponse<bool>> DeleteCciRepositoryScoreStatisticByWeekAsync(string publicationWeek,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously retrieves CCI repository score statistics based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the CCI repository score statistics.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with the CCI repository score statistic response.
    /// </returns>
    Task<IApiResponse<GetCciRepositoryScoreStatisticResponse>> GetCciRepositoryScoreStatisticsAsync(
        GetCciRepositoryScoreStatisticsRequest request, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously curates weekly CCI repository score statistics based on the provided message.
    /// </summary>
    /// <param name="message">The message containing the CCI products ranking information.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the operation was successful.</returns>
    Task<bool> CurateWeeklyCciRepositoryScoreStatisticsAsync(CurateWeeklyStatisticsMessage message);

    Task<bool> CurateWeeklyTrendAsync(CurateWeeklyTrendMessage message);

    /// <summary>
    /// Asynchronously retrieves product trends based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the product trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of product team score trend responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    Task<IApiResponse<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>>
        GetProductTrendsAsync(GetProductTrendsRequest request, CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously retrieves product trends based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the product trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of product team score trend responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    Task<IApiResponse<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>>
        GetProductTrendsRecentAsync(GetProductTrendsRequest request, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously retrieves the CCI products rankings based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the rankings.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a list of CCI products ranking responses.
    /// </returns>
    Task<IApiResponse<List<GetCciProductsRankingResponse>>> GetCciProductsRankingsAsync(
        GetCciProductsRankingsRequest request, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously creates the latest publication CCI products ranking.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <param name="request">Optional request to publish publication for a duration.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with the created CCI products ranking.
    /// </returns>
    Task<IApiResponse<bool>> PublishCciProductsRankingAsync(PublishCciProductsRankingRequest request,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously retrieves the CCI repository table scores based on products for the previous week.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <param name="request">A representation of queryable operations on the GetCciRepositoryTableScoresBasedOnProductsAsync.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of CCI repository score responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    Task<IApiResponse<Dictionary<string, List<GetCciRepositoryScoreResponse>>>>
        GetCciRepositoryTableScoresBasedOnProductsAsync(GetCciRepositoryTableScoresBasedOnProductsRequest request,
            CancellationToken ct = default);
    
    
    /// <summary>
    /// Asynchronously retrieves the CCI repository table scores based on products for the previous week.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <param name="request">A representation of queryable operations on the GetCciRepositoryTableScoresBasedOnProductsAsync.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of CCI repository score responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    Task<IApiResponse<PagedResult<GetCciRepositoryScoreResponse>>>
        GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync(GetCciRepositoryTableScoresBasedOnProductsRequest request,
            CancellationToken ct = default);

    /// <summary>
    /// Asynchronously gets a list of cciRepositoryScores.
    /// </summary>
    /// <param name="filter">The filter to apply to the cciRepositoryScore list.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the list of cciRepositoryScores.</returns>
    Task<IApiResponse<PagedResult<GetCciRepositoryScoreResponse>>> GetCciRepositoryScoresAsync(
        SearchCciRepositoryScoresRequest filter, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously gets an cciRepositoryScore by ID.
    /// </summary>
    /// <param name="id">The ID of the cciRepositoryScore to get.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the cciRepositoryScore details.</returns>
    Task<IApiResponse<GetCciRepositoryScoreResponse>> GetCciRepositoryScoreAsync(string id,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously adds a new cciRepositoryScore.
    /// </summary>
    /// <param name="request">The request to add a new cciRepositoryScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the added cciRepositoryScore.</returns>
    Task<IApiResponse<GetCciRepositoryScoreResponse>> AddCciRepositoryScoreAsync(
        CreateCciRepositoryScoreRequest request, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously updates an cciRepositoryScore by ID.
    /// </summary>
    /// <param name="id">The ID of the cciRepositoryScore to update.</param>
    /// <param name="request">The request to update the cciRepositoryScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the update was successful.</returns>
    Task<IApiResponse<bool>> UpdateCciRepositoryScoreAsync(string id, UpdateCciRepositoryScoreRequest request,
        CancellationToken ct = default);

    /// <summary>
    /// Asynchronously deletes a cciRepositoryScore by ID.
    /// </summary>
    /// <param name="id">The ID of the cciRepositoryScore to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the deletion was successful.</returns>
    Task<IApiResponse<bool>> DeleteCciRepositoryScoreAsync(string id, CancellationToken ct = default);


    /// <summary>
    /// Asynchronously deletes CCI publication rankings for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI publication rankings.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI publication rankings were marked as deleted successfully.</returns>
    Task<IApiResponse<bool>> DeleteCciPublicationRankingByWeekAsync(string publicationWeek,
        CancellationToken ct = default);


    /// <summary>
    /// Asynchronously deletes CCI publication trends for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI publication trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI publication trends were marked as deleted successfully.</returns>
    Task<IApiResponse<bool>> DeleteCciPublicationTrendsByWeekAsync(string publicationWeek,
        CancellationToken ct = default);


    /// <summary>
    /// Asynchronously deletes CCI scores for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI scores.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI scores were marked as deleted successfully.</returns>
    Task<IApiResponse<bool>> DeleteCciScoresByWeekAsync(string publicationWeek, CancellationToken ct = default);

    /// <summary>
    /// Asynchronously adds a bulk list of CCI repository scores.
    /// </summary>
    /// <param name="request">The list of requests to create new CCI repository scores.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains the API response with the count of added CCI repository scores.
    /// </returns>
    Task<IApiResponse<int>> AddBulkCciRepositoryScoresAsync(
        CreateBulkCciRepositoryScoresRequest request, CancellationToken ct = default);
    
    /// <summary>
    /// Asynchronously retrieves CCI repository score statistics based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the CCI repository score statistics.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with the CCI repository score statistic response.
    /// </returns>
    Task<IApiResponse<GetCciRepositoryScoreStatisticResponse>> UpdateCciRepositoryStatisticsNewAsync(
        UpdateCciRepositoryStatisticsRequest request, CancellationToken ct = default);
    
    
    /// <summary>
    /// Get the CCI Trends by Publication Date Range
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the CCI trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with the CCI trends.
    /// </returns>
    Task<IApiResponse<GetCciTrendResponse>> GetCciTrendsByPublicationDateRangeAsync(
        CciTrendRequest request, CancellationToken ct = default);
    
    
    Task<IApiResponse<bool>> TriggerCciRepositoryScorePublishAsync(
        CancellationToken ct = default);
}