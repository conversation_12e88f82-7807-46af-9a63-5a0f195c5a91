using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.SonarQube;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;

namespace Hubtel.CCI.Api.Services.Interfaces;

public interface ISonarQubeService
{
    /// <summary>
    /// Imports an Azure DevOps repository into SonarQube.
    /// </summary>
    /// <param name="projectName">The name of the Azure DevOps project.</param>
    /// <param name="repositoryName">The name of the Azure DevOps repository.</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the response from the SonarQube server.</returns>
    Task<IApiResponse<SonarQubeApiResponse>> ImportAzureDevOpsRepositoryAsync(string projectName, string repositoryName,
        CancellationToken ct = default);
    
    /// <summary>
    /// Fetches new metrics for a pull request.
    /// </summary>
    /// <param name="pullRequestId">The ID of the pull request.</param>
    /// <param name="repositoryId">The ID of the repository.</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the new metrics for the pull request.</returns>
    Task<IApiResponse<SonarQubeMetricsResponse>> GetNewMetricsOnPrAsync(int pullRequestId, string repositoryId,
        CancellationToken ct = default);
    
    
    /// <summary>
    /// Fetches the engineers sonarQube metrics for the past day
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the engineers sonarQube metrics.</returns>
    Task<IApiResponse<bool>> FetchEngineersSonarQubeMetricsAsync(CancellationToken ct = default);

    
    /// <summary>
    /// Executes the Engineer SonarQube Metrics flow asynchronously.
    /// </summary>
    /// <param name="message"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    Task<bool> ExecuteEngineerSqMetricsFlowAsync(EngineerSonarQubeMetricsMessage message, CancellationToken ct = default);


    decimal ComputeRepoScoreAsync(
        string sonarKey,
        List<EngineerSonarQubeMetrics> metrics,
        List<Repository> repositories);

    CciEngineerScore BuildCciEngineerScoreAsync(
        string? engineerEmail,
        List<EngineerSonarQubeMetrics> prList,
        DateTime publicationDate,
        List<Engineer> allEngineers,
        List<Repository> allRepositories);
    
    
    /// <summary>
    /// Triggers the Gather Repository Language Distribution flow
    /// </summary>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the engineers sonarQube metrics.</returns>
    Task<IApiResponse<bool>> FetchRepositoryLanguageDistributionAsync(CancellationToken ct = default);
    
    
    Task<bool> ExecuteFetchRepoLanguageDistributionFlowAsync(RepositoryLanguageDistributionMessage message, CancellationToken ct = default);
    
    
    /// <summary>
    /// Fetches new metrics for a pull request.
    /// </summary>
    /// <param name="filter">The filter to query for statistics records</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the new metrics for the pull request.</returns>
    Task<IApiResponse<RepositoryLanguageStatistic?>> GetLanguagePublicationStatisticsAsync(GetLanguageDistributionStatisticsRequest filter,
        CancellationToken ct = default);
    
    /// <summary>
    /// Fetches new metrics for a pull request.
    /// </summary>
    /// <param name="filter">The filter to query for statistics records</param>
    /// <param name="ct">Cancellation Token</param>
    /// <returns>An <see cref="IApiResponse{T}"/> containing the new metrics for the pull request.</returns>
    Task<IApiResponse<PagedResult<RepositoryLanguageDistribution>>> GetLanguagePublicationTableAsync(GetLanguageDistributionStatisticsRequest filter,
        CancellationToken ct = default);
}