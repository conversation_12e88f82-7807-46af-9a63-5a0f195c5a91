using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;

namespace Hubtel.CCI.Api.Services.Interfaces
{
    public interface IToolingToolsService
    {
        Task<IApiResponse<CreateToolResponse>> CreateToolAsync(CreateToolRequest request, CancellationToken ct);
        Task<IApiResponse<object>> DeleteToolAsync(string id, CancellationToken ct);
        Task<IApiResponse<GetToolResponse>> GetToolAsync(string id, CancellationToken ct);
        Task<IApiResponse<PagedResult<GetToolResponse>>> GetToolsAsync(SearchToolFilter filter, CancellationToken ct);
        Task<IApiResponse<UpdateToolResponse>> UpdateToolAsync(string id, UpdateToolRequest request, CancellationToken ct);
        Task<IApiResponse<PagedResult<GetToolingReportsResponse>>> GetToolingReportsAsync(GetToolingReportsRequest request, CancellationToken ct);

        Task<IApiResponse<UpdateToolingReportResponse>> UpdateToolingReportAsync(string id, UpdateToolingReportRequest request, CancellationToken ct);
        Task<IApiResponse<GetToolingReportsResponse>> GetToolingReportAsync(string id, CancellationToken ct);
        Task<IApiResponse<string>> GenerateAndSendToolingReport(string id,CancellationToken ct);


    }
}
