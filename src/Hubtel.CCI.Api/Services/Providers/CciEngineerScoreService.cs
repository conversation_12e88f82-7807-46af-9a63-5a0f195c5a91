using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciEngineerScore;
using Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class CciEngineerScoreService(
    ILogger<CciEngineerScoreService> logger,
    IRepositoryContext repositoryContext): ICciEngineerScoreService
{
    public async Task<IApiResponse<CciEngineerScoreResponse>> GetCciEngineerScoreByIdAsync(string id, CancellationToken ct)
    {
        logger.LogInformation("[CciEngineerScoreService::GetCciEngineerScoreByIdAsync] Fetching Cci Engineer Score by Id: {Id}", id);
        
        var cciEngineerScore = await repositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .ProjectToType<CciEngineerScoreResponse>()
            .FirstOrDefaultAsync(c => c.Id == id, ct);

        return cciEngineerScore == null ? 
            ApiResponse<CciEngineerScoreResponse>.Default.ToNotFoundApiResponse("Cci Engineer Score not found") : 
            cciEngineerScore.ToOkApiResponse("Cci Engineer Score fetched successfully");
    }

    public async Task<IApiResponse<CciEngineerSonarQubeMetricsResponse>> GetCciEngineerSonarQubeMetricsByIdAsync(string id, CancellationToken ct)
    {
        logger.LogInformation("[CciEngineerScoreService::GetCciEngineerSonarQubeMetricsByIdAsync] Fetching Cci Engineer SonarQube Metrics by Id: {Id}", id);
        
        var cciEngineerSonarQubeMetrics = await repositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .AsNoTracking()
            .ProjectToType<CciEngineerSonarQubeMetricsResponse>()
            .FirstOrDefaultAsync(c => c.Id == id, ct);
        
        return cciEngineerSonarQubeMetrics == null ?
            ApiResponse<CciEngineerSonarQubeMetricsResponse>.Default.ToNotFoundApiResponse("Cci Engineer SonarQube Metrics not found") :
            cciEngineerSonarQubeMetrics.ToOkApiResponse("Cci Engineer SonarQube Metrics fetched successfully");
    }

    public async Task<IApiResponse<PagedResult<CciEngineerScoreResponse>>> GetCciEngineerTableScoresAsync(GetCciEngineerTableScoresFilter request, CancellationToken ct)
    {
        logger.LogInformation("[CciEngineerScoreService::GetCciEngineerTableScoresAsync] Fetching Cci Engineer Table Scores with filter: {Request}", 
            request.Serialize());
        
        var startDate = request.PublicationDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var endDate = request.PublicationDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        
        var productGroupId = request.ProductGroupId;
        var engineerEmails = new List<string>();
        if (!string.IsNullOrWhiteSpace(productGroupId))
        {
            var productGroup = await repositoryContext.ProductGroupRepository
                .GetQueryable()
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == productGroupId, ct);
            
            engineerEmails = productGroup?.ProductTeams.Items
                .SelectMany(x => x.Members.Items)
                .Select(x => x.Email)
                .ToList() ?? new List<string>();
        }

        var query = repositoryContext.CciEngineerScoreRepository.GetQueryable().AsNoTracking();
        
        var response = await query
            .Where(c => (startDate == null || c.PublicationDate >= startDate) &&
                        (endDate == null || c.PublicationDate <= endDate))
            .Where(c => request.PublicationWeek == null || c.PublicationWeek == request.PublicationWeek)
            .Where(c => string.IsNullOrWhiteSpace(request.Search) || c.EngineerName!.Contains(request.Search) || c.EngineerEmail!.Contains(request.Search))
            .Where(c => engineerEmails.Count == 0 || engineerEmails.Contains(c.EngineerEmail!))
            .Where(c=> request.Domain == null || c.EngineerDomain == request.Domain)
            .OrderByDynamic(request.SortColumn, request.SortDir == "desc")
            .ProjectToType<CciEngineerScoreResponse>()
            .GetPagedAsync(request.PageIndex, request.PageSize, ct);
            
        return response.ToOkApiResponse("Cci Engineer Table Scores fetched successfully");

    }

    public async Task<IApiResponse<GetCciEngineerScoreStatisticResponse>> FetchEngineerCciPublicationStatisticsAsync(FetchCciEngineerStatisticsRequest request, CancellationToken ct)
    {
        
        var query = repositoryContext.CciEngineersScoreStatisticRepository
            .GetQueryable().AsNoTracking();
        
        var response = await query
            .Where(c => request.PublicationDate ==null || c.PublicationDate >= request.PublicationDate.Value.Date)
            .Where(c => request.PublicationWeek == null || c.PublicationWeek == request.PublicationWeek)
            .OrderByDescending(c => c.PublicationDate)
            .FirstOrDefaultAsync(ct);
        
        return response.Adapt<GetCciEngineerScoreStatisticResponse>().ToOkApiResponse("Cci Engineer Publication Statistics fetched successfully");
    }

    public async Task<IApiResponse<CciEngineerRankingsResponse>> FetchEngineerCciPublicationRankingsAsync(FetchCciEngineerStatisticsRequest request, CancellationToken ct)
    {
        var query = repositoryContext.CciEngineerRankingRepository
            .GetQueryable().AsNoTracking();

        var response = await query
            .Where(c => request.PublicationDate == null || c.PublicationDate >= request.PublicationDate.Value.Date)
            .Where(c => request.PublicationWeek == null || c.PublicationWeek == request.PublicationWeek)
            .OrderByDescending(c => c.PublicationDate)
            .FirstOrDefaultAsync(ct);
        
        return response.Adapt<CciEngineerRankingsResponse>()
            .ToOkApiResponse("Cci Engineer Publication Rankings fetched successfully");
        
    }

    public async Task<IApiResponse<bool>> DeleteEngineerCciPublicationAsync(DateTime publicationDate, CancellationToken ct)
    {
        logger.LogInformation("[CciEngineerScoreService::DeleteEngineerCciPublicationAsyncAsync] Deleting Cci Engineer Publication with date: {PublicationDate}", publicationDate);
        
        var engineerScores = await repositoryContext.CciEngineerScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.PublicationDate == publicationDate.Date)
            .ToListAsync(ct);
        
        var publicationRanking = await repositoryContext.CciEngineerRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.PublicationDate == publicationDate.Date)
            .ToListAsync(ct);
        
        var engineerSonarQubeMetrics = await repositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.PublicationDate == publicationDate.Date)
            .ToListAsync(ct);
        
        var statistics = await repositoryContext.CciEngineersScoreStatisticRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.PublicationDate == publicationDate.Date)
            .ToListAsync(ct);
        
        if (engineerScores.Count == 0 && 
            publicationRanking.Count == 0 && 
            engineerSonarQubeMetrics.Count == 0 && 
            statistics.Count == 0)
            return ApiResponse<bool>.Default.ToNotFoundApiResponse("Cci Engineer Publication not found");
        
        await repositoryContext.CciEngineerScoreRepository.DeleteRangeAsync(engineerScores, ct);
        await repositoryContext.CciEngineerRankingRepository.DeleteRangeAsync(publicationRanking, ct);
        await repositoryContext.EngineerSonarQubeMetricsRepository.DeleteRangeAsync(engineerSonarQubeMetrics, ct);
        await repositoryContext.CciEngineersScoreStatisticRepository.DeleteRangeAsync(statistics, ct);
        return true.ToOkApiResponse("Cci Engineer Publication deleted successfully");
    }

    public async Task<IApiResponse<PagedResult<CciEngineerSonarQubeMetricsResponse>>> GetCciEngineerTableSqAsync(GetCciEngineerSqMetricsTableScoresFilter request, CancellationToken ct)
    {
        logger.LogInformation("[CciEngineerScoreService::GetCciEngineerTableSqAsync] Fetching Cci Engineer Table SonarQube Metrics with filter: {Request}", 
            request.Serialize());
        
        var startDate = request.PublicationDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var endDate = request.PublicationDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        var query = repositoryContext.EngineerSonarQubeMetricsRepository.GetQueryable().AsNoTracking();
        
        var response = await query
            .Where(c => (startDate == null || c.PublicationDate >= startDate) &&
                        (endDate == null || c.PublicationDate <= endDate))
            .Where(c => request.PublicationWeek == null || c.PublicationWeek == request.PublicationWeek)
            .Where(c => string.IsNullOrWhiteSpace(request.Search) || c.EngineerName!.Contains(request.Search) || c.EngineerEmail!.Contains(request.Search))
            .Where(c => string.IsNullOrWhiteSpace(request.EngineerEmail) || c.EngineerEmail == request.EngineerEmail)
            .OrderByDynamic(request.SortColumn, request.SortDir == "desc")
            .ProjectToType<CciEngineerSonarQubeMetricsResponse>()
            .GetPagedAsync(request.PageIndex, request.PageSize, ct);
            
        return response.ToOkApiResponse("Cci Engineer Table Sq Metrics fetched successfully");
    }

    public async Task<IApiResponse<CciEngineerSqMetricsOverview>> FetchEngineerCciPublicationSqMetricsOverviewAsync(GetCciEngineerSqMetricsTableScoresFilter request, CancellationToken ct)
    {
        logger.LogInformation("[CciEngineerScoreService::FetchEngineerCciPublicationSqMetricsOverviewAsync] Fetching Cci Engineer Publication Sq Metrics Overview with filter: {Request}", 
            request.Serialize());

        var query = repositoryContext.EngineerSonarQubeMetricsRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => request.PublicationDate == null || c.PublicationDate >= request.PublicationDate.Value.Date)
            .Where(c => request.PublicationWeek == null || c.PublicationWeek == request.PublicationWeek)
            .Where(c => string.IsNullOrWhiteSpace(request.Search) || c.EngineerName!.Contains(request.Search) || c.EngineerEmail!.Contains(request.Search))
            .Where(c => string.IsNullOrWhiteSpace(request.EngineerEmail) || c.EngineerEmail == request.EngineerEmail);

        var data = await query.ToListAsync(ct);

        var uniqueEngineerCount = data.Select(x => x.EngineerEmail).Distinct().Count();
        var uniqueRepositoryCount = data.Select(x => x.RepositoryId).Distinct().Count();

        var engineerSummaries = data
            .GroupBy(x => new { x.EngineerName, x.EngineerEmail })
            .Select(group => new EngineerSummary
            {
                EngineerName = group.Key.EngineerName,
                EngineerEmail = group.Key.EngineerEmail,
                UniqueRepositoryCount = group.Select(x => x.RepositoryId).Distinct().Count(),
                UniquePullRequestCount = group.Select(x => x.PullRequest.PullRequestId).Distinct().Count(), 
                PullRequests = group.Select(x => new PullRequestSummary()
                {
                    PullRequestId = x.PullRequest.PullRequestId,
                    RepositoryName = x.PullRequest.Repository.Name,
                    RepositoryId = x.PullRequest.Repository.Id,
                    PullRequestUrl = x.PullRequest.Url,
                    LinesOfCode = decimal.TryParse(x.SonarQubeMetrics.Measures
                        .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Ncloc)?.Value, out var ncloc)? ncloc: 0m,
                    Coverage = decimal.TryParse(x.SonarQubeMetrics.Measures
                        .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Coverage)?.Value, out var cov)? cov: 0m,
                }).ToList()
            })
            .ToList();
        

        var result = new CciEngineerSqMetricsOverview()
        {
            PublicationWeek = request.PublicationWeek,
            PublicationDate = request.PublicationDate?.Date,
            UniqueEngineerCount = uniqueEngineerCount,
            UniqueRepositoryCount = uniqueRepositoryCount,
            Engineers = engineerSummaries
            
        };
        
        return result.ToOkApiResponse("Cci Engineer Publication Sq Metrics Overview fetched successfully");
    }
}