using System.Globalization;
using Akka.Actor;
using Flurl.Http;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Responses.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Hubtel.CCI.Api.Services.Providers;

/// <summary>
///     Service responsible for all related operations for CciRepositoryScore entity.
/// </summary>
public class CciArsenalScoreService : ICciArsenalScoreService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger _logger;
    private readonly IActorService<MainActor> _mainActor;
    private readonly SonarQubeConfig _sonarQubeConfig;

    public CciArsenalScoreService(ILogger<CciArsenalScoreService> logger, IRepositoryContext repositoryContext,
        IActorService<MainActor> mainActor, IOptions<SonarQubeConfig> sonarQubeConfig)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
        _mainActor = mainActor;
        _sonarQubeConfig = sonarQubeConfig.Value;
    }

    /// <summary>
    /// Asynchronously retrieves CCI repository score statistics based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the CCI repository score statistics.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with the CCI repository score statistic response.
    /// </returns>
    public async Task<IApiResponse<GetCciRepositoryScoreStatisticResponse>> GetCciRepositoryScoreStatisticsAsync(
        GetCciRepositoryScoreStatisticsRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetCciRepositoryScoreStatisticsAsync] Received Request to get CciRepositoryScoreStatistics => RequestPayload => {Request}",
            request.Serialize());

        var startDate = request.PublicationDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var endDate = request.PublicationDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        var query = _repositoryContext.CciRepositoryScoreStatisticRepository.GetQueryable().AsNoTracking();
        
        var cciRepositoryScore = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x =>
                (startDate == null || x.PublicationStartDate >= startDate) &&
                (endDate == null || x.PublicationEndDate <= endDate))
            .FirstOrDefaultAsync(ct);

        var response = await query
            .Where(x => request.PublicationWeek == null || x.PublicationWeek == request.PublicationWeek)
            .Where(x => request.PublicationDate == null || 
                        (cciRepositoryScore != null && x.PublicationDate == cciRepositoryScore.PublicationDate))
            .OrderByDescending(x => x.PublicationDate)
            .FirstOrDefaultAsync(ct);

        return response.Adapt<GetCciRepositoryScoreStatisticResponse>().ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously curates weekly CCI repository score statistics based on the provided message.
    /// </summary>
    /// <param name="message">The message containing the CCI products ranking information.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the operation was successful.</returns>
    // Stryker disable all
    public async Task<bool> CurateWeeklyCciRepositoryScoreStatisticsAsync(CurateWeeklyStatisticsMessage message)
    {
        _logger.LogInformation(
            "[CciRepositoryScoreService: CurateWeeklyCciRepositoryScoreStatisticsAsync] Received Request to curate weekly statistics Request => {Statistics}",
            message);

        var startDate = message.Ranking.PublicationStartDate;
        var endDate = message.Ranking.PublicationEndDate;

        var repositoryScores = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate >= startDate &&
                        x.PublicationEndDate <= endDate &&
                        x.ProductTeamId != null && x.Status != CommonConstants.ValidationConstants.ProductStatus.Retired)
            .ToListAsync();

        if (repositoryScores.Count == 0)
        {
            _logger.LogWarning(
                "[CciRepositoryScoreService: CurateWeeklyCciRepositoryScoreStatisticsAsync] No CciRepositoryScores found for the latest publication week");
            return false;
        }
        
        var eightWeeksAgo = DateTime.UtcNow.AddDays(-8 * 7);
        
        var recordedRankings = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate != null
                        && x.PublicationStartDate >= eightWeeksAgo)
            .OrderByDescending(x => x.PublicationStartDate)
            .ToListAsync();

        var statistics = new CciRepositoryScoreStatistic
        {
            PublicationDate = message.Ranking.PublicationDate,
            PublicationWeek = message.Ranking.PublicationWeek,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ScoreStatistic = new ScoreStatistic
            {
                Overview = new Overview
                {
                    CurrentScore = message.Ranking.CurrentScore ?? 0,
                    TotalBugs = repositoryScores.Sum(x => x.Bugs ?? 0),
                    TotalCodeSmells = repositoryScores.Sum(x => x.CodeSmells ?? 0),
                    TotalVulnerabilities = repositoryScores.Sum(x => x.Vulnerabilities ?? 0),
                    TotalSecurityHotspots = repositoryScores.Sum(x => x.SecurityHotspots ?? 0),
                    TotalCognitiveComplexity = repositoryScores.Sum(x => x.CognitiveComplexity ?? 0),
                    AverageDuplications = repositoryScores.Average(x => x.DuplicatedLinesDensity ?? 0),
                    AverageCodeCoverage = repositoryScores.Average(x => x.Coverage ?? 0),
                    TotalRelativeCognitiveComplexity = repositoryScores.Sum(x => x.RelativeCognitiveComplexity ?? 0),
                    TotalNonCommentedLinesOfCode = repositoryScores.Sum(x => x.NonCommentedLinesOfCode?? 0),
                    TotalLinesOfCode = repositoryScores.Sum(x => x.LinesOfCode ?? 0),
                },
                Highlights = new Highlights
                {
                    Highest = new Highlight
                    {
                        Name = message.Ranking.Rankings.Overall.OrderByDescending(x => x.Rating).FirstOrDefault()
                            ?.ProductTeamName ?? string.Empty,
                        Score = message.Ranking.Rankings.Overall.OrderByDescending(x => x.Rating).FirstOrDefault()
                            ?.Rating ?? 0
                    },
                    Lowest = new Highlight
                    {
                        Name =
                            message.Ranking.Rankings.Overall.OrderBy(x => x.Rating).FirstOrDefault()?.ProductTeamName ??
                            string.Empty,
                        Score = message.Ranking.Rankings.Overall.OrderBy(x => x.Rating).FirstOrDefault()?.Rating ?? 0
                    }
                }
            }
        };

        var productTeams = await _repositoryContext.ProductTeamRepository.GetQueryable().AsNoTracking().ToListAsync();

        ComputeStatisticsOnProductTeams(message, productTeams, repositoryScores, statistics);

        statistics.ScoreStatistic.OverviewByScope =
            CreateOverviewByScope(repositoryScores, statistics.ScoreStatistic.Ranking);
        statistics.ScoreStatistic.OverviewByScope.Backend.Score = message.Ranking.Rankings.Backend.Count == 0
            ? 0
            : message.Ranking.Rankings.Backend.Average(x => x.Rating) ?? 0;
        statistics.ScoreStatistic.OverviewByScope.Frontend.Score = message.Ranking.Rankings.Frontend.Count == 0
            ? 0
            : message.Ranking.Rankings.Frontend.Average(x => x.Rating) ?? 0;
        statistics.ScoreStatistic.OverviewByScope.Overall.Score = message.Ranking.CurrentScore ?? 0;
        
        statistics.ScoreStatistic.WeeklyFinalAverageTrend = ComputeWeeklyFinalAverageTrendV2(recordedRankings);
        
        statistics.ScoreStatistic.IssueDistribution = ComputeIssueDistribution(repositoryScores);
        
        var eightMonthsAgo = DateTime.UtcNow.AddMonths(-8);

        var monthlyFinalRankings = await GetMonthlyRecordedRankings(eightMonthsAgo);
        
        statistics.ScoreStatistic.MonthlyFinalAverageTrend = ComputeWeeklyFinalAverageTrendV2(monthlyFinalRankings, true);

        var saved = await _repositoryContext.CciRepositoryScoreStatisticRepository.AddAsync(statistics);

        if (saved > 0) return true;
        _logger.LogWarning(
            "[CciRepositoryScoreService: CurateWeeklyCciRepositoryScoreStatisticsAsync] Could not save statistics for product team {Statistics}",
            statistics.Serialize());
        return false;
    }
    // Stryker restore all


    /// <summary>
    /// Asynchronously curates the weekly trend based on the provided message.
    /// </summary>
    /// <param name="message">The message containing the CCI products ranking information.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the operation was successful.</returns>
    public async Task<bool> CurateWeeklyTrendAsync(CurateWeeklyTrendMessage message)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: CurateWeeklyTrendAsync] Received Request to curate weekly trend Request => {Trends}",
            message);
        var publicationWeek = message.CciProductsRanking.PublicationWeek;
        
        var currentStartDate = message.CciProductsRanking.PublicationStartDate?.Date ?? DateTime.MinValue;
        var previousDateRange = Miscellaneous.GetPreviousPublicationDayRange(currentStartDate);
        
        var previousRanking = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x =>
                x.PublicationStartDate >= previousDateRange.Start &&
                x.PublicationEndDate <= previousDateRange.End)
            .OrderByDescending(x => x.PublicationDate)
            .FirstOrDefaultAsync();

        var trends = new List<CciProductTeamsScoreTrend>();

        foreach (var category in new[]
                 {
                     ValidationConstants.ProductTeamScopes.Backend, ValidationConstants.ProductTeamScopes.Frontend,
                     ValidationConstants.ProductTeamScopes.Overall
                 })
        {
            var rankings =
                message.CciProductsRanking.Rankings.GetType().GetProperty(category)
                    ?.GetValue(message.CciProductsRanking.Rankings) as IEnumerable<RankingItem>;

            rankings ??= new List<RankingItem>();

            var rankingItems = rankings.ToList();

            var previousRankings =
                previousRanking?.Rankings.GetType().GetProperty(category)?.GetValue(previousRanking.Rankings) as
                    IEnumerable<RankingItem>;

            previousRankings ??= new List<RankingItem>();

            var previousRankingsList = previousRankings.ToList();

            var previousRankingDict = previousRankingsList.ToDictionary(x => x.ProductTeamId ?? string.Empty, x => x);
            trends.AddRange(from item in rankingItems
                let previousRankingItem = previousRankingDict.GetValueOrDefault(item.ProductTeamId ?? string.Empty)
                select new CciProductTeamsScoreTrend
                {
                    PublicationWeek = publicationWeek,
                    PublicationDate = message.CciProductsRanking.PublicationDate,
                    ProductTeamId = item.ProductTeamId,
                    ProductTeamName = item.ProductTeamName,
                    ProductGroupId = item.ProductGroupId,
                    ProductGroupName = item.ProductGroupName,
                    AverageScore = item.Rating,
                    TrendDirection =
                        Miscellaneous.GetTrendDirection(item.Rating ?? 0, previousRankingItem?.Rating ?? 0),
                    ScoreChange = (item.Rating ?? 0) - (previousRankingItem?.Rating ?? 0),
                    ProductScope = category,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
        }

        var saved = await _repositoryContext.CciProductTeamsScoreTrendRepository.AddRangeAsync(trends);

        if (saved <= 0)
            _logger.LogWarning(
                "[CciRepositoryScoreService: CurateWeeklyTrendAsync] Could not save trend for product team {Trends}",
                trends.Serialize());

        _logger.LogDebug(
            "[CciRepositoryScoreService: CurateWeeklyTrendAsync] Saved trend for product team {Trends}",
            trends.Serialize());

        return true;
    }

    /// <summary>
    /// Asynchronously retrieves product trends based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the product trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of product team score trend responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    public async Task<IApiResponse<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>>
        GetProductTrendsAsync(GetProductTrendsRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetProductTrendsAsync] Received Request to get ProductTrends");

        var query = _repositoryContext.CciProductTeamsScoreTrendRepository.GetQueryable().AsNoTracking();

        var response = await query
            .Where(x => request.ProductTeamId == null || x.ProductTeamId == request.ProductTeamId)
            .Where(t => t.PublicationWeek != null && t.PublicationWeek.CompareTo(request.WeekFrom) >= 0 &&
                        t.PublicationWeek.CompareTo(request.WeekTo) <= 0)
            .GroupBy(x => x.ProductTeamName)
            .ToDictionaryAsync(x => x.Key ?? ValidationConstants.ProductTeamScopes.Miscellaneous, y => y.ToList(), ct);

        return response.Adapt<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>().ToOkApiResponse();
    }
    
    
    
        /// <summary>
    /// Asynchronously retrieves product trends based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the product trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of product team score trend responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    public async Task<IApiResponse<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>>
        GetProductTrendsRecentAsync(GetProductTrendsRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetProductTrendsAsync] Received Request to get ProductTrends");

        var query = _repositoryContext.CciProductTeamsScoreTrendRepository.GetQueryable().AsNoTracking();

        var response = await query
            .Where(x => request.ProductTeamId == null || x.ProductTeamId == request.ProductTeamId)
            .Where(t => t.PublicationWeek != null && t.PublicationWeek.CompareTo(request.WeekFrom) >= 0 &&
                        t.PublicationWeek.CompareTo(request.WeekTo) <= 0)
            .OrderByDescending(x => x.PublicationDate)
            .GroupBy(x => x.ProductTeamName)
            .Select(g => new
            {
                ProductTeamName = g.Key,
                LatestPublicationDate = g.Max(x => x.PublicationDate),
                Records = g.Where(x => x.PublicationDate == g.Max(y => y.PublicationDate)).ToList()
            })
            .ToDictionaryAsync(
                x => x.ProductTeamName ?? ValidationConstants.ProductTeamScopes.Miscellaneous,
                x => x.Records, ct);

        return response.Adapt<Dictionary<string, List<GetCciProductTeamsScoreTrendResponse>>>().ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously retrieves the CCI products rankings based on the provided request criteria.
    /// </summary>
    /// <param name="request">The request containing the filter criteria for the rankings.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a list of CCI products ranking responses.
    /// </returns>
    public async Task<IApiResponse<List<GetCciProductsRankingResponse>>> GetCciProductsRankingsAsync(
        GetCciProductsRankingsRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetCciProductsRankingsAsync] Received Request to get CciRepositoryTableScoresBasedOnProducts");

        var query = _repositoryContext.CciProductsRankingRepository.GetQueryable().AsNoTracking();
        
        var startDate = request.PublicationDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var endDate = request.PublicationDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        var response = await query
            .Where(x => request.PublicationWeek == null || x.PublicationWeek == request.PublicationWeek)
            .Where(x =>
                (startDate == null || x.PublicationStartDate >= startDate) &&
                (endDate == null || x.PublicationEndDate <= endDate))
            .OrderByDescending(x => x.PublicationDate)
            .ToListAsync(ct);

        if (response.Count != 0 || request.PublicationWeek != null || request.PublicationDate != null)
            return response.Adapt<List<GetCciProductsRankingResponse>>().ToOkApiResponse();
        
        var latestResponse = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .OrderByDescending(x => x.PublicationDate)
            .Take(1)
            .ToListAsync(ct);

        return latestResponse.Adapt<List<GetCciProductsRankingResponse>>().ToOkApiResponse();
        
    }

    

    /// <summary>
    /// Asynchronously creates the latest publication CCI products ranking.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <param name="request">Optional request to publish publication for a duration.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with the created CCI products ranking.
    /// </returns>
    // Stryker disable all
    public async Task<IApiResponse<bool>> PublishCciProductsRankingAsync(PublishCciProductsRankingRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CreateLatestPublicationCciProductsRankingAsync: CreateLatestPublicationCciProductsRankingAsync] Received Request to create CciProductsRanking");

        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var dates = (
            StartDate: publicationDate.Date,
            EndDate: publicationDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59)
        );
        var startDate = request.StartDate ?? dates.StartDate;
        var endDate = request.EndDate ?? dates.EndDate;

        var latestRanking = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate >= startDate && x.PublicationEndDate <= endDate)
            .FirstOrDefaultAsync(ct);

        if (latestRanking != null)
        {
            _logger.LogWarning(
                "[CreateLatestPublicationCciProductsRankingAsync: CreateLatestPublicationCciProductsRankingAsync] Skipping creating ranking CciProductsRanking already exists for the latest publication day");
            return true.ToOkApiResponse();
        }

        var response = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate >= dates.StartDate &&
                        x.PublicationEndDate <= dates.EndDate &&
                        x.ProductTeamId != null && x.Status != ValidationConstants.ProductStatus.Retired)
            .GroupBy(x => x.ProductTeamName)
            .ToDictionaryAsync(x => x.Key ?? ValidationConstants.ProductTeamScopes.Miscellaneous,
                y => new PublicationInfo()
                {
                    ProductTeamName = y.FirstOrDefault()?.ProductTeamName,
                    ProductTeamId = y.FirstOrDefault()?.ProductTeamId,
                    ProductGroupId = y.FirstOrDefault()?.ProductGroupId,
                    ProductGroupName = y.FirstOrDefault()?.ProductGroupName,
                    PublicationWeek = y.FirstOrDefault()?.PublicationWeek,
                    PublicationDate = y.FirstOrDefault()?.PublicationDate,
                    PublicationStartDate = y.FirstOrDefault()?.PublicationStartDate,
                    PublicationEndDate = y.FirstOrDefault()?.PublicationEndDate,
                    Backend = y.Where(z => z.RepositoryType == ValidationConstants.RepositoryType.Backend).ToList(),
                    Frontend = y.Where(z => z.RepositoryType == ValidationConstants.RepositoryType.Frontend).ToList()
                }, ct);

        if (response.Count == 0)
        {
            _logger.LogWarning(
                "[CreateLatestPublicationCciProductsRankingAsync: CreateLatestPublicationCciProductsRankingAsync] No CciRepositoryScores found for the latest publication week");
            return ApiResponse<bool>.Default.ToNotFoundApiResponse(
                "No CciRepositoryScores found for the latest publication week");
        }


        var ranking = Miscellaneous.CurateProductTeamsRanking(response);

        var savedCount = await _repositoryContext.CciProductsRankingRepository.AddAsync(ranking, ct);

        if (savedCount <= 0)
        {
            _logger.LogError(
                "[CreateLatestPublicationCciProductsRankingAsync: CreateLatestPublicationCciProductsRankingAsync] Could not create CciProductsRanking! Please try again, Data: {Ranking}",
                ranking.Serialize());
            return ApiResponse<bool>.Default.ToFailedDependencyApiResponse(
                "Could not create CciProductsRanking! Please try again"
            );
        }

        _logger.LogDebug(
            "[CreateLatestPublicationCciProductsRankingAsync: CreateLatestPublicationCciProductsRankingAsync] Created CciProductsRanking for the latest publication week => {Ranking}",
            ranking.Serialize());

        await _mainActor.Tell(new CurateWeeklyTrendMessage(ranking), ActorRefs.Nobody);
        await _mainActor.Tell(new CurateWeeklyStatisticsMessage(ranking), ActorRefs.Nobody);

        return true.ToOkApiResponse();
    }
    // Stryker restore all

    /// <summary>
    /// Asynchronously retrieves the CCI repository table scores based on products for the previous week.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <param name="request">A representation of queryable operations on the GetCciRepositoryTableScoresBasedOnProductsAsync.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of CCI repository score responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    public async Task<IApiResponse<Dictionary<string, List<GetCciRepositoryScoreResponse>>>>
        GetCciRepositoryTableScoresBasedOnProductsAsync(GetCciRepositoryTableScoresBasedOnProductsRequest request,
            CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetCciRepositoryTableScoresBasedOnProductsAsync] Received Request to get CciRepositoryTableScoresBasedOnProducts");

        var startDate = request.PublicationStartDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var endDate = request.PublicationEndDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        
        
        var query = _repositoryContext.CciRepositoryScoreRepository.GetQueryable().AsNoTracking();
        
        var filteredQuery = ApplyCciTableFilters(query, request, startDate, endDate);
        var response = await filteredQuery
            .GroupBy(x => x.ProductTeamName)
            .ToDictionaryAsync(
                x => x.Key ?? ValidationConstants.ProductTeamScopes.Miscellaneous,
                y =>
                {
                    if (request.PublicationWeek != null)
                    {
                        return y
                            .GroupBy(c => c.RepositoryId)
                            .Select(g => g
                                .OrderByDescending(c => c.PublicationEndDate)
                                .ThenByDescending(c => c.PublicationDate)
                                .FirstOrDefault())
                            .Where(c => c != null)
                            .Cast<CciRepositoryScore>()
                            .ToList();
                    }

                    return y.OrderByDescending(c=>c.PublicationDate).ToList();
                },
                ct);

        return response.Adapt<Dictionary<string, List<GetCciRepositoryScoreResponse>>>().ToOkApiResponse();
    }
    
    
    /// <summary>
    /// Asynchronously retrieves the CCI repository table scores based on products for the previous week.
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <param name="request">A representation of queryable operations on the GetCciRepositoryTableScoresBasedOnProductsAsync.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an API response with a dictionary
    /// where the key is the product team name and the value is a list of CCI repository score responses.
    /// If the product team name is null, the key will be "Miscellaneous".
    /// </returns>
    public async Task<IApiResponse<PagedResult<GetCciRepositoryScoreResponse>>>
        GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync(GetCciRepositoryTableScoresBasedOnProductsRequest request,
            CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService::GetCciRepositoryTableScoresBasedOnProductsPaginatedAsync] Received Request to get CciRepositoryTableScoresBasedOnProducts");
        
            var startDate = request.PublicationStartDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var endDate = request.PublicationEndDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        var query = _repositoryContext.CciRepositoryScoreRepository.GetQueryable().AsNoTracking();

        var filteredQuery = ApplyCciTableFilters(query, request, startDate, endDate);
        
        var pagedResult = await filteredQuery
            .OrderByDynamic<CciRepositoryScore>(request.SortColumn.ToPascalCase(), request.SortDir == "desc")
            .GetPagedAsync(request.PageIndex, request.PageSize, ct);

        var results = pagedResult.Results.Select(x => x.Adapt<GetCciRepositoryScoreResponse>()).ToList();

        var response = new PagedResult<GetCciRepositoryScoreResponse>
        {
            Results = results,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound
        };

        return response.ToOkApiResponse();
        
    }
    
    
    // Stryker disable all
    private static IQueryable<CciRepositoryScore> ApplyCciTableFilters(IQueryable<CciRepositoryScore> query, GetCciRepositoryTableScoresBasedOnProductsRequest request, DateTime? startDate, DateTime? endDate)
    {
        return query
            .Where(x => request.ProductTeamId == null || x.ProductTeamId == request.ProductTeamId)
            .Where(x => request.RepositoryId == null || x.RepositoryId == request.RepositoryId)
            .Where(x => request.PublicationWeek == null || x.PublicationWeek == request.PublicationWeek)
            .Where(x => request.RepositoryType == null || x.RepositoryType == request.RepositoryType)
            .Where(x => (startDate == null || x.PublicationStartDate >= startDate) &&
                        (endDate == null || x.PublicationEndDate <= endDate))
            .Where(x => request.Search == null || x.RepositoryName!.ToLower().Contains(request.Search.ToLower()))
            .Where(x => x.Status != CommonConstants.ValidationConstants.ProductStatus.Retired);
    }
    // Stryker restore all

    /// <summary>
    /// Asynchronously gets a list of cciRepositoryScores with pagination.
    /// </summary>
    /// <param name="filter">The filter to apply to the repository list. This includes the search term, page index, and page size.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a paged list of cciRepositoryScores.</returns>
    public async Task<IApiResponse<PagedResult<GetCciRepositoryScoreResponse>>> GetCciRepositoryScoresAsync(
        SearchCciRepositoryScoresRequest filter, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetCciRepositoryScoresAsync] Received Request to get CciRepositoryScores => RequestPayload => {Request}",
            filter.Serialize());

        var query = _repositoryContext.CciRepositoryScoreRepository.GetQueryable().AsNoTracking();

        var pagedResult = await query
            .Where(c => filter.SearchTerm == null ||
                        (c.RepositoryName ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()))
            .Where(x => filter.PublicationWeek == null || x.PublicationWeek == filter.PublicationWeek)
            .Where(x => filter.ProductTeamId == null || x.ProductTeamId == filter.ProductTeamId)
            .Where(x => filter.ProductGroupId == null || x.ProductGroupId == filter.ProductGroupId)
            .OrderByDynamic(filter.SortColumn, filter.SortDir == "desc")
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => p.Adapt<GetCciRepositoryScoreResponse>()).ToList();

        var response = new PagedResult<GetCciRepositoryScoreResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously retrieves a specific repository by their ID.
    /// </summary>
    /// <param name="id">The ID of the repository to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved repository, or a not found response if no repository with the provided ID exists.</returns>
    public async Task<IApiResponse<GetCciRepositoryScoreResponse>> GetCciRepositoryScoreAsync(string id,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: GetCciRepositoryScoreAsync] Received Request to get CciRepositoryScore with ID: {Id}",
            id);

        var result = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .ProjectToType<GetCciRepositoryScoreResponse>()
            .FirstOrDefaultAsync(c => c.Id == id, ct);

        return result is null ? result.ToNotFoundApiResponse() : result.ToOkApiResponse();
    }


    /// <summary>
    /// Asynchronously adds a new repository.
    /// </summary>
    /// <param name="request">The request to create a new cciRepositoryScore. This includes the cciRepositoryScore's details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the created cciRepositoryScore.</returns>
    public async Task<IApiResponse<GetCciRepositoryScoreResponse>> AddCciRepositoryScoreAsync(
        CreateCciRepositoryScoreRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: AddCciRepositoryScoreAsync] Received Request to create CciRepositoryScore => RequestPayload => {Request}",
            request.Serialize());

        var cci = request.Adapt<CciRepositoryScore>();

        cci.CreatedAt = DateTime.UtcNow;
        cci.UpdatedAt = DateTime.UtcNow;

        var existingCciRepositoryScore = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.RepositorySonarQubeKey == cci.RepositorySonarQubeKey && x.PublicationWeek == cci.PublicationWeek,
                ct);

        if (existingCciRepositoryScore != null)
        {
            _logger.LogWarning(
                "[CciRepositoryScoreService: AddCciRepositoryScoreAsync] CciRepositoryScore already exists with RepositorySonarQubeKey: {RepositorySonarQubeKey} and PublicationWeek: {PublicationWeek}",
                cci.RepositorySonarQubeKey, cci.PublicationWeek);
            return existingCciRepositoryScore.Adapt<GetCciRepositoryScoreResponse>().ToOkApiResponse();
        }

        var savedCount = await _repositoryContext.CciRepositoryScoreRepository.AddAsync(cci, ct);

        if (savedCount > 0) return cci.Adapt<GetCciRepositoryScoreResponse>().ToCreatedApiResponse();
        _logger.LogError(
            "[CciRepositoryScoreService: AddCciRepositoryScoreAsync] Could not create CciRepositoryScore! Please try again, Data: {CciRepositoryScore}",
            cci.Serialize());
        return ApiResponse<GetCciRepositoryScoreResponse>.Default.ToFailedDependencyApiResponse(
            "Could not create CciRepositoryScore! Please try again"
        );
    }


    /// <summary>
    /// Asynchronously adds a bulk list of CCI repository scores.
    /// </summary>
    /// <param name="request">The list of requests to create new CCI repository scores.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains the API response with the count of added CCI repository scores.
    /// </returns>
    public async Task<IApiResponse<int>> AddBulkCciRepositoryScoresAsync(
        CreateBulkCciRepositoryScoresRequest request, CancellationToken ct = default)
    {
        _logger.LogInformation(
            "[CciRepositoryScoreService: AddBulkCciRepositoryScoresAsync] Received Request to create Bulk CciRepositoryScores => RequestPayload => {Requests}",
            request.Serialize());

        var cciRepositoryScores = request.Scores.Adapt<List<CciRepositoryScore>>();

        foreach (var cciRepositoryScore in cciRepositoryScores)
        {
            cciRepositoryScore.CreatedAt = DateTime.UtcNow;
            cciRepositoryScore.UpdatedAt = DateTime.UtcNow;
        }
        
        var existingCciRepositoryScores = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => request.Scores.Select(r => r.RepositorySonarQubeKey).Contains(x.RepositorySonarQubeKey))
            .ToListAsync(ct);

        // Filter in-memory by date range
        existingCciRepositoryScores = existingCciRepositoryScores
            .Where(x => request.Scores.Any(r =>
                r.RepositorySonarQubeKey == x.RepositorySonarQubeKey &&
                x.PublicationStartDate >= r.PublicationStartDate &&
                x.PublicationEndDate <= r.PublicationEndDate))
            .ToList();

        if (existingCciRepositoryScores.Count != 0)
        {
            _logger.LogWarning(
                "[CciRepositoryScoreService: AddBulkCciRepositoryScoresAsync] CciRepositoryScores already exist with RepositorySonarQubeKeys: {RepositorySonarQubeKeys} and PublicationWeeks: {PublicationWeeks}",
                existingCciRepositoryScores.Select(x => x.RepositorySonarQubeKey).Serialize(),
                existingCciRepositoryScores.Select(x => x.PublicationWeek).Serialize());
            return existingCciRepositoryScores.Count.ToOkApiResponse();
        }

        var savedCount = await _repositoryContext.CciRepositoryScoreRepository.AddRangeAsync(cciRepositoryScores, ct);

        if (savedCount > 0) return savedCount.ToOkApiResponse();
        _logger.LogError(
            "[CciRepositoryScoreService: AddBulkCciRepositoryScoresAsync] Could not create Bulk CciRepositoryScores! Please try again, Data: {CciRepositoryScores}",
            cciRepositoryScores.Serialize());
        return ApiResponse<int>.Default.ToFailedDependencyApiResponse(
            "Could not create Bulk CciRepositoryScores! Please try again"
        );
    }

    // stryker disable all
    public async Task<IApiResponse<GetCciRepositoryScoreStatisticResponse>> UpdateCciRepositoryStatisticsNewAsync(UpdateCciRepositoryStatisticsRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService::GetCciRepositoryStatisticsNewAsync] Received Request to get CciRepositoryScoreStatistics => RequestPayload => {Request}",
            request.Serialize());

        var query = _repositoryContext.CciRepositoryScoreStatisticRepository.GetQueryable().AsNoTracking();

        var response = await query
            .Where(x => request.PublicationWeek == null || x.PublicationWeek == request.PublicationWeek)
            .Where(x => request.Id == null || x.Id == request.Id)
            .FirstOrDefaultAsync(ct);

        if (response == null)
        {
            _logger.LogWarning("No repository statistics found for the given request.");
            return ApiResponse<GetCciRepositoryScoreStatisticResponse>.Default.ToNotFoundApiResponse();
        }

        if (response.PublicationDate == null)
        {
            _logger.LogWarning("Publication date is null in the fetched response.");
            return ApiResponse<GetCciRepositoryScoreStatisticResponse>.Default.ToNotFoundApiResponse();
        }

        // Compute the starting date for eight recent weeks using the PublicationDate
        var publicationDate = response.PublicationDate.Value;
        var eightWeeksAgo = publicationDate.AddDays(-7 * 8);

        // Query for recent publications (eight weeks up until the publication date)
        var recordedRankings = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate != null
                        && x.PublicationStartDate >= eightWeeksAgo
                        && x.PublicationStartDate <= publicationDate)
            .OrderByDescending(x => x.PublicationStartDate)
            .ToListAsync(ct);

        
        if (recordedRankings.Count == 0)
        {
            _logger.LogWarning("No recent publications found for the given publication week.");
            return ApiResponse<GetCciRepositoryScoreStatisticResponse>.Default.ToNotFoundApiResponse(
                "No recent publications found for the given publication week.");
        }
        
        var mostRecentPublication = recordedRankings[0];
        var startDate = mostRecentPublication.PublicationStartDate;
        var endDate = mostRecentPublication.PublicationEndDate;

        if (startDate == null || endDate == null)
        {
            _logger.LogWarning("Most recent publication has null start or end dates.");
            return ApiResponse<GetCciRepositoryScoreStatisticResponse>.Default.ToNotFoundApiResponse();
        }

        // Query for repository scores within the given start and end date
        var repositoryScores = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate >= startDate
                        && x.PublicationEndDate <= endDate
                        && x.ProductTeamId != null)
            .ToListAsync(ct);
        
        response!.ScoreStatistic.WeeklyFinalAverageTrend = ComputeWeeklyFinalAverageTrendV2(recordedRankings);
        
        response!.ScoreStatistic.IssueDistribution = ComputeIssueDistribution(repositoryScores);
        
        var eightMonthsAgo = publicationDate.AddMonths(-8);

        var monthlyFinalRankings = await GetMonthlyRecordedRankings(eightMonthsAgo);
        
        response!.ScoreStatistic.MonthlyFinalAverageTrend = ComputeWeeklyFinalAverageTrendV2(monthlyFinalRankings, true);
        
        var updated = await _repositoryContext.CciRepositoryScoreStatisticRepository.UpdateAsync(response, ct);
        
        return updated > 0 ? response.Adapt<GetCciRepositoryScoreStatisticResponse>().ToOkApiResponse() 
            : response.Adapt<GetCciRepositoryScoreStatisticResponse>()
                .ToFailedDependencyApiResponse("Failed to update the repository statistics.");
    }
    // stryker restore all

    public async Task<IApiResponse<GetCciTrendResponse>> GetCciTrendsByPublicationDateRangeAsync(CciTrendRequest request, CancellationToken ct = default)
    {
        var today = DateTime.UtcNow.Date;
        
        var startDate = request.PublicationStartDate?.Date.AddHours(0).AddMinutes(0).AddSeconds(0) ?? today.AddMonths(-8);
        var endDate = request.PublicationEndDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(59) ?? today;
        
        var rankings = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate >= startDate && x.PublicationEndDate <= endDate)
            .OrderByDescending(x => x.PublicationEndDate)
            .ToListAsync(ct);

        if (rankings.Count == 0)
        {
            return ApiResponse<GetCciTrendResponse>.Default
                .ToNotFoundApiResponse("No rankings found in the provided date range.");
        }

        var dailyRankings = rankings
            .OrderByDescending(x => x.PublicationEndDate)
            .Take(8)
            .OrderBy(x => x.PublicationEndDate)
            .ToList();
        var dailyTrend = ComputeWeeklyFinalAverageTrendV2(dailyRankings);

        var weeklyGrouped = rankings
            .GroupBy(x => ISOWeek.GetYear(x.PublicationEndDate!.Value) + "-W" + ISOWeek.GetWeekOfYear(x.PublicationEndDate!.Value))
            .Select(g => g.OrderByDescending(r => r.PublicationEndDate).First())
            .OrderByDescending(x => x.PublicationEndDate)
            .Take(8)
            .OrderBy(x => x.PublicationEndDate)
            .ToList();
        var weeklyTrend = ComputeWeeklyFinalAverageTrendV2(weeklyGrouped);

        var monthlyGrouped = rankings
            .GroupBy(x => new { x.PublicationEndDate!.Value.Year, x.PublicationEndDate.Value.Month })
            .Select(g => g.OrderByDescending(r => r.PublicationEndDate).First())
            .OrderByDescending(x => x.PublicationEndDate)
            .Take(8)
            .OrderBy(x => x.PublicationEndDate)
            .ToList();
        var monthlyTrend = ComputeWeeklyFinalAverageTrendV2(monthlyGrouped, isMonthly: true);

        var response = new GetCciTrendResponse
        {
            DailyAverageTrend = dailyTrend,
            WeeklyFinalAverageTrend = weeklyTrend,
            MonthlyFinalAverageTrend = monthlyTrend
        };

        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<bool>> TriggerCciRepositoryScorePublishAsync(CancellationToken ct = default)
    {
        var dayPublished = DateTime.UtcNow;
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationStartDate = publicationDate.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var publicationEndDate = publicationDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        
        var existingCciRepositoryScore = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(x => 
                x.PublicationStartDate>= publicationStartDate &&
                x.PublicationEndDate >= publicationEndDate, ct);
        
        
        if (existingCciRepositoryScore != null)
        {
            _logger.LogWarning(
                "[CciRepositoryScoreService: TriggerCciRepositoryScorePublishAsync] CciRepositoryScore already published for this date: {PublicationDate}",
                publicationDate);
            return true.ToOkApiResponse($"CciRepositoryScore already published for day: {publicationDate} in week: {publicationWeek}");
        }
        
        var repositories = await _repositoryContext.RepositoryRepository
            .GetQueryable()
            .AsNoTracking()
            .ToListAsync(ct);

        if (repositories.Count == 0)
        {
            _logger.LogWarning(
                "[CciRepositoryScoreService: TriggerCciRepositoryScorePublishAsync] No repositories found to publish CciRepositoryScore");
            return false.ToNotFoundApiResponse("No repositories found to publish CciRepositoryScore");
        }
        
        var cciRepositoryScores = new List<CciRepositoryScore>();

        foreach (var repo in repositories)
        {
            var metrics = await GetSonarQubeMetricsAsync(repo.SonarQubeKey, ct);
            if (metrics == null)
            {
                _logger.LogWarning(
                    "[CciRepositoryScoreService: TriggerCciRepositoryScorePublishAsync] No metrics found for repository {RepositoryId}",
                    repo.Id);
                continue;
            }
            
            await ProcessCciRepositoryScoreComputation(cciRepositoryScores,repo, metrics, publicationDate,dayPublished, publicationWeek, ct);
        }
        
        if (cciRepositoryScores.Count == 0)
        {
            _logger.LogWarning(
                "[CciRepositoryScoreService: TriggerCciRepositoryScorePublishAsync] No CciRepositoryScores found to publish");
            return false.ToFailedDependencyApiResponse("No CciRepositoryScores found to publish");
        }
        
        var savedCount = await _repositoryContext.CciRepositoryScoreRepository
            .AddRangeAsync(cciRepositoryScores, ct);

        if (savedCount <= 0)
        {
            _logger.LogError(
                "[CciRepositoryScoreService: TriggerCciRepositoryScorePublishAsync] Could not create CciRepositoryScores! Please try again, Data: {CciRepositoryScores}",
                cciRepositoryScores.Serialize());
            return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Could not create CciRepositoryScores! Please try again");
        }

        await _mainActor.Tell(new PublishCciRankingsMessage
        {
            PublicationDate = dayPublished,
        }, ActorRefs.Nobody);
        
        return true.ToOkApiResponse("CciRepositoryScore published successfully");
    }


    private async Task<bool> ProcessCciRepositoryScoreComputation(
        List<CciRepositoryScore> cciRepositoryScores,
        Repository repo, 
        SonarQubeMetricsResponse metrics,
        DateTime publicationDate,
        DateTime dayPublished,
        string publicationWeek,
        CancellationToken ct)
    {
            
            var productGroup = await _repositoryContext.GetProductGroupByRepositoryFilterAsync(repo.Id ?? string.Empty,
                repo.SonarQubeKey ?? string.Empty, repo, ct);
            
            var matchingProductTeam = productGroup?.ProductTeams.Items
                .Find(pt => pt.Repositories.Items.Exists(r =>
                    (!string.IsNullOrEmpty(repo.Id) && r.Id == repo.Id) ||
                    (!string.IsNullOrEmpty(repo.SonarQubeKey) && r.SonarQubeKey == repo.SonarQubeKey)));
            
            var bugs = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Bugs)?.Value, out var b)? b: 0m;

            var vulnerabilities = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Vulnerabilities)?.Value, out var v)? v: 0m;

            var codeSmells = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.CodeSmells)?.Value, out var c)? c: 0m;

            var cognitive = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.CognitiveComplexity)?.Value, out var cc)? cc: 0m;
            
            var nonCommentedLines = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Ncloc)?.Value, out var ncloc)? ncloc: 0m;
            
            var lines = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Lines)?.Value, out var l)? l: 0m;

            var coverage = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.Coverage)?.Value, out var co)? co: 0m;
            
            var securityRating = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.SecurityRating)?.Value, out var sr)? sr: 0m;
            
            var securityHotspots = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.SecurityHotspots)?.Value, out var sh)? sh: 0m;
            
            var reliabilityRating = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.ReliabilityRating)?.Value, out var rr)? rr: 0m;
            
            var reopenedIssues = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.ReopenedIssues)?.Value, out var ri)? ri: 0m;

            var duplications = decimal.TryParse(metrics.Component.Measures
                .FirstOrDefault(x => x.Metric == ValidationConstants.SonarMetricKeys.DuplicatedLinesDensity)?.Value, out var du)? du: 0m;

            var relativeCognitiveComplexity = (cognitive / nonCommentedLines) * 1000;

            List<decimal> scores = [bugs,vulnerabilities,codeSmells,cognitive,coverage,reliabilityRating,reopenedIssues,securityRating,duplications];
            var cciMetrics = new CciMetrics()
            {
                Bugs = bugs,
                Vulnerabilities = vulnerabilities,
                CodeSmells = codeSmells,
                CognitiveComplexity = cognitive,
                Coverage = coverage,
                SemanticScore = repo?.SemanticScoreComputation ?? 0m,
                FrameworkUpgrade = repo?.FrameworkUpgradeComputation ?? 0m,
                RelativeCognitiveComplexity = relativeCognitiveComplexity,
                DuplicatedLinesDensity = duplications,
                NonCommentedLines = ncloc,
                Lines = lines,
            };

            var cciRepositoryScore = new CciRepositoryScore()
            {
                RepositoryId = repo?.Id,
                RepositoryName = repo?.Name,
                RepositoryUrl = repo?.Url,
                RepositorySonarQubeKey = repo?.SonarQubeKey!,
                RepositoryType = repo?.Type,
                ProductTeamId = matchingProductTeam?.Id,
                ProductTeamName = matchingProductTeam?.Name,
                ProductGroupId = productGroup?.ProductGroupId,
                ProductGroupName = productGroup?.ProductGroupName,
                PublicationDate = dayPublished,
                PublicationWeek = publicationWeek,
                PublicationStartDate = publicationDate.Date.AddHours(0).AddMinutes(0).AddSeconds(0),
                PublicationEndDate = publicationDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59),
                Bugs = bugs,
                BugsComputation = CciEvaluator.EvaluateBugs(bugs),
                Vulnerabilities = vulnerabilities,
                VulnerabilitiesComputation = CciEvaluator.EvaluateVulnerabilities(vulnerabilities),
                CodeSmells = codeSmells,
                CodeSmellsComputation = CciEvaluator.EvaluateCodeSmells(codeSmells),
                SecurityRating = securityRating,
                SecurityRatingComputation = CciEvaluator.EvaluateSecurityRating(securityRating),
                SecurityHotspots = securityHotspots,
                SecurityHotspotsComputation = CciEvaluator.EvaluateSecurityHotspots(securityHotspots),
                ReliabilityRating = reliabilityRating,
                ReliabilityRatingComputation = CciEvaluator.EvaluateReliabilityRating(reliabilityRating),
                ReopenedIssues = reopenedIssues,
                ReopenedIssuesComputation = CciEvaluator.EvaluateReopenedIssues(reopenedIssues),
                Coverage = coverage,
                CoverageComputation = CciEvaluator.EvaluateCoverage(coverage),
                DuplicatedLinesDensity = duplications,
                DuplicatedLinesDensityComputation = CciEvaluator.EvaluateDuplicatedLinesDensity(duplications),
                CognitiveComplexity = cognitive,
                RelativeCognitiveComplexity = relativeCognitiveComplexity,
                CognitiveComplexityComputation = CciEvaluator.EvaluateCognitiveComplexity(cognitive),
                RelativeCognitiveComplexityComputation = CciEvaluator.EvaluateCognitiveComplexityRelative(cognitive),
                Average = scores.Sum()/scores.Count,
                FinalAverage = CciEvaluator.ComputeEngineerFinalAverage(repo?.Type!, cciMetrics),
                SemanticScoreComputation = repo?.SemanticScoreComputation,
                FrameworkUpgradeComputation = repo?.FrameworkUpgradeComputation,
                Status = repo?.Status,
                SonarQubeMetricsAcquired = true,
                NonCommentedLinesOfCode = nonCommentedLines,
                LinesOfCode = lines,
            };
            
            cciRepositoryScores.Add(cciRepositoryScore);

            return true;
    }
    
    private async Task<SonarQubeMetricsResponse?> GetSonarQubeMetricsAsync(
         string sonarQubeKey, CancellationToken ct = default)
    {
        try
        {
            _logger.LogDebug("Fetching SonarQube metrics for repository {SonarQubeKey}", sonarQubeKey);

            var url = $"{_sonarQubeConfig.Host}/api/measures/component";


            var response = await url
                .WithBasicAuth(_sonarQubeConfig.Token, "")
                .SetQueryParam("component", sonarQubeKey)
                .SetQueryParam("metricKeys",
                    "bugs,vulnerabilities,code_smells,security_hotspots,coverage,duplicated_lines_density,cognitive_complexity,reliability_rating,reopened_issues,security_rating,ncloc,lines")
                .GetJsonAsync<SonarQubeMetricsResponse>(cancellationToken: ct);

            _logger.LogDebug("Fetched SonarQube metrics for repository {RepositoryId}", sonarQubeKey);

            _logger.LogDebug("Response payload ---> {ResponsePayload}", new { response }.Serialize());

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,"Error fetching SonarQube metrics for repository {RepositoryId}",
                sonarQubeKey);
            return null;
        }

    }


    /// <summary>
    /// Asynchronously updates an existing cciRepositoryScore.
    /// </summary>
    /// <param name="id">The ID of the cciRepositoryScore to update.</param>
    /// <param name="request">The request to update the cciRepositoryScore. This includes the new details of the cciRepositoryScore.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the cciRepositoryScore was updated successfully.</returns>
    public async Task<IApiResponse<bool>> UpdateCciRepositoryScoreAsync(string id,
        UpdateCciRepositoryScoreRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService:UpdateCciRepositoryScoreAsync] Updating cciRepositoryScore with ID: {Id} and RequestPayload => {Request}",
            id, request.Serialize()
        );

        var existingCciRepositoryScore = await _repositoryContext.CciRepositoryScoreRepository.GetByIdAsync(id, ct);

        if (existingCciRepositoryScore is null)
        {
            _logger.LogWarning("cciRepositoryScore not found with ID: {Id}", id);
            return false.ToNotFoundApiResponse();
        }

        existingCciRepositoryScore.Bugs = request.Bugs ?? existingCciRepositoryScore.Bugs;
        existingCciRepositoryScore.Average = request.Average ?? existingCciRepositoryScore.Average;
        existingCciRepositoryScore.Coverage = request.Coverage ?? existingCciRepositoryScore.Coverage;
        existingCciRepositoryScore.CodeSmells = request.CodeSmells ?? existingCciRepositoryScore.CodeSmells;
        existingCciRepositoryScore.FinalAverage = request.FinalAverage ?? existingCciRepositoryScore.FinalAverage;
        existingCciRepositoryScore.RepositoryUrl = request.RepositoryUrl ?? existingCciRepositoryScore.RepositoryUrl;
        existingCciRepositoryScore.ProductTeamId = request.ProductTeamId ?? existingCciRepositoryScore.ProductTeamId;
        existingCciRepositoryScore.RepositoryName = request.RepositoryName ?? existingCciRepositoryScore.RepositoryName;
        var verifyScoreUpdateConsistencyAsyncResponse =
            await VerifyScoreUpdateConsistencyAsync(request, existingCciRepositoryScore, ct);
        if (verifyScoreUpdateConsistencyAsyncResponse is not null) return verifyScoreUpdateConsistencyAsyncResponse;
        existingCciRepositoryScore.RepositoryId = request.RepositoryId ?? existingCciRepositoryScore.RepositoryId;
        existingCciRepositoryScore.RepositoryType = request.RepositoryType ?? existingCciRepositoryScore.RepositoryType;
        existingCciRepositoryScore.SecurityRating = request.SecurityRating ?? existingCciRepositoryScore.SecurityRating;
        existingCciRepositoryScore.ReopenedIssues = request.ReopenedIssues ?? existingCciRepositoryScore.ReopenedIssues;
        existingCciRepositoryScore.ProductGroupId = request.ProductGroupId ?? existingCciRepositoryScore.ProductGroupId;
        existingCciRepositoryScore.ProductTeamName =
            request.ProductTeamName ?? existingCciRepositoryScore.ProductTeamName;
        existingCciRepositoryScore.PublicationDate =
            request.PublicationDate ?? existingCciRepositoryScore.PublicationDate;
        existingCciRepositoryScore.PublicationWeek =
            request.PublicationWeek ?? existingCciRepositoryScore.PublicationWeek;
        existingCciRepositoryScore.Vulnerabilities =
            request.Vulnerabilities ?? existingCciRepositoryScore.Vulnerabilities;
        existingCciRepositoryScore.BugsComputation =
            request.BugsComputation ?? existingCciRepositoryScore.BugsComputation;
        existingCciRepositoryScore.SemanticScoreComputation = request.SemanticScoreComputation ??
                                                              existingCciRepositoryScore.SemanticScoreComputation;
        existingCciRepositoryScore.FrameworkUpgradeComputation = request.FrameworkUpgradeComputation ??
                                                                 existingCciRepositoryScore.FrameworkUpgradeComputation;
        existingCciRepositoryScore.SecurityHotspots =
            request.SecurityHotspots ?? existingCciRepositoryScore.SecurityHotspots;
        existingCciRepositoryScore.ProductGroupName =
            request.ProductGroupName ?? existingCciRepositoryScore.ProductGroupName;
        existingCciRepositoryScore.ReliabilityRating =
            request.ReliabilityRating ?? existingCciRepositoryScore.ReliabilityRating;
        existingCciRepositoryScore.PublicationEndDate =
            request.PublicationEndDate ?? existingCciRepositoryScore.PublicationEndDate;
        existingCciRepositoryScore.CoverageComputation =
            request.CoverageComputation ?? existingCciRepositoryScore.CoverageComputation;
        existingCciRepositoryScore.CodeSmellsComputation =
            request.CodeSmellsComputation ?? existingCciRepositoryScore.CodeSmellsComputation;
        existingCciRepositoryScore.RepositorySonarQubeKey =
            request.RepositorySonarQubeKey ?? existingCciRepositoryScore.RepositorySonarQubeKey;
        existingCciRepositoryScore.DuplicatedLinesDensity =
            request.DuplicatedLinesDensity ?? existingCciRepositoryScore.DuplicatedLinesDensity;
        existingCciRepositoryScore.SonarQubeMetricsAcquired = request.SonarQubeMetricsAcquired ??
                                                              existingCciRepositoryScore.SonarQubeMetricsAcquired;
        existingCciRepositoryScore.ReopenedIssuesComputation = request.ReopenedIssuesComputation ??
                                                               existingCciRepositoryScore.ReopenedIssuesComputation;
        existingCciRepositoryScore.VulnerabilitiesComputation = request.VulnerabilitiesComputation ??
                                                                existingCciRepositoryScore.VulnerabilitiesComputation;
        existingCciRepositoryScore.SecurityHotspotsComputation = request.SecurityHotspotsComputation ??
                                                                 existingCciRepositoryScore.SecurityHotspotsComputation;
        existingCciRepositoryScore.DuplicatedLinesDensityComputation = request.DuplicatedLinesDensityComputation ??
                                                                       existingCciRepositoryScore
                                                                           .DuplicatedLinesDensityComputation;

        existingCciRepositoryScore.UpdatedAt = DateTime.UtcNow;

        _logger.LogDebug(
            "Updating cciRepositoryScore in the repository: {CciRepositoryScore}",
            existingCciRepositoryScore.Serialize()
        );

        var updated =
            await _repositoryContext.CciRepositoryScoreRepository.UpdateAsync(existingCciRepositoryScore, ct) > 0;

        return updated ? updated.ToOkApiResponse() : updated.ToNotFoundApiResponse();
    }

    /// <summary>
    /// Verifies the consistency of the score update by checking both the repository ID and publication week.
    /// </summary>
    /// <param name="request">The request containing the updated score details.</param>
    /// <param name="existingCciRepositoryScore">The existing CCI repository score to be updated.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>An API response indicating whether the score update is consistent.</returns>
    private async Task<IApiResponse<bool>?> VerifyScoreUpdateConsistencyAsync(UpdateCciRepositoryScoreRequest request,
        CciRepositoryScore existingCciRepositoryScore, CancellationToken ct)
    {
        var verifyScoreRepositoryIdUpdateAsyncResponse =
            await VerifyScoreRepositoryIdUpdateAsync(request, existingCciRepositoryScore, ct);

        var verifyScorePublicationWeekUpdateAsyncResponse =
            await VerifyScorePublicationWeekUpdateAsync(request, existingCciRepositoryScore, ct);

        return verifyScorePublicationWeekUpdateAsyncResponse ?? verifyScoreRepositoryIdUpdateAsyncResponse;
    }

    /// <summary>
    /// Verifies if the publication week update is consistent by checking for existing records with the same publication week and repository ID.
    /// </summary>
    /// <param name="request">The request containing the updated score details.</param>
    /// <param name="existingCciRepositoryScore">The existing CCI repository score to be updated.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>An API response indicating whether the publication week update is consistent.</returns>
    private async Task<IApiResponse<bool>?> VerifyScorePublicationWeekUpdateAsync(
        UpdateCciRepositoryScoreRequest request, CciRepositoryScore existingCciRepositoryScore, CancellationToken ct)
    {
        if (request.PublicationWeek is null || existingCciRepositoryScore.PublicationWeek is null ||
            !existingCciRepositoryScore.PublicationWeek.Equals(request.PublicationWeek,
                StringComparison.OrdinalIgnoreCase)) return null;

        var existingRepository = await _repositoryContext.CciRepositoryScoreRepository.GetQueryable().AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.PublicationWeek == request.PublicationWeek &&
                     x.RepositoryId == existingCciRepositoryScore.RepositoryId, ct);

        if (existingRepository != null)
        {
            _logger.LogWarning(
                "cciRepositoryScore already exists with RepositoryId: {RepositoryId} and PublicationWeek: {PublicationWeek}",
                request.RepositoryId, existingCciRepositoryScore.PublicationWeek);
            return existingRepository.Adapt<bool>().ToOkApiResponse();
        }

        existingCciRepositoryScore.PublicationWeek = request.PublicationWeek;

        return null;
    }

    /// <summary>
    /// Verifies if the repository ID update is consistent by checking for existing records with the same repository ID and publication week.
    /// </summary>
    /// <param name="request">The request containing the updated score details.</param>
    /// <param name="existingCciRepositoryScore">The existing CCI repository score to be updated.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>An API response indicating whether the repository ID update is consistent.</returns>
    private async Task<IApiResponse<bool>?> VerifyScoreRepositoryIdUpdateAsync(UpdateCciRepositoryScoreRequest request,
        CciRepositoryScore existingCciRepositoryScore, CancellationToken ct)
    {
        if (request.RepositoryId is null || existingCciRepositoryScore.RepositoryId is null ||
            !existingCciRepositoryScore.RepositoryId.Equals(request.RepositoryId, StringComparison.OrdinalIgnoreCase))
            return null;

        var existingRepository = await _repositoryContext.CciRepositoryScoreRepository.GetQueryable().AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.RepositoryId == request.RepositoryId &&
                     x.PublicationWeek == existingCciRepositoryScore.PublicationWeek, ct);

        if (existingRepository != null)
        {
            _logger.LogWarning(
                "cciRepositoryScore already exists with RepositoryId: {RepositoryId} and PublicationWeek: {PublicationWeek}",
                request.RepositoryId, existingCciRepositoryScore.PublicationWeek);
            return existingRepository.Adapt<bool>().ToOkApiResponse();
        }

        existingCciRepositoryScore.RepositoryId = request.RepositoryId;

        return null;
    }

    /// <summary>
    /// Asynchronously deletes a sGetPreviousWeekDate specific cciRepositoryScore by marking it as deleted.
    /// </summary>
    /// <param name="id">The ID of the cciRepositoryScore to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the cciRepositoryScore was marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteCciRepositoryScoreAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug("Deleting cciRepositoryScore with ID: {StockModelId}", id);

        var existingDocument = await _repositoryContext.CciRepositoryScoreRepository.GetByIdAsync(id, ct);

        if (existingDocument == null)
        {
            _logger.LogDebug("cciRepositoryScore not found with ID: {RepositoryId}", id);

            return false.ToNotFoundApiResponse();
        }

        existingDocument.IsDeleted = true;

        _logger.LogDebug("Deleting cciRepositoryScore in the repository: {Repository}", existingDocument.Serialize());

        var deletedCount = await _repositoryContext.CciRepositoryScoreRepository.DeleteAsync(existingDocument, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("cciRepositoryScore deleted: {Deleted}", deleted);

        return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes CCI scores for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI scores.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI scores were marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteCciScoresByWeekAsync(string publicationWeek,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: DeleteCciScoresByWeekAsync] Received Request to delete CciPublicationByWeek with PublicationWeek: {PublicationWeek}",
            publicationWeek);

        var existingDocuments = await _repositoryContext.CciRepositoryScoreRepository.GetQueryable().AsNoTracking()
            .Where(x => x.PublicationWeek == publicationWeek).ToListAsync(ct);

        if (existingDocuments.Count == 0)
        {
            _logger.LogDebug("cciRepositoryScore not found with PublicationWeek: {PublicationWeek}", publicationWeek);

            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        foreach (var existingDocument in existingDocuments)
        {
            existingDocument.IsDeleted = true;
        }

        _logger.LogDebug("Deleting cciRepositoryScore in the repository: {Repository}", existingDocuments.Serialize());

        var deletedCount =
            await _repositoryContext.CciRepositoryScoreRepository.DeleteRangeAsync(existingDocuments, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("cciRepositoryScore deleted: {Deleted}", deleted);

        return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes CCI publication statistics for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI publication stats.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI publication stats were marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteCciRepositoryScoreStatisticByWeekAsync(string publicationWeek,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: DeleteCciRepositoryScoreStatisticByWeekAsync] Received Request to delete CciRepositoryScoresStatistic with PublicationWeek: {PublicationWeek}",
            publicationWeek);

        var existingDocuments = await _repositoryContext.CciRepositoryScoreStatisticRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationWeek == publicationWeek).ToListAsync(ct);

        if (existingDocuments.Count == 0)
        {
            _logger.LogWarning("CciRepositoryScoreStatistic not found with PublicationWeek: {PublicationWeek}",
                publicationWeek);

            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        foreach (var existingDocument in existingDocuments)
        {
            existingDocument.IsDeleted = true;
        }

        _logger.LogDebug("Deleting CciRepositoryScoreStatistic in the repository: {Repository}", existingDocuments.Serialize());

        var deletedCount =
            await _repositoryContext.CciRepositoryScoreStatisticRepository.DeleteRangeAsync(existingDocuments, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("CciRepositoryScoreStatistic deleted: {Deleted}", deleted);

        return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes CCI publication trends for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI publication trends.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI publication trends were marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteCciPublicationTrendsByWeekAsync(string publicationWeek,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: DeleteCciPublicationTrendsByWeekAsync] Received Request to delete CciPublicationTrendsByWeek with PublicationWeek: {PublicationWeek}",
            publicationWeek);

        var existingDocuments = await _repositoryContext.CciProductTeamsScoreTrendRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationWeek == publicationWeek).ToListAsync(ct);

        if (existingDocuments.Count == 0)
        {
            _logger.LogWarning("cciProductTeamsScoreTrend not found with PublicationWeek: {PublicationWeek}",
                publicationWeek);

            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        foreach (var existingDocument in existingDocuments)
        {
            existingDocument.IsDeleted = true;
        }

        _logger.LogDebug("Deleting cciProductTeamsScoreTrend in the repository: {Repository}", existingDocuments.Serialize());

        var deletedCount =
            await _repositoryContext.CciProductTeamsScoreTrendRepository.DeleteRangeAsync(existingDocuments, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("cciProductTeamsScoreTrend deleted: {Deleted}", deleted);

        return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes CCI publication rankings for a specific publication week by marking them as deleted.
    /// </summary>
    /// <param name="publicationWeek">The publication week for which to delete the CCI publication rankings.</param>
    /// <param name="ct">A cancellation token to cancel the request.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the CCI publication rankings were marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteCciPublicationRankingByWeekAsync(string publicationWeek,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[CciRepositoryScoreService: DeleteCciPublicationRankingByWeekAsync] Received Request to delete CciPublicationRankingByWeek with PublicationWeek: {PublicationWeek}",
            publicationWeek);

        var existingDocument = await _repositoryContext.CciProductsRankingRepository.GetQueryable().AsNoTracking()
            .FirstOrDefaultAsync(x => x.PublicationWeek == publicationWeek, ct);

        if (existingDocument == null)
        {
            _logger.LogDebug("cciProductsRanking not found with PublicationWeek: {PublicationWeek}", publicationWeek);

            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingDocument.IsDeleted = true;

        _logger.LogDebug("Deleting cciProductsRanking in the repository: {Repository}", existingDocument.Serialize());

        var deletedCount = await _repositoryContext.CciProductsRankingRepository.DeleteAsync(existingDocument, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("cciProductsRanking deleted: {Deleted}", deleted);

        return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse();
    }

    /// <summary>
    /// Creates a ScoreRankingItem for a given product team based on the provided ranking items.
    /// </summary>
    /// <param name="productTeam">The product team for which the score ranking item is created.</param>
    /// <param name="rankingItems">The list of ranking items to find the score for the product team.</param>
    /// <returns>A ScoreRankingItem containing the product team's ID, name, and score.</returns>
    private static ScoreRankingItem CreateScoreRankingItem(ProductTeam productTeam, List<RankingItem> rankingItems)
    {
        var rankingItem = rankingItems.Find(x => x.ProductTeamId == productTeam.Id);
        return new ScoreRankingItem
        {
            Id = productTeam.Id,
            Name = productTeam.Name,
            Score = rankingItem?.Rating ?? 0
        };
    }

    /// <summary>
    /// Adds performance metrics for a given product team to the provided PerformanceByMetrics object.
    /// </summary>
    /// <param name="performanceByMetrics">The PerformanceByMetrics object to which the metrics are added.</param>
    /// <param name="productTeam">The product team for which the metrics are added.</param>
    /// <param name="relatedScores">The list of related CciRepositoryScores for the product team.</param>
    // Stryker disable all
    private static void AddPerformanceMetrics(PerformanceByMetrics performanceByMetrics, ProductTeam productTeam,
        List<CciRepositoryScore> relatedScores)
    {
        performanceByMetrics.Bugs.Add(CreateProductTeamMetricItem(productTeam, relatedScores.Sum(x => x.Bugs ?? 0)));
        performanceByMetrics.CodeSmells.Add(CreateProductTeamMetricItem(productTeam,
            relatedScores.Sum(x => x.CodeSmells ?? 0)));
        performanceByMetrics.Vulnerabilities.Add(CreateProductTeamMetricItem(productTeam,
            relatedScores.Sum(x => x.Vulnerabilities ?? 0)));
        performanceByMetrics.SecurityHotspots.Add(CreateProductTeamMetricItem(productTeam,
            relatedScores.Sum(x => x.SecurityHotspots ?? 0)));
        performanceByMetrics.CognitiveComplexity.Add(CreateProductTeamMetricItem(productTeam,
            relatedScores.Sum(x => x.CognitiveComplexity ?? 0)));
        performanceByMetrics.CodeCoverage.Add(CreateProductTeamMetricItem(productTeam,
            relatedScores.Count == 0 ? 0 : relatedScores.Average(x => x.Coverage ?? 0)));
        performanceByMetrics.Duplications.Add(CreateProductTeamMetricItem(productTeam,
            relatedScores.Count == 0 ? 0 : relatedScores.Average(x => x.DuplicatedLinesDensity ?? 0)));
    }
    // Stryker restore all

    /// <summary>
    /// Creates a ProductTeamMetricItem for a given product team with the specified score.
    /// </summary>
    /// <param name="productTeam">The product team for which the metric item is created.</param>
    /// <param name="score">The score to be assigned to the metric item.</param>
    /// <returns>A ProductTeamMetricItem containing the product team's ID, name, and score.</returns>
    private static ProductTeamMetricItem CreateProductTeamMetricItem(ProductTeam productTeam, decimal score)
    {
        return new ProductTeamMetricItem
        {
            Id = productTeam.Id,
            Name = productTeam.Name,
            Score = score
        };
    }

    /// <summary>
    /// Creates an OverviewByScope object based on the provided repository scores and score ranking.
    /// </summary>
    /// <param name="repositoryScores">The list of repository scores to be used for creating the overview.</param>
    /// <param name="ranking">The score ranking to be used for creating the overview.</param>
    /// <returns>An OverviewByScope object containing the overview by scope for backend, frontend, and overall.</returns>
    private static OverviewByScope CreateOverviewByScope(List<CciRepositoryScore> repositoryScores,
        ScoreRanking ranking)
    {
        return new OverviewByScope
        {
            Backend = CreateOverviewByScopeItem(repositoryScores, ranking.Backend,
                ValidationConstants.RepositoryType.Backend),
            Frontend = CreateOverviewByScopeItem(repositoryScores, ranking.Frontend,
                ValidationConstants.RepositoryType.Frontend),
            Overall = CreateOverviewByScopeItem(repositoryScores, ranking.Overall, null)
        };
    }

    /// <summary>
    /// Creates an OverviewByScopeItem for a given repository type based on the provided repository scores and ranking items.
    /// </summary>
    /// <param name="repositoryScores">The list of repository scores to be used for creating the overview item.</param>
    /// <param name="rankingItems">The list of ranking items to be used for creating the overview item.</param>
    /// <param name="repositoryType">The type of repository (backend, frontend, or null for overall).</param>
    /// <returns>An OverviewByScopeItem containing the overview metrics for the specified repository type.</returns>
    // Stryker disable all
    private static OverviewByScopeItem CreateOverviewByScopeItem(List<CciRepositoryScore> repositoryScores,
        List<ScoreRankingItem> rankingItems, string? repositoryType)
    {
        var filteredScores = repositoryType == null
            ? repositoryScores
            : repositoryScores.Where(x => x.RepositoryType == repositoryType).ToList();
        return new OverviewByScopeItem
        {
            Repositories = filteredScores.Count,
            TotalBugs = rankingItems.Sum(x => x.Bugs),
            TotalCodeSmells = rankingItems.Sum(x => x.CodeSmells),
            TotalVulnerabilities = rankingItems.Sum(x => x.Vulnerabilities),
            TotalSecurityHotspots = rankingItems.Sum(x => x.SecurityHotspots),
            TotalCognitiveComplexity = rankingItems.Sum(x => x.CognitiveComplexity),
            TotalRelativeCognitiveComplexity = rankingItems.Sum(x => x.RelativeCognitiveComplexity),
            TotalLinesOfCode = rankingItems.Sum(x => x.LinesOfCode),
            TotalNonCommentedLinesOfCode = rankingItems.Sum(x => x.NonCommentedLinesOfCode),
            AverageCodeCoverage = rankingItems.Count == 0 ? 0 : rankingItems.Average(x => x.AverageCodeCoverage),
            AverageDuplications = rankingItems.Count == 0 ? 0 : rankingItems.Average(x => x.AverageDuplications)
        };
    }
    // Stryker restore all


    /// <summary>
    /// Computes statistics for each product team based on the provided message, product teams, and repository scores.
    /// </summary>
    /// <param name="message">The message containing the CCI products ranking information.</param>
    /// <param name="productTeams">The list of product teams to compute statistics for.</param>
    /// <param name="repositoryScores">The list of repository scores to be used for computing statistics.</param>
    /// <param name="statistics">The statistics object to be updated with the computed values.</param>
    // Stryker disable all
    private static void ComputeStatisticsOnProductTeams(CurateWeeklyStatisticsMessage message,
        List<ProductTeam> productTeams,
        List<CciRepositoryScore> repositoryScores, CciRepositoryScoreStatistic statistics)
    {
        foreach (var productTeam in productTeams)
        {
            var repositoryIds = productTeam.Repositories.Items.Select(z => z.SonarQubeKey).ToList();
            var relatedScores = repositoryScores.Where(x => repositoryIds.Contains(x.RepositorySonarQubeKey)).ToList();

            if (relatedScores.Count == 0) continue;

            var backendRelatedScores = relatedScores
                .Where(x => x.RepositoryType == ValidationConstants.RepositoryType.Backend).ToList();
            var frontendRelatedScores = relatedScores
                .Where(x => x.RepositoryType == ValidationConstants.RepositoryType.Frontend).ToList();

            var backendScoreItem = CreateScoreRankingItem(productTeam, message.Ranking.Rankings.Backend);
            backendScoreItem.Bugs = backendRelatedScores.Sum(x => x.Bugs ?? 0);
            backendScoreItem.CodeSmells = backendRelatedScores.Sum(x => x.CodeSmells ?? 0);
            backendScoreItem.Vulnerabilities = backendRelatedScores.Sum(x => x.Vulnerabilities ?? 0);
            backendScoreItem.SecurityHotspots = backendRelatedScores.Sum(x => x.SecurityHotspots ?? 0);
            backendScoreItem.CognitiveComplexity = backendRelatedScores.Sum(x => x.CognitiveComplexity ?? 0);
            backendScoreItem.RelativeCognitiveComplexity =
                backendRelatedScores.Sum(x => x.RelativeCognitiveComplexity ?? 0);
            backendScoreItem.NonCommentedLinesOfCode =
                backendRelatedScores.Sum(x => x.NonCommentedLinesOfCode ?? 0);
            backendScoreItem.LinesOfCode = backendRelatedScores.Sum(x => x.LinesOfCode ?? 0);
            backendScoreItem.AverageDuplications = backendRelatedScores.Count > 0
                ? backendRelatedScores.Average(x => x.DuplicatedLinesDensity ?? 0)
                : 0;
            backendScoreItem.AverageCodeCoverage = backendRelatedScores.Count > 0
                ? backendRelatedScores.Average(x => x.Coverage ?? 0)
                : 0;

            var frontendScoreItem = CreateScoreRankingItem(productTeam, message.Ranking.Rankings.Frontend);
            frontendScoreItem.Bugs = frontendRelatedScores.Sum(x => x.Bugs ?? 0);
            frontendScoreItem.CodeSmells = frontendRelatedScores.Sum(x => x.CodeSmells ?? 0);
            frontendScoreItem.Vulnerabilities = frontendRelatedScores.Sum(x => x.Vulnerabilities ?? 0);
            frontendScoreItem.SecurityHotspots = frontendRelatedScores.Sum(x => x.SecurityHotspots ?? 0);
            frontendScoreItem.CognitiveComplexity = frontendRelatedScores.Sum(x => x.CognitiveComplexity ?? 0);
            frontendScoreItem.RelativeCognitiveComplexity =
                frontendRelatedScores.Sum(x => x.RelativeCognitiveComplexity ?? 0);
            frontendScoreItem.NonCommentedLinesOfCode =  
                frontendRelatedScores.Sum(x => x.NonCommentedLinesOfCode ?? 0);
            frontendScoreItem.LinesOfCode = frontendRelatedScores.Sum(x => x.LinesOfCode ?? 0);
            frontendScoreItem.AverageDuplications = frontendRelatedScores.Count == 0
                ? 0
                : frontendRelatedScores.Average(x => x.DuplicatedLinesDensity ?? 0);
            frontendScoreItem.AverageCodeCoverage = frontendRelatedScores.Count == 0
                ? 0
                : frontendRelatedScores.Average(x => x.Coverage ?? 0);

            var overallScoreItem = CreateScoreRankingItem(productTeam, message.Ranking.Rankings.Overall);
            overallScoreItem.Bugs = relatedScores.Sum(x => x.Bugs ?? 0);
            overallScoreItem.CodeSmells = relatedScores.Sum(x => x.CodeSmells ?? 0);
            overallScoreItem.Vulnerabilities = relatedScores.Sum(x => x.Vulnerabilities ?? 0);
            overallScoreItem.SecurityHotspots = relatedScores.Sum(x => x.SecurityHotspots ?? 0);
            overallScoreItem.CognitiveComplexity = relatedScores.Sum(x => x.CognitiveComplexity ?? 0);
            overallScoreItem.RelativeCognitiveComplexity =
                relatedScores.Sum(x => x.RelativeCognitiveComplexity ?? 0);
            overallScoreItem.NonCommentedLinesOfCode =  
                relatedScores.Sum(x => x.NonCommentedLinesOfCode ?? 0);
            overallScoreItem.LinesOfCode = relatedScores.Sum(x => x.LinesOfCode ?? 0);
            overallScoreItem.AverageDuplications = relatedScores.Average(x => x.DuplicatedLinesDensity ?? 0);
            overallScoreItem.AverageCodeCoverage = relatedScores.Average(x => x.Coverage ?? 0);

            AddPerformanceMetrics(statistics.ScoreStatistic.PerformanceByMetrics, productTeam, relatedScores);

            statistics.ScoreStatistic.Ranking.Backend.Add(backendScoreItem);
            statistics.ScoreStatistic.Ranking.Frontend.Add(frontendScoreItem);
            statistics.ScoreStatistic.Ranking.Overall.Add(overallScoreItem);
        }
    }
    // Stryker restore all
    
    
    /// <summary>
    /// Computes the issue distribution for backend, frontend, and overall scores.
    /// </summary>
    /// <param name="repositoryScores">The list of repository scores.</param>
    /// <returns>The computed IssueDistribution object.</returns>
    // Stryker disable all
    private static IssueDistribution ComputeIssueDistribution(List<CciRepositoryScore> repositoryScores)
    {
        var issueDistribution = new IssueDistribution();

        var backendScores = repositoryScores.Where(x => x.RepositoryType == "Backend").ToList();
        if (backendScores.Count != 0)
        {
            issueDistribution.Backend = new IssueDistributionItem
            {
                Bugs = backendScores.Sum(x => x.Bugs ?? 0),
                CodeSmells = backendScores.Sum(x => x.CodeSmells ?? 0),
                Vulnerabilities = backendScores.Sum(x => x.Vulnerabilities ?? 0),
                SecurityHotspots = backendScores.Sum(x => x.SecurityHotspots ?? 0)
            };
        }

        var frontendScores = repositoryScores.Where(x => x.RepositoryType == "Frontend").ToList();
        if (frontendScores.Count != 0)
        {
            issueDistribution.Frontend = new IssueDistributionItem
            {
                Bugs = frontendScores.Sum(x => x.Bugs ?? 0),
                CodeSmells = frontendScores.Sum(x => x.CodeSmells ?? 0),
                Vulnerabilities = frontendScores.Sum(x => x.Vulnerabilities ?? 0),
                SecurityHotspots = frontendScores.Sum(x => x.SecurityHotspots ?? 0)
            };
        }

        if (repositoryScores.Count != 0)
        {
            issueDistribution.Overall = new IssueDistributionItem
            {
                Bugs = repositoryScores.Sum(x => x.Bugs ?? 0),
                CodeSmells = repositoryScores.Sum(x => x.CodeSmells ?? 0),
                Vulnerabilities = repositoryScores.Sum(x => x.Vulnerabilities ?? 0),
                SecurityHotspots = repositoryScores.Sum(x => x.SecurityHotspots ?? 0)
            };
        }

        return issueDistribution;
    }
    // Stryker restore all


    /// <summary>
    /// Computes the weekly final average trends.
    /// </summary>
    /// <param name="recordedRankings">The list of repository scores.</param>
    /// <param name="isMonthly">A boolean value indicating whether the trends are monthly.</param>
    /// <returns>A WeeklyFinalAverageTrend object.</returns>
    private WeeklyFinalAverageTrend ComputeWeeklyFinalAverageTrendV2(List<CciProductsRanking> recordedRankings, bool isMonthly = false)
    {
        _logger.LogDebug("[ComputeWeeklyFinalAverageTrendV2] Received {Count} recorded rankings, Is monthly: {IsMonthly}",
            recordedRankings.Count, isMonthly);
        var weeklyFinalAverageTrend = new WeeklyFinalAverageTrend();
        
        if (recordedRankings.Count == 0)
        {
            return weeklyFinalAverageTrend;
        }

        foreach (var rankings in recordedRankings)
        {
            var scores = rankings.Rankings;
            var backend = scores.Backend;
            var frontend = scores.Frontend;
            
            var publicationDate = rankings.PublicationEndDate!.Value.Date;

            
            var frontendScores = frontend.Count == 0 ? 0 : frontend.Average(x=> x.Rating ?? 0);

            weeklyFinalAverageTrend.Frontend.Add(new WeeklyFinalAverageTrendItem
            {
                Date = publicationDate,
                AverageScore = Math.Round(frontendScores, 2)
            });
            

            var backendScores = backend.Count == 0 ? 0 : backend.Average(x=> x.Rating ?? 0);

            weeklyFinalAverageTrend.Backend.Add(new WeeklyFinalAverageTrendItem
            {
                Date = publicationDate,
                AverageScore = Math.Round(backendScores, 2)
            });
            
            weeklyFinalAverageTrend.Overall.Add(new WeeklyFinalAverageTrendItem
            {
                Date = publicationDate,
                AverageScore = Math.Round(rankings.CurrentScore ?? 0, 2)
            });
        }

        return weeklyFinalAverageTrend;
    }


    private async Task<List<CciProductsRanking>> GetMonthlyRecordedRankings(
        DateTime monthsAgo)
    {

        var monthlyRecordedRankings = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate != null && x.PublicationStartDate >= monthsAgo)
            .OrderByDescending(x => x.PublicationStartDate)
            .ToListAsync();

        var monthlyFinalRankings = monthlyRecordedRankings
            .GroupBy(x => new { x.PublicationStartDate!.Value.Year, x.PublicationStartDate.Value.Month })
            .Select(g => g.OrderByDescending(x => x.PublicationStartDate).First())
            .ToList();
        
        return monthlyFinalRankings;
    }
}