using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.RepositoryService;
using Hubtel.CCI.Api.Dtos.Responses.RepositoryService;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class ArsenalServiceService : IArsenalServiceService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger<ArsenalServiceService> _logger;

    public ArsenalServiceService(IRepositoryContext repositoryContext, ILogger<ArsenalServiceService> logger)
    {
        _repositoryContext = repositoryContext;
        _logger = logger;
    }

    public async Task<IApiResponse<PagedResult<GetRepositoryServiceResponse>>> GetRepositoryServicesAsync(RepositoryServiceSearchFilter filter,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryServiceService::GetRepositoryServicesAsync] Received Request to get RepositoryServices => RequestPayload => {Filter}",
            filter.Serialize());

        var query = _repositoryContext.ServiceRepository.GetQueryable().AsNoTracking();

        if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            query = query.Where(c => c.Name.ToLower().Contains(filter.SearchTerm.ToLower()));

        if (filter.RepositoryIds is { Count: > 0 })
            query = query.Where(c => filter.RepositoryIds.Contains(c.RepositoryId));
        
        if (!string.IsNullOrWhiteSpace(filter.Status))
            query = query.Where(c => c.Status != null && c.Status.ToLower().Contains(filter.Status.ToLower()));
        
        if (!string.IsNullOrWhiteSpace(filter.RepositoryId))
            query = query.Where(c => c.RepositoryId == filter.RepositoryId);
        
        if (!string.IsNullOrWhiteSpace(filter.RepositoryType))
            query = query.Where(c => c.RepositoryType == filter.RepositoryType);

        var pagedResult = await query.OrderByDescending(x => x.CreatedAt)
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => p.Adapt<GetRepositoryServiceResponse>()).ToList();

        var response = new PagedResult<GetRepositoryServiceResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<GetRepositoryServiceResponse>> GetRepositoryServiceAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryServiceService::GetRepositoryServiceAsync] Received Request to get RepositoryService => RequestPayload => {Id}",
            id);

        var service = await _repositoryContext.ServiceRepository.GetByIdAsync(id, ct);

        return service == null ? ApiResponse<GetRepositoryServiceResponse>.Default.ToNotFoundApiResponse() : service.Adapt<GetRepositoryServiceResponse>().ToOkApiResponse();
    }

    public async Task<IApiResponse<GetRepositoryServiceResponse>> AddRepositoryServiceAsync(
        CreateRepositoryServiceRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryServiceService::AddRepositoryServiceAsync] Received Request to add RepositoryService => RequestPayload => {Request}",
            request.Serialize());
        
        var repository = await _repositoryContext.RepositoryRepository.GetByIdAsync(request.RepositoryId, ct);

        if (repository == null)
            return ApiResponse<GetRepositoryServiceResponse>.Default.ToBadRequestApiResponse(
                "Repository does not exist"
            );

        var existingService = await _repositoryContext.ServiceRepository
            .FindOneAsync(c => c.RepositoryId == request.RepositoryId && c.ServiceReleaseDefinitionId == request.ServiceReleaseDefinitionId && c.Name == request.Name, ct);

        if (existingService != null)
            return ApiResponse<GetRepositoryServiceResponse>.Default.ToBadRequestApiResponse(
                "Service with the same name already exists"
            );

        var service = request.Adapt<Service>();
        service.RepositoryId = request.RepositoryId;
        service.RepositoryName = repository.Name;
        service.RepositoryType = repository.Type;
        service.Status = ValidationConstants.RepositoryServiceStatus.Active;
        
        service.CreatedAt = DateTime.UtcNow;
        service.UpdatedAt = DateTime.UtcNow;

        var savedCount = await _repositoryContext.ServiceRepository.AddAsync(service, ct);

        if (savedCount <= 0)
            return ApiResponse<GetRepositoryServiceResponse>.Default.ToFailedDependencyApiResponse(
                "Could not create Service! Please try again"
            );

        return service.Adapt<GetRepositoryServiceResponse>().ToCreatedApiResponse();
    }
    public async Task<IApiResponse<bool>> DeleteRepositoryServiceAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryServiceService::DeleteRepositoryServiceAsync] Received Request to delete RepositoryService => RequestPayload => {Id}",
            id);

        var service = await _repositoryContext.ServiceRepository.GetByIdAsync(id, ct);

        if (service == null)
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();

        var deleted = await _repositoryContext.ServiceRepository.DeleteAsync(service, ct);

        if (deleted < 0)
            return ApiResponse<bool>.Default.ToFailedDependencyApiResponse(
                "Could not delete Service! Please try again"
            );

        return true.ToOkApiResponse();
    }
    
        public async Task<IApiResponse<bool>> UpdateRepositoryServiceAsync(
        string id, UpdateRepositoryServiceRequest request, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryServiceService::UpdateRepositoryServiceAsync] Received Request to update RepositoryService => RequestPayload => {Request}",
            request.Serialize());

        var service = await _repositoryContext.ServiceRepository.GetByIdAsync(id, ct);

        if (service == null)
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();

        if (!string.IsNullOrEmpty(request.Name) && request.Name != service.Name)
        {
            var existingService = await _repositoryContext.ServiceRepository
                .FindOneAsync(c => c.Name == request.Name && c.Id != id, ct);

            if (existingService != null)
                return ApiResponse<bool>.Default.ToBadRequestApiResponse(
                    "Service with the same name already exists"
                );
        }

        UpdatePartially(request, service);
        
        service.UpdatedAt = DateTime.UtcNow;

        var updated = await _repositoryContext.ServiceRepository.UpdateAsync(service, ct);

        if (updated < 0)
            return ApiResponse<bool>.Default.ToFailedDependencyApiResponse(
                "Could not update Service! Please try again"
            );

        return true.ToOkApiResponse();
    }

    private static void UpdatePartially(UpdateRepositoryServiceRequest request, Service service)
    {
        UpdatePartially1(request, service);
        UpdatePartially2(request, service);
    }

    private static void UpdatePartially2(UpdateRepositoryServiceRequest request, Service service)
    {
        if (request.ServiceSendsMoney is not null ) service.ServiceSendsMoney = request.ServiceSendsMoney.Value; 
        if (request.ServiceReceivesMoney != null) service.ServiceReceivesMoney = request.ServiceReceivesMoney.Value;
        if (request.EndUsersTypes != null) service.EndUsersTypes = request.EndUsersTypes;
        if (request.HasMoreThanThousandEndUsers != null) service.HasMoreThanThousandEndUsers = request.HasMoreThanThousandEndUsers;
        if (request.CanMisrepresentData != null) service.CanMisrepresentData = request.CanMisrepresentData;
        if (request.ServiceInMarket != null) service.ServiceInMarket = request.ServiceInMarket;
        if (request.WillAffectOtherServices != null) service.WillAffectOtherServices = request.WillAffectOtherServices;
        if (request.DownstreamServicesCount != null) service.DownstreamServicesCount = request.DownstreamServicesCount.Value;
        if (request.ReceiveMerComponent != null) service.ReceiveMerComponent = request.ReceiveMerComponent.Value;
        UpdatePartially21(request, service);
    }

    private static void UpdatePartially21(UpdateRepositoryServiceRequest request, Service service)
    {
        if (request.SendsMerComponent != null) service.SendsMerComponent = request.SendsMerComponent.Value;
        if (!string.IsNullOrEmpty(request.ReceiveMerExposure)) service.ReceiveMerExposure = request.ReceiveMerExposure;
        if (!string.IsNullOrEmpty(request.SendsMerExposure)) service.SendsMerExposure = request.SendsMerExposure;
        if (!string.IsNullOrEmpty(request.PossibilityOfDataBreach)) service.PossibilityOfDataBreach = request.PossibilityOfDataBreach;
        if (request.TotalRiskScore != null) service.TotalRiskScore = request.TotalRiskScore;
        if (request.Status != null) service.Status = request.Status;
        UpdatePartially211(request, service);
    }

    private static void UpdatePartially211(UpdateRepositoryServiceRequest request, Service service)
    {
        if (!string.IsNullOrEmpty(request.RiskClassification)) service.RiskClassification = request.RiskClassification;
        if (!string.IsNullOrEmpty(request.DeploymentStrategy)) service.DeploymentStrategy = request.DeploymentStrategy;
        if (!string.IsNullOrEmpty(request.EngineerCompetenceRequirement)) service.EngineerCompetenceRequirement = request.EngineerCompetenceRequirement;
        if (!string.IsNullOrEmpty(request.CodeReviewRequirement)) service.CodeReviewRequirement = request.CodeReviewRequirement;
        if (!string.IsNullOrEmpty(request.ServiceReleaseUrl)) service.ServiceReleaseUrl = request.ServiceReleaseUrl;
        if (!string.IsNullOrEmpty(request.ServiceReleaseDefinitionId)) service.ServiceReleaseDefinitionId = request.ServiceReleaseDefinitionId;
    }

    private static void UpdatePartially1(UpdateRepositoryServiceRequest request, Service service)
    {
        if (!string.IsNullOrEmpty(request.Name)) service.Name = request.Name;
        if (!string.IsNullOrEmpty(request.Description)) service.Description = request.Description;
        if (!string.IsNullOrEmpty(request.RepositoryId)) service.RepositoryId = request.RepositoryId;
        if (!string.IsNullOrEmpty(request.RepositoryName)) service.RepositoryName = request.RepositoryName;
        if (!string.IsNullOrEmpty(request.TagName)) service.TagName = request.TagName;
        if (!string.IsNullOrEmpty(request.ServiceType)) service.ServiceType = request.ServiceType;
        if (!string.IsNullOrEmpty(request.RepositoryType)) service.RepositoryType = request.RepositoryType;
        UpdatePartially11(request, service);
    }

    private static void UpdatePartially11(UpdateRepositoryServiceRequest request, Service service)
    {
        if (!string.IsNullOrEmpty(request.EngineerResponsibleId)) service.EngineerResponsibleId = request.EngineerResponsibleId;
        if (!string.IsNullOrEmpty(request.EngineerResponsibleName)) service.EngineerResponsibleName = request.EngineerResponsibleName;
        if (!string.IsNullOrEmpty(request.EngineerAccountableId)) service.EngineerAccountableId = request.EngineerAccountableId;
        if (!string.IsNullOrEmpty(request.EngineerAccountableName)) service.EngineerAccountableName = request.EngineerAccountableName;
        if (!string.IsNullOrEmpty(request.ServiceFacingDirection)) service.ServiceFacingDirection = request.ServiceFacingDirection;
        if (!string.IsNullOrEmpty(request.WhoToConsult)) service.WhoToConsult = request.WhoToConsult;
        if (!string.IsNullOrEmpty(request.WhoToInform)) service.WhoToInform = request.WhoToInform;
        if (!string.IsNullOrEmpty(request.CdSetupStatus)) service.CdSetupStatus = request.CdSetupStatus;
    }

}