using System.Globalization;
using System.Net;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Flurl;
using Flurl.Http;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Models.Azure;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

namespace Hubtel.CCI.Api.Services.Providers;

public partial class AzureProxyService : IAzureProxyService
{
    private readonly AzureOrgConfig _azureConfig;
    private readonly ILogger<AzureProxyService> _logger;
    private readonly IMemoryCache _cache;
    private readonly string _base64Pat;
    private readonly string _workItemToken;
    
    private static readonly TimeSpan RepositoryCacheExpiry = TimeSpan.FromHours(4);
    private static readonly TimeSpan PullRequestCacheExpiry = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan WorkItemCacheExpiry = TimeSpan.FromMinutes(30);
    private static readonly TimeSpan BuildCacheExpiry = TimeSpan.FromMinutes(20);
    private static readonly TimeSpan ReleaseCacheExpiry = TimeSpan.FromMinutes(10);
    
    private static readonly Regex AzureUrlRegex = AzureRepositoryUrlRegex();

    public AzureProxyService(
        IOptions<AzureOrgConfig> azureConfig,
        ILogger<AzureProxyService> logger,
        IMemoryCache cache)
    {
        _azureConfig = azureConfig.Value;
        _logger = logger;
        _cache = cache;
        _base64Pat = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{_azureConfig.AccessToken}"));
        _workItemToken = _azureConfig.WorkItemToken;
    }

    public async Task<IApiResponse<AzureRepositoryResponse>> GetRepositoryDetailAsync(
        string project, 
        string repositoryName, 
        CancellationToken ct = default)
    {
        var cacheKey = $"repo:{project}:{repositoryName}";
        
        if (_cache.TryGetValue(cacheKey, out AzureRepositoryResponse? cachedRepo) && cachedRepo != null)
        {
            return cachedRepo.ToOkApiResponse();
        }

        try
        {
            var url = $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryName}";
            
            var response = await url
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureRepositoryResponse>(cancellationToken: ct);

            _cache.Set(cacheKey, response, RepositoryCacheExpiry);
            
            return response.ToOkApiResponse();
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to get repository ID for {Repository} in {Project}: {Error}", 
                repositoryName, project, errorMsg);
            return ApiResponse<AzureRepositoryResponse>.Default.ToNotFoundApiResponse<AzureRepositoryResponse>();
        }
    }

    public async IAsyncEnumerable<AzurePullRequestInfo> GetPullRequestsAsync(
        string project,
        string repositoryId,
        string status = "completed",
        [EnumeratorCancellation] CancellationToken ct = default)
    {
        var cacheKey = $"prs:{project}:{repositoryId}:{status}";

        if (_cache.TryGetValue(cacheKey, out List<AzurePullRequestInfo>? cachedPrs) && cachedPrs != null)
        {
            foreach (var pr in cachedPrs)
                yield return pr;
            yield break;
        }

        var allPrs = new List<AzurePullRequestInfo>();
        string? continuationToken = null;

        do
        {
            var (batch, nextToken, errorOccurred) = await FetchPullRequestBatchAsync(
                project, repositoryId, status, continuationToken, ct);

            if (errorOccurred)
                yield break;

            foreach (var pr in batch)
                yield return pr;

            allPrs.AddRange(batch);
            continuationToken = nextToken;

        } while (!string.IsNullOrEmpty(continuationToken));

        if (allPrs.Count > 0)
            _cache.Set(cacheKey, allPrs, PullRequestCacheExpiry);
    }

    private async Task<(List<AzurePullRequestInfo> Batch, string? NextToken, bool ErrorOccurred)> FetchPullRequestBatchAsync(
        string project,
        string repositoryId,
        string status,
        string? continuationToken,
        CancellationToken ct)
    {
        try
        {
            var request = BuildPullRequestUrl(project, repositoryId, status, continuationToken);
            var response = await request
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .SendAsync(HttpMethod.Get, cancellationToken: ct);

            var prList = await response.GetJsonAsync<AzureListResponse<AzurePullRequestInfo>>();
            response.Headers.TryGetFirst("x-ms-continuationtoken", out string? nextToken);

            var batch = prList?.Value?.Where(pr => pr != null).Cast<AzurePullRequestInfo>().ToList() ?? new();
            return (batch, nextToken, false);
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to fetch PRs for {Repository}: {Error}", repositoryId, errorMsg);
            return (new List<AzurePullRequestInfo>(), null, true);
        }
    }

    private Url BuildPullRequestUrl(
        string project,
        string repositoryId,
        string status,
        string? continuationToken)
    {
        var url = $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryId}/pullrequests"
            .SetQueryParam("searchCriteria.status", status)
            .SetQueryParam("$top", _azureConfig.DefaultPageSize)
            .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, "7.1");

        if (!string.IsNullOrEmpty(continuationToken))
            url = url.SetQueryParam("continuationToken", continuationToken);

        return url;
    }

    public async Task<IApiResponse<List<AzureWorkItemListItemInfo>>> GetWorkItemsAsync(
        string project, 
        string repositoryId, 
        int pullRequestId, 
        CancellationToken ct = default)
    {
        var cacheKey = $"workitems:{project}:{repositoryId}:{pullRequestId}";
        
        if (_cache.TryGetValue(cacheKey, out List<AzureWorkItemListItemInfo>? cachedItems) && cachedItems != null)
        {
            return cachedItems.ToOkApiResponse();
        }

        try
        {
            var url = $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/git/repositories/{repositoryId}/pullRequests/{pullRequestId}/workitems";
            
            var response = await url
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureListResponse<AzureWorkItemListItemInfo>>(cancellationToken: ct);
            
            var workItems = response.Value;
            
            if (workItems == null)
                return new List<AzureWorkItemListItemInfo>().ToOkApiResponse();
            
            _cache.Set(cacheKey, workItems, WorkItemCacheExpiry);
            
            return workItems.ToOkApiResponse()!;
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to get work items for PR {PullRequestId}: {Error}", pullRequestId, errorMsg);
            return new List<AzureWorkItemListItemInfo>().ToFailedDependencyApiResponse(errorMsg);
        }
    }

    public async Task<IApiResponse<List<AzureBuildChangeListItemInfo>>> GetBuildChangesAsync(
        string project, 
        string buildId, 
        CancellationToken ct = default)
    {
        var cacheKey = $"buildchanges:{project}:{buildId}";
        
        if (_cache.TryGetValue(cacheKey, out List<AzureBuildChangeListItemInfo>? cachedChanges) && cachedChanges != null)
        {
            return cachedChanges.ToOkApiResponse();
        }

        try
        {
            var url = $"{_azureConfig.DevUrl}/{_azureConfig.Organization}/{project}/_apis/build/builds/{buildId}/changes";
            
            var response = await url
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureListResponse<AzureBuildChangeListItemInfo>>(cancellationToken: ct);

            var changes = response.Value;
            
            if (changes == null)
                return new List<AzureBuildChangeListItemInfo>().ToOkApiResponse();
            
            _cache.Set(cacheKey, changes, BuildCacheExpiry);
            
            return changes.ToOkApiResponse()!;
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to get build changes for {BuildId}: {Error}", buildId, errorMsg);
            return new List<AzureBuildChangeListItemInfo>().ToFailedDependencyApiResponse<List<AzureBuildChangeListItemInfo>>(errorMsg);
        }
    }

    public async Task<IApiResponse<List<WorkItemQueryResult>>> QueryWorkItemsAsync(
        string project, 
        string wiqlQuery, 
        CancellationToken ct = default)
    {
        var queryHash = ComputeHashKey(project, wiqlQuery.GetHashCode());
        var cacheKey = $"wiql:{queryHash}";
        
        if (_cache.TryGetValue(cacheKey, out List<WorkItemQueryResult>? cachedResults) && cachedResults != null)
        {
            return cachedResults.ToOkApiResponse();
        }

        try
        {
            var url = $"{_azureConfig.DevUrl}/{project}/_apis/wit/wiql";
            var queryPayload = new { query = wiqlQuery };
            
            var response = await url
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_workItemToken}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .PostJsonAsync(queryPayload, cancellationToken: ct)
                .ReceiveJson<AzureWorkItemQuery>();

            var workItemIds = (response.WorkItems ?? [])
                .Select(wi => wi.Id)
                .Where(id => id > 0)
                .ToList();
            
            if (workItemIds.Count == 0)
            {
                var emptyResult = new List<WorkItemQueryResult>();
                _cache.Set(cacheKey, emptyResult, WorkItemCacheExpiry);
                return emptyResult.ToOkApiResponse();
            }

            var itemsUrl = $"{_azureConfig.DevUrl}/{project}/_apis/wit/workitems";
            var detailedResponse = await itemsUrl
                .SetQueryParam("ids", string.Join(",", workItemIds.Take(200)))
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_workItemToken}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureListResponse<AzureWorkItemDetailsListItemInfo>>(cancellationToken: ct);

            var results = (detailedResponse.Value ?? [])
                .Where(wi => wi != null)
                .Select(wi => new WorkItemQueryResult
                {
                    Id = wi?.Id,
                    Title = wi?.Fields?.SystemTitle?.ToString(),
                    Severity = wi?.Fields?.VstsCommonPriority?.ToString(),
                    CreatedDate = DateTime.TryParse(
                        wi?.Fields?.SystemCreatedDate?.ToString(),
                        CultureInfo.InvariantCulture,
                        DateTimeStyles.None,
                        out var date)
                        ? date
                        : DateTime.MinValue
                })
                .ToList();

            _cache.Set(cacheKey, results, WorkItemCacheExpiry);
            return results.ToOkApiResponse();
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to query work items: {Error}", errorMsg);
            return new List<WorkItemQueryResult>().ToFailedDependencyApiResponse<List<WorkItemQueryResult>>(errorMsg);
        }
    }

    public async Task<IApiResponse<decimal>> CalculatePullRequestAnalysisScoreAsync(
        string project, 
        string repositoryId, 
        List<string> commitIds, 
        CancellationToken ct = default)
    {
        var sortedCommits = string.Join(",", commitIds.OrderBy(x => x));
        var cacheKey = $"pranalysis:{project}:{repositoryId}:{ComputeHashKey(project, sortedCommits.GetHashCode())}";
        
        if (_cache.TryGetValue(cacheKey, out decimal cachedScore))
        {
            return cachedScore.ToOkApiResponse();
        }

        try
        {
            var totalPrs = 0;
            var reviewedCount = 0;
            var linkedWorkCount = 0;
            var testFilesCount = 0;

            var commitIdSet = commitIds.ToHashSet(StringComparer.OrdinalIgnoreCase);

            await foreach (var pr in GetPullRequestsAsync(project, repositoryId, "completed", ct))
            {
                var lastMergeCommit = pr.LastMergeCommit?.CommitId;
                if (string.IsNullOrEmpty(lastMergeCommit)) continue;
                
                var targetRefName = pr.TargetRefName ?? string.Empty;
                if (!commitIdSet.Contains(lastMergeCommit) || 
                    (!targetRefName.EndsWith("/main", StringComparison.OrdinalIgnoreCase) && 
                     !targetRefName.EndsWith("/master", StringComparison.OrdinalIgnoreCase)))
                    continue;

                totalPrs++;

                var approvedCount = (pr.Reviewers ?? []).Count(r => r.Vote > 0);
                if (approvedCount >= 2)
                    reviewedCount++;

                var workItemsResponse = await GetWorkItemsAsync(project, repositoryId, pr.PullRequestId ?? 0, ct);
                if (workItemsResponse.Code == 200 && (workItemsResponse.Data?.Count ?? 0) > 0)
                    linkedWorkCount++;

                testFilesCount++;
            }

            var denominator = 3 * totalPrs;
            decimal score = 0.0m;
            if (denominator != 0)
            {
                score = (decimal)(reviewedCount + linkedWorkCount + testFilesCount) / denominator;
            }
            var roundedScore = Math.Round(score, 2);
            
            _cache.Set(cacheKey, roundedScore, TimeSpan.FromMinutes(10));
            
            return roundedScore.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[AzureProxyService] Failed to calculate PR analysis score");
            return 0.0m.ToFailedDependencyApiResponse<decimal>(ex.Message);
        }
    }

    public async Task<IApiResponse<decimal>> CalculatePostDeploymentIssueScoreAsync(
        string serviceName, 
        DateTime startTime, 
        string project = "Hubtel", 
        CancellationToken ct = default)
    {
        var dateOnly = startTime.Date.ToString("yyyy-MM-dd");
        var cacheKey = $"postdeploy:{project}:{serviceName}:{dateOnly}";
        
        if (_cache.TryGetValue(cacheKey, out decimal cachedScore))
        {
            return cachedScore.ToOkApiResponse();
        }

        try
        {
            var wiqlQuery = $@"
                SELECT [System.Id] FROM WorkItems
                WHERE [System.TeamProject] = '{project}'
                AND [System.CreatedDate] >= '{dateOnly}'
                AND ([System.Title] CONTAINS '{serviceName}' OR [System.Tags] CONTAINS '{serviceName}')";

            var workItemsResponse = await QueryWorkItemsAsync(project, wiqlQuery, ct);
            
            if (workItemsResponse.Code != 200 || (workItemsResponse.Data?.Count ?? 0) == 0)
            {
                const decimal defaultScore = 1.0m;
                _cache.Set(cacheKey, defaultScore, TimeSpan.FromHours(1));
                return defaultScore.ToOkApiResponse();
            }

            var severityScores = new Dictionary<string, decimal>(StringComparer.OrdinalIgnoreCase)
            {
                { "minor", 1.0m },
                { "moderate", 0.7m },
                { "critical", 0.4m }
            };

            var totalScore = (workItemsResponse.Data ?? []).Where(wi => !string.IsNullOrEmpty(wi.Severity))
                .Sum(wi => severityScores.GetValueOrDefault(wi.Severity!, 0.0m));

            var averageScore = totalScore / Math.Max((workItemsResponse.Data ?? []).Count, 1);
            var roundedScore = Math.Round(averageScore, 2);
            
            _cache.Set(cacheKey, roundedScore, TimeSpan.FromHours(1));
            return roundedScore.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[AzureProxyService] Failed to calculate post-deployment issue score");
            return 0.0m.ToFailedDependencyApiResponse<decimal>(ex.Message);
        }
    }

    private static string ComputeHashKey(string projectName, int id)
    {
        var raw = $"{projectName}:{id}";
        var bytes = SHA256.HashData(Encoding.UTF8.GetBytes(raw));
        return Convert.ToHexString(bytes);
    }

    public (string Project, string Repository) ExtractProjectAndRepository(string azureUrl)
    {
        var match = AzureUrlRegex.Match(azureUrl);
        return match.Success 
            ? (Uri.UnescapeDataString(match.Groups["project"].Value), Uri.UnescapeDataString(match.Groups["repo"].Value)) 
            : (string.Empty, string.Empty);
    }


    public async Task<RepositoryServiceDeploymentMetricsSummary?> GetRepositoryServiceDeploymentMetricsSummaryAsync(
        string projectName,
        int releaseDefinitionId,
        int top = 5,
        CancellationToken ct = default)
    {
        try
        {
            var url = $"{_azureConfig.ReleaseUrl}/{_azureConfig.Organization}/{projectName}/_apis/release/releases";

            var response = await url
                .SetQueryParam("definitionId", releaseDefinitionId)
                .SetQueryParam("$top", top)
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureListResponse<AzureReleaseInfo>>(cancellationToken: ct);

            var metricSummary = new RepositoryServiceDeploymentMetricsSummary();
            foreach (var release in response.Value)
            {
                var releaseDetail = await GetReleaseDetailAsync(projectName, release?.Id ?? 0, ct);

                metricSummary.TotalSuccessfulDeployments+= releaseDetail.Data?.Environments?.Count(e => e.Status == "succeeded") ?? 0;
                metricSummary.TotalFailedDeployments += releaseDetail.Data?.Environments?.Count(e => e.Status == "rejected") ?? 0;
                metricSummary.TotalDeployments += releaseDetail.Data?.Environments?.Count ?? 0;
            }

            return metricSummary;
        }
        catch (Exception e)
        {
            _logger.LogError(e,
                " [AzureProxyService] Failed to get repository service deployment metrics summary for {ProjectName} and {ReleaseDefinitionId}",
                projectName, releaseDefinitionId);
            return null;
        }
    }

    public async Task<IApiResponse<List<AzureReleaseDetail>>> GetRecentReleasesAsync(
        string project,
        int releaseDefinitionId,
        int top = 5,
        CancellationToken ct = default)
    {
        var cacheKey = $"releases:{project}:{releaseDefinitionId}:{top}";
        
        if (_cache.TryGetValue(cacheKey, out List<AzureReleaseDetail>? cachedReleases) && cachedReleases != null)
        {
            return cachedReleases.ToOkApiResponse();
        }

        try
        {
            var url = $"{_azureConfig.ReleaseUrl}/{_azureConfig.Organization}/{project}/_apis/release/releases";
            
            var response = await url
                .SetQueryParam("definitionId", releaseDefinitionId)
                .SetQueryParam("$top", top)
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureListResponse<AzureReleaseInfo>>(cancellationToken: ct);

            var releases = new List<AzureReleaseDetail>();
            
            var releaseTasks = (response.Value ?? [])
                .Where(r => r?.Id != null)
                .Select(async release =>
                {
                    if (release?.Id == null) return null;
                    var releaseDetail = await GetReleaseDetailAsync(project, release.Id ?? 0, ct);
                    return releaseDetail.Code == (int)HttpStatusCode.OK ? releaseDetail.Data : null;
                })
                .ToArray();

            var releaseDetails = await Task.WhenAll(releaseTasks);
            releases.AddRange(releaseDetails.Where(r => r != null).Cast<AzureReleaseDetail>());

            _cache.Set(cacheKey, releases, ReleaseCacheExpiry);
            return releases.ToOkApiResponse();
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to get recent releases: {Error}", errorMsg);
            return new List<AzureReleaseDetail>().ToFailedDependencyApiResponse<List<AzureReleaseDetail>>(errorMsg);
        }
    }

    public async Task<IApiResponse<AzureReleaseDetail>> GetReleaseDetailAsync(
        string project,
        int releaseId,
        CancellationToken ct = default)
    {
        var cacheKey = $"releasedetail:{project}:{releaseId}";
        
        if (_cache.TryGetValue(cacheKey, out AzureReleaseDetail? cachedDetail) && cachedDetail != null)
        {
            return cachedDetail.ToOkApiResponse();
        }

        try
        {
            var url = $"{_azureConfig.ReleaseUrl}/{_azureConfig.Organization}/{project}/_apis/release/releases/{releaseId}";
            
            var response = await url
                .SetQueryParam(ValidationConstants.AzureProxyRelated.ApiVersion, _azureConfig.ApiVersion)
                .WithHeader(ValidationConstants.AzureProxyRelated.Authorization, $"Basic {_base64Pat}")
                .WithTimeout(TimeSpan.FromSeconds(_azureConfig.RequestTimeoutSeconds))
                .GetJsonAsync<AzureReleaseDetail>(cancellationToken: ct);
            
            _cache.Set(cacheKey, response, ReleaseCacheExpiry);
            return response.ToOkApiResponse();
        }
        catch (FlurlHttpException ex)
        {
            var errorMsg = await ex.GetResponseStringAsync();
            _logger.LogError(ex, "[AzureProxyService] Failed to get release detail: {Error}", errorMsg);
            return ApiResponse<AzureReleaseDetail>.Default.ToFailedDependencyApiResponse(errorMsg);
        }
    }

    public async Task<IApiResponse<DateTime?>> GetPullRequestCreatedTimeByCommitAsync(
        string project,
        string repositoryId,
        string commitSha,
        CancellationToken ct = default)
    {
        var cacheKey = $"prtime:{project}:{repositoryId}:{commitSha}";
        
        if (_cache.TryGetValue(cacheKey, out DateTime? cachedTime))
        {
            return cachedTime.ToOkApiResponse();
        }

        try
        {
            await foreach (var pr in GetPullRequestsAsync(project, repositoryId, "completed", ct))
            {
                var lastMergeCommit = pr.LastMergeCommit?.CommitId ?? string.Empty;
                var targetRefName = pr.TargetRefName ?? string.Empty;
                
                if (string.Equals(lastMergeCommit, commitSha, StringComparison.OrdinalIgnoreCase) && 
                    (targetRefName.EndsWith("/main", StringComparison.OrdinalIgnoreCase) || 
                     targetRefName.EndsWith("/master", StringComparison.OrdinalIgnoreCase)))
                {
                    _cache.Set(cacheKey, pr.CreationDate, TimeSpan.FromHours(2));
                    return pr.CreationDate.ToOkApiResponse();
                }
            }

            _cache.Set(cacheKey, (DateTime?)null, TimeSpan.FromMinutes(30));
            return ((DateTime?)null).ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[AzureProxyService] Failed to get PR created time for commit {CommitSha}", commitSha);
            return ((DateTime?)null).ToFailedDependencyApiResponse<DateTime?>(ex.Message);
        }
    }

    public async Task<IApiResponse<DeploymentMetrics>> CalculateDeploymentMetricsAsync(
        CalculateDeploymentMetricsRequest request,
        CancellationToken ct = default)
    {
        var cacheKey = $"metrics:{request.Project}:{request.ReleaseDefinitionId}:{request.ServiceName}:{request.Top}";

        if (_cache.TryGetValue(cacheKey, out DeploymentMetrics? cachedMetrics) && cachedMetrics != null)
            return cachedMetrics.ToOkApiResponse();

        try
        {
            var releasesResponse = await GetRecentReleasesAsync(request.Project, request.ReleaseDefinitionId, request.Top, ct);

            if (releasesResponse.Code != (int)HttpStatusCode.OK || (releasesResponse.Data?.Count ?? 0) == 0)
            {
                var emptyMetrics = new DeploymentMetrics();
                _cache.Set(cacheKey, emptyMetrics, TimeSpan.FromMinutes(5));
                return emptyMetrics.ToOkApiResponse();
            }

            var buildCommitMap = await GetBuildCommitMapAsync(request, releasesResponse.Data!, ct);
            var prScores = await GetPullRequestScoresAsync(request, buildCommitMap, ct);

            var (successful, failed, durations, issueScore) = await ProcessReleaseEnvironmentsAsync(
                request, releasesResponse.Data!, ct);

            var total = successful + failed;

            var metrics = new DeploymentMetrics
            {
                SuccessRate = total > 0 ? Math.Round((decimal)successful / total, 2) : 0.0m,
                StabilityScore = failed == 0 ? 1.0m : Math.Round(1.0m - ((decimal)failed / total), 2),
                SpeedScore = durations.Count != 0
                    ? Math.Max(0.0m, Math.Min(1.0m, Math.Round(1.0m - ((decimal)durations.Sum() / (durations.Count * 259200)), 2)))
                    : 0.5m,
                ProcessComplianceScore = prScores.Count > 0 ? Math.Round(prScores.Average(), 2) : 0.0m,
                IssueSeverityScore = issueScore
            };

            _cache.Set(cacheKey, metrics, TimeSpan.FromMinutes(5));
            return metrics.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[AzureProxyService] Failed to calculate deployment metrics");
            return new DeploymentMetrics().ToFailedDependencyApiResponse<DeploymentMetrics>(ex.Message);
        }
    }

    private async Task<Dictionary<string, List<string>>> GetBuildCommitMapAsync(
        CalculateDeploymentMetricsRequest request,
        List<AzureReleaseDetail> releases,
        CancellationToken ct)
    {
        var buildChangesTasks = (from release in releases
            select release.Artifacts?.FirstOrDefault(a => a.Type == "Build")
                ?.DefinitionReference?.GetValueOrDefault("version")
                ?.Id
            into buildId
            where !string.IsNullOrEmpty(buildId)
            select GetBuildChangesWithCommitIds(request.Project, buildId, ct)).ToList();

        var buildChangesResults = await Task.WhenAll(buildChangesTasks);
        return buildChangesResults
            .Where(r => r.BuildId != null)
            .GroupBy(x => x.BuildId!)
            .ToDictionary(r => r.Key, r => r.SelectMany(x => x.CommitIds).Distinct().ToList());
    }

    private async Task<List<decimal>> GetPullRequestScoresAsync(
        CalculateDeploymentMetricsRequest request,
        Dictionary<string, List<string>> buildCommitMap,
        CancellationToken ct)
    {
        var prScoreTasks = buildCommitMap.Values
            .Select(commitIds => CalculatePullRequestAnalysisScoreAsync(request.Project, request.RepositoryId, commitIds, ct))
            .ToArray();

        var prScoreResults = await Task.WhenAll(prScoreTasks);
        return prScoreResults
            .Where(r => r.Code == (int)HttpStatusCode.OK)
            .Select(r => r.Data)
            .ToList();
    }

    private async Task<(int Successful, int Failed, List<double> Durations, decimal IssueScore)> ProcessReleaseEnvironmentsAsync(
        CalculateDeploymentMetricsRequest request,
        List<AzureReleaseDetail> releases,
        CancellationToken ct)
    {
        int successful = 0, failed = 0;
        var durations = new List<double>();
        var issueScore = 0.0m;
        bool isLastSuccessful = false, doneWithIssueScore = false;

        foreach (var release in releases)
        {
            foreach (var env in GetProductionEnvironments(release))
            {
                UpdateDeploymentCounters(env, ref successful, ref failed, ref isLastSuccessful);

                var commitSha = ExtractCommitSha(release);
                var prTime = await GetPullRequestCreatedTimeByCommitAsync(request.Project, request.RepositoryId, commitSha, ct);
                var (startTime, endTime) = ExtractDeploymentTimes(env, prTime.Data);

                AddDurationIfValid(startTime, endTime, durations);

                if (!CanCalculateIssueScore(isLastSuccessful, doneWithIssueScore, startTime)) continue;
                issueScore = await FetchIssueScoreAsync(request, startTime!.Value, ct);
                doneWithIssueScore = true;
            }
        }

        return (successful, failed, durations, issueScore);
    }

    private static void UpdateDeploymentCounters(ReleaseEnvironment env, ref int successful, ref int failed, ref bool isLastSuccessful)
    {
        switch (env.Status?.ToLowerInvariant())
        {
            case "succeeded":
                if (successful == 0 && !isLastSuccessful)
                    isLastSuccessful = true;
                successful++;
                break;
            case "rejected":
                failed++;
                break;
        }
    }

    private static string ExtractCommitSha(AzureReleaseDetail release) =>
        release.Artifacts?.FirstOrDefault()?.DefinitionReference?.GetValueOrDefault("sourceVersion")?.Id ?? string.Empty;

    private static (DateTime? StartTime, DateTime? EndTime) ExtractDeploymentTimes(ReleaseEnvironment env, DateTime? prTime)
    {
        var startTime = prTime ?? env.DeploySteps?.FirstOrDefault()?.QueuedOn;
        var endTime = env.DeploySteps?.LastOrDefault()
            ?.ReleaseDeployPhases?.LastOrDefault()
            ?.DeploymentJobs?.LastOrDefault()
            ?.Tasks?.LastOrDefault()?.FinishTime;
        return (startTime, endTime);
    }

    private static void AddDurationIfValid(DateTime? startTime, DateTime? endTime, List<double> durations)
    {
        if (startTime.HasValue && endTime.HasValue)
            durations.Add((endTime.Value - startTime.Value).TotalSeconds);
    }

    private static bool CanCalculateIssueScore(bool isLastSuccessful, bool doneWithIssueScore, DateTime? startTime) =>
        isLastSuccessful && !doneWithIssueScore && startTime.HasValue;

    private async Task<decimal> FetchIssueScoreAsync(CalculateDeploymentMetricsRequest request, DateTime startTime, CancellationToken ct)
    {
        var issueScoreResponse = await CalculatePostDeploymentIssueScoreAsync(
            $"service:{request.ServiceName}", startTime, "Hubtel", ct);

        return issueScoreResponse.Code == (int)HttpStatusCode.OK
            ? issueScoreResponse.Data
            : 0.0m;
    }

    private static IEnumerable<ReleaseEnvironment> GetProductionEnvironments(AzureReleaseDetail release)
    {
        return release.Environments?.Where(e =>
            (e.Name ?? string.Empty).Contains(ValidationConstants.AzureProxyRelated.Production, StringComparison.OrdinalIgnoreCase)) ?? [];
    }

    private async Task<(string? BuildId, List<string> CommitIds)> GetBuildChangesWithCommitIds(
        string project, 
        string buildId, 
        CancellationToken ct)
    {
        try
        {
            var buildChangesResponse = await GetBuildChangesAsync(project, buildId, ct);
            
            if (buildChangesResponse.Code == (int)HttpStatusCode.OK && buildChangesResponse.Data != null)
            {
                var commitIds = buildChangesResponse.Data
                    .Where(c => !string.IsNullOrEmpty(c.Id))
                    .Select(c => c.Id!)
                    .ToList();
                
                return (buildId, commitIds);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "[AzureProxyService] Failed to get build changes for {BuildId}", buildId);
        }

        return (buildId, new List<string>());
    }

    [GeneratedRegex(@"https:\/\/dev\.azure\.com\/[^\/]+\/(?<project>[^\/]+)\/_git\/(?<repo>[^\/]+)", RegexOptions.Compiled)]
    private static partial Regex AzureRepositoryUrlRegex();
}