

using System.Globalization;
using System.Net.Http.Headers;
using System.Text;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.MicrosoftTeams;
using Hubtel.CCI.Api.Dtos.Responses.ProductTeam;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Services.Providers;


public class TeamsNotificationService(
    ILogger<TeamsNotificationService> logger,
    IOptions<TeamsBotAppConfig> teamsBotAppConfig,
    IRepositoryContext repositoryContext,
    IProductTeamService productTeamService,
    IHostEnvironment env,
    IPdfGenerator pdfGenerator)
    : ITeamsNotificationService
{
    private readonly TeamsBotAppConfig _teamsBotAppConfig = teamsBotAppConfig.Value;
    private const string ODataType = "@odata.type";
    private const string AddUserConversationMember = "#microsoft.graph.aadUserConversationMember";
    private const string Owner = "owner";
    private const string Roles = "roles";
    private const string UserDataBind = "<EMAIL>";
    private const string ApplicationJson = "application/json";
    private readonly string _templatePath = Path.Combine(env.ContentRootPath, "Templates", "cci_report.template.html");
    private readonly string _graphDateFormat = "MMMM yyyy";


    private IConfidentialClientApplication BuildClientApp() => ConfidentialClientApplicationBuilder.Create(_teamsBotAppConfig.ClientId)
        .WithClientSecret(_teamsBotAppConfig.ClientSecret)
        .WithRedirectUri(_teamsBotAppConfig.RedirectUri)
        .WithAuthority(AzureCloudInstance.AzurePublic, _teamsBotAppConfig.TenantId)
        .Build();

    /// <summary>
    /// This Starts the Auth Process Via MSAL To Obtain The Delegate Token For Sending Messages
    /// </summary>
    /// <returns></returns>
    public async Task<IApiResponse<string>> StartMSALDelegateAuthFlowAsync()
    {
        logger.LogInformation("[TeamsNotificationService::StartMSALDelegateAuthFlowAsync] Starting the Delegate Auth Flow");

        var clientApp = BuildClientApp();
        var authUrl = clientApp.GetAuthorizationRequestUrl(_teamsBotAppConfig.Scopes)
            .WithLoginHint(_teamsBotAppConfig.SqBotUser)
            .WithRedirectUri(_teamsBotAppConfig.RedirectUri)
            .ExecuteAsync().Result;

        var token = await repositoryContext.MsTeamsBotTokenRepository
                    .GetQueryable()
                    .AsNoTracking()
                    .Where(tok => tok.UserName == _teamsBotAppConfig.SqBotUser)
                    .OrderByDescending(tok => tok.CreatedAt)
                    .FirstOrDefaultAsync();

        if (token == null || string.IsNullOrWhiteSpace(token.SerializedTokenCache))
            return authUrl.ToString().ToOkApiResponse("MSAL Delegate Auth Flow Triggered Successfully");

        var cacheBytes = Convert.FromBase64String(token.SerializedTokenCache);

        clientApp.UserTokenCache.SetBeforeAccess(args =>
        {
            args.TokenCache.DeserializeMsalV3(cacheBytes);
        });

        try
        {
            var account = await clientApp.GetAccountAsync(token.Identifier);
            if (account != null)
            {
                var silentResult = await clientApp.AcquireTokenSilent(_teamsBotAppConfig.Scopes, account).ExecuteAsync();
                return silentResult.Account.Username.ToOkApiResponse("There is already a valid token");
            }
        }
        catch (MsalUiRequiredException e)
        {
            logger.LogError(e, "[TeamsNotificationService::StartMSALDelegateAuthFlowAsync] No Or Invalid Token, moving to trigger new login");
        }

        return authUrl.ToString().ToOkApiResponse("MSAL Delegate Auth Flow Triggered Successfully");
    }

    /// <summary>
    /// This Completes the Auth Process Via MSAL To Obtain The Delegate Token For Sending Messages
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<IApiResponse<string>> CompleteMSALDelegateAuthFlowAsync(RedirectPayloadRequest request)
    {
        logger.LogInformation("[TeamsNorificationService::CompleteMSALDelegateAuthFlowAsync] Completing MSAL Delegate Auth Flow Request with payload: {Request}",
         JsonConvert.SerializeObject(request));

        var code = request.ExtractCodeFromUrl();

        var clientApp = BuildClientApp();
        byte[]? cacheBytes = null;

        clientApp.UserTokenCache.SetAfterAccess(args =>
        {
            if (args.HasStateChanged)
            {
                cacheBytes = args.TokenCache.SerializeMsalV3();
            }
        });

        var result = await clientApp.AcquireTokenByAuthorizationCode(_teamsBotAppConfig.Scopes, code)
            .ExecuteAsync();


        if (cacheBytes == null)
        {
            return "Failed to persist token cache".ToFailedDependencyApiResponse("MSAL cache hook did not trigger.");
        }


        var tokenData = new MsTeamsBotToken
        {
            AccessToken = result.AccessToken,
            ExpiresOn = result.ExpiresOn,
            Scopes = [.. result.Scopes],
            UserName = result.Account.Username,
            HomeAccountId = result.Account.HomeAccountId.ToString(),
            Identifier = result.Account.HomeAccountId.Identifier,
            SerializedTokenCache = Convert.ToBase64String(cacheBytes!)
        };


        var added = await repositoryContext.MsTeamsBotTokenRepository.AddAsync(tokenData);

        return added <= 0 ? "Auth Flow Failed To Complete".ToFailedDependencyApiResponse("Auth Flow Tokens Failed To Persist, Try again later") :
            "Auth Flow Completed Successfully".ToOkApiResponse("Auth Flow Completed");
    }
    

    public async Task<IApiResponse<string>> SendTeamsMessageAsync(TeamsMessageRequest request)
    {
        logger.LogInformation("Send Teams Message: {Content} to recipient: {Recipient} From Sender: {Sender}"
        , request.MessageContent, request.RecipientEmail, _teamsBotAppConfig.SqBotUser);

        var (httpClient, authError) = await GetAuthorizedHttpClientAsync();
        if (authError is not null) return authError;

        // Get recipient ID
        var recipientRes = await httpClient.GetAsync($"https://graph.microsoft.com/v1.0/users/{request.RecipientEmail}");
        recipientRes.EnsureSuccessStatusCode();
        var recipientJson = await recipientRes.Content.ReadAsStringAsync();
        var recipient = JsonConvert.DeserializeObject<GraphUser>(recipientJson);
        var recipientId = recipient?.Id;

        // Get sender ID
        var senderRes = await httpClient.GetAsync(_teamsBotAppConfig.MsGraphMeRequestUri);
        senderRes.EnsureSuccessStatusCode();
        var senderJson = await senderRes.Content.ReadAsStringAsync();
        var sender = JsonConvert.DeserializeObject<GraphUser>(senderJson);
        var senderId = sender?.Id;

        // Create chat
        var chatPayload = new
        {
            chatType = "oneOnOne",
            members = new object[]
            {
                new Dictionary<string, object>
                {
                    [ODataType] = AddUserConversationMember,
                    [Roles] = new[] { Owner },
                    [UserDataBind] = $"https://graph.microsoft.com/v1.0/users('{senderId}')"
                },
                new Dictionary<string, object>
                {
                    [ODataType] = AddUserConversationMember,
                    [Roles] = new[] { Owner },
                    [UserDataBind] = $"https://graph.microsoft.com/v1.0/users('{recipientId}')"
                }
            }
        };

        var chatResponse = await httpClient.PostAsync(_teamsBotAppConfig.MsGraphChatRequestUri,
            new StringContent(JsonConvert.SerializeObject(chatPayload), Encoding.UTF8, ApplicationJson));
        chatResponse.EnsureSuccessStatusCode();
        var chatJson = await chatResponse.Content.ReadAsStringAsync();
        var chat = JsonConvert.DeserializeObject<GraphChat>(chatJson);
        var chatId = chat?.Id;

        // Send message
        var messagePayload = new
        {
            body = new { content = request.MessageContent }
        };

        var messageRes = await httpClient.PostAsync($"https://graph.microsoft.com/v1.0/chats/{chatId}/messages",
            new StringContent(JsonConvert.SerializeObject(messagePayload), Encoding.UTF8, ApplicationJson));

        messageRes.EnsureSuccessStatusCode();

        return "✅ Message sent successfully.".ToOkApiResponse("Message delivered");
    }
    
    
    public async Task<IApiResponse<string>> SendTeamsMessageWithFilesAsync(string recipientEmail, string messageContent, List<IFormFile> uploadedFiles)
    {
        logger.LogInformation("Send Teams Message With Multiple Files: {Content} to recipient: {Recipient} From Sender: {Sender}", messageContent, recipientEmail, _teamsBotAppConfig.SqBotUser);

        if (string.IsNullOrWhiteSpace(recipientEmail) || string.IsNullOrWhiteSpace(messageContent))
        {
            return "Recipient Email Or Message Content Should not be empty".ToBadRequestApiResponse(
                "Invalid Message Content/ Recipient Email");
        }
        
        var (httpClient, authError) = await GetAuthorizedHttpClientAsync();
        if (authError is not null) return authError;

        var uploadedResults = new List<(string FileName, string WebUrl)>();

        foreach (var file in uploadedFiles)
        {
            var sanitizedFileName = file.FileName
                .Replace(':', '-') // turn "Hubtel for Government: MyAssembly.com.pdf"
                // into "Hubtel for Government- MyAssembly.com.pdf"
                .Replace('/', '-');
            var safeName   = Uri.EscapeDataString(sanitizedFileName);
            var sessionPayload = new Dictionary<string, object>
            {
                ["item"] = new Dictionary<string, object>
                {
                    ["@microsoft.graph.conflictBehavior"] = "replace"
                }
            };

            var sessionRes = await httpClient.PostAsync(
                $"https://graph.microsoft.com/v1.0/me/drive/root:/TeamsFiles/{safeName}:/createUploadSession",
                new StringContent(JsonConvert.SerializeObject(sessionPayload), Encoding.UTF8, ApplicationJson));
            if (!sessionRes.IsSuccessStatusCode)
            {
                var error = await sessionRes.Content.ReadAsStringAsync();
                logger.LogError("CreateUploadSession failed (status {StatusCode}): {ErrorBody}",
                    sessionRes.StatusCode, error);
                throw new HttpRequestException($"CreateUploadSession returned {sessionRes.StatusCode}");
            }
            sessionRes.EnsureSuccessStatusCode();
            var sessionJson = await sessionRes.Content.ReadAsStringAsync();
            var uploadUrl = JsonConvert.DeserializeObject<dynamic>(sessionJson)?.uploadUrl.ToString();

            await using var stream = file.OpenReadStream();
            var buffer = new byte[file.Length];
            var readAsync = await stream.ReadAsync(buffer);
            logger.LogDebug("Bytes Read: {Bytes}", readAsync);

            using var content = new ByteArrayContent(buffer);
            content.Headers.ContentRange = new ContentRangeHeaderValue(0, buffer.Length - 1, buffer.Length);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");

            var uploadRes = await httpClient.PutAsync(uploadUrl, content);
            uploadRes.EnsureSuccessStatusCode();

            var uploadedJson = await uploadRes.Content.ReadAsStringAsync();
            var uploadedFile = JsonConvert.DeserializeObject<DriveItem>(uploadedJson);
            
            var createLinkRes = await httpClient.PostAsync(
                $"https://graph.microsoft.com/v1.0/me/drive/items/{uploadedFile.Id}/createLink",
                new StringContent("{\"type\": \"view\", \"scope\": \"organization\"}", Encoding.UTF8, "application/json")
            );
            createLinkRes.EnsureSuccessStatusCode();
            var linkJson = await createLinkRes.Content.ReadAsStringAsync();
            var shareLink = JsonConvert.DeserializeObject<ShareLinkResponse>(linkJson);
            var shareableUrl = shareLink?.Link?.WebUrl;

            uploadedResults.Add((safeName, shareableUrl!));
        }

        // Resolve recipient and sender
        var recipientRes = await httpClient.GetAsync($"https://graph.microsoft.com/v1.0/users/{recipientEmail}");
        recipientRes.EnsureSuccessStatusCode();
        var recipient = JsonConvert.DeserializeObject<GraphUser>(await recipientRes.Content.ReadAsStringAsync());
        var recipientId = recipient?.Id;
        var recipientDisplayName = recipient?.DisplayName;

        var senderRes = await httpClient.GetAsync(_teamsBotAppConfig.MsGraphMeRequestUri);
        senderRes.EnsureSuccessStatusCode();
        var senderId = JsonConvert.DeserializeObject<GraphUser>(await senderRes.Content.ReadAsStringAsync())?.Id;

        // Create chat
        var chatPayload = new
        {
            chatType = "oneOnOne",
            members = new object[]
            {
                new Dictionary<string, object>
                {
                    [ODataType] = AddUserConversationMember,
                    [Roles] = new[] { Owner },
                    [UserDataBind] = $"https://graph.microsoft.com/v1.0/users('{senderId}')"
                },
                new Dictionary<string, object>
                {
                    [ODataType] = AddUserConversationMember,
                    [Roles] = new[] { Owner },
                    [UserDataBind] = $"https://graph.microsoft.com/v1.0/users('{recipientId}')"
                }
            }
        };

        var chatRes = await httpClient.PostAsync(_teamsBotAppConfig.MsGraphChatRequestUri,
            new StringContent(JsonConvert.SerializeObject(chatPayload), Encoding.UTF8, ApplicationJson));
        chatRes.EnsureSuccessStatusCode();
        var chatId = JsonConvert.DeserializeObject<GraphChat>(await chatRes.Content.ReadAsStringAsync())?.Id;

        // Send message with multiple attachments
        var messagePayload = new
        {
            body = new
            {
                contentType = "html",
                content = $"Hello <at id=\"0\">{recipientDisplayName}</at>,<br>{messageContent.Replace("\n", "<br>")}"
            },
            attachments = uploadedResults.Select((file, index) => new
            {
                id = (index + 1).ToString(),
                contentType = "reference",
                contentUrl = file.WebUrl,
                name = file.FileName,
                content = (string?)null
            }).ToArray(),
            mentions = new[]
            {
                new
                {
                    id = 0,
                    mentionText = recipientDisplayName,
                    mentioned = new
                    {
                        user = new
                        {
                            id = recipientId,
                            displayName = recipientDisplayName
                        }
                    }
                }
            },
        };

        var messageRes = await httpClient.PostAsync($"https://graph.microsoft.com/v1.0/chats/{chatId}/messages",
            new StringContent(JsonConvert.SerializeObject(messagePayload), Encoding.UTF8, ApplicationJson));

        messageRes.EnsureSuccessStatusCode();

        return "✅ Message with multiple files sent successfully.".ToOkApiResponse("Message with attachments delivered");
    }
    
    
    private async Task<(HttpClient HttpClient, IApiResponse<string>? Error)> GetAuthorizedHttpClientAsync()
    {
        var token = await repositoryContext.MsTeamsBotTokenRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(tok => tok.UserName == _teamsBotAppConfig.SqBotUser)
            .OrderByDescending(tok => tok.CreatedAt)
            .FirstOrDefaultAsync();

        if (token == null || string.IsNullOrWhiteSpace(token.SerializedTokenCache))
        {
            return (null!, "❌ Bot token not found or invalid.".ToUnAuthorizedApiResponse("Not authorized to send a message"));
        }

        var cacheBytes = Convert.FromBase64String(token.SerializedTokenCache);

        var clientApp = BuildClientApp();
        clientApp.UserTokenCache.SetBeforeAccess(args =>
        {
            args.TokenCache.DeserializeMsalV3(cacheBytes);
        });

        var account = await clientApp.GetAccountAsync(token.Identifier);
        var result = await clientApp.AcquireTokenSilent(_teamsBotAppConfig.Scopes, account).ExecuteAsync();
        var accessToken = result.AccessToken;

        var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        return (httpClient, null);
    }

    
    public async Task<IApiResponse<string>> TriggerFlowToAutomateReportNotifierAsync()
    {
        logger.LogInformation("[TeamsNotificationService::TriggerFlowToAutomateReportNotifierAsync] Starting Flow...");
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(Miscellaneous.GetCurrentPublicationTargetDay());
        var productGroups = await repositoryContext.ProductGroupRepository.GetQueryable().AsNoTracking().ToListAsync();
        var productTeams = await repositoryContext.ProductTeamRepository.GetQueryable().AsNoTracking().ToListAsync();
        
        var rules = await repositoryContext.ReportDeliveryRuleRepository
            .GetQueryable()
            .AsNoTracking()
            .ToListAsync();

        foreach (var productGroup in productGroups)
        {
            var activeTeams = productTeams
                .Where(team => productGroup.ProductTeams.Items.Select(t => t.Id).Contains(team.Id) &&
                               team.Status.Equals(ValidationConstants.ProductStatus.Active))
                .ToList();

            if (activeTeams.Count == 0) continue;

            logger.LogDebug("Active productTeams under ProductGroup: {ProductGroup}. Count: {Count}",
                productGroup.GroupName, activeTeams.Count);
            
            var items = await GenerateAllReportsForGroupAsync(activeTeams, productGroup, publicationWeek);
            
            var buckets = BuildBuckets(productGroup.Id, items, rules);
            
            await SendGroupBucketsAsync(productGroup.GroupName, buckets);
        }

        return "Triggered Successfully".ToOkApiResponse();
    }
    
    private static Dictionary<string, List<IFormFile>> BuildBuckets(
        string groupId,
        List<(IFormFile File, string TeamId, string Scope)> items,
        IReadOnlyList<ReportDeliveryRule> rules)
    {
        return rules
            .Where(r => r.ProductGroupId == groupId)
            .Select(rule => new
            {
                rule.RecipientEmail,
                Files = items
                    .Where(item =>
                        // either rule applies to all teams or specifically this one
                        (rule.ProductTeamIds.Count == 0 || 
                         rule.ProductTeamIds.Contains(item.TeamId, StringComparer.OrdinalIgnoreCase))
                        &&
                        // and either rule has no scope or matches this item’s scope
                        (string.IsNullOrWhiteSpace(rule.Scope) || 
                         item.Scope.Equals(rule.Scope, StringComparison.OrdinalIgnoreCase))
                    )
                    .Select(item => item.File)
                    .ToList()
            })
            // drop any rules that matched zero files
            .Where(x => x.Files.Count > 0)
            // group by recipient email
            .GroupBy(x => x.RecipientEmail, StringComparer.OrdinalIgnoreCase)
            // flatten each group’s file lists into one List<IFormFile>
            .ToDictionary(
                g => g.Key,
                g => g.SelectMany(x => x.Files).ToList(),
                StringComparer.OrdinalIgnoreCase
            );
    }

    
    
    private async Task<List<(IFormFile File, string TeamId, string Scope)>> 
        GenerateAllReportsForGroupAsync(
            List<ProductTeam> activeTeams,
            ProductGroup productGroup,
            string publicationWeek)
    {
        var list = new List<(IFormFile, string, string)>();

        foreach (var team in activeTeams)
        {
            var result = await productTeamService
                .GetProductTeamPublicationAsync(team.Id, publicationWeek, CancellationToken.None);
            if (result.Code != StatusCodes.Status200OK || result.Data == null) 
                continue;

            if (result.Data.Backend is { } backend)
            {
                var file = await GenerateProductScopePdfFileAsync(
                    ValidationConstants.ProductTeamScopes.Backend,
                    backend, team, productGroup, publicationWeek);
                list.Add((file, team.Id, ValidationConstants.ProductTeamScopes.Backend));
            }

            if (result.Data.Frontend is not { } frontend) 
                continue;
            
            var frontendFile = await GenerateProductScopePdfFileAsync(
                    ValidationConstants.ProductTeamScopes.Frontend,
                    frontend, team, productGroup, publicationWeek);
                list.Add((frontendFile, team.Id, ValidationConstants.ProductTeamScopes.Frontend));
            
        }

        return list;
    }
    
    private async Task SendGroupBucketsAsync(
        string groupName,
        Dictionary<string, List<IFormFile>> buckets)
    {
        foreach (var (email, files) in buckets)
        {
            var message = $"\nKindly find attached the summary report(s) for the <b>{groupName}</b> product group." +
                          "\nPlease review the insights and recommendations.\nBest regards,\n<b>Code Quality And Tooling Team</b>";

            await SendTeamsMessageWithFilesAsync(email, message, files);
            logger.LogInformation("Sent {Count} PDFs for group {Group} to {Email}",
                files.Count, groupName, email);
        }
    }


    private async Task<string> LoadTemplateAsync(string teamName)
    {
        var html = teamName switch
        {
            ValidationConstants.CqtTeams.CodeQuality => await File.ReadAllTextAsync(_templatePath),
            _ => ""
        };
        return html;
    }


        private async Task<IFormFile> GenerateProductScopePdfFileAsync(string scope, ProductSectionStats sectionStats, ProductTeam team, ProductGroup productGroup, string publicationWeek)
        {
            sectionStats.PastEightDaysRankItem = sectionStats.PastEightDaysRankItem
                .OrderBy(x => x.PublicationDate)
                .ToList();
            sectionStats.PastEightWeeksRankItem = sectionStats.PastEightWeeksRankItem
                .OrderBy(x => x.PublicationDate)
                .ToList();
            sectionStats.PastEightMonthsRankItem = sectionStats.PastEightMonthsRankItem
                .OrderBy(x => x.PublicationDate)
                .ToList();
            sectionStats.PastEightDaysCodeGrowth = sectionStats.PastEightDaysCodeGrowth
                .OrderBy(x => x.PublicationDate)
                .ToList();
            sectionStats.PastEightWeeksCodeGrowth = sectionStats.PastEightWeeksCodeGrowth
                .OrderBy(x => x.PublicationDate)
                .ToList();
            sectionStats.PastEightMonthsCodeGrowth = sectionStats.PastEightMonthsCodeGrowth
                .OrderBy(x => x.PublicationDate)
                .ToList();
            
            var chartData = new ChartData
            {
                Days = new TimeSeriesData
                {
                    Data = sectionStats.PastEightDaysRankItem.Select(item => 
                            Math.Round(item.Rankings?.Rating ?? 0m, 1)
                        )
                        .ToList(),
                    Labels = sectionStats
                        .PastEightDaysRankItem
                        .Select(item =>
                        {
                            var dt = item.PublicationDate;
                            return dt.ToString(_graphDateFormat, CultureInfo.InvariantCulture);
                        })
                        .ToList()
                },
                Weeks = new TimeSeriesData
                {
                    Data = sectionStats.PastEightWeeksRankItem.Select(item => 
                            Math.Round(item.Rankings?.Rating ?? 0m, 1)
                        )
                        .ToList(),
                    Labels = sectionStats.PastEightWeeksRankItem.Select(item =>
                        {
                            var dt = item.PublicationDate;
                            return dt.ToString(_graphDateFormat, CultureInfo.InvariantCulture);
                        })
                        .ToList()
                },
                Months = new TimeSeriesData
                {
                    Data = sectionStats.PastEightMonthsRankItem.Select(item => 
                            Math.Round(item.Rankings?.Rating ?? 0m, 1)
                        )
                        .ToList(),
                    Labels = sectionStats.PastEightMonthsRankItem.Select(item =>
                        {
                            var dt = item.PublicationDate;
                            return dt.ToString(_graphDateFormat, CultureInfo.InvariantCulture);
                        })
                        .ToList()
                },
                CodeGrowth = new CodeGrowthData
                {
                    Days = new TimeSeriesData
                    {
                        Data = sectionStats.PastEightDaysCodeGrowth.Select(item => item.NonCommentedLinesOfCode).ToList(),
                        Labels = sectionStats.PastEightDaysCodeGrowth.Select(item =>
                            {
                                var dt = item.PublicationDate;
                                return dt.ToString(_graphDateFormat, CultureInfo.InvariantCulture);
                            })
                            .ToList()
                    },
                    Weeks = new TimeSeriesData
                    {
                        Data = sectionStats.PastEightWeeksCodeGrowth.Select(item => item.NonCommentedLinesOfCode).ToList(),
                        Labels = sectionStats.PastEightWeeksCodeGrowth.Select(item =>
                            {
                                var dt = item.PublicationDate;
                                return dt.ToString(_graphDateFormat, CultureInfo.InvariantCulture);
                            })
                            .ToList()
                    },
                    Months = new TimeSeriesData
                    {
                        Data = sectionStats.PastEightMonthsCodeGrowth.Select(item => item.NonCommentedLinesOfCode).ToList(),
                        Labels = sectionStats.PastEightMonthsCodeGrowth.Select(item =>
                            {
                                var dt = item.PublicationDate;
                                return dt.ToString(_graphDateFormat, CultureInfo.InvariantCulture);
                            })
                            .ToList()
                    }
                }
            };
                
            var jsonData = JsonConvert.SerializeObject(chartData);

            var html = await LoadTemplateAsync(ValidationConstants.CqtTeams.CodeQuality);
                
            html = html.Replace("/* INJECT_CHART_DATA */", $"const serverData = {jsonData};");
            html = html.Replace("{{TeamName}}", team.Name);
            html = html.Replace("{{ProductGroupName}}", productGroup.GroupName);
            html = html.Replace("{{Scope}}", scope); // Scope
            html = html.Replace("{{CCIAverage}}", Math.Round(sectionStats.CciAverage,1).ToString(CultureInfo.InvariantCulture)); // CCI Average
            html = html.Replace("{{Rank}}", sectionStats.Rank.ToString());    // Rank
            html = html.Replace("{{ProductCount}}", sectionStats.TotalProducts.ToString(CultureInfo.InvariantCulture));    // Products Count
            html = html.Replace("{{CodeCoverage}}", Math.Round(sectionStats.CodeCoverage,2).ToString(CultureInfo.InvariantCulture)); // Code Coverage
            html = html.Replace("{{Duplication}}", Math.Round(sectionStats.Duplication,2).ToString(CultureInfo.InvariantCulture)); // Duplication
            html = html.Replace("{{Bugs}}", sectionStats.Bugs.ToString(CultureInfo.InvariantCulture)); // Bugs
            html = html.Replace("{{CodeSmells}}", sectionStats.CodeSmells.ToString(CultureInfo.InvariantCulture));   // Code Smells
            html = html.Replace("{{Vulnerabilities}}", sectionStats.Vulnerabilities.ToString(CultureInfo.InvariantCulture));   // Vulnerabilities
            html = html.Replace("{{Tloc}}", sectionStats.TotalLinesOfCode.ToString(CultureInfo.InvariantCulture)); // Total Lines of Code
                

            var pdfBytes = await pdfGenerator.GeneratePdfAsync(html);
            var ms = new MemoryStream(pdfBytes);
            IFormFile pdfFile = new FormFile(ms,                   
                    0,                   
                    pdfBytes.Length,      
                    "file",               
                    $"{team.Name}-{scope}-{publicationWeek}-{DateTime.UtcNow.Year}.pdf")
                {
                    Headers     = new HeaderDictionary(),
                    ContentType = "application/pdf"
                };

            return pdfFile;
        }
}