using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Engineer;
using Hubtel.CCI.Api.Dtos.Responses.Engineer;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Hubtel.OpenTelemetry.Instrumentation.Extensions;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

/// <summary>
///     Service responsible for all related operations for engineer entity.
/// </summary>
public class EngineerService : IEngineerService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger _logger;
    private readonly IActorService<MainActor> _mainActor;

    public EngineerService(ILogger<EngineerService> logger, IRepositoryContext repositoryContext,
        IActorService<MainActor> mainActor)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
        _mainActor = mainActor;
    }


    /// <summary>
    /// Asynchronously gets a list of engineers with pagination.
    /// </summary>
    /// <param name="filter">The filter to apply to the engineer list. This includes the search term, page index, and page size.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a paged list of engineers.</returns>
    public async Task<IApiResponse<PagedResult<GetEngineerResponse>>> GetEngineersAsync(SearchFilter filter,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[EngineerService: GetEngineersAsync] Received Request to get Engineers => RequestPayload => \n{Message}",
            filter.ToJsonString());

        var query = _repositoryContext.EngineerRepository.GetQueryable().AsNoTracking();

        if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            query = query.Where(c =>
                c.Name.ToLower().Contains(filter.SearchTerm.ToLower()) ||
                c.Email.ToLower().Contains(filter.SearchTerm.ToLower()));

        var pagedResult = await query.OrderByDescending(x => x.CreatedAt)
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => p.Adapt<GetEngineerResponse>()).ToList();

        var response = new PagedResult<GetEngineerResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        _logger.LogDebug("[EngineerService: GetEngineersAsync] Engineers retrieved successfully => ResponsePayload => {Message}",
            response.ToJsonString());
        
        _logger.LogDebug("Testing Two [EngineerService: GetEngineersAsync] Engineers retrieved successfully => ResponsePayload => {Message}",
            response.ToJsonString());
        
        return response.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously retrieves a specific engineer by their ID.
    /// </summary>
    /// <param name="id">The ID of the engineer to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved engineer, or a not found response if no engineer with the provided ID exists.</returns>
    public async Task<IApiResponse<GetEngineerResponse>> GetEngineerAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug("[EngineerService: GetEngineerAsync] Received Request to get Engineer with ID: {Id}",
            id);

        var result = await _repositoryContext.EngineerRepository
            .GetQueryable()
            .AsNoTracking()
            .ProjectToType<GetEngineerResponse>()
            .FirstOrDefaultAsync(c => c.Id == id, ct);

        return result is null ? result.ToNotFoundApiResponse() : result.ToOkApiResponse();
    }


    /// <summary>
    /// Asynchronously adds a new engineer.
    /// </summary>
    /// <param name="request">The request to create a new engineer. This includes the engineer's details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the created engineer.</returns>
    public async Task<IApiResponse<GetEngineerResponse>> AddEngineerAsync(CreateEngineerRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[EngineerService: AddEngineerAsync] Received Request to create Engineer => RequestPayload => {Request}",
            request.Serialize());

        var engineer = request.Adapt<Engineer>();

        var existingEngineer =
            await _repositoryContext.EngineerRepository.FindOneAsync(x => x.Email == engineer.Email, ct);

        if (existingEngineer != null)
            return ApiResponse<GetEngineerResponse>.Default.ToBadRequestApiResponse(
                "Engineer with email already exists"
            );

        engineer.CreatedAt = DateTime.UtcNow;
        engineer.UpdatedAt = DateTime.UtcNow;

        var savedCount = await _repositoryContext.EngineerRepository.AddAsync(engineer, ct);

        if (savedCount <= 0)
            return ApiResponse<GetEngineerResponse>.Default.ToFailedDependencyApiResponse(
                "Could not create Engineer! Please try again"
            );

        return engineer.Adapt<GetEngineerResponse>().ToCreatedApiResponse();
    }

    /// <summary>
    /// Asynchronously updates an existing engineer.
    /// </summary>
    /// <param name="id">The ID of the engineer to update.</param>
    /// <param name="request">The request to update the engineer. This includes the new details of the engineer.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the engineer was updated successfully.</returns>
    public async Task<IApiResponse<bool>> UpdateEngineerAsync(string id, UpdateEngineerRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[EngineerService:UpdateEngineerAsync] Updating engineer with ID: {Id} and RequestPayload => {Request}",
            id, request.Serialize()
        );

        var existingEngineer = await _repositoryContext.EngineerRepository.GetByIdAsync(id, ct);

        if (existingEngineer is null)
        {
            _logger.LogWarning("Engineer not found with ID: {Id}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingEngineer.Name = request.Name ?? existingEngineer.Name;
        existingEngineer.Domain = request.Domain ?? existingEngineer.Domain;

        if (request.Email != null && existingEngineer.Email != request.Email)
        {
            var existingEngineerWithEmail =
                await _repositoryContext.EngineerRepository.FindOneAsync(x => x.Email == request.Email, ct);

            if (existingEngineerWithEmail != null)
                return ApiResponse<bool>.Default.ToBadRequestApiResponse(
                    "Engineer with email already exists"
                );

            existingEngineer.Email = request.Email ?? existingEngineer.Email;
        }

        existingEngineer.JobLevel = request.JobLevel ?? existingEngineer.JobLevel;

        existingEngineer.UpdatedAt = DateTime.UtcNow;

        _logger.LogDebug(
            "Updating engineer in the repository: {Engineer}",
            existingEngineer.Serialize()
        );

        var updated = await _repositoryContext.EngineerRepository.UpdateAsync(existingEngineer, ct) > 0;

        if (!updated) return updated.ToNotFoundApiResponse();

        await _mainActor.Tell(new CascadeEngineerUpdateMessage(existingEngineer), ActorRefs.Nobody);
        return updated.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously retrieves a specific engineer by their email.
    /// </summary>
    /// <param name="email">The email of the engineer to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved engineer, or a not found response if no engineer with the provided email exists.</returns>
    public async Task<IApiResponse<Engineer>> GetEngineerByEmailAsync(string email, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[EngineerService: GetEngineerByEmailAsync] Received Request to get Engineer with Email: {Email}",
            email);

        var result = await _repositoryContext.EngineerRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Email == email, ct);

        return result is null ? result.ToNotFoundApiResponse() : result.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes a specific engineer by marking it as deleted.
    /// </summary>
    /// <param name="id">The ID of the engineer to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the engineer was marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteEngineerAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug("Deleting Engineer with ID: {StockModelId}", id);

        var existingDocument = await _repositoryContext.EngineerRepository.GetByIdAsync(id, ct);

        if (existingDocument == null)
        {
            _logger.LogWarning("Engineer not found with ID: {EngineerId}", id);

            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingDocument.IsDeleted = true;

        _logger.LogDebug("Deleting engineer in the repository: {Engineer}", existingDocument.Serialize());

        var deletedCount = await _repositoryContext.EngineerRepository.DeleteAsync(existingDocument, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("engineer deleted: {Deleted}", deleted);

        if (!deleted) return deleted.ToNotFoundApiResponse();
        await _mainActor.Tell(new CascadeEngineerDeleteMessage(existingDocument), ActorRefs.Nobody);
        return deleted.ToOkApiResponse();
    }
}