using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using Akka.Actor;
using Flurl;
using Flurl.Http;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Azure;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Repository = Hubtel.CCI.Api.Data.Entities.Repository;

namespace Hubtel.CCI.Api.Services.Providers;

/// <summary>
/// Azure Service responsible for api integrations with anything Azure
/// </summary>
/// <param name="logger"></param>
/// <param name="azureOrgConfig"></param>
/// <param name="repositoryContext"></param>
/// <param name="actorService"></param>
/// <param name="bypassedPrService"></param>
public class AzureService(
    ILogger<AzureService> logger, 
    IOptions<AzureOrgConfig> azureOrgConfig,
    IRepositoryContext repositoryContext,
    IActorService<MainActor> actorService,
    IBypassedPrService bypassedPrService
    ): IAzureService
{
    private readonly string _organization = azureOrgConfig.Value.Organization;

    private readonly string _accessToken = azureOrgConfig.Value.AccessToken;
    
    private readonly string _releaseUrl = azureOrgConfig.Value.ReleaseUrl;

    private readonly List<string> _projects = azureOrgConfig.Value.Projects;
    
    
    public async Task<IApiResponse<bool>> ImportAzureDevOpsReleaseDefinitionsAsync(CancellationToken ct = default)
    {
        logger.LogInformation("[AzureService::ImportAzureDevOpsReleaseDefinitionsAsync] Importing Azure DevOps release definitions");

        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationExists = await repositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .AnyAsync(x => x.PublicationDate.Date == publicationDate.Date, ct);

        if (publicationExists)
        {
            return false.ToOkApiResponse("Already processed for the day");
        }

        var totalFetched = 0;
        var totalCreated = 0;

        foreach (var projectName in _projects)
        {
            logger.LogInformation("[AzureService] Fetching release definitions for project: {Project}", projectName);

            List<AzureReleaseDefinition> releaseDefinitions = [];

            await foreach (var definition in FetchAzureReleaseDefinitions(projectName, token: ct))
            {
                logger.LogInformation("[AzureService] Fetched release definition: {DefinitionId} - {DefinitionName}", definition.Id, definition.Name);
                totalFetched++;

                releaseDefinitions.Add(new AzureReleaseDefinition
                {
                    Source = definition.Source,
                    Revision = definition.Revision,
                    Description = definition.Description,
                    CreatedBy = definition.CreatedBy,
                    CreatedOn = definition.CreatedOn,
                    ModifiedBy = definition.ModifiedBy,
                    ModifiedOn = definition.ModifiedOn,
                    IsDeletedAzure = definition.IsDeleted,
                    IsDisabled = definition.IsDisabled,
                    ReleaseNameFormat = definition.ReleaseNameFormat,
                    Comment = definition.Comment,
                    DefinitionId = definition.Id,
                    Name = definition.Name,
                    Path = definition.Path,
                    Url = definition.Url,
                    Links = definition.Links,
                    CompositeDefinitionId = ComputeHashKey(projectName, definition.Id),
                    ProjectName = projectName
                });
            }

            if (releaseDefinitions.Count == 0)
                continue;

            var fetchedCompositeIds = releaseDefinitions
                .Select(r => r.CompositeDefinitionId)
                .ToHashSet();

            var existingCompositeIds = await repositoryContext.AzureReleaseDefinitionRepository
                .GetQueryable()
                .AsNoTracking()
                .Where(x => fetchedCompositeIds.Contains(x.CompositeDefinitionId))
                .Select(x => x.CompositeDefinitionId)
                .ToListAsync(ct);

            var newDefinitions = releaseDefinitions
                .Where(x => !existingCompositeIds.Contains(x.CompositeDefinitionId))
                .ToList();

            var createdCount = await repositoryContext.AzureReleaseDefinitionRepository
                .AddRangeAsync(newDefinitions, ct);

            logger.LogDebug("[AzureService] Created {Count} new release definitions for project: {Project}", createdCount, projectName);

            totalCreated += createdCount;
        }
        
        await actorService.Tell(new AzureDeploymentRecordsMessage(DateTime.UtcNow), ActorRefs.Nobody);

        if (totalCreated <= 0)
            return false.ToOkApiResponse();

        logger.LogInformation("[AzureService] Created {Count} new release definitions, from Fetched {FetchedCount}", totalCreated, totalFetched);
        return true.ToOkApiResponse();
    }
        
    
        public async IAsyncEnumerable<ReleaseDefinition> FetchAzureReleaseDefinitions(
        string project,
        [EnumeratorCancellation] CancellationToken token)
    {
        string? continuationToken = null;
        var base64Pat = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{_accessToken}"));

        do
        {
            List<ReleaseDefinition> releaseDefinitions;

            try
            {
                var request = $"{_releaseUrl}/{_organization}/{project}/_apis/release/definitions"
                    .SetQueryParam("$top", 100)
                    .SetQueryParam("api-version", "7.0");

                if (!string.IsNullOrEmpty(continuationToken))
                    request = request.SetQueryParam("continuationToken", continuationToken);

                var response = await request
                    .WithHeader("Authorization", $"Basic {base64Pat}")
                    .WithTimeout(TimeSpan.FromSeconds(60))
                    .SendAsync(HttpMethod.Get, cancellationToken: token);

                var prList = await response.GetJsonAsync<AzureReleaseDefinitionResponse>();

                releaseDefinitions = prList.Value;
                

                response.Headers.TryGetFirst("x-ms-continuationtoken", out continuationToken);
            }
            catch (FlurlHttpException ex)
            {
                var msg = await ex.GetResponseStringAsync();
                logger.LogDebug(ex,"[Release Definition Fetch Error] Project: {Project} - {Msg}", project, msg);
                continuationToken = null;
                releaseDefinitions = [];
            }

            foreach (var rd in releaseDefinitions)
            {
                yield return rd;
            }

        } while (!string.IsNullOrEmpty(continuationToken));
    }
        
        
    public static string ComputeHashKey(string projectName, int id)
    {
        var raw = $"{projectName}:{id}";
        var bytes = SHA256.HashData(Encoding.UTF8.GetBytes(raw));
        return Convert.ToHexString(bytes); // .NET 5+
    }
    
    
    public async Task<IApiResponse<bool>> TriggerProcessDefinitionsAsync(
        AzureDeploymentRecordsMessage message,CancellationToken ct = default)
    {
        
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        
        // check if publication exists for given day
        var publicationExists = await repositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .AnyAsync(x => x.PublicationDate.Date == publicationDate.Date, ct);

        if (publicationExists)
        {
            return false.ToOkApiResponse("Already processed for the day");
        }

        const int batchSize = 100;
        var skip = 0;
        var totalDefinitions = await repositoryContext.AzureReleaseDefinitionRepository
            .GetQueryable()
            .AsNoTracking()
            .CountAsync(ct);

        while (skip < totalDefinitions)
        {
            var definitions = await repositoryContext.AzureReleaseDefinitionRepository
                .GetQueryable()
                .AsNoTracking()
                .Skip(skip)
                .Take(batchSize)
                .ToListAsync(ct);

            var releaseDefinitionDeployments = 
                await ProcessDefinitionsAsync(definitions, publicationDate, publicationWeek, ct);
            
            // Save the metrics to the database
            if (releaseDefinitionDeployments.Count > 0 )
            {
                await repositoryContext.AzureReleaseDefinitionDeploymentRepository
                    .AddRangeAsync(releaseDefinitionDeployments, ct);
            }
            
            skip += batchSize;
        }
        
        return true.ToOkApiResponse("Processed successfully");
    }

    public async Task<IApiResponse<PagedResult<GetAzureReleaseDefinitionResponse>>>
        FetchReleaseDefinitionsAsync(FetchAzureDefinitionRequest filter,CancellationToken ct = default)
    {
        logger.LogInformation("[AzureService::FetchReleaseDefinitionsAsync] Fetching Azure DevOps release definitions");
        
        var query = repositoryContext.AzureReleaseDefinitionRepository.GetQueryable().AsNoTracking();

        var pagedResult = await query
            .Where(c => filter.SearchTerm == null ||
                        (c.Name ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()) ||
                         (c.ProjectName ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()))
            .Where(c => filter.ProjectName == null ||
                        (c.ProjectName ?? string.Empty).ToLower().Contains(filter.ProjectName.ToLower()))
            .OrderByDynamic(filter.SortColumn, filter.SortDir == "desc")
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => 
            p.Adapt<GetAzureReleaseDefinitionResponse>()).ToList();

        var response = new PagedResult<GetAzureReleaseDefinitionResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<PagedResult<GetAzureReleaseDeploymentRecords>>> 
        FetchReleaseDeploymentsAsync(FetchAzureDeploymentsRequest filter, CancellationToken ct = default)
    {
        logger.LogInformation("[AzureService::FetchReleaseDefinitionsAsync] Fetching Azure DevOps release definitions");
        
        var query = repositoryContext.AzureReleaseDefinitionDeploymentRepository.GetQueryable().AsNoTracking();

        var pagedResult = await query
            .Where(c => filter.SearchTerm == null ||
                        (c.DefinitionName ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()) ||
                        (c.DefinitionProjectName ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()))
            .Where(c => filter.ProjectName == null ||
                        (c.DefinitionProjectName ?? string.Empty).ToLower().Contains(filter.ProjectName.ToLower()))
            .Where(x => filter.PublicationWeek == null || x.PublicationWeek == filter.PublicationWeek)
            .Where(x => filter.PublicationDate == null || x.PublicationDate.Date == filter.PublicationDate.Value.Date)
            .Where(x => !filter.HasDeploymentRecords || x.TotalDeployments > 0)
            .OrderByDynamic(filter.SortColumn, filter.SortDir == "desc")
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => 
            p.Adapt<GetAzureReleaseDeploymentRecords>()).ToList();

        var response = new PagedResult<GetAzureReleaseDeploymentRecords>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }
    
    public async Task<IApiResponse<GetDeploymentStatistics>> FetchReleaseDeploymentsPublicationStatisticsAsync(
        FetchAzureDeploymentStatisticsRequest filter,
        CancellationToken ct = default)
    {
        var query = BuildPublicationFilteredQuery(filter);

        var deployments = await query.ToListAsync(ct);

        var totalDeployments = deployments.Sum(x => x.TotalDeployments);
        var totalRollbacks = deployments.Sum(x => x.TotalDeploymentsInRolledBack);
        var totalFailed = deployments.Sum(x => x.TotalDeploymentsFailed);
        var totalAzureFailed = deployments.Sum(x => x.TotalDeploymentsFailedAzure);
        var totalSuccessful = deployments.Sum(x => x.TotalDeploymentsSuccess);

        var groupedStats = deployments
            .GroupBy(x => new {x.DefinitionId, x.DefinitionName, x.DefinitionProjectName})
            .Select(g => new DeploymentGroupedStats()
            {
                DefinitionId = g.Key.DefinitionId,
                DefinitionName = g.Key.DefinitionName,
                ProjectName = g.Key.DefinitionProjectName,
                DeploymentCount = g.Sum(x => x.TotalDeployments),
                RollbackCount = g.Sum(x => x.TotalDeploymentsInRolledBack),
                FailedCount = g.Sum(x => x.TotalDeploymentsFailed),
                SuccessfulCount = g.Sum(x => x.TotalDeploymentsSuccess),
                FailedAzureCount = g.Sum(x => x.TotalDeploymentsFailedAzure)
            })
            .ToList();
        
        // Create dictionary of definitionId to definitionName

        var top10DefinitionsByDeployments = groupedStats
            .OrderByDescending(x => x.DeploymentCount)
            .Take(10)
            .ToList();

        var top10DefinitionsByRollbacks = groupedStats
            .OrderByDescending(x => x.RollbackCount)
            .Take(10)
            .ToList();

        var top10DefinitionsByFailures = groupedStats
            .OrderByDescending(x => x.FailedCount)
            .Take(10)
            .ToList();

        var top10DefinitionsBySuccesses = groupedStats
            .OrderByDescending(x => x.SuccessfulCount)
            .Take(10)
            .ToList();

        var result = new GetDeploymentStatistics()
        {
            TotalDeployments = totalDeployments,
            TotalRollbacks = totalRollbacks,
            TotalFailed = totalFailed,
            TotalSuccessful = totalSuccessful,
            TotalAzureFailed = totalAzureFailed,
            TopDefinitionsByDeploymentCount = top10DefinitionsByDeployments,
            TopDefinitionsByRollbackCount = top10DefinitionsByRollbacks,
            TopDefinitionsByFailedCount = top10DefinitionsByFailures,
            TopDefinitionsBySuccessCount = top10DefinitionsBySuccesses
        };

        return result.ToOkApiResponse("Fetched successfully");
    }

    public async Task<IApiResponse<GetDeploymentMetricDetails>> FetchReleaseDeploymentsPublicationDetailsAsync(FetchAzureDeploymentStatisticsRequest filter,
        CancellationToken ct = default)
    {
        var query = BuildPublicationFilteredQuery(filter);
        
        var deployments = await query.ToListAsync(ct);

        var rolledBackDeployments = deployments
            .SelectMany(x => x.RolledBackDeployments)
            .ToList();
        var successfulDeployments = deployments
            .SelectMany(x => x.SuccessfulDeployments)
            .ToList();
        var azureFailedDeployments = deployments
            .SelectMany(x => x.AzureFailedDeployments)
            .ToList();

        var detailsResponse = new GetDeploymentMetricDetails()
        {
            AzureFailedDeployments = azureFailedDeployments,
            SuccessfulDeployments = successfulDeployments,
            RolledBackDeployments = rolledBackDeployments,
            PublicationDate = filter.PublicationDate ?? Miscellaneous.GetCurrentPublicationTargetDay(),
            PublicationWeek = filter.PublicationWeek ??
                              Miscellaneous.GetCurrentPublicationWeek(filter.PublicationDate ??
                                                                      Miscellaneous.GetCurrentPublicationTargetDay()),
        };
        
        return detailsResponse.ToOkApiResponse("Fetched successfully");
    }
    
    
    private IQueryable<AzureReleaseDefinitionDeployment> BuildPublicationFilteredQuery(
        FetchAzureDeploymentStatisticsRequest filter)
    {
        var query = repositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking();

        query = query
            .Where(x => filter.PublicationWeek == null || x.PublicationWeek == filter.PublicationWeek)
            .Where(x => filter.PublicationDate == null || x.PublicationDate.Date == filter.PublicationDate.Value.Date);

        if (string.IsNullOrWhiteSpace(filter.PublicationWeek) && filter.PublicationDate == null)
        {
            var latestDate = Miscellaneous.GetCurrentPublicationTargetDay();
            query = query.Where(x => x.PublicationDate.Date == latestDate.Date);
        }

        return query;
    }



    public async Task<IApiResponse<AzureRepositoryUpdateSummary>> UpdateRepositoriesAsync(CancellationToken ct = default)
    {
        var repositoryMap = await bypassedPrService.FetchAllRepositoriesAsync();
        var notFoundCount = 0;
        var updatedCount = 0;
        List<Repository> updatedRepositories = [];
        List<AzureRepositoryDto> notFoundRepositories = [];

        foreach (var (_, repositories) in repositoryMap)
        {
            foreach (var repository in repositories)
            {
                var dbRepository = await repositoryContext.RepositoryRepository
                    .GetQueryable()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Url == repository.WebUrl, ct);

                if (dbRepository == null)
                {
                    notFoundCount++;
                    notFoundRepositories.Add(repository);
                    logger.LogWarning("[AzureService::UpdateRepositoriesAsync] Repository not found: {Repository}", repository.WebUrl);
                    continue;
                }
                
                dbRepository.AzureRepositoryId = repository.AzureRepoId;
                
                updatedRepositories.Add(dbRepository);
                updatedCount++;
                
            }
        }
        
        if (updatedCount > 0)
        {
            await repositoryContext.RepositoryRepository.UpdateRangeAsync(updatedRepositories, ct);
        }

        var response = new AzureRepositoryUpdateSummary()
        {
            NotFoundCount = notFoundCount,
            UpdatedCount = updatedCount,
            UpdatedRepositories = updatedRepositories,
            NotFoundRepositories = notFoundRepositories
        };
        
        return response.ToOkApiResponse("Fetched successfully");
    }

    public async Task<IApiResponse<bool>> DeleteAzureDeploymentPublicationAsync(DateTime publicationDate, CancellationToken ct)
    {
        logger.LogInformation("[AzureService::DeleteAzureDeploymentPublicationAsync] Deleting Azure deployment publication for date: {PublicationDate}", publicationDate);
        
        var azureReleaseDefinitionDeployments = await repositoryContext.AzureReleaseDefinitionDeploymentRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.PublicationDate == publicationDate.Date)
            .ToListAsync(ct);
        
        if (azureReleaseDefinitionDeployments.Count == 0)
            return ApiResponse<bool>.Default.ToNotFoundApiResponse("Azure Deployment Publication not found");
        
        await repositoryContext.AzureReleaseDefinitionDeploymentRepository.DeleteRangeAsync(azureReleaseDefinitionDeployments, ct);
        return true.ToOkApiResponse("Azure Deployment Publication deleted successfully");
    }


    public void CalculateRollbacks(List<AzureReleaseDeployment> deployments, List<AzureReleaseDeployment> deploymentsToday)
    {
        foreach (var deployment in deploymentsToday)
        {
            if (string.IsNullOrWhiteSpace(deployment.SourceVersion) || string.IsNullOrWhiteSpace(deployment.ReleaseEnvironment?.Name))
                continue;

            var wasUsedBefore = deployments
                .Any(d =>
                    d.CompletedOn < deployment.CompletedOn &&
                    d.SourceVersion == deployment.SourceVersion &&
                    d.ReleaseEnvironment?.Name == deployment.ReleaseEnvironment?.Name);

            deployment.IsRollBack = wasUsedBefore;
        }
    }
    
    private async Task<List<AzureReleaseDefinitionDeployment>> ProcessDefinitionsAsync(
        List<AzureReleaseDefinition> definitions,
        DateTime publicationDate,
        string publicationWeek,
        CancellationToken ct)
    {
        var releaseDefinitionDeployments = new List<AzureReleaseDefinitionDeployment>();

        foreach (var definition in definitions)
        {
                var deploymentsToday = new List<AzureReleaseDeployment>();
                var deployments = new List<AzureReleaseDeployment>();
                
                await foreach (var deployment in FetchAzureReleaseDeployments(definition.ProjectName!, definition.DefinitionId, ct))
                {
                    var sourceVersion = deployment.Release.Artifacts?.FirstOrDefault()?.DefinitionReference.SourceVersion?.Id;
                    var deploymentRecord = new AzureReleaseDeployment()
                    {
                        ReleaseId = deployment.Id,
                        Release = deployment.Release,
                        ReleaseDefinition = deployment.ReleaseDefinition,
                        ReleaseEnvironment = deployment.ReleaseEnvironment,
                        ProjectReference = deployment.ProjectReference,
                        DefinitionEnvironmentId = deployment.DefinitionEnvironmentId,
                        Attempt = deployment.Attempt,
                        Reason = deployment.Reason,
                        DeploymentStatus = deployment.DeploymentStatus,
                        OperationStatus = deployment.OperationStatus,
                        RequestedBy = deployment.RequestedBy,
                        RequestedFor = deployment.RequestedFor,
                        QueuedOn = deployment.QueuedOn,
                        StartedOn = deployment.StartedOn,
                        CompletedOn = deployment.CompletedOn,
                        LastModifiedOn = deployment.LastModifiedOn,
                        LastModifiedBy = deployment.LastModifiedBy,
                        Conditions = deployment.Conditions,
                        PreDeployApprovals = deployment.PreDeployApprovals,
                        PostDeployApprovals = deployment.PostDeployApprovals,
                        SourceVersion = sourceVersion
                    };
                    
                    deployments.Add(deploymentRecord);
                    
                    if (deployment.CompletedOn.Date == publicationDate.Date)
                    {
                        deploymentsToday.Add(deploymentRecord);
                    }
                }
                
                CalculateRollbacks(deployments, deploymentsToday);
                    
                var total = deploymentsToday.Count;
                var totalFailedFlag = deploymentsToday.Count(d => d.DeploymentStatus == "failed");
                var totalRollbacks = deploymentsToday.Count(d => d.IsRollBack);               
                var totalSuccess   = total - totalRollbacks;

                var metricsModel = new AzureReleaseDefinitionDeployment
                {
                    PublicationDate = publicationDate,
                    PublicationWeek = publicationWeek,
                    DefinitionId = definition.DefinitionId.ToString(),
                    DefinitionName = definition.Name,
                    DefinitionProjectName = definition.ProjectName!,
                    CompositeDefinitionId = definition.CompositeDefinitionId!,
                    TotalDeployments = total,
                    TotalDeploymentsSuccess = totalSuccess,
                    TotalDeploymentsFailed = totalRollbacks,
                    TotalDeploymentsFailedAzure = totalFailedFlag,
                    TotalDeploymentsInRolledBack = totalRollbacks,
                    ReleaseDeployments = deploymentsToday,
                    SuccessfulDeployments = deploymentsToday.Where(d => !d.IsRollBack).ToList(),
                    RolledBackDeployments = deploymentsToday.Where(d => d.IsRollBack).ToList(),
                    AzureFailedDeployments = deploymentsToday.Where(d => d.DeploymentStatus == "failed").ToList()
                };
                    
                releaseDefinitionDeployments.Add(metricsModel);
        }

        return releaseDefinitionDeployments;
    }
    
    
    
    public async IAsyncEnumerable<ReleaseDeployment> FetchAzureReleaseDeployments(
        string project,
        int definitionId,
        [EnumeratorCancellation] CancellationToken token)
    {
        string? continuationToken = null;
        var base64Pat = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{_accessToken}"));

        do
        {
            List<ReleaseDeployment> releaseDeployments;

            try
            {
                var request = $"{_releaseUrl}/{_organization}/{project}/_apis/release/deployments"
                    .SetQueryParam("$top", 100)
                    .SetQueryParam("definitionId", definitionId)
                    .SetQueryParam("api-version", "7.0");

                if (!string.IsNullOrEmpty(continuationToken))
                    request = request.SetQueryParam("continuationToken", continuationToken);

                var response = await request
                    .WithHeader("Authorization", $"Basic {base64Pat}")
                    .WithTimeout(TimeSpan.FromSeconds(60))
                    .SendAsync(HttpMethod.Get, cancellationToken: token);

                var prList = await response.GetJsonAsync<AzureReleaseDeploymentResponse>();

                releaseDeployments = prList.Value.Where(rd=>rd.ReleaseEnvironment.Name == "Production").ToList();
                

                response.Headers.TryGetFirst("x-ms-continuationtoken", out continuationToken);
            }
            catch (FlurlHttpException ex)
            {
                var msg = await ex.GetResponseStringAsync();
                logger.LogDebug(ex,"[Release Definition Fetch Error] Project: {Project} - {Msg}", project, msg);
                continuationToken = null;
                releaseDeployments = [];
            }

            foreach (var rd in releaseDeployments)
            {
                yield return rd;
            }

        } while (!string.IsNullOrEmpty(continuationToken));
    }
}