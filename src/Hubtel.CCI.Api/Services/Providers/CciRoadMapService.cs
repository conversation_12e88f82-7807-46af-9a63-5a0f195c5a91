using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMapRecord;
using Hubtel.CCI.Api.Dtos.Responses.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Responses.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Responses.CciRoadMapRecord;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class CciRoadMapService(ILogger<CciRoadMapService> logger,IRepositoryContext repositoryContext): ICciRoadMapService
{
    public async Task<IApiResponse<GetCciRoadMapResponse>> AddCciRoadMapAsync(CreateCciRoadMapRequest request, CancellationToken ct = default)
    {
        logger.LogDebug("[CciRoadMapService::AddCciRoadMapAsync] Adding new cciRoadMap with request: {Request}",
            request.Serialize());

        var cciRoadMap = request.Adapt<CciRoadMap>();
        
        cciRoadMap.CreatedAt = DateTime.UtcNow;
        cciRoadMap.UpdatedAt = DateTime.UtcNow;
        
        // check if product team exists
        var productTeam = await repositoryContext.ProductTeamRepository
            .GetQueryable().FirstOrDefaultAsync(x => x.Id == cciRoadMap.ProductTeamId, ct);
        if (productTeam == null)
            return IApiResponse<GetCciRoadMapResponse>.Default.ToBadRequestApiResponse("Product team does not exist");
        
        // check if repository exists
        var repository = await repositoryContext.RepositoryRepository
            .GetQueryable().FirstOrDefaultAsync(x => x.Id == cciRoadMap.RepositoryId, ct);
        if (repository == null)
            return IApiResponse<GetCciRoadMapResponse>.Default.ToBadRequestApiResponse("Repository does not exist");
        
        // check if engineer exists
        var engineer = await repositoryContext.EngineerRepository
            .GetQueryable().FirstOrDefaultAsync(x => x.Id == cciRoadMap.EngineerAssignedId, ct);
        if (engineer == null)
            return IApiResponse<GetCciRoadMapResponse>.Default.ToBadRequestApiResponse("Engineer does not exist");
        
        // get product team cci repository score
        var repositorySonarQubeKey = repository?.SonarQubeKey!;
        var cciScores = await repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.RepositorySonarQubeKey == repositorySonarQubeKey)
            .OrderByDescending(c => c.CreatedAt)
            .ProjectToType<GetCciRepositoryScoreResponse>()
            .FirstOrDefaultAsync(ct);

        // proceed to set the product team name, repository name and engineer name
        cciRoadMap.ProductTeamName = productTeam?.Name;
        cciRoadMap.RepositoryName = repository?.Name;
        cciRoadMap.EngineerName = engineer?.Name;
        cciRoadMap.Bugs = cciScores?.Bugs;
        cciRoadMap.Coverage = cciScores?.Coverage;
        cciRoadMap.CodeSmells = cciScores?.CodeSmells;
        cciRoadMap.Vulnerabilities = cciScores?.Vulnerabilities;
        cciRoadMap.DuplicatedLines = cciScores?.DuplicatedLinesDensity;
        cciRoadMap.SecurityHotspots = cciScores?.SecurityHotspots;
        
        // For record keeping
        cciRoadMap.StartingBugs = cciScores?.Bugs;
        cciRoadMap.StartingCodeSmells = cciScores?.CodeSmells;
        cciRoadMap.StartingCoverage = cciScores?.Coverage;
        cciRoadMap.StartingVulnerabilities = cciScores?.Vulnerabilities;
        cciRoadMap.StartingDuplicatedLines = cciScores?.DuplicatedLinesDensity;
        cciRoadMap.StartingSecurityHotspots = cciScores?.SecurityHotspots;
        
        var savedCount = await repositoryContext.CciRoadMapRepository.AddAsync(cciRoadMap, ct);
        
        if (savedCount > 0) return cciRoadMap.Adapt<GetCciRoadMapResponse>().ToCreatedApiResponse();
        
        logger.LogError(
            "[CciRoadMapService: AddCciRoadMapAsync] Could not create CciRoadMap! Please try again, Data: {CciRoadMap}",
            cciRoadMap.Serialize());
        
        return ApiResponse<GetCciRoadMapResponse>.Default.ToFailedDependencyApiResponse(
            "Could not create CciRoadMap! Please try again");
    }

    
    /// <summary>
    /// Asynchronously gets a list of cciRepositoryScores with pagination.
    /// </summary>
    /// <param name="filter">The filter to apply to the repository list. This includes the search term, page index, and page size.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a paged list of cciRepositoryScores.</returns>
    public async Task<IApiResponse<PagedResult<GetCciRoadMapResponse>>> GetCciRoadMapsAsync(SearchCciRoadMapRequest filter, CancellationToken ct = default)
    {
        logger.LogDebug(
            "[CciRoadMapService::GetCciRoadMapsAsync] Received Request to get CciRoadMaps => RequestPayload => {Request}",
            filter.Serialize());

        var query = repositoryContext.CciRoadMapRepository.GetQueryable().AsNoTracking();

        var pagedResult = await query
            .Where(c => filter.SearchTerm == null ||
                        (c.RepositoryName ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()))
            .Where(x => filter.ProductTeamId == null || x.ProductTeamId == filter.ProductTeamId)
            .OrderByDescending(x => x.CreatedAt).GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => p.Adapt<GetCciRoadMapResponse>()).ToList();

        var response = new PagedResult<GetCciRoadMapResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<bool>> DeleteCciRoadMapAsync(string id, CancellationToken ct = default)
    {
        logger.LogDebug(
            "[CciRoadMapService::DeleteCciRoadMapAsync] Received Request to delete CciRoadMap with Id => {Id}",
            id);

        var query = await repositoryContext.CciRoadMapRepository.FindOneAsync(r=>r.Id == id, ct);
        
        if (query == null)
            return ApiResponse<bool>.Default.ToNotFoundApiResponse("CciRoadMap not found");
        
        var deleted = await repositoryContext.CciRoadMapRepository.DeleteAsync(query, ct);
        
        return deleted > 0 ? 
            true.ToOkApiResponse("CciRoadMap deleted successfully") 
            : ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Could not delete CciRoadMap");
    }

    public async Task<IApiResponse<GetCciRoadMapRecordResponse>> AddCciRoadMapV2Async(CreateCciRoadMapRecordRequest request, CancellationToken ct = default)
    {
        logger.LogDebug("[CciRoadMapService::AddCciRoadMapV2Async] Adding new cciRoadMap with request: {Request}",
            request.Serialize());
        
        var cciRoadMap = request.Adapt<CciRoadMapRecord>();
        
        cciRoadMap.CreatedAt = DateTime.UtcNow;
        cciRoadMap.UpdatedAt = DateTime.UtcNow;

        var productTeams = await repositoryContext.ProductTeamRepository
            .GetQueryable()
            .AsNoTracking()
            .ToListAsync(ct);

        var productTeam = productTeams
            .Where(pg => pg.Repositories.Items.Any(r => r.Id == cciRoadMap.Repository.Id))
            .Select(pg => new { pg.Id, pg.Name })
            .FirstOrDefault();
        
        if (productTeam == null)
            return IApiResponse<GetCciRoadMapRecordResponse>.Default.ToBadRequestApiResponse("Product team does not exist");
        
        // check if repository exists
        var repository = await repositoryContext.RepositoryRepository
            .GetQueryable().FirstOrDefaultAsync(x => x.Id == cciRoadMap.Repository.Id, ct);
        
        if (repository == null)
            return IApiResponse<GetCciRoadMapRecordResponse>.Default.ToBadRequestApiResponse("Repository does not exist");
        
        
        var repositorySonarQubeKey = repository.SonarQubeKey;
        var cciScores = await repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(c => c.RepositorySonarQubeKey == repositorySonarQubeKey)
            .OrderByDescending(c => c.CreatedAt)
            .ProjectToType<GetCciRepositoryScoreResponse>()
            .FirstOrDefaultAsync(ct);
        
        // proceed to set the product team name, repository name and engineer name
        cciRoadMap.Product.Name = productTeam.Name;
        cciRoadMap.Repository.Name = repository?.Name!;
        cciRoadMap.Repository.Type = repository?.Type!;
        cciRoadMap.OverallQuality.Start = (decimal)cciScores?.FinalAverage!;
        cciRoadMap.OverallQuality.Current = (decimal)cciScores?.FinalAverage!;
        
        PopulateCciRoadMapRecords(cciScores!, cciRoadMap);
        
        var savedCount = await repositoryContext.CciRoadMapRecordRepository.AddAsync(cciRoadMap, ct);
        
        if (savedCount > 0) return cciRoadMap.Adapt<GetCciRoadMapRecordResponse>().ToCreatedApiResponse();
        
        logger.LogError(
            "[CciRoadMapService: AddCciRoadMapV2Async] Could not create CciRoadMap! Please try again, Data: {CciRoadMap}",
            cciRoadMap.Serialize());
        
        return ApiResponse<GetCciRoadMapRecordResponse>.Default.ToFailedDependencyApiResponse(
            "Could not create CciRoadMap! Please try again");

    }

    public async Task<IApiResponse<GetCciRoadMapRecordResponse>> GetCciRoadMapRecordByIdAsync(string id, CancellationToken ct = default)
    {
        logger.LogDebug("[CciRoadMapService::GetCciRoadMapRecordByIdAsync] Received Request to get CciRoadMapRecord with Id => {Id}",
            id);

        var roadMapRecord = await repositoryContext.CciRoadMapRecordRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == id, ct);
        
        if (roadMapRecord == null)
            return ApiResponse<GetCciRoadMapRecordResponse>.Default.ToNotFoundApiResponse("CciRoadMapRecord not found");
        
        var response = roadMapRecord.Adapt<GetCciRoadMapRecordResponse>();
        
        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<PagedResult<GetCciRoadMapRecordResponse>>> GetCciRoadMapRecordsV2Async(SearchCciRoadMapRequest filter, CancellationToken ct = default)
    {
        logger.LogDebug(
            "[CciRoadMapService::GetCciRoadMapRecordsV2Async] Received Request to get CciRoadMapRecords => RequestPayload => {Request}",
            filter.Serialize());
        
        var query = repositoryContext.CciRoadMapRecordRepository.GetQueryable().AsNoTracking();
        
        var pagedResult = await query
            .Where(c => filter.SearchTerm == null ||
                        (c.Repository.Name ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()))
            .Where(x => filter.ProductTeamId == null || x.Product.Id == filter.ProductTeamId)
            .OrderByDescending(x => x.CreatedAt).GetPagedAsync(filter.PageIndex, filter.PageSize, ct);
        
        var results = pagedResult.Results.Select(p => p.Adapt<GetCciRoadMapRecordResponse>()).ToList();
        
        var response = new PagedResult<GetCciRoadMapRecordResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };
        
        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<bool>> DeleteCciRoadMapRecordAsync(string id, CancellationToken ct = default)
    {
        logger.LogDebug("[CciRoadMapService::DeleteCciRoadMapRecordAsync] Received Request to delete CciRoadMapRecord with Id => {Id}",
            id);
        
        var query = await repositoryContext.CciRoadMapRecordRepository
            .FindOneAsync(r=>r.Id == id, ct);
            
            
        if (query == null)
            return ApiResponse<bool>.Default.ToNotFoundApiResponse("CciRoadMapRecord not found");
        
        var deleted = await repositoryContext.CciRoadMapRecordRepository.DeleteAsync(query, ct);
        
        return deleted > 0 ? 
            true.ToOkApiResponse("CciRoadMapRecord deleted successfully") 
            : ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Could not delete CciRoadMapRecord");
    }


    private void PopulateCciRoadMapRecords(GetCciRepositoryScoreResponse cciRepositoryScoreResponse,
        CciRoadMapRecord cciRoadMapRecord)
    {
        logger.LogDebug(
            "[CciRoadMapService::PopulateCciRoadMapRecords] Populating CciRoadMapRecord with CciRepositoryScoreResponse: {CciRepositoryScoreResponse}",
            cciRepositoryScoreResponse.Serialize());

        UpdateMetric(cciRoadMapRecord.Bugs, cciRepositoryScoreResponse.Bugs);
        UpdateMetric(cciRoadMapRecord.CodeSmells, cciRepositoryScoreResponse.CodeSmells);
        UpdateMetric(cciRoadMapRecord.Coverage, cciRepositoryScoreResponse.Coverage);
        UpdateMetric(cciRoadMapRecord.DuplicatedLines, cciRepositoryScoreResponse.DuplicatedLinesDensity);
        UpdateMetric(cciRoadMapRecord.Vulnerabilities, cciRepositoryScoreResponse.Vulnerabilities);
        UpdateMetric(cciRoadMapRecord.SecurityHotspots, cciRepositoryScoreResponse.SecurityHotspots);
    }
    
    
    private static void UpdateMetric(MetricData? metric, decimal? value)
    {
        if (metric == null || !value.HasValue) return;
        var intValue = value.Value;
        metric.StartValue = intValue;
        metric.CurrentValue = intValue;
    }
}