using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ReportDeliveryRule;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class ReportDeliveryRuleService(ILogger<ReportDeliveryRuleService> logger, IRepositoryContext repositoryContext): IReportDeliveryRuleService
{
    public async Task<IApiResponse<ReportDeliveryRule>> GetReportDeliveryRuleAsync(string id, CancellationToken ct = default)
    {
        logger.LogInformation("Retrieving report delivery rule with ID: {Id}", id);
        var rule = await repositoryContext.ReportDeliveryRuleRepository.GetQueryable().AsNoTracking()
            .Where(rule => rule.Id.Equals(id)).FirstOrDefaultAsync(cancellationToken: ct);
        
        if (rule == null)
            return ApiResponse<ReportDeliveryRule>.Default.ToNotFoundApiResponse("Report delivery rule not found.");
        
        logger.LogInformation("Successfully retrieved report delivery rule with ID: {Id}", id);
        
        return rule.ToOkApiResponse("Report delivery rule retrieved successfully.");
    }

    public async Task<IApiResponse<ReportDeliveryRule>> CreateReportDeliveryRuleAsync(CreateReportDeliveryRule rule, CancellationToken ct = default)
    {
        logger.LogInformation("Creating a new report delivery rule with details: {@Rule}", rule);
        
        var existingProductGroup = await repositoryContext.ProductGroupRepository.GetQueryable()
            .AsNoTracking()
            .Where(pg => pg.Id.Equals(rule.ProductGroupId))
            .FirstOrDefaultAsync(ct);
        
        if (existingProductGroup == null)
            return ApiResponse<ReportDeliveryRule>.Default
                .ToNotFoundApiResponse("Product group not found. Please provide a valid ProductGroupId.");

        var newRule = rule.Adapt<ReportDeliveryRule>();
        newRule.ProductGroupName = existingProductGroup.GroupName;
        
        var added = await repositoryContext.ReportDeliveryRuleRepository.AddAsync(newRule, ct);

        if (added == 0)
        {
            return ApiResponse<ReportDeliveryRule>.Default
                .ToFailedDependencyApiResponse("Failed to create report delivery rule. Please try again.");
        }
        
        logger.LogInformation("Successfully created report delivery rule with ID: {Id}", newRule.Id);
        
        return newRule.ToCreatedApiResponse("Report delivery rule created successfully.");
    }

    public async Task<IApiResponse<PagedResult<ReportDeliveryRule>>> GetReportDeliveryRulesAsync(SearchFilter filter, CancellationToken ct = default)
    {
        logger.LogInformation("Retrieving all report delivery rules.");
        
        var result = await repositoryContext.ReportDeliveryRuleRepository.GetQueryable()
            .AsNoTracking()
            .OrderByDescending(rule => rule.CreatedAt)
            .AsQueryable()
            .Where(rule=> string.IsNullOrWhiteSpace(filter.SearchTerm) || rule.RecipientEmail.Contains(filter.SearchTerm))
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        return result.ToOkApiResponse("Report delivery rules retrieved successfully");
    }

    public async Task<IApiResponse<ReportDeliveryRule>> DeleteReportDeliveryRuleAsync(string id, CancellationToken ct = default)
    {
        logger.LogInformation("Retrieving report delivery rule with ID: {Id}", id);
        var rule = await repositoryContext.ReportDeliveryRuleRepository.GetQueryable().AsNoTracking()
            .Where(rule => rule.Id.Equals(id)).FirstOrDefaultAsync(cancellationToken: ct);
        
        if (rule == null)
            return ApiResponse<ReportDeliveryRule>.Default.ToNotFoundApiResponse("Report delivery rule not found.");
        
        var deleted = await repositoryContext.ReportDeliveryRuleRepository.DeleteAsync(rule, ct);

        if (deleted != 0) return rule.ToOkApiResponse("Report delivery rule deleted successfully.");
        logger.LogError("Failed to delete report delivery rule with ID: {Id}", id);
        return ApiResponse<ReportDeliveryRule>.Default.ToFailedDependencyApiResponse("Failed to delete report delivery rule. Please try again.");

    }
}