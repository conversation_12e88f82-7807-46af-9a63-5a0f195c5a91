using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.Repository;
using Hubtel.CCI.Api.Dtos.Responses.Repository;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

/// <summary>
///     Service responsible for all related operations for repository entity.
/// </summary>
public class ArsenalService : IArsenalService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger _logger;
    private readonly IActorService<MainActor> _mainActor;

    public ArsenalService(ILogger<ArsenalService> logger, IRepositoryContext repositoryContext,
        IActorService<MainActor> mainActor)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
        _mainActor = mainActor;
    }


    /// <summary>
    /// Asynchronously gets a list of repositories with pagination.
    /// </summary>
    /// <param name="filter">The filter to apply to the repository list. This includes the search term, page index, and page size.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a paged list of repositories.</returns>
    public async Task<IApiResponse<PagedResult<GetRepositoryResponse>>> GetRepositoriesAsync(SearchFilter filter,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryService: GetRepositoriesAsync] Received Request to get Repositories => RequestPayload => {Request}",
            filter.Serialize());

        var query = _repositoryContext.RepositoryRepository.GetQueryable().AsNoTracking();

        if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            query = query.Where(c => c.Name.ToLower().Contains(filter.SearchTerm.ToLower()));

        var pagedResult = await query.OrderByDescending(x => x.CreatedAt)
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(p => p.Adapt<GetRepositoryResponse>()).ToList();

        var response = new PagedResult<GetRepositoryResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously retrieves a specific repository by their ID.
    /// </summary>
    /// <param name="id">The ID of the repository to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved repository, or a not found response if no repository with the provided ID exists.</returns>
    public async Task<IApiResponse<GetRepositoryResponse>> GetRepositoryAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryService: GetRepositoryAsync] Received Request to get Repository with ID: {Id}", id);

        var result = await _repositoryContext.RepositoryRepository
            .GetQueryable()
            .AsNoTracking()
            .ProjectToType<GetRepositoryResponse>()
            .FirstOrDefaultAsync(c => c.Id == id || c.SonarQubeKey == id, ct);

        return result is null ? result.ToNotFoundApiResponse() : result.ToOkApiResponse();
    }


    /// <summary>
    /// Asynchronously adds a new repository.
    /// </summary>
    /// <param name="request">The request to create a new repository. This includes the repository's details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the created repository.</returns>
    public async Task<IApiResponse<GetRepositoryResponse>> AddRepositoryAsync(CreateRepositoryRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryService: AddRepositoryAsync] Received Request to create Repository => RequestPayload => {Request}",
            request.Serialize());

        var existingRepository = await _repositoryContext.RepositoryRepository
            .FindOneAsync(c => c.SonarQubeKey == request.SonarQubeKey || c.Url == request.Url, ct);

        if (existingRepository != null)
            return ApiResponse<GetRepositoryResponse>.Default.ToBadRequestApiResponse(
                "Repository with the same SonarQubeKey already exists or Url already exists"
            );

        var repository = request.Adapt<Repository>();

        repository.CreatedAt = DateTime.UtcNow;
        repository.UpdatedAt = DateTime.UtcNow;

        var savedCount = await _repositoryContext.RepositoryRepository.AddAsync(repository, ct);

        if (savedCount <= 0)
            return ApiResponse<GetRepositoryResponse>.Default.ToFailedDependencyApiResponse(
                "Could not create Repository! Please try again"
            );

        return repository.Adapt<GetRepositoryResponse>().ToCreatedApiResponse();
    }

    /// <summary>
    /// Asynchronously updates an existing repository.
    /// </summary>
    /// <param name="id">The ID of the repository to update.</param>
    /// <param name="request">The request to update the repository. This includes the new details of the repository.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the repository was updated successfully.</returns>
    public async Task<IApiResponse<bool>> UpdateRepositoryAsync(string id, UpdateRepositoryRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[RepositoryService:UpdateRepositoryAsync] Updating repository with ID: {Id} and RequestPayload => {Request}",
            id, request.Serialize()
        );

        var existingRepository = await _repositoryContext.RepositoryRepository.GetByIdAsync(id, ct);

        if (existingRepository is null)
        {
            _logger.LogWarning("Repository not found with ID: {Id}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingRepository.Name = request.Name ?? existingRepository.Name;
        existingRepository.Description = request.Description ?? existingRepository.Description;
        existingRepository.FrameworkUpgradeComputation =
            request.FrameworkUpgradeComputation ?? existingRepository.FrameworkUpgradeComputation;
        existingRepository.SemanticScoreComputation =
            request.SemanticScoreComputation ?? existingRepository.SemanticScoreComputation;
        existingRepository.Status = request.Status;
        if (request.Url != null && request.Url != existingRepository.Url)
        {
            var existingRepositoryWithUrl =
                await _repositoryContext.RepositoryRepository.FindOneAsync(c => c.Url == request.Url, ct);
            if (existingRepositoryWithUrl != null)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Repository with the same Url already exists");

            existingRepository.Url = request.Url;
        }

        existingRepository.Type = request.Type ?? existingRepository.Type;

        if (request.SonarQubeKey != null && request.SonarQubeKey != existingRepository.SonarQubeKey)
        {
            var existingRepositoryWithSonarQubeKey =
                await _repositoryContext.RepositoryRepository.FindOneAsync(c => c.SonarQubeKey == request.SonarQubeKey,
                    ct);
            if (existingRepositoryWithSonarQubeKey != null)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Repository with the same SonarQubeKey already exists");

            existingRepository.SonarQubeKey = request.SonarQubeKey;
        }

        existingRepository.UpdatedAt = DateTime.UtcNow;

        _logger.LogDebug(
            "Updating repository in the repository: {@Repository}",
            existingRepository.Serialize()
        );

        var updated = await _repositoryContext.RepositoryRepository.UpdateAsync(existingRepository, ct) > 0;

        if (!updated) return updated.ToNotFoundApiResponse();

        await _mainActor.Tell(new CascadeRepositoryUpdateMessage(existingRepository), ActorRefs.Nobody);
        return updated.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes a specific repository by marking it as deleted.
    /// </summary>
    /// <param name="id">The ID of the repository to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the repository was marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteRepositoryAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug("Deleting Repository with ID: {StockModelId}", id);

        var existingDocument = await _repositoryContext.RepositoryRepository.GetByIdAsync(id, ct);

        if (existingDocument == null)
        {
            _logger.LogDebug("Repository not found with ID: {RepositoryId}", id);

            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingDocument.IsDeleted = true;

        _logger.LogDebug("Deleting repository in the repository: {@Repository}", existingDocument.Serialize());

        var deletedCount = await _repositoryContext.RepositoryRepository.DeleteAsync(existingDocument, ct);

        var deleted = deletedCount > 0;

        _logger.LogDebug("repository deleted: {Deleted}", deleted);

        if (!deleted) return deleted.ToNotFoundApiResponse();
        await _mainActor.Tell(new CascadeRepositoryDeleteMessage(existingDocument), ActorRefs.Nobody);
        return deleted.ToOkApiResponse();
    }
}