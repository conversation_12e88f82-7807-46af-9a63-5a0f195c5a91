using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Dtos.Responses.ProductTeam;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

/// <summary>
///     Service responsible for all related operations for product team entity.
/// </summary>
public class ProductTeamService : IProductTeamService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger _logger;
    private readonly IActorService<MainActor> _mainActor;

    public ProductTeamService(ILogger<ProductTeamService> logger, IRepositoryContext repositoryContext,
        IActorService<MainActor> mainActor)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
        _mainActor = mainActor;
    }

    /// <summary>
    /// Asynchronously gets a list of productTeams with pagination.
    /// </summary>
    /// <param name="filter">The filter to apply to the engineer list. This includes the search term, page index, and page size.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a paged list of productTeams.</returns>
    public async Task<IApiResponse<PagedResult<GetProductTeamResponse>>> GetProductTeamsAsync(SearchFilter filter,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductTeamService: GetProductTeamsAsync] Received Request to get ProductTeams => RequestPayload => {Request}",
            filter.Serialize());

        var query = _repositoryContext.ProductTeamRepository.GetQueryable().AsNoTracking();

        if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            query = query.Where(c => c.Name.ToLower().Contains(filter.SearchTerm.ToLower()));

        var pagedResult = await query.OrderByDescending(x => x.CreatedAt)
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(TransformToProductTeamResponse()).ToList();

        var response = new PagedResult<GetProductTeamResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously retrieves a specific productTeam by their ID.
    /// </summary>
    /// <param name="id">The ID of the productTeam to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved productTeam, or a not found response if no productTeam with the provided ID exists.</returns>
    public async Task<IApiResponse<GetProductTeamResponse>> GetProductTeamAsync(string id,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductTeamService: GetProductTeamAsync] Received Request to get ProductTeam with ID: {Id}", id);

        var result = await _repositoryContext.ProductTeamRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == id, ct);

        return result is null
            ? result.Adapt<GetProductTeamResponse>().ToNotFoundApiResponse()
            : TransformToProductTeamResponse()(result).ToOkApiResponse();
    }


    /// <summary>
    /// Asynchronously adds a new productTeam.
    /// </summary>
    /// <param name="request">The request to create a new productTeam. This includes the productTeam's details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the created productTeam.</returns>
    public async Task<IApiResponse<GetProductTeamResponse>> AddProductTeamAsync(CreateProductTeamRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductTeamService: AddProductTeamAsync] Received Request to create ProductTeam => RequestPayload => {Request}",
            request.Serialize());

        var existingProductTeam = await _repositoryContext.ProductTeamRepository.GetQueryable()
            .FirstOrDefaultAsync(c => c.Name == request.Name, ct);

        if (existingProductTeam is not null)
            return ApiResponse<GetProductTeamResponse>.Default.ToBadRequestApiResponse(
                "ProductTeam with the same name already exists!"
            );

        var productTeam = request.Adapt<ProductTeam>();

        await TransformAddProductTeamRequestToProductTeam(request, productTeam, ct);

        productTeam.CreatedAt = DateTime.UtcNow;
        productTeam.UpdatedAt = DateTime.UtcNow;

        var savedCount = await _repositoryContext.ProductTeamRepository.AddAsync(productTeam, ct);

        if (savedCount <= 0)
            return ApiResponse<GetProductTeamResponse>.Default.ToFailedDependencyApiResponse(
                "Could not create ProductTeam! Please try again"
            );

        return TransformToProductTeamResponse()(productTeam).ToCreatedApiResponse();
    }

    /// <summary>
    /// Asynchronously updates an existing productTeam.
    /// </summary>
    /// <param name="id">The ID of the productTeam to update.</param>
    /// <param name="request">The request to update the productTeam. This includes the new details of the productTeam.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the productTeam was updated successfully.</returns>
    public async Task<IApiResponse<bool>> UpdateProductTeamAsync(string id, UpdateProductTeamRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductTeamService:UpdateProductTeamAsync] Updating productTeam with ID: {Id} and RequestPayload => {Request}",
            id, request.Serialize()
        );

        var existingProductTeam = await _repositoryContext.ProductTeamRepository.GetByIdAsync(id, ct);

        if (existingProductTeam is null)
        {
            _logger.LogWarning("ProductTeam not found with ID: {Id}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        if (request.Name != null && existingProductTeam.Name != request.Name)
        {
            var existingProductTeamWithSameName = await _repositoryContext.ProductTeamRepository.GetQueryable()
                .FirstOrDefaultAsync(c => c.Name == request.Name, ct);

            if (existingProductTeamWithSameName is not null)
                return ApiResponse<bool>.Default.ToBadRequestApiResponse(
                    "ProductTeam with the same name already exists!"
                );
        }

        existingProductTeam.Name = request.Name ?? existingProductTeam.Name;
        existingProductTeam.Status = !string.IsNullOrWhiteSpace(request.Status) ? request.Status : existingProductTeam.Status;
        existingProductTeam.Tag = !string.IsNullOrWhiteSpace(request.Tag) ? request.Tag : existingProductTeam.Tag;
        existingProductTeam.Ranking = request.Ranking;

        await TransformUpdateRequestToProductTeam(request, existingProductTeam, ct);

        existingProductTeam.UpdatedAt = DateTime.UtcNow;

        _logger.LogDebug(
            "Updating productTeam in the repository: {ProductTeam}",
            existingProductTeam.Serialize()
        );

        var updated = await _repositoryContext.ProductTeamRepository.UpdateAsync(existingProductTeam, ct) > 0;

        if (!updated) return updated.ToNotFoundApiResponse();
        await _mainActor.Tell(new CascadeProductTeamUpdateMessage(existingProductTeam), ActorRefs.Nobody);
        return updated.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously deletes a specific product team by marking it as deleted.
    /// </summary>
    /// <param name="id">The ID of the product team to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the product team was marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteProductTeamAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug("[ProductTeamService:DeleteProductTeamAsync] Deleting productTeam with ID: {Id}", id);
        var existingDocument = await _repositoryContext.ProductTeamRepository.GetByIdAsync(id, ct);

        if (existingDocument == null)
        {
            _logger.LogDebug("ProductTeam not found with ID: {ProductTeamId}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingDocument.IsDeleted = true;

        _logger.LogDebug("Updating productTeam in the repository: {ProductTeam}", existingDocument.Serialize());

        var updatedCount = await _repositoryContext.ProductTeamRepository.DeleteAsync(existingDocument, ct);

        var deleted = updatedCount > 0;

        _logger.LogDebug("productTeam deleted: {Deleted}", deleted);

        if (!deleted) return deleted.ToNotFoundApiResponse();
        await _mainActor.Tell(new CascadeProductTeamDeleteMessage(existingDocument), ActorRefs.Nobody);
        return deleted.ToOkApiResponse();
    }

    /// <summary>
    /// Asynchronously updates an existing productTeam's repository status with its own.
    /// </summary>
    /// <param name="id">The ID of the productTeam to update.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the productTeam was updated successfully.</returns>
    public async Task<IApiResponse<bool>> UpdateProductTeamRepositoriesAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug("[ProductTeamService:UpdateProductTeamRepositoriesAsync] Updating productTeam repositories with ID: {Id}", id);

        var productTeam = await _repositoryContext.ProductTeamRepository.GetByIdAsync(id, ct);

        if (productTeam is null)
        {
            _logger.LogWarning("ProductTeam not found with ID: {Id}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        var repositoryIds = productTeam.Repositories.Items.Select(x => x.Id).Distinct().ToList();

        var repositories = await _repositoryContext.RepositoryRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => repositoryIds.Contains(x.Id))
            .ToListAsync(ct);

        repositories.ForEach(r => r.Status = productTeam.Status);

        await _repositoryContext.RepositoryRepository.UpdateRangeAsync(repositories, ct);

        return true.ToOkApiResponse();
    }


    /// <summary>
    /// Asynchronously updates product teams repositories
    /// </summary>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the update was successful.</returns>
    public async Task<IApiResponse<bool>> UpdateProductTeamsRepositoriesAsync(CancellationToken ct = default)
    {
        _logger.LogDebug("[ProductTeamService:UpdateProductTeamsRepositoriesAsync] Updating productTeams repositories");

        var productTeams = await _repositoryContext.ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .ToListAsync(ct);

        await _mainActor.Tell(new CascadeProductTeamsUpdateRepositoriesMessage(productTeams), ActorRefs.Nobody);

        return true.ToOkApiResponse("ProductTeams repositories update process started successfully");
    }


    /// <summary>
    /// Transforms the provided CreateProductTeamRequest into ProductTeam entity.
    /// </summary>
    /// <param name="request">The request containing the product team details.</param>
    /// <param name="productTeam">The product team entity to be transformed.</param>
    /// <param name="ct">Cancellation token</param>
    private async Task TransformAddProductTeamRequestToProductTeam(CreateProductTeamRequest request,
        ProductTeam productTeam, CancellationToken ct = default)
    {
        var repositoryIds = request.Repositories.Select(x => x.Id).Distinct().ToList();
        var memberIds = request.Members.Select(x => x.Id).Distinct().ToList();

        var repositories = await _repositoryContext.RepositoryRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => repositoryIds.Contains(x.Id))
            .ToListAsync(ct);

        var members = await _repositoryContext.EngineerRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => memberIds.Contains(x.Id))
            .ToListAsync(ct);

        productTeam.Repositories.Items = repositories.Adapt<List<RepositoryItem>>();
        productTeam.Members.Items = members.Adapt<List<Member>>();
    }

    /// <summary>
    /// Transforms the provided UpdateProductTeamRequest into an existing ProductTeam entity.
    /// </summary>
    /// <param name="request">The request containing the product team details.</param>
    /// <param name="productTeam">The product team entity to be transformed.</param>
    /// <param name="ct">Cancellation token</param>
    private async Task TransformUpdateRequestToProductTeam(UpdateProductTeamRequest request, ProductTeam productTeam,
        CancellationToken ct = default)
    {
        if (request.Repositories is not null)
        {
            var repositoryIds = request.Repositories.Select(x => x.Id).Distinct().ToList();

            var repositories = await _repositoryContext.RepositoryRepository.GetQueryable()
                .AsNoTracking()
                .Where(x => repositoryIds.Contains(x.Id))
                .ToListAsync(ct);

            productTeam.Repositories.Items = repositories.Adapt<List<RepositoryItem>>();
        }

        if (request.Members is not null)
        {
            var memberIds = request.Members.Select(x => x.Id).Distinct().ToList();

            var members = await _repositoryContext.EngineerRepository.GetQueryable()
                .AsNoTracking()
                .Where(x => memberIds.Contains(x.Id))
                .ToListAsync(ct);

            productTeam.Members.Items = members.Adapt<List<Member>>();
        }
    }


    /// <summary>
    /// Transforms a ProductGroup entity to a GetProductTeamResponse DTO.
    /// </summary>
    /// <returns>A function that takes a ProductTeam entity and returns a GetProductTeamResponse DTO.</returns>
    private static Func<ProductTeam, GetProductTeamResponse> TransformToProductTeamResponse()
    {
        return p =>
        {
            var item = p.Adapt<GetProductTeamResponse>();
            item.Repositories = p.Repositories.Items;
            item.Members = p.Members.Items;
            return item;
        };
    }


    private async Task<ProductSectionStats> AggregateSectionAsync(string repoType, List<string> repoIds, string productTeamId, string week, CancellationToken ct)
    {
        const string publicationDate = "PublicationDate";
        const string nonCommentedLinesOfCode = "NonCommentedLinesOfCode";
        var recentScores = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationWeek == week)
            .Where(x => repoIds.Contains(x.RepositoryId!))
            .ToListAsync(ct);

        var ranking = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(rank => rank.PublicationWeek == week)
            .OrderByDescending(x => x.PublicationDate)
            .Take(1)
            .FirstOrDefaultAsync(ct);

        
        var rankingsList = repoType switch
        {
            ValidationConstants.ProductTeamScopes.Backend => ranking?.Rankings.Backend,
            ValidationConstants.ProductTeamScopes.Frontend => ranking?.Rankings.Frontend,
            _ => null
        };
        
        var sortedRankings = rankingsList?
            .OrderByDescending(x => x.Rating)
            .ToList();

        var rank = sortedRankings?.FirstOrDefault(r => r.ProductTeamId == productTeamId);
        var ranked = sortedRankings?
            .Select((item, index) => new { item, index })
            .FirstOrDefault(x => x.item.ProductTeamId == productTeamId)?.index + 1;
        var totalProducts = sortedRankings?.Count ?? 0;

        var bugs = recentScores.Sum(scores => scores.Bugs);
        var codeSmells = recentScores.Sum(scores => scores.CodeSmells);
        var vulnerabilities = recentScores.Sum(scores => scores.Vulnerabilities);
        var codeCoverage = recentScores.Count != 0 ? recentScores.Average(scores => scores.Coverage) : 0;
        var duplication = recentScores.Count != 0 ? recentScores.Average(scores => scores.DuplicatedLinesDensity) : 0;
        var totalLinesOfCode = recentScores.Sum(scores => scores.NonCommentedLinesOfCode);
        var repositoryCount = recentScores.Count;
        var cciAverage = recentScores.Count != 0 ? recentScores.Average(scores => scores.FinalAverage) : 0;
        var rankStatus = rank?.Status;

        var latestDate = recentScores.FirstOrDefault()?.PublicationDate;
        if (latestDate == null)
        {
            return new ProductSectionStats();
        }

        var eightDaysAgo = latestDate.Value.AddDays(-8);
        var eightMonthsAgo = latestDate.Value.AddMonths(-8);

        var pastRankings = await _repositoryContext.CciProductsRankingRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationStartDate >= eightMonthsAgo)
            .OrderByDescending(x => x.PublicationDate)
            .ToListAsync(ct);

        var dailyRankTrend = pastRankings
            .Where(x => x.PublicationStartDate >= eightDaysAgo)
            .OrderByDescending(x => x.PublicationDate)
            .Select(x => new
            {
                x.PublicationDate,
                Ranking = (repoType == ValidationConstants.ProductTeamScopes.Backend ? x.Rankings.Backend : x.Rankings.Frontend)
                    .FirstOrDefault(r => r.ProductTeamId == productTeamId)
            })
            .ToList();

        var weeklyRankTrend = pastRankings
            .GroupBy(x => new { x.PublicationStartDate!.Value.Year, Week = Miscellaneous.GetCurrentPublicationWeek(x.PublicationStartDate.Value) })
            .Select(g => g.OrderByDescending(x => x.PublicationStartDate).First())
            .OrderByDescending(x => x.PublicationStartDate)
            .Select(x => new
            {
                x.PublicationDate,
                Ranking = (repoType == ValidationConstants.ProductTeamScopes.Backend ? x.Rankings.Backend : x.Rankings.Frontend)
                    .FirstOrDefault(r => r.ProductTeamId == productTeamId)
            })
            .ToList();

        var monthlyRankTrend = pastRankings
            .GroupBy(x => new { x.PublicationStartDate!.Value.Year, x.PublicationStartDate.Value.Month })
            .Select(g => g.OrderByDescending(x => x.PublicationStartDate).First())
            .OrderByDescending(x => x.PublicationStartDate)
            .Select(x => new
            {
                x.PublicationDate,
                Ranking = (repoType == ValidationConstants.ProductTeamScopes.Backend ? x.Rankings.Backend : x.Rankings.Frontend)
                    .FirstOrDefault(r => r.ProductTeamId == productTeamId)
            })
            .ToList();

        var pastScores = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.ProductTeamId == productTeamId)
            .Where(x => x.RepositoryType == repoType)
            .Where(x => x.PublicationStartDate >= eightMonthsAgo)
            .OrderByDescending(x => x.PublicationStartDate)
            .ToListAsync(ct);

        var dailyGrowthTrend = pastScores
            .Where(x => x.PublicationStartDate >= eightDaysAgo)
            .GroupBy(x => x.PublicationDate)
            .Select(x => new Dictionary<object, object?>
            {
                {publicationDate, x.Key},
                {nonCommentedLinesOfCode, x.Sum(y => y.NonCommentedLinesOfCode)}
            })
            .ToList();

        var weeklyGrowthTrend = pastScores
            .GroupBy(x => new { x.PublicationStartDate!.Value.Year, Week = Miscellaneous.GetCurrentPublicationWeek(x.PublicationStartDate.Value) })
            .Select(g => g.OrderByDescending(x => x.PublicationStartDate).First())
            .OrderByDescending(x => x.PublicationStartDate)
            .GroupBy(x => x.PublicationDate)
            .Select(x => new Dictionary<object, object?>
            {
                {publicationDate, x.Key},
                {nonCommentedLinesOfCode, x.Sum(y => y.NonCommentedLinesOfCode)}
            })
            .ToList();

        var monthlyGrowthTrend = pastScores
            .GroupBy(x => new { x.PublicationStartDate!.Value.Year, x.PublicationStartDate.Value.Month })
            .Select(g => g.OrderByDescending(x => x.PublicationStartDate).First())
            .OrderByDescending(x => x.PublicationStartDate)
            .GroupBy(x => x.PublicationDate)
            .Select(x => new Dictionary<object, object?>
            {
                {publicationDate, x.Key},
                {nonCommentedLinesOfCode, x.Sum(y => y.NonCommentedLinesOfCode)}
            })
            .ToList();


    return new ProductSectionStats
    {
        Bugs = bugs ?? 0,
        CodeSmells = codeSmells ?? 0,
        Vulnerabilities = vulnerabilities ?? 0,
        CodeCoverage = codeCoverage ?? 0,
        Duplication = duplication ?? 0,
        TotalLinesOfCode = totalLinesOfCode ?? 0,
        RepositoryCount = repositoryCount,
        CciAverage = cciAverage ?? 0,
        Rank = ranked,
        Status = rankStatus,
        TotalProducts = totalProducts,
        RankingList = sortedRankings,
        PastEightDaysRankItem = dailyRankTrend.Select(x => new TrendItem { PublicationDate = x.PublicationDate!.Value, Rankings = x.Ranking }).ToList(),
        PastEightWeeksRankItem = weeklyRankTrend.Select(x => new TrendItem { PublicationDate = x.PublicationDate!.Value, Rankings = x.Ranking }).ToList(),
        PastEightMonthsRankItem = monthlyRankTrend.Select(x => new TrendItem { PublicationDate = x.PublicationDate!.Value, Rankings = x.Ranking }).ToList(),
        PastEightDaysCodeGrowth = dailyGrowthTrend.Select(x => new GrowthItem { PublicationDate = (DateTime)x[publicationDate]!, NonCommentedLinesOfCode = (decimal)x[nonCommentedLinesOfCode]! }).ToList(),
        PastEightWeeksCodeGrowth = weeklyGrowthTrend.Select(x => new GrowthItem { PublicationDate = (DateTime)x[publicationDate]!, NonCommentedLinesOfCode = (decimal)x[nonCommentedLinesOfCode]! }).ToList(),
        PastEightMonthsCodeGrowth = monthlyGrowthTrend.Select(x => new GrowthItem { PublicationDate = (DateTime)x[publicationDate]!, NonCommentedLinesOfCode = (decimal)x[nonCommentedLinesOfCode]! }).ToList(),
    };
    }


    public async Task<IApiResponse<ProductTeamPublicationResponse>> GetProductTeamPublicationAsync(string productTeamId, string week, CancellationToken ct = default)
    {
        _logger.LogInformation("[ProductTeamService::GetProductTeamPublicationAsync] Publication Week To Fetch For Product Team:{ProductTeam}. in week: {Week}", productTeamId, week);

        var productTeam = await _repositoryContext.ProductTeamRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == productTeamId, ct);

        if (productTeam == null)
        {
            return ApiResponse<ProductTeamPublicationResponse>.Default.ToNotFoundApiResponse($"Product Team With Id: {productTeamId} not found");
        }

        var backendIds = productTeam.Repositories.Items.Where(x => x.Type == "Backend").Select(x => x.Id).ToList();
        var frontendIds = productTeam.Repositories.Items.Where(x => x.Type == "Frontend").Select(x => x.Id).ToList();

        var publicationDate = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationWeek == week && 
                        (backendIds.Contains(x.RepositoryId!) || frontendIds.Contains(x.RepositoryId!)))
            .Select(x => x.PublicationDate)
            .FirstOrDefaultAsync(ct);

        if (publicationDate == null)
        {
            return ApiResponse<ProductTeamPublicationResponse>.Default.ToNotFoundApiResponse("No CCI publication found for specified week.");
        }

        var backendData = await AggregateSectionAsync(ValidationConstants.ProductTeamScopes.Backend, backendIds, productTeamId, week, ct);
        var frontendData = await AggregateSectionAsync(ValidationConstants.ProductTeamScopes.Frontend, frontendIds, productTeamId, week, ct);

        var recentScore = await _repositoryContext.CciRepositoryScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .Where(x => x.PublicationWeek == week && backendIds.Contains(x.RepositoryId!))
            .FirstOrDefaultAsync(ct);


        var response = new ProductTeamPublicationResponse
            {
                ProductTeamId = productTeam.Id,
                ProductTeamName = productTeam.Name,
                ProductGroupId = recentScore?.ProductGroupId,
                ProductGroupName = recentScore?.ProductGroupName,
                Backend = backendData,
                Frontend = frontendData
            };

        return response.ToOkApiResponse("Team Publication Fetched Successfully");
    }

}