using System.Runtime.CompilerServices;
using System.Text;
using Akka.Actor;
using Flurl;
using Flurl.Http;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Dtos.Responses.BypassedPr;
using Hubtel.CCI.Api.Dtos.Responses.RepositoryBprSyncLogs;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Hubtel.CCI.Api.Services.Providers;

public class BypassedPrService(
    ILogger<BypassedPrService> logger,
    IRepositoryContext repositoryContext,
    IActorService<MainActor> mainActor,
    IOptions<AzureOrgConfig> azureOrgConfig): IBypassedPrService
{

    private readonly string _organization = azureOrgConfig.Value.Organization;

    private readonly string _accessToken = azureOrgConfig.Value.AccessToken;
    
    private readonly string _devUrl = azureOrgConfig.Value.DevUrl;

    private readonly List<string> _projects = azureOrgConfig.Value.Projects;

    public async Task<IApiResponse<BypassedPr>> GetByIdAsync(string id, CancellationToken ct = default)
    {
        logger.LogDebug("[BypassedPrService::GetByIAsync] Fetch bypassed pr by id: {Id}", id);

        var bypassedPr = await repositoryContext.BypassedPrRepository.GetByIdAsync(id, ct);
        
        if (bypassedPr is null)
        {
            logger.LogWarning("[BypassedPrService::GetByIAsync] Bypassed pr with id: {Id} not found", id);
            return ApiResponse<BypassedPr>.Default.ToNotFoundApiResponse("Bypassed pr not found");
        }
        
        logger.LogDebug("[BypassedPrService::GetByIAsync] Bypassed pr with id: {Id} found", id);

        return bypassedPr.ToOkApiResponse();
    }

    public async Task<IApiResponse<bool>> TriggerBypassedPrsAsync(CancellationToken ct = default)
    {
        logger.LogDebug("[BypassedPrService::TriggerBypassedPrsAsync] Triggering flow to fetch bypassed prs");
        
        await mainActor.Tell(new AzureByPassedPrMessage(DateTime.UtcNow), ActorRefs.Nobody);

        return true.ToOkApiResponse();
    }
    
    
    public async Task<bool> ExecuteBypassedPrsFlowAsync(CancellationToken ct = default)
    {
        try
        {
            logger.LogDebug("[BypassedPrService::ExecuteBypassedPrsFlowAsync] Executing flow to fetch bypassed prs");

            var repoMap = await FetchAllRepositoriesAsync();

            foreach (var (projectName, repoList) in repoMap)
            {
                foreach (var repo in repoList)
                {
                    await ProcessRepositoryAsync(projectName, repo, ct);
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "[BypassedPrService::ExecuteBypassedPrsFlowAsync] An error occurred while executing flow to fetch bypassed prs");
            return false;
        }
    }
    
    
    public async Task<bool> ProcessRepositoryAsync(string projectName, AzureRepositoryDto repo, CancellationToken ct)
    {
        logger.LogInformation("Processing repo: {RepoName} under project: {Project}", repo.Name, projectName);

        var lastSynced = await repositoryContext.RepositorySyncLogBpRepository.GetQueryable()
            .Where(x => x.Url == repo.WebUrl)
            .OrderByDescending(x => x.LastSyncedAt)
            .FirstOrDefaultAsync(ct);
        
        var lastSyncDate = lastSynced?.LastSyncedAt ?? DateTime.UtcNow.AddMonths(-2);
        var bypassedPrs = new List<BypassedPr>();
        var now = DateTime.UtcNow.Date;

        var repository = await repositoryContext.RepositoryRepository.GetQueryable()
            .Where(x => x.Url == repo.WebUrl)
            .FirstOrDefaultAsync(ct);

        await foreach (var pr in FetchCompletedPullRequests(lastSyncDate, now, projectName, repo.AzureRepoId, ct))
        {
            if (pr.CompletionOptions?.BypassPolicy == true)
            {
                bypassedPrs.Add(MapToBypassedPr(pr, repository?.Id));
            }
        }

        if (bypassedPrs.Count != 0)
        {
            logger.LogInformation("Found {Count} bypassed PRs in {RepoName}, LastSynced: {LastSynced}", 
                bypassedPrs.Count, repo.Name, lastSyncDate);
            await repositoryContext.BypassedPrRepository.AddRangeAsync(bypassedPrs, ct);
        }

        await repositoryContext.RepositorySyncLogBpRepository.AddAsync(new RepositorySyncLogBp
        {
            Url = repo.WebUrl,
            LastSyncedAt = now,
            RepositoryId = repository?.Id,
            RepositoryName = repo.Name,
            AzureRepoId = repo.AzureRepoId,
            ProjectName = projectName
        }, ct);
        return true;
    }

    public async Task<IApiResponse<BypassedPrsOverviewDto>> GetBypassedPrsOverviewAsync(CancellationToken ct = default)
    {
        var queryable = repositoryContext.BypassedPrRepository.GetQueryable();

        // Daily Summary
        var dailySummary = await queryable
            .Where(x => x.CreationDate.HasValue)
            .GroupBy(x => x.CreationDate!.Value.Date)
            .Select(g => new DailyBypassSummary
            {
                Date = DateOnly.FromDateTime(g.Key),
                Count = g.Count()
            })
            .OrderByDescending(x => x.Date)
            .ToListAsync(ct);

        // Repository Summary
        var repoSummary = await queryable
            .Where(x => x.Repository.Name != null && x.CreationDate != null)
            .GroupBy(x => x.Repository.Name)
            .Select(g => new RepositoryBypassSummary
            {
                RepositoryName = g.Key!,
                TotalCount = g.Count(),

                DailyCounts = g
                    .GroupBy(x => x.CreationDate!.Value.Date)
                    .Select(d => new DailyBypassSummary
                    {
                        Date = DateOnly.FromDateTime(d.Key),
                        Count = d.Count()
                    })
                    .OrderByDescending(d => d.Date)
                    .ToList()
            })
            .OrderByDescending(g => g.TotalCount)
            .ToListAsync(ct);

        var response = new BypassedPrsOverviewDto()
        {
            DailySummary = dailySummary,
            RepositorySummary = repoSummary
        };
        
        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<SyncBPrLogOverviewDto>> GetBypassedPrsSyncLogsOverviewAsync(CancellationToken ct = default)
    {
        var queryable = repositoryContext.RepositorySyncLogBpRepository.GetQueryable();

        // Daily Summary
        var dailySummary = await queryable
            .Where(x => x.LastSyncedAt.HasValue)
            .GroupBy(x => x.LastSyncedAt!.Value.Date)
            .Select(g => new DailySyncSummary
            {
                Date = DateOnly.FromDateTime(g.Key),
                Count = g.Count()
            })
            .OrderByDescending(x => x.Date)
            .ToListAsync(ct);

        // Repository Summary
        var repoSummary = await queryable
            .Where(x => x.RepositoryName != null && x.LastSyncedAt != null)
            .GroupBy(x => x.RepositoryName)
            .Select(g => new RepositorySyncSummary
            {
                RepositoryName = g.Key!,
                TotalCount = g.Count(),
                DailyCounts = g
                    .GroupBy(x => x.LastSyncedAt!.Value.Date)
                    .Select(d => new DailySyncSummary
                    {
                        Date = DateOnly.FromDateTime(d.Key),
                        Count = d.Count()
                    })
                    .OrderByDescending(d => d.Date)
                    .ToList()
            })
            .OrderByDescending(x => x.TotalCount)
            .ToListAsync(ct);

        var response = new SyncBPrLogOverviewDto()
        {
            DailySummary = dailySummary,
            RepositorySummary = repoSummary
        };

        return response.ToOkApiResponse();
    }

    public async Task<IApiResponse<bool>> ExecuteClearBypassedPrRecordsAsync(CancellationToken ct = default)
    {
        // delete bypassed prs
        var bypassedPrs = repositoryContext.BypassedPrRepository.GetQueryable();
        await repositoryContext.BypassedPrRepository.RemoveRangeAsync(bypassedPrs, ct);
        
        // delete sync logs
        var syncLogs = repositoryContext.RepositorySyncLogBpRepository.GetQueryable();
        await repositoryContext.RepositorySyncLogBpRepository.RemoveRangeAsync(syncLogs, ct);
        
        
        return true.ToOkApiResponse();
    }
    
    private static BypassedPr MapToBypassedPr(PullRequest pr, string? repositoryId)
    {
        return new BypassedPr
        {
            PullRequestId = pr.PullRequestId,
            Title = pr.Title,
            CreatedBy = pr.CreatedBy.Adapt<PrCreatedBy>(),
            CreatedAt = pr.CreationDate,
            ClosedDate = pr.ClosedDate,
            RepositoryId = repositoryId,
            Status = pr.Status,
            CodeReviewId = pr.CodeReviewId,
            CompletionOptions = pr.CompletionOptions.Adapt<PrCompletionOptions>(),
            Url = pr.Url,
            Repository = pr.Repository.Adapt<PrRepository>(),
            Reviewers = pr.Reviewers.Adapt<List<PrReviewer>>(),
            CreationDate = pr.CreationDate,
            Description = pr.Description,
            MergeStatus = pr.MergeStatus,
            CompletionQueueTime = pr.CompletionQueueTime
        };
    }
    
    public async IAsyncEnumerable<PullRequest> FetchCompletedPullRequests(
        DateTime from,
        DateTime to,
        string project,
        string repoId,
        [EnumeratorCancellation] CancellationToken token,
        bool onlyMainBranches=false
        )
    {
        string? continuationToken = null;
        var base64Pat = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{_accessToken}"));
        var allowed = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            { "staging", "dev", "development", "develop" };

        do
        {
            List<PullRequest> pullRequests;

            try
            {
                var request = $"{_devUrl}/{_organization}/{project}/_apis/git/repositories/{repoId}/pullrequests"
                    .SetQueryParam("searchCriteria.status", "completed")
                    .SetQueryParam("searchCriteria.creationDate", from.ToString("O"))
                    .SetQueryParam("$top", 100)
                    .SetQueryParam("api-version", "7.0");

                if (!string.IsNullOrEmpty(continuationToken))
                    request = request.SetQueryParam("continuationToken", continuationToken);

                var response = await request
                    .WithHeader("Authorization", $"Basic {base64Pat}")
                    .WithTimeout(TimeSpan.FromSeconds(60))
                    .SendAsync(HttpMethod.Get, cancellationToken: token);

                var prList = await response.GetJsonAsync<PullRequestListResponse>();

                pullRequests = prList.Value
                    .Where(pr => pr.ClosedDate <= to  && pr.CreationDate >= from)
                    .Where(pr => !onlyMainBranches || 
                                 (
                                     pr.TargetRefName != null && 
                                  allowed.Contains(pr.TargetRefName.Replace("refs/heads/", "", StringComparison.OrdinalIgnoreCase))
                                  )
                                 )
                    .ToList();
                

                response.Headers.TryGetFirst("x-ms-continuationtoken", out continuationToken);
            }
            catch (FlurlHttpException ex)
            {
                var msg = await ex.GetResponseStringAsync();
                logger.LogDebug(ex,"[PR Fetch Error] Repo: {RepoId} - {Msg}", repoId, msg);
                continuationToken = null;
                pullRequests = [];
            }

            foreach (var pr in pullRequests)
            {
                yield return pr;
            }

        } while (!string.IsNullOrEmpty(continuationToken));
    }
    
    
    public async Task<Dictionary<string, List<AzureRepositoryDto>>> FetchAllRepositoriesAsync()
    {
        var mapping = new Dictionary<string, List<AzureRepositoryDto>>();
        var base64Pat = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{_accessToken}"));

        foreach (var project in _projects)
        {
            try
            {
                var url = $"{_devUrl}/{_organization}/{project}/_apis/git/repositories"
                    .SetQueryParam("api-version", "7.0");

                var result = await url
                    .WithHeader("Authorization", $"Basic {base64Pat}")
                    .GetJsonAsync<RepositoryListResponse>();
                
                var repos = (result?.Value ?? []).Select(repo => new AzureRepositoryDto
                {
                    Id = Guid.NewGuid(),
                    AzureRepoId = repo.Id,
                    Name = repo.Name,
                    Organization = _organization,
                    ProjectName = project,
                    WebUrl = repo.WebUrl
                }).ToList();

                mapping[project] = repos;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to fetch repositories for project '{Project}'", project);
                mapping[project] = [];
            }
            

        }

        return mapping;
    }
}