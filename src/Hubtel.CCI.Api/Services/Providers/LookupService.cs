using Flurl.Http;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.Extensions.Options;

namespace Hubtel.CCI.Api.Services.Providers;

public class LookupService : ILookupService
{
    private readonly ILogger<LookupService> _logger;
    private readonly LookupConfig _lookupConfig;

    public LookupService(ILogger<LookupService> logger, IOptions<LookupConfig> lookupConfig)
    {
        _logger = logger;
        _lookupConfig = lookupConfig.Value;
    }


    /// <summary>
    /// Asynchronously looks up information security details of a user by their email.
    /// </summary>
    /// <param name="email">The email of the user to look up.</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the user's information security details.</returns>
    public async Task<LookupApiResponse<LookupInfoSecUser>> LookupInfoSecUserAsync(string email,
        CancellationToken ct = default)
    {
        _logger.LogDebug("[LookupService:LookupInfoSecUserAsync]: Request payload ---> {RequestPayload}",
            new { email }.Serialize());

        var url = $"{_lookupConfig.Url}/{email}";
        var response = await url
            .GetJsonAsync<LookupApiResponse<LookupInfoSecUser>>(cancellationToken: ct);

        _logger.LogDebug("[LookupService:LookupInfoSecUserAsync]: Response payload ---> {ResponsePayload}",
            new { response }.Serialize());
        return response;
    }
}