using Akka.Routing;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Hubtel.CCI.Api.Services.Providers
{
    public class IssueTrackerService : IIssueTrackerService
    {
        private readonly IRepositoryContext _repositoryContext;
        private readonly ILogger<IssueTrackerService> _logger;

        public IssueTrackerService(IRepositoryContext repositoryContext, ILogger<IssueTrackerService> logger)
        {
            _repositoryContext = repositoryContext;
            _logger = logger;
        }
        public async Task<IApiResponse<CreateIssueTrackerResponse>> CreateIssueAsync(CreateIssueTrackerRequest createIssue, CancellationToken ct = default)
        {
            _logger.LogDebug("[IssueTrackerService:CreateIssueAsync]: Request payload ---> {RequestPayload}",
                new { createIssue }.Serialize());

            var issueModel = createIssue.Adapt<IssueTracker>();

            var (pgResponse, productGroupNames, productTeamNames, validRepoIds) =
                await TryResolveProductGroupsAndTeamsAsync(createIssue, ct);
            if (pgResponse is not null) return pgResponse;

            var (svcResponse, serviceNames) =
                await TryResolveServicesAsync(createIssue, validRepoIds, ct);
            if (svcResponse is not null) return svcResponse;

            var tool = await _repositoryContext.ToolingToolsRepository.FindOneAsync(x => x.Id == createIssue.ToolName, ct);
            if (tool is null)
                return ApiResponse<CreateIssueTrackerResponse>.Default.ToNotFoundApiResponse("Tool not found! Please try again");

            issueModel.Tool = tool;
            issueModel.ProductGroup = productGroupNames;
            issueModel.ProductTeam = productTeamNames;
            issueModel.ServicesAffected = serviceNames;

            var saved = await _repositoryContext.IssueTrackerRepository.AddAsync(issueModel, ct);
            if (saved <= 0)
                return ApiResponse<CreateIssueTrackerResponse>.Default.ToFailedDependencyApiResponse("Could not create Issue! Please try again");

            return issueModel.Adapt<CreateIssueTrackerResponse>().ToCreatedApiResponse();
        }

        private async Task<(IApiResponse<CreateIssueTrackerResponse>? response, List<string> groupNames, List<string> teamNames, List<string> repoIds)>
            TryResolveProductGroupsAndTeamsAsync(CreateIssueTrackerRequest request, CancellationToken ct)
        {
            var productGroupNames = new List<string>();
            var productTeamNames = new List<string>();
            var validRepoIds = new List<string>();

            foreach (var groupId in request.ProductGroup)
            {
                if (string.IsNullOrWhiteSpace(groupId))
                    return (BadRequest("Product Group cannot be null or empty"), productGroupNames, productTeamNames, validRepoIds);

                var group = await _repositoryContext.ProductGroupRepository.FindOneAsync(x => x.Id == groupId, ct);
                if (group is null)
                    return (BadRequest($"Product Group {groupId} not found! Please try again"), productGroupNames, productTeamNames, validRepoIds);
                if (group.ProductTeams.Items.Count == 0)
                    return (BadRequest($"Product Group {groupId} has no Product Teams! Please try again"), productGroupNames, productTeamNames, validRepoIds);

                productGroupNames.Add(group.GroupName);

                foreach (var teamId in request.ProductTeam.ToList())
                {
                    if (string.IsNullOrWhiteSpace(teamId))
                        return (BadRequest("Product Team cannot be null or empty"), productGroupNames, productTeamNames, validRepoIds);

                    var productTeamsInGroup = group.ProductTeams.Items;
                    var mappedTeam = productTeamsInGroup.FirstOrDefault(t => t.Id == teamId);
                    if (mappedTeam is null)
                        return (BadRequest($"Product Team not found in Product Group {group.GroupName}! Please try again"), productGroupNames, productTeamNames, validRepoIds);

                    productTeamNames.Add(mappedTeam.Name);
                    request.ProductTeam.Remove(teamId);
                    validRepoIds.AddRange(mappedTeam.Repositories.Items.Select(r => r.Id));
                }
            }
            return (null, productGroupNames, productTeamNames, validRepoIds);
        }

        private async Task<(IApiResponse<CreateIssueTrackerResponse>? response, List<string> names)>
            TryResolveServicesAsync(CreateIssueTrackerRequest request, List<string> validRepoIds, CancellationToken ct)
        {
            var resolvedNames = new List<string>();
            foreach (var svcId in request.ServicesAffected)
            {
                if (string.IsNullOrWhiteSpace(svcId)) continue;

                var service = await _repositoryContext.ServiceRepository.FindOneAsync(x => x.Id == svcId, ct);
                if (service is null)
                    return (BadRequest($"Service {svcId} not found! Please try again"), resolvedNames);

                if (!validRepoIds.Contains(service.RepositoryId))
                    return (BadRequest($"Service {svcId} does not belong to any repository in the selected product group or team! Please try again"), resolvedNames);

                resolvedNames.Add(service.Name);
            }
            return (null, resolvedNames);
        }

        private static IApiResponse<CreateIssueTrackerResponse> BadRequest(string message)
            => ApiResponse<CreateIssueTrackerResponse>.Default.ToBadRequestApiResponse(message);


        public async Task<IApiResponse<GetIssueTrackerResponse>> GetIssueByIdAsync(string id, CancellationToken ct = default)
        {
            _logger.LogInformation("[IssueTrackerService:GetIssueByIdAsync]: Request payload ---> {RequestPayload}",
                new { id }.Serialize());
            if (string.IsNullOrWhiteSpace(id))
                return ApiResponse<GetIssueTrackerResponse>.Default.ToNotFoundApiResponse("Issue ID cannot be null or empty");

            var issue = await _repositoryContext.IssueTrackerRepository.GetQueryable().Include(t => t.Tool).FirstOrDefaultAsync(i => i.Id == id,ct);

            if (issue == null)
            {
                return ApiResponse<GetIssueTrackerResponse>.Default.ToNotFoundApiResponse("Issue not found! Please try again");
            }

            var response = issue.Adapt<GetIssueTrackerResponse>();
            return response.ToOkApiResponse();

        }

        public async Task<IApiResponse<PagedResult<GetIssueTrackerResponse>>> GetIssuesAsync(GetIssueTrackerRequest filter, CancellationToken ct)
        {
            _logger.LogInformation("[IssueTrackerService:GetIssuesAsync]: Request payload ---> {RequestPayload}",
                new { filter }.Serialize());

            var issues = _repositoryContext.IssueTrackerRepository.GetQueryable();
            if (!string.IsNullOrEmpty(filter.Domain))
            {
                issues = issues.Where(x => x.Domain == Enum.Parse<Domain>(filter.Domain));
            }
            if (filter.ProductGroup.Count > 0)
            {
                issues = issues.Where(x => x.ProductGroup == filter.ProductGroup);
            }
            if (filter.ProductTeam.Count > 0)
            {
                issues = issues.Where(x => x.ProductTeam == filter.ProductTeam);
            }
            if (!string.IsNullOrEmpty(filter.Tool))
            {
                issues = issues.Where(x => x.Tool.Name == filter.Tool);
            }
            if (!string.IsNullOrEmpty(filter.ReportedBy))
            {
                issues = issues.Where(x => x.ReportedBy == filter.ReportedBy);
            }
            if (!string.IsNullOrEmpty(filter.Status))
            {
                issues = issues.Where(x => x.Status == Enum.Parse<IssueStatus>(filter.Status));
            }
            var pagedResult = await issues.Include(t => t.Tool).OrderByDescending(x => x.CreatedAt)
                .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

            var results = pagedResult.Results.Select(x => x.Adapt<GetIssueTrackerResponse>()).ToList();

            var response = new PagedResult<GetIssueTrackerResponse>
            {
                Results = results,
                UpperBound = pagedResult.UpperBound,
                LowerBound = pagedResult.LowerBound,
                PageIndex = pagedResult.PageIndex,
                PageSize = pagedResult.PageSize,
                TotalCount = pagedResult.TotalCount,
                TotalPages = pagedResult.TotalPages
            };

            return response.ToOkApiResponse();

        }

        public Task<IApiResponse<CreateIssueTrackerResponse>> PatchIssueAsync(string id, UpdateIssueTrackerRequest updateIssue, CancellationToken ct = default)
        {
            throw new NotImplementedException();
        }

        public async Task<IApiResponse<CreateIssueTrackerResponse>> UpdateIssueAsync(string id, UpdateIssueTrackerRequest updateIssue, CancellationToken ct = default)
        {
            _logger.LogDebug("[IssueTrackerService:UpdateIssueAsync]: Request payload ---> {RequestPayload}",
                new { id, updateIssue }.Serialize());


            var searchToolResponse = await _repositoryContext.ToolingToolsRepository.FindOneAsync(x => x.Id == updateIssue.Tool, ct);
            if (searchToolResponse == null)
            {
                return ApiResponse<CreateIssueTrackerResponse>.Default.ToNotFoundApiResponse(
                    "Tool not found! Please try again");
            }



            var existingIssue = await _repositoryContext.IssueTrackerRepository.FindOneAsync(x => x.Id == id, ct);


            if (existingIssue == null)
            {
                return ApiResponse<CreateIssueTrackerResponse>.Default.ToNotFoundApiResponse("Issue not found! Please try again");
            }

            // Manually map properties except Tool to avoid type conversion issues
            existingIssue.ReportedBy = updateIssue.ReportedBy;
            existingIssue.ProductGroup = updateIssue.ProductGroup?.ToList() ?? new List<string>();
            existingIssue.ProductTeam = updateIssue.ProductTeam?.ToList() ?? new List<string>();
            existingIssue.ServicesAffected = updateIssue.ServicesAffected ?? new List<string>();
            existingIssue.Domain = updateIssue.Domain;
            existingIssue.IncidentDescription = updateIssue.IncidentDescription;
            existingIssue.ActionTaken = updateIssue.ActionTaken;
            existingIssue.Status = updateIssue.Status;
            existingIssue.Tool = searchToolResponse;

            var updated = await _repositoryContext.IssueTrackerRepository.UpdateAsync(existingIssue, ct);
            if (updated <= 0)
            {
                _logger.LogError("Failed to update Report with ID: {Id}", id);
                return ApiResponse<CreateIssueTrackerResponse>.Default.ToFailedDependencyApiResponse(
                    "Could not update Tool! Please try again"
                );
            }
            _logger.LogInformation("Report with ID: {Id} updated successfully", id);
            return existingIssue.Adapt<CreateIssueTrackerResponse>().ToOkApiResponse();
        }
    }
}
