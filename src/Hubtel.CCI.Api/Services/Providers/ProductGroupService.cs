using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ProductGroup;
using Hubtel.CCI.Api.Dtos.Responses.ProductGroup;
using Hubtel.CCI.Api.Dtos.Responses.ProductTeam;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

/// <summary>
///     Service responsible for all related operations for product group entity.
/// </summary>
public class ProductGroupService : IProductGroupService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger _logger;

    public ProductGroupService(ILogger<ProductGroupService> logger, IRepositoryContext repositoryContext)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
    }

    /// <summary>
    /// Asynchronously gets a list of productGroups with pagination.
    /// </summary>
    /// <param name="filter">The filter to apply to the engineer list. This includes the search term, page index, and page size.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with a paged list of productGroups.</returns>
    public async Task<IApiResponse<PagedResult<GetProductGroupResponse>>> GetProductGroupsAsync(SearchFilter filter,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductGroupService: GetProductGroupsAsync] Received Request to get ProductGroups => RequestPayload => {Request}",
            filter.Serialize());

        var query = _repositoryContext.ProductGroupRepository.GetQueryable().AsNoTracking();

        if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            query = query.Where(c => c.GroupName.ToLower().Contains(filter.SearchTerm.ToLower()));

        var pagedResult = await query.OrderByDescending(x => x.CreatedAt)
            .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

        var results = pagedResult.Results.Select(TransformToProductGroupResponse()).ToList();

        var response = new PagedResult<GetProductGroupResponse>
        {
            Results = results,
            UpperBound = pagedResult.UpperBound,
            LowerBound = pagedResult.LowerBound,
            PageIndex = pagedResult.PageIndex,
            PageSize = pagedResult.PageSize,
            TotalCount = pagedResult.TotalCount,
            TotalPages = pagedResult.TotalPages
        };

        return response.ToOkApiResponse();
    }
    
    

    /// <summary>
    /// Transforms a ProductGroup entity to a GetProductGroupResponse DTO.
    /// </summary>
    /// <returns>A function that takes a ProductGroup entity and returns a GetProductGroupResponse DTO.</returns>
    private static Func<ProductGroup, GetProductGroupResponse> TransformToProductGroupResponse()
    {
        return p =>
        {
            var item = p.Adapt<GetProductGroupResponse>();
            item.ProductTeams = p.ProductTeams.Items.Select(x =>
            {
                var productTeamItemResponse = x.Adapt<GetProductTeamItemResponse>();
                productTeamItemResponse.Repositories = x.Repositories.Items;
                productTeamItemResponse.Members = x.Members.Items;
                return productTeamItemResponse;
            }).ToList();
            item.Supervisors = p.Supervisors.Items;
            return item;
        };
    }

    /// <summary>
    /// Asynchronously retrieves a specific productGroup by their ID.
    /// </summary>
    /// <param name="id">The ID of the productGroup to retrieve.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved productGroup, or a not found response if no productGroup with the provided ID exists.</returns>
    public async Task<IApiResponse<GetProductGroupResponse>> GetProductGroupAsync(string id,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductGroupService: GetProductGroupAsync] Received Request to get ProductGroup with ID: {Id}", id);

        var result = await _repositoryContext.ProductGroupRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == id, ct);

        return result is null
            ? result.Adapt<GetProductGroupResponse>().ToNotFoundApiResponse()
            : TransformToProductGroupResponse()(result).ToOkApiResponse();
    }


    /// <summary>
    /// Asynchronously adds a new productGroup.
    /// </summary>
    /// <param name="request">The request to create a new productGroup. This includes the productGroup's details.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the created productGroup.</returns>
    public async Task<IApiResponse<GetProductGroupResponse>> AddProductGroupAsync(CreateProductGroupRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductGroupService: AddProductGroupAsync] Received Request to create ProductGroup => RequestPayload => {Request}",
            request.Serialize());

        var existingProductGroup = await _repositoryContext.ProductGroupRepository.GetQueryable()
            .FirstOrDefaultAsync(c => c.GroupName.ToLower() == request.GroupName.ToLower(), ct);

        if (existingProductGroup != null)
            return ApiResponse<GetProductGroupResponse>.Default.ToBadRequestApiResponse(
                $"ProductGroup with name '{request.GroupName}' already exists"
            );

        var productGroup = ToProductGroupDbEntity(request);

        productGroup.CreatedAt = DateTime.UtcNow;
        productGroup.UpdatedAt = DateTime.UtcNow;

        var savedCount = await _repositoryContext.ProductGroupRepository.AddAsync(productGroup, ct);

        if (savedCount <= 0)
            return ApiResponse<GetProductGroupResponse>.Default.ToFailedDependencyApiResponse(
                "Could not create ProductGroup! Please try again"
            );

        return TransformToProductGroupResponse()(productGroup).ToCreatedApiResponse();
    }
    
    public ProductGroup ToProductGroupDbEntity(CreateProductGroupRequest request)
    {
        return new ProductGroup
        {
            GroupName = request.GroupName,
            Supervisors = new Supervisors
            {
                Items = request.Supervisors
            },
            ProductTeams = new ProductTeams
            {
                Items = request.ProductTeams.Select(pt => new ProductTeamItem
                {
                    Id = pt.Id,
                    Name = pt.Name,
                    Members = new Members { Items = pt.Members },
                    Repositories = new Data.Entities.Repositories { Items = pt.Repositories }
                }).ToList()
            }
        };
    }

    /// <summary>
    /// Asynchronously updates an existing productGroup.
    /// </summary>
    /// <param name="id">The ID of the productGroup to update.</param>
    /// <param name="request">The request to update the productGroup. This includes the new details of the productGroup.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the productGroup was updated successfully.</returns>
    public async Task<IApiResponse<bool>> UpdateProductGroupAsync(string id, UpdateProductGroupRequest request,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductGroupService:UpdateProductGroupAsync] Updating productGroup with ID: {Id} and RequestPayload => {Request}",
            id, request.Serialize()
        );

        var existingProductGroup = await _repositoryContext.ProductGroupRepository.GetByIdAsync(id, ct);

        if (existingProductGroup is null)
        {
            _logger.LogWarning("ProductGroup not found with ID: {Id}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        if (request.GroupName != null && !string.Equals(existingProductGroup.GroupName, request.GroupName,
                StringComparison.CurrentCultureIgnoreCase))
        {
            var existingProductGroupWithSameName = await _repositoryContext.ProductGroupRepository.GetQueryable()
                .FirstOrDefaultAsync(c => c.GroupName.ToLower() == request.GroupName.ToLower(), ct);

            if (existingProductGroupWithSameName != null)
                return ApiResponse<bool>.Default.ToBadRequestApiResponse(
                    $"ProductGroup with name '{request.GroupName}' already exists"
                );
        }

        existingProductGroup.GroupName = request.GroupName ?? existingProductGroup.GroupName;

        await TransformUpdateRequestToProductGroup(request, existingProductGroup, ct);

        existingProductGroup.UpdatedAt = DateTime.UtcNow;

        _logger.LogDebug(
            "Updating productGroup in the repository: {ProductGroup}",
            existingProductGroup.Serialize()
        );

        var updated = await _repositoryContext.ProductGroupRepository.UpdateAsync(existingProductGroup, ct) > 0;

        return updated ? updated.ToOkApiResponse() : updated.ToNotFoundApiResponse("not found");
    }

    /// <summary>
    /// Transforms the provided CreateProductGroupRequest into ProductGroup entity.
    /// </summary>
    /// <param name="request">The request containing the product group details.</param>
    /// <param name="productGroup">The product group entity to be transformed.</param>
    /// <param name="ct">Cancellation token</param>
    private async Task TransformAddProductGroupRequestToProductGroup(CreateProductGroupRequest request,
        ProductGroup productGroup, CancellationToken ct = default)
    {
        var productTeamIds = request.ProductTeams.Select(x => x.Id).Distinct().ToList();
        var supervisorIds = request.Supervisors.Select(x => x.Id).Distinct().ToList();

        var productTeams = await _repositoryContext.ProductTeamRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => productTeamIds.Contains(x.Id))
            .ToListAsync(ct);


        var supervisors = await _repositoryContext.EngineerRepository.GetQueryable()
            .AsNoTracking()
            .Where(x => supervisorIds.Contains(x.Id))
            .ToListAsync(ct);

        productGroup.ProductTeams.Items = productTeams.Select(x =>
        {
            var productTeamItem = x.Adapt<ProductTeamItem>();
            productTeamItem.Members.Items = x.Members.Items;
            productTeamItem.Repositories.Items = x.Repositories.Items;
            return productTeamItem;
        }).ToList();

        productGroup.Supervisors.Items = supervisors.Adapt<List<Supervisor>>();
    }


    /// <summary>
    /// Transforms the provided UpdateProductGroupRequest into an existing ProductGroup entity.
    /// </summary>
    /// <param name="request">The request containing the product group details.</param>
    /// <param name="productGroup">The product group entity to be transformed.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    private async Task TransformUpdateRequestToProductGroup(UpdateProductGroupRequest request,
        ProductGroup productGroup, CancellationToken ct = default)
    {
        if (request.ProductTeams is not null)
        {
            var productTeamIds = request.ProductTeams.Select(x => x.Id).Distinct().ToList();

            var productTeams = await _repositoryContext.ProductTeamRepository.GetQueryable()
                .AsNoTracking()
                .Where(x => productTeamIds.Contains(x.Id))
                .ToListAsync(ct);

            productGroup.ProductTeams.Items = productTeams.Select(x =>
            {
                var productTeamItem = x.Adapt<ProductTeamItem>();
                productTeamItem.Members.Items = x.Members.Items;
                productTeamItem.Repositories.Items = x.Repositories.Items;
                return productTeamItem;
            }).ToList();
        }

        if (request.Supervisors is not null)
        {
            var supervisorIds = request.Supervisors.Select(x => x.Id).Distinct().ToList();

            var supervisors = await _repositoryContext.EngineerRepository.GetQueryable()
                .AsNoTracking()
                .Where(x => supervisorIds.Contains(x.Id))
                .ToListAsync(ct);

            productGroup.Supervisors.Items = supervisors.Adapt<List<Supervisor>>();
        }
    }

    /// <summary>
    /// Asynchronously deletes a specific product group by marking it as deleted.
    /// </summary>
    /// <param name="id">The ID of the product group to delete.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response indicating whether the product group was marked as deleted successfully.</returns>
    public async Task<IApiResponse<bool>> DeleteProductGroupAsync(string id, CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductGroupService:DeleteProductGroupAsync] deleting productGroup with ID: {Id}", id
        );

        var existingDocument = await _repositoryContext.ProductGroupRepository.GetByIdAsync(id, ct);

        if (existingDocument == null)
        {
            _logger.LogDebug("ProductGroup not found with ID: {ProductGroupId}", id);
            return ApiResponse<bool>.Default.ToNotFoundApiResponse();
        }

        existingDocument.IsDeleted = true;

        _logger.LogDebug("Deleting productGroup in the repository: {ProductGroup}", existingDocument.Serialize());

        var updatedCount = await _repositoryContext.ProductGroupRepository.DeleteAsync(existingDocument, ct);

        var deleted = updatedCount > 0;

        _logger.LogDebug("productGroup deleted: {Deleted}", deleted);

        return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse("not found");
    }

    /// <summary>
    /// Asynchronously retrieves a product group by repository ID or SonarQube key.
    /// </summary>
    /// <param name="repositoryId">The ID of the repository to search for.</param>
    /// <param name="sonarQubeKey">The SonarQube key of the repository to search for.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the API response with the details of the retrieved product group, or a not found response if no product group matches the provided criteria.</returns>
    public async Task<IApiResponse<GetRepositoryProductGroupResponse?>> GetProductGroupByRepositoryIdOrSonarQubeKey(
        string? repositoryId,
        string? sonarQubeKey,
        CancellationToken ct = default)
    {
        _logger.LogDebug(
            "[ProductGroupService:GetProductGroupByRepositoryIdOrSonarQubeKey] Received request to get product group by repository ID or SonarQube key: {Id}",
            sonarQubeKey ?? repositoryId
        );

        var repositoryItem = await _repositoryContext.RepositoryRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(
                c => (!string.IsNullOrEmpty(repositoryId) && c.Id == repositoryId) ||
                     (!string.IsNullOrEmpty(sonarQubeKey) && c.SonarQubeKey == sonarQubeKey),
                ct);

        if (repositoryItem == null)
        {
            _logger.LogWarning("Repository not found with ID: {Id}", repositoryId ?? sonarQubeKey);
            return ApiResponse<GetRepositoryProductGroupResponse>.Default.ToNotFoundApiResponse();
        }


        var productGroup = await _repositoryContext.GetProductGroupByRepositoryFilterAsync(repositoryId ?? string.Empty,
            sonarQubeKey ?? string.Empty, repositoryItem, ct);

        if (productGroup == null)
        {
            _logger.LogWarning("Repository not found with ID: {Id}", repositoryId ?? sonarQubeKey);
            return ApiResponse<GetRepositoryProductGroupResponse>.Default.ToNotFoundApiResponse();
        }

        var matchingProductTeam = productGroup.ProductTeams.Items
            .Find(pt => pt.Repositories.Items.Exists(r =>
                (!string.IsNullOrEmpty(repositoryId) && r.Id == repositoryId) ||
                (!string.IsNullOrEmpty(sonarQubeKey) && r.SonarQubeKey == sonarQubeKey)));

        var result = new GetRepositoryProductGroupResponse
        {
            ProductGroupId = productGroup.ProductGroupId,
            ProductGroupName = productGroup.ProductGroupName,
            ProductTeamId = matchingProductTeam?.Id,
            ProductTeamName = matchingProductTeam?.Name,
            RepositoryUrl = productGroup.RepositoryDetails.Url,
            RepositoryType = productGroup.RepositoryDetails.Type
        };

        return result.ToOkApiResponse();
    }
}