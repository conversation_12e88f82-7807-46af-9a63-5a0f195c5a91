using System.Net;
using Akka.Actor;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Models.Azure;
using Hubtel.CCI.Api.Dtos.Requests.DciServiceScore;
using Hubtel.CCI.Api.Dtos.Responses.DciServiceScore;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class DciServiceScoreService : IDciServiceScoreService
{
    private readonly ILogger<DciServiceScoreService> _logger;
    private readonly IRepositoryContext _repositoryContext;
    private readonly IAzureProxyService _azureProxyService;
    private readonly IActorService<MainActor> _mainActor;

    public DciServiceScoreService(
        ILogger<DciServiceScoreService> logger, 
        IRepositoryContext repositoryContext,
        IAzureProxyService azureProxyService,
        IActorService<MainActor> mainActor
        )
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
        _azureProxyService = azureProxyService;
        _mainActor = mainActor;
    }
    
    
    // <inheritdoc/>
    public async Task<IApiResponse<bool>> TriggerDciServiceScorePublicationAsync(CancellationToken ct = default)
    {
        var dayPublished = DateTime.UtcNow;
        var publicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
        var publicationStartDate = publicationDate.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        var publicationEndDate = publicationDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        
        var publicationWeek = Miscellaneous.GetCurrentPublicationWeek(publicationDate);
        
        _logger.LogInformation("Processing DCI scores for week {PublicationWeek} and date {PublicationDate}",
            publicationWeek, publicationDate);
        
        var existingDciScores = await _repositoryContext.DciServiceScoreRepository
            .GetQueryable()
            .AsNoTracking()
            .FirstOrDefaultAsync(x => 
                x.PublicationStartDate>= publicationStartDate &&
                x.PublicationEndDate >= publicationEndDate, ct);
        
        
        if (existingDciScores != null)
        {
            _logger.LogWarning(
                "[DciServiceScoreService: TriggerDciServiceScorePublicationAsync] DCI scores already published for this date: {PublicationDate}",
                publicationDate);
            return true.ToOkApiResponse($"DCI scores already published for day: {publicationDate} in week: {publicationWeek}");
        }

        await _mainActor.Tell(new PublishDciServiceScoresMessage()
        {
            PublicationDate = dayPublished,
            PublicationWeek = publicationWeek,
            PublicationStartDate = publicationStartDate,
            PublicationEndDate = publicationEndDate
        }, ActorRefs.Nobody);
        
        return true.ToOkApiResponse("DciServiceScore publication triggered successfully");
    }

    public async Task<IApiResponse<bool>> PublishDciServiceScoresAsync(
        PublishDciServiceScoresMessage request,
        CancellationToken ct = default)
    {
        try
        {
            var rankingData = new List<RepositoryDciScore>();
            var serviceStats = new ServiceDciScoreComputationStatistics();
            var baseQuery = _repositoryContext.RepositoryRepository.GetQueryable().AsNoTracking();
            var totalCount = await baseQuery.CountAsync(ct);

            if (totalCount == 0)
            {
                _logger.LogWarning("No repositories found");
                return ApiResponse<bool>.Default.ToNotFoundApiResponse("No repositories found");
            }

            var totalPages = (int)Math.Ceiling(totalCount / 50.0);
            _logger.LogInformation("Processing {Total} repositories in {Pages} pages of 50.", totalCount, totalPages);

            var (processed, successful, failed) = await ProcessRepositoryPagesAsync(baseQuery, totalPages, request, serviceStats, rankingData, ct);

            if (rankingData.Count > 0)
                await _mainActor.Tell(new CurateDciProductRankingMessage(rankingData, request.PublicationDate, request.PublicationWeek, request.PublicationStartDate, request.PublicationEndDate), ActorRefs.Nobody);

            await _mainActor.Tell(new DeploymentAggregatorActorMessage
            {
                ActivatedOn = DateTime.UtcNow,
                PublicationDate = request.PublicationDate,
                PublicationWeek = request.PublicationWeek,
                PublicationStartDate = request.PublicationStartDate,
                PublicationEndDate = request.PublicationEndDate
            }, ActorRefs.Nobody);

            _logger.LogInformation("Processed {Processed} repositories, {Successful} were successful and {Failed} failed", processed, successful, failed);
            return true.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed processing repositories");
            return false.ToFailedDependencyApiResponse(ex.Message);
        }
    }

    private async Task<(int Processed, int Successful, int Failed)> ProcessRepositoryPagesAsync(
        IQueryable<Repository> baseQuery,
        int totalPages,
        PublishDciServiceScoresMessage request,
        ServiceDciScoreComputationStatistics serviceStats,
        List<RepositoryDciScore> rankingData,
        CancellationToken ct)
    {
        int processed = 0, successful = 0, failed = 0;
        for (var pageIndex = 0; pageIndex < totalPages; pageIndex++)
        {
            var batch = await baseQuery
                .OrderBy(r => r.CreatedAt)
                .ThenBy(r => r.Id)
                .Skip(pageIndex * 50)
                .Take(50)
                .ToListAsync(ct);

            if (batch.Count == 0) break;
            _logger.LogDebug("Processing page {Page}/{TotalPages} (size {Count})", pageIndex + 1, totalPages, batch.Count);

            var (p, s, f) = await ProcessRepositoryBatchAsync(batch, request, serviceStats, rankingData, ct);
            processed += p;
            successful += s;
            failed += f;
        }
        return (processed, successful, failed);
    }

    private async Task<(int Processed, int Successful, int Failed)> ProcessRepositoryBatchAsync(
        List<Repository> batch,
        PublishDciServiceScoresMessage request,
        ServiceDciScoreComputationStatistics serviceStats,
        List<RepositoryDciScore> rankingData,
        CancellationToken ct)
    {
        int processed = 0, successful = 0, failed = 0;
        foreach (var repo in batch)
        {
            processed++;
            var response = await ComputeDciForRepoServicesAsync(
                repo, request.PublicationWeek, request.PublicationDate,
                request.PublicationStartDate, request.PublicationEndDate,
                serviceStats, ct);

            if (response is { Data: null, Code: not (int)HttpStatusCode.OK })
            {
                failed++;
                _logger.LogWarning("[DciServiceScoreService] Failed to calculate DCI for repository {RepositoryId}", repo.Id);
                continue;
            }

            if (response.Data == null) continue;
            successful++;
            rankingData.Add(response.Data);
        }
        return (processed, successful, failed);
    }

    /// <summary>
    /// Computes the DCI score for all services within a repository.
    /// </summary>
    private async Task<IApiResponse<RepositoryDciScore>> ComputeDciForRepoServicesAsync(
        Repository repository,
        string publicationWeek,
        DateTime publicationDate,
        DateTime publicationStartDate,
        DateTime publicationEndDate,
        ServiceDciScoreComputationStatistics serviceStats,
        CancellationToken ct)
    {
        try
        {
            _logger.LogDebug("[DciServiceScoreService] Calculating DCI for repository {RepositoryId}", repository.Id);

            var repoExtraInfo = await _repositoryContext.GetProductGroupByRepositoryFilterAsync(
                repository.Id, repository.SonarQubeKey, repository, ct);

            var baseQuery = _repositoryContext.ServiceRepository
                .GetQueryable()
                .AsNoTracking()
                .Where(s => s.RepositoryId == repository.Id);

            var totalCount = await baseQuery.CountAsync(ct);
            if (totalCount == 0)
            {
                _logger.LogInformation("No services matched for repository {RepositoryId}", repository.Id);
                return ApiResponse<RepositoryDciScore>.Default.ToNotFoundApiResponse();
            }

            var repoServicesDciScores = new List<DciServiceScore>();
            int processed = 0, failed = 0, successful = 0;
            int totalPages = (int)Math.Ceiling(totalCount / 50.0);

            for (var pageIndex = 0; pageIndex < totalPages; pageIndex++)
            {
                var batch = await baseQuery
                    .OrderBy(r => r.CreatedAt)
                    .ThenBy(r => r.Id)
                    .Skip(pageIndex * 50)
                    .Take(50)
                    .ToListAsync(ct);

                if (batch.Count == 0) break;

                var result = await ProcessServiceBatchAsync(new ProcessServiceBatchRequest(batch, repository, repoExtraInfo ?? new ProductGroupDetails(), publicationWeek, publicationDate, publicationStartDate, publicationEndDate, serviceStats, ct));
                repoServicesDciScores.AddRange(result.Scores);
                processed += result.Processed;
                successful += result.Successful;
                failed += result.Failed;
            }

            var repositoryDciScore = repoServicesDciScores.Count > 0
                ? repoServicesDciScores.Sum(x => x.FinalScore) / repoServicesDciScores.Count
                : 0;

            var productTeamResult = (repoExtraInfo?.ProductTeams.Items ?? []).FirstOrDefault(x =>
                x.Repositories.Items.Any(r => r.Id == repository.Id));

            var repositoryDciScoreResult = new RepositoryDciScore
            {
                RepositoryId = repository.Id,
                RepositoryName = repository.Name,
                RepositoryUrl = repository.Url,
                RepositoryType = repository.Type,
                DciScore = repositoryDciScore ?? 0,
                ProductGroupId = repoExtraInfo?.ProductGroupId ?? string.Empty,
                ProductGroupName = repoExtraInfo?.ProductGroupName ?? string.Empty,
                ProductTeamId = productTeamResult?.Id ?? string.Empty,
                ProductTeamName = productTeamResult?.Name ?? string.Empty
            };

            var savedCount = await _repositoryContext.DciServiceScoreRepository.AddRangeAsync(repoServicesDciScores, ct);

            if (savedCount > 0)
            {
                _logger.LogDebug("Processed {Processed} services, {Successful} successful, {Failed} failed for repository {RepositoryId} ({RepositoryName})", processed, successful, failed, repository.Id, repository.Name);
                return repositoryDciScoreResult.ToOkApiResponse();
            }

            _logger.LogError("[DciServiceScoreService] Failed to save DCI scores for repository {RepositoryId}", repository.Id);
            return ApiResponse<RepositoryDciScore>.Default.ToFailedDependencyApiResponse("Failed to save DCI scores");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed processing repository {RepositoryId}", repository.Id);
            return ApiResponse<RepositoryDciScore>.Default.ToFailedDependencyApiResponse(e.Message);
        }
    }

    private async Task<(List<DciServiceScore> Scores, int Processed, int Successful, int Failed)> ProcessServiceBatchAsync(ProcessServiceBatchRequest request)
    {
        var scores = new List<DciServiceScore>();
        int processed = 0, successful = 0, failed = 0;

        foreach (var service in request.Batch)
        {
            processed++;
            request.ServiceStats.Total++;
            var serviceScore = await CalculateServiceDciAsync(request.Repository, service, request.PublicationStartDate, request.PublicationEndDate, request.Ct);

            if (serviceScore.Code != (int)HttpStatusCode.OK)
            {
                request.ServiceStats.Failed++;
                failed++;
                _logger.LogWarning("[DciServiceScoreService] Failed to calculate DCI for service {ServiceId} with status code {StatusCode} and message {Message}", service.Id, serviceScore.Code, serviceScore.Message);
                continue;
            }

            var productTeam = (request.RepoExtraInfo?.ProductTeams.Items ?? []).FirstOrDefault(x =>
                x.Repositories.Items.Any(r => r.Id == request.Repository.Id));
            var dciData = serviceScore.Data;
            scores.Add(CreateDciScore(new CreateDciScoreItemRequest(request.Repository, request.RepoExtraInfo ?? new ProductGroupDetails(), productTeam ?? new ProductTeamItem(), service, dciData, request.PublicationWeek, request.PublicationDate, request.PublicationStartDate, request.PublicationEndDate)));
            request.ServiceStats.Successful++;
            successful++;
        }

        return (scores, processed, successful, failed);
    }

    private DciServiceScore CreateDciScore(CreateDciScoreItemRequest createDciScoreItemRequest)
    {
        var dciInput = createDciScoreItemRequest.DciData.Item1;
        var dciResult = createDciScoreItemRequest.DciData.Item2;
        return new DciServiceScore
        {
            ProductGroupId = createDciScoreItemRequest.RepoExtraInfo.ProductGroupId,
            ProductGroupName = createDciScoreItemRequest.RepoExtraInfo.ProductGroupName,
            ProductTeamId = createDciScoreItemRequest.ProductTeam.Id,
            ProductTeamName = createDciScoreItemRequest.ProductTeam.Name,
            RepositoryId = createDciScoreItemRequest.Repository.Id,
            RepositoryName = createDciScoreItemRequest.Repository.Name,
            RepositoryType = createDciScoreItemRequest.Repository.Type,
            RepositoryUrl = createDciScoreItemRequest.Repository.Url,
            RepositorySonarQubeKey = createDciScoreItemRequest.Repository.SonarQubeKey,
            ServiceId = createDciScoreItemRequest.Service.Id,
            FinalScore = dciResult.DciScore,
            StabilityScore = dciInput.StabilityScore,
            ProcessComplianceScore = dciInput.ProcessComplianceScore,
            SpeedScore = dciInput.SpeedScore,
            SuccessRateScore = dciInput.SuccessRate,
            IssueSeverityScore = dciInput.IssueSeverityScore,
            CodeConfidenceIndexScore = dciInput.CodeConfidenceIndex,
            PublicationWeek = createDciScoreItemRequest.PublicationWeek,
            PublicationDate = createDciScoreItemRequest.PublicationDate,
            PublicationStartDate = createDciScoreItemRequest.PublicationStartDate,
            PublicationEndDate = createDciScoreItemRequest.PublicationEndDate,
            CreatedAt = DateTime.UtcNow
        };
    }

    private async Task<IApiResponse<(DciInput, DciResult)>> CalculateServiceDciAsync(Repository repository, Service service,
        DateTime? startDate, DateTime? endDate, CancellationToken ct)
    {
        try
        {
            _logger.LogDebug("[DciServiceScoreService] Calculating DCI for service {ServiceId} with start date and end date as {StartDate} {EndDate}", service.Id, startDate, endDate);
            
            var (project, repoName) = _azureProxyService.ExtractProjectAndRepository(repository.Url);
            
            if (string.IsNullOrEmpty(project) || string.IsNullOrEmpty(repoName))
            {
                return ApiResponse<(DciInput, DciResult)>.Default.ToFailedDependencyApiResponse("Invalid repository URL");
            }

            var azureRepoIdResponse = await _azureProxyService.GetRepositoryDetailAsync(project, repoName, ct);
            
            if (azureRepoIdResponse.Data == null || azureRepoIdResponse.Code != (int)(HttpStatusCode.OK))
            {
                return ApiResponse<(DciInput, DciResult)>.Default.ToFailedDependencyApiResponse("Failed to get Azure repository ID");
            }

            
            var releaseDefinitionId = int.TryParse(service.ServiceReleaseDefinitionId, out var id) ? id : 0;
            
            var deploymentMetricsResponse = await CalculateDeploymentMetricsWithAdaptiveWindowAsync(project, releaseDefinitionId, azureRepoIdResponse.Data?.Id ?? string.Empty, service.Name, endDate ?? DateTime.UtcNow, ct);

            if (deploymentMetricsResponse.Data == null || deploymentMetricsResponse.Code != (int)(HttpStatusCode.OK))
            {
                _logger.LogError(
                    "[DciServiceScoreService] Failed to calculate deployment metrics for service {ServiceId} with status code {StatusCode} and message {Message}",
                    service.Id, deploymentMetricsResponse.Code, deploymentMetricsResponse.Message);
                return ApiResponse<(DciInput, DciResult)>.Default.ToFailedDependencyApiResponse("Failed to calculate deployment metrics");
            }

            var cciScore = await GetCodeConfidenceIndexAsync(service.RepositoryId, repository.Name, ct);

            var dciInput = new DciInput
            {
                StabilityScore = deploymentMetricsResponse.Data.StabilityScore,
                ProcessComplianceScore = deploymentMetricsResponse.Data.ProcessComplianceScore,
                SpeedScore = deploymentMetricsResponse.Data.SpeedScore,
                SuccessRate = deploymentMetricsResponse.Data.SuccessRate,
                IssueSeverityScore = deploymentMetricsResponse.Data.IssueSeverityScore,
                CodeConfidenceIndex = cciScore / 100,
                DataQuality = deploymentMetricsResponse.Data.DataQuality
            };

            var dciResult = CalculateDciWithQualityIndicator(dciInput);


            return (dciInput, dciResult).ToOkApiResponse();

        }
        catch (Exception e)
        {
           
            _logger.LogError(e, "[DciServiceScoreService] Failed to calculate DCI for service {ServiceId}", service.Id);
            return ApiResponse<(DciInput, DciResult)>.Default.ToServerErrorApiResponse();
        }
    }
    private async Task<decimal> GetCodeConfidenceIndexAsync(string repositoryId, string repositoryName, CancellationToken ct)
    {
        try
        {
            var cciScores = await _repositoryContext.CciRepositoryScoreRepository
                .GetQueryable()
                .AsNoTracking()
                .Where(r => r.RepositoryId == repositoryId || r.RepositoryName == repositoryName)
                .OrderByDescending(r => r.PublicationDate)
                .FirstOrDefaultAsync(ct);

            return cciScores?.FinalAverage ?? 0.0m;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "[DciServiceScoreService] Failed to get CCI score for repository {RepositoryId}", repositoryId);
            return 0.0m;
        }
    }

    private static DciResult CalculateDciWithQualityIndicator(DciInput input)
    {
        var weights = new Dictionary<string, decimal>
        {
            { "stability_score", 0.20m },
            { "process_compliance_score", 0.20m },
            { "speed_score", 0.10m },
            { "success_rate", 0.20m },
            { "issue_severity_score", 0.15m },
            { "code_confidence_index", 0.15m }
        };

        var score = (input.StabilityScore * weights["stability_score"]) +
                   (input.ProcessComplianceScore * weights["process_compliance_score"]) +
                   (input.SpeedScore * weights["speed_score"]) +
                   (input.SuccessRate * weights["success_rate"]) +
                   (input.IssueSeverityScore * weights["issue_severity_score"]) +
                   (input.CodeConfidenceIndex * weights["code_confidence_index"]);

        var baseLabel = score switch
        {
            >= 0.9m => "Excellent",
            >= 0.75m => "Good", 
            >= 0.6m => "Moderate",
            >= 0.4m => "Low",
            _ => "Critical"
        };

        var qualityModifier = input.DataQuality switch
        {
            DataQuality.NoData => " (Insufficient Data)",
            DataQuality.SlidingWindowFallback => " (Historical Data)",
            DataQuality.SlidingWindow => " (Recent History)",
            DataQuality.PeriodBased => "",
            _ => ""
        };

        return new DciResult
        {
            DciScore = Math.Round(score, 4),
            ConfidenceLabel = baseLabel + qualityModifier
        };
    }

    private async Task<IApiResponse<EnhancedDeploymentMetrics>> CalculateDeploymentMetricsWithAdaptiveWindowAsync(
        string project,
        int releaseDefinitionId,
        string repositoryId,
        string serviceName,
        DateTime calculationDate,
        CancellationToken ct)
    {
        try
        {
            var deploymentPattern = await AnalyzeDeploymentFrequencyAsync(project, releaseDefinitionId, calculationDate, ct);
            
            var strategy = DetermineOptimalStrategy(deploymentPattern, calculationDate);
            
            _logger.LogDebug("[DciServiceScoreService] Using strategy {Strategy} for service {ServiceName} with pattern {Pattern}", 
                strategy.StrategyType, serviceName, deploymentPattern.FrequencyType);

            return strategy.StrategyType switch
            {
                DciCalculationStrategy.DailyPeriod => await CalculateDailyPeriodMetricsAsync(project, releaseDefinitionId, repositoryId, serviceName, calculationDate, ct),
                DciCalculationStrategy.WeeklyPeriod => await CalculateWeeklyPeriodMetricsAsync(project, releaseDefinitionId, repositoryId, serviceName, calculationDate, ct),
                DciCalculationStrategy.SlidingWindow => await CalculateSlidingWindowMetricsAsync(project, releaseDefinitionId, repositoryId, serviceName, strategy.WindowSize, ct),
                DciCalculationStrategy.Interpolated => await CalculateInterpolatedMetricsAsync(project, releaseDefinitionId, repositoryId, serviceName, calculationDate, ct),
                _ => await GetDefaultMetricsAsync(serviceName)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DciServiceScoreService] Failed to calculate adaptive deployment metrics");
            return new EnhancedDeploymentMetrics().ToFailedDependencyApiResponse(ex.Message);
        }
    }

    private async Task<DeploymentFrequencyPattern> AnalyzeDeploymentFrequencyAsync(
        string project, 
        int releaseDefinitionId, 
        DateTime calculationDate, 
        CancellationToken ct)
    {
        // Look back 90 days
        var lookbackStart = calculationDate.AddDays(-90);
        
        var recentReleases = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, 200, ct);
        
        if (recentReleases.Data == null || recentReleases.Code != (int)(HttpStatusCode.OK) || recentReleases.Data?.Count == 0)
        {
            return new DeploymentFrequencyPattern { FrequencyType = DeploymentFrequency.None };
        }

        if (recentReleases.Data == null)
            return new DeploymentFrequencyPattern { FrequencyType = DeploymentFrequency.None };
        var releases = recentReleases.Data
            .Where(r => (r.Environments ?? []).Any(env => (env.Name ?? string.Empty).Contains(ValidationConstants.AzureProxyRelated.Production, StringComparison.CurrentCultureIgnoreCase)))
            .Where(r => GetReleaseDate(r) >= lookbackStart)
            .OrderByDescending(GetReleaseDate)
            .ToList();

        if (releases.Count == 0)
        {
            return new DeploymentFrequencyPattern { FrequencyType = DeploymentFrequency.None };
        }

        var daysBetweenDeployments = CalculateAverageDaysBetweenDeployments(releases);
        var totalDeployments = releases.Count;
        var daysWithData = (calculationDate - lookbackStart).TotalDays;
        var deploymentsPerDay = totalDeployments / daysWithData;

        return new DeploymentFrequencyPattern
        {
            FrequencyType = daysBetweenDeployments switch
            {
                <= 1.0 => DeploymentFrequency.MultipleDaily,
                <= 3.0 => DeploymentFrequency.Daily,
                <= 7.0 => DeploymentFrequency.Weekly,
                <= 14.0 => DeploymentFrequency.BiWeekly,
                <= 30.0 => DeploymentFrequency.Monthly,
                _ => DeploymentFrequency.Infrequent
            },
            AverageDaysBetween = daysBetweenDeployments,
            DeploymentsPerDay = deploymentsPerDay,
            TotalDeployments = totalDeployments,
            LastDeploymentDate = GetReleaseDate(releases[0])
        };

    }
    
    
    private static DateTime GetReleaseDate(AzureReleaseDetail release)
    {
        return (release.Environments ?? []).Where(env => (env.Name ?? string.Empty).Contains(ValidationConstants.AzureProxyRelated.Production, StringComparison.CurrentCultureIgnoreCase))
            .Select(env => env.DeploySteps?.LastOrDefault()
                ?.ReleaseDeployPhases?.LastOrDefault()
                ?.DeploymentJobs?.LastOrDefault()
                ?.Tasks?.LastOrDefault()?.FinishTime)
            .FirstOrDefault() ?? release.CreatedOn ?? DateTime.MinValue;
    }

    /// <summary>
    /// Determines the optimal DCI calculation strategy based on the deployment frequency pattern and calculation date.
    /// </summary>
    /// <param name="pattern">The deployment frequency pattern containing the last deployment date and frequency type.</param>
    /// <param name="calculationDate">The date for which the strategy is being calculated.</param>
    /// <returns>
    /// A <see cref="DciCalculationStrategyInfo"/> object representing the selected strategy and data quality.
    /// </returns>
    private static DciCalculationStrategyInfo DetermineOptimalStrategy(DeploymentFrequencyPattern pattern, DateTime calculationDate)
    {
        var daysSinceLastDeployment = pattern.LastDeploymentDate.HasValue 
            ? (calculationDate - pattern.LastDeploymentDate.Value).TotalDays 
            : double.MaxValue;

        return pattern.FrequencyType switch
        {
            DeploymentFrequency.MultipleDaily or DeploymentFrequency.Daily => 
                daysSinceLastDeployment <= 1.0 
                    ? new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.DailyPeriod, DataQuality = DataQuality.PeriodBased }
                    : new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.SlidingWindow, WindowSize = 5, DataQuality = DataQuality.SlidingWindow },

            DeploymentFrequency.Weekly => 
                daysSinceLastDeployment <= 7.0
                    ? new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.WeeklyPeriod, DataQuality = DataQuality.PeriodBased }
                    : new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.SlidingWindow, WindowSize = 3, DataQuality = DataQuality.SlidingWindow },

            DeploymentFrequency.BiWeekly or DeploymentFrequency.Monthly => 
                new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.SlidingWindow, WindowSize = 3, DataQuality = DataQuality.SlidingWindow },

            DeploymentFrequency.Infrequent => 
                daysSinceLastDeployment <= 60.0
                    ? new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.Interpolated, DataQuality = DataQuality.SlidingWindowFallback }
                    : new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.Default, DataQuality = DataQuality.NoData },
            
            DeploymentFrequency.None => 
                new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.Default, DataQuality = DataQuality.NoData },
            
            _ => new DciCalculationStrategyInfo { StrategyType = DciCalculationStrategy.Default, DataQuality = DataQuality.NoData },
        };
    }

    private async Task<IApiResponse<EnhancedDeploymentMetrics>> CalculateDailyPeriodMetricsAsync(
        string project,
        int releaseDefinitionId,
        string repositoryId,
        string serviceName,
        DateTime calculationDate,
        CancellationToken ct)
    {
        try
        {
            var startDate = calculationDate.Date;
            var endDate = calculationDate.Date.AddDays(1).AddSeconds(-1);
            
            var request = new CalculateDeploymentMetricsRequest
            {
                Project = project,
                ReleaseDefinitionId = releaseDefinitionId,
                RepositoryId = repositoryId,
                ServiceName = serviceName,
                Top = 50
            };

            var metricsResponse = await _azureProxyService.CalculateDeploymentMetricsAsync(request, ct);
            
            if (metricsResponse.Data is null || metricsResponse.Code != (int)(HttpStatusCode.OK))
            {
                return await GetDefaultMetricsAsync(serviceName);
            }

            var releasesResponse = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, 50, ct);
            var dayDeployments = releasesResponse.Data?
                .Count(r => (r.Environments ?? []).Any(env =>
                    (env.Name ?? string.Empty).Contains(ValidationConstants.AzureProxyRelated.Production, StringComparison.CurrentCultureIgnoreCase) &&
                    GetReleaseDate(r) >= startDate && GetReleaseDate(r) <= endDate)) ?? 0;

            return new EnhancedDeploymentMetrics
            {
                StabilityScore = metricsResponse.Data.StabilityScore,
                ProcessComplianceScore = metricsResponse.Data.ProcessComplianceScore,
                SpeedScore = metricsResponse.Data.SpeedScore,
                SuccessRate = metricsResponse.Data.SuccessRate,
                IssueSeverityScore = metricsResponse.Data.IssueSeverityScore,
                DeploymentCount = dayDeployments,
                DataQuality = DataQuality.PeriodBased
            }.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DciServiceScoreService] Failed to calculate daily period metrics for {ServiceName}", serviceName);
            return await GetDefaultMetricsAsync(serviceName);
        }
    }

    private async Task<IApiResponse<EnhancedDeploymentMetrics>> CalculateWeeklyPeriodMetricsAsync(
        string project,
        int releaseDefinitionId,
        string repositoryId,
        string serviceName,
        DateTime calculationDate,
        CancellationToken ct)
    {
        try
        {
            var weekStart = calculationDate.Date.AddDays(-(int)calculationDate.DayOfWeek);
            var weekEnd = weekStart.AddDays(7).AddSeconds(-1);
            
            var request = new CalculateDeploymentMetricsRequest
            {
                Project = project,
                ReleaseDefinitionId = releaseDefinitionId,
                RepositoryId = repositoryId,
                ServiceName = serviceName,
                Top = 100
            };

            var metricsResponse = await _azureProxyService.CalculateDeploymentMetricsAsync(request, ct);
            
            if (metricsResponse.Data is null || metricsResponse.Code != (int)(HttpStatusCode.OK))
            {
                return await GetDefaultMetricsAsync(serviceName);
            }

            var releasesResponse = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, 100, ct);
            var weekDeployments = releasesResponse.Data?
                .Count(r => (r.Environments ?? []).Any(env =>
                    (env.Name ?? string.Empty).ToLower().Contains(ValidationConstants.AzureProxyRelated.Production.ToLower()) &&
                    GetReleaseDate(r) >= weekStart && GetReleaseDate(r) <= weekEnd)) ?? 0;

            return new EnhancedDeploymentMetrics
            {
                StabilityScore = metricsResponse.Data.StabilityScore,
                ProcessComplianceScore = metricsResponse.Data.ProcessComplianceScore,
                SpeedScore = metricsResponse.Data.SpeedScore,
                SuccessRate = metricsResponse.Data.SuccessRate,
                IssueSeverityScore = metricsResponse.Data.IssueSeverityScore,
                DeploymentCount = weekDeployments,
                DataQuality = DataQuality.PeriodBased
            }.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DciServiceScoreService] Failed to calculate weekly period metrics for {ServiceName}", serviceName);
            return await GetDefaultMetricsAsync(serviceName);
        }
    }

    private async Task<IApiResponse<EnhancedDeploymentMetrics>> CalculateSlidingWindowMetricsAsync(
        string project,
        int releaseDefinitionId,
        string repositoryId,
        string serviceName,
        int windowSize,
        CancellationToken ct)
    {
        try
        {
            var request = new CalculateDeploymentMetricsRequest
            {
                Project = project,
                ReleaseDefinitionId = releaseDefinitionId,
                RepositoryId = repositoryId,
                ServiceName = serviceName,
                Top = windowSize * 2
            };

            var metricsResponse = await _azureProxyService.CalculateDeploymentMetricsAsync(request, ct);
            
            if (metricsResponse.Data is null || metricsResponse.Code != (int)(HttpStatusCode.OK))
            {
                return await GetDefaultMetricsAsync(serviceName);
            }
            
            var releasesResponse = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, windowSize * 2, ct);
            var recentDeployments = releasesResponse.Data?
                .Where(r => (r.Environments ?? []).Any(env => (env.Name ?? string.Empty).ToLower().Contains(ValidationConstants.AzureProxyRelated.Production.ToLower())))
                .OrderByDescending(GetReleaseDate)
                .Take(windowSize)
                .Count() ?? 0;

            return new EnhancedDeploymentMetrics
            {
                StabilityScore = metricsResponse.Data.StabilityScore,
                ProcessComplianceScore = metricsResponse.Data.ProcessComplianceScore,
                SpeedScore = metricsResponse.Data.SpeedScore,
                SuccessRate = metricsResponse.Data.SuccessRate,
                IssueSeverityScore = metricsResponse.Data.IssueSeverityScore,
                DeploymentCount = recentDeployments,
                DataQuality = DataQuality.SlidingWindow
            }.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DciServiceScoreService] Failed to calculate sliding window metrics for {ServiceName}", serviceName);
            return await GetDefaultMetricsAsync(serviceName);
        }
    }

    private async Task<IApiResponse<EnhancedDeploymentMetrics>> CalculateInterpolatedMetricsAsync(
        string project,
        int releaseDefinitionId,
        string repositoryId,
        string serviceName,
        DateTime calculationDate,
        CancellationToken ct)
    {
        try
        {
            var slidingResponse = await CalculateSlidingWindowMetricsAsync(project, releaseDefinitionId, repositoryId, serviceName, 3, ct);
            
            if (slidingResponse.Data is null || slidingResponse.Code != (int)(HttpStatusCode.OK))
            {
                return await GetDefaultMetricsAsync(serviceName);
            }

            var releasesResponse = await _azureProxyService.GetRecentReleasesAsync(project, releaseDefinitionId, 10, ct);
            var lastDeployment = releasesResponse.Data?
                .Where(r => (r.Environments ?? []).Any(env => (env.Name ?? string.Empty).ToLower().Contains(ValidationConstants.AzureProxyRelated.Production.ToLower())))
                .OrderByDescending(GetReleaseDate)
                .FirstOrDefault();

            var daysSinceLastDeployment = lastDeployment != null 
                ? (calculationDate - GetReleaseDate(lastDeployment)).TotalDays 
                : double.MaxValue;

            var decayFactor = daysSinceLastDeployment switch
            {
                <= 7 => 1.0m,
                <= 14 => 0.9m,
                <= 30 => 0.8m,
                <= 60 => 0.7m,
                _ => 0.5m
            };

            var metrics = slidingResponse.Data;
            
            return new EnhancedDeploymentMetrics
            {
                StabilityScore = Math.Max(0.1m, metrics.StabilityScore * decayFactor),
                ProcessComplianceScore = Math.Max(0.1m, metrics.ProcessComplianceScore * decayFactor),
                SpeedScore = Math.Max(0.1m, metrics.SpeedScore * decayFactor),
                SuccessRate = Math.Max(0.1m, metrics.SuccessRate * decayFactor),
                IssueSeverityScore = Math.Max(0.1m, metrics.IssueSeverityScore * decayFactor),
                DeploymentCount = metrics.DeploymentCount,
                DataQuality = DataQuality.SlidingWindowFallback
            }.ToOkApiResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DciServiceScoreService] Failed to calculate interpolated metrics for {ServiceName}", serviceName);
            return await GetDefaultMetricsAsync(serviceName);
        }
    }

    private async Task<IApiResponse<EnhancedDeploymentMetrics>> GetDefaultMetricsAsync(string serviceName)
    {
        _logger.LogWarning("[DciServiceScoreService] Using default metrics for service {ServiceName}", serviceName);
        
        return await Task.FromResult(new EnhancedDeploymentMetrics
        {
            StabilityScore = 0.5m,
            ProcessComplianceScore = 0.5m,
            SpeedScore = 0.5m,
            SuccessRate = 0.5m,
            IssueSeverityScore = 0.5m,
            DeploymentCount = 0,
            DataQuality = DataQuality.NoData
        }.ToOkApiResponse());
    }

    private static double CalculateAverageDaysBetweenDeployments(List<AzureReleaseDetail> releases)
    {
        if (releases.Count <= 1) return double.MaxValue;
        
        var dates = releases.Select(GetReleaseDate).OrderBy(d => d).ToList();
        
        var intervals = new List<double>();
        
        for (var i = 1; i < dates.Count; i++)
        {
            intervals.Add((dates[i] - dates[i - 1]).TotalDays);
        }
        
        return intervals.Count != 0 ? intervals.Average() : double.MaxValue;
    }

    public async Task<IApiResponse<PagedResult<GetDciServiceScoreResponse>>> GetDciServiceScoresAsync(SearchFilter filter, CancellationToken ct = default)
    {
        try
        {
            _logger.LogDebug(
                "[DciServiceScoreService::GetDciServiceScoresAsync] Received Request to get DciServiceScores => RequestPayload => {Filter}",
                filter.Serialize());

            var query = _repositoryContext.DciServiceScoreRepository.GetQueryable().AsNoTracking();

            var pagedResult = await query
                .Where(c => filter.SearchTerm == null ||
                            (c.ServiceName ?? string.Empty).ToLower().Contains(filter.SearchTerm.ToLower()))
                .OrderByDescending(x => x.CreatedAt).GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

            var results = pagedResult.Results.Select(p => p.Adapt<GetDciServiceScoreResponse>()).ToList();

            var response = new PagedResult<GetDciServiceScoreResponse>
            {
                Results = results,
                UpperBound = pagedResult.UpperBound,
                LowerBound = pagedResult.LowerBound,
                PageIndex = pagedResult.PageIndex,
                PageSize = pagedResult.PageSize,
                TotalCount = pagedResult.TotalCount,
                TotalPages = pagedResult.TotalPages
            };

            return response.ToOkApiResponse();
        }
        catch (Exception e)
        {
           
            _logger.LogError(e, "[DciServiceScoreService::GetDciServiceScoresAsync] Error getting DciServiceScores => RequestPayload => {Filter}",
                filter.Serialize());
            return ApiResponse<PagedResult<GetDciServiceScoreResponse>>.Default.ToServerErrorApiResponse();
        }
    }

    public async Task<IApiResponse<GetDciServiceScoreResponse>> GetDciServiceScoreAsync(string id, CancellationToken ct = default)
    {
        try
        {
            _logger.LogDebug(
                "[DciServiceScoreService::GetDciServiceScoreAsync] Received Request to get DciServiceScore => RequestPayload => {Id}",
                id);

            var entity = await _repositoryContext.DciServiceScoreRepository.GetByIdAsync(id, ct);
            return entity == null ? ApiResponse<GetDciServiceScoreResponse>.Default.ToNotFoundApiResponse() : entity.Adapt<GetDciServiceScoreResponse>().ToOkApiResponse();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "[DciServiceScoreService::GetDciServiceScoreAsync] Error getting DciServiceScore => RequestPayload => {Id}",
                id);
            return ApiResponse<GetDciServiceScoreResponse>.Default.ToServerErrorApiResponse();
        }
    }

    public async Task<IApiResponse<bool>> UpdateDciServiceScoreAsync(string id, UpdateDciServiceScoreRequest request, CancellationToken ct = default)
    {
        try
        {
            _logger.LogDebug(
                "[DciServiceScoreService::UpdateDciServiceScoreAsync] Received Request to update DciServiceScore => RequestPayload => {Request}",
                request.Serialize());
            
            var existingEntity = await _repositoryContext.DciServiceScoreRepository.GetByIdAsync(id, ct);
            
            if (existingEntity == null)
            {
                _logger.LogWarning("DciServiceScore not found with ID: {Id}", id);
                return ApiResponse<bool>.Default.ToNotFoundApiResponse();
            }
            
            existingEntity.FinalScore = request.FinalScore ?? existingEntity.FinalScore;
            existingEntity.StabilityScore = request.StabilityScore ?? existingEntity.StabilityScore;
            existingEntity.ProcessComplianceScore = request.ProcessComplianceScore ?? existingEntity.ProcessComplianceScore;
            existingEntity.SpeedScore = request.SpeedScore ?? existingEntity.SpeedScore;
            existingEntity.SuccessRateScore = request.SuccessRateScore ?? existingEntity.SuccessRateScore;
            existingEntity.IssueSeverityScore = request.IssueSeverityScore ?? existingEntity.IssueSeverityScore;
            existingEntity.CodeConfidenceIndexScore = request.CodeConfidenceIndexScore ?? existingEntity.CodeConfidenceIndexScore;
            existingEntity.RepositoryId = request.RepositoryId ?? existingEntity.RepositoryId;
            existingEntity.RepositoryName = request.RepositoryName ?? existingEntity.RepositoryName;
            existingEntity.RepositoryType = request.RepositoryType ?? existingEntity.RepositoryType;
            existingEntity.RepositoryUrl = request.RepositoryUrl ?? existingEntity.RepositoryUrl;
            existingEntity.RepositorySonarQubeKey = request.RepositorySonarQubeKey ?? existingEntity.RepositorySonarQubeKey;
            existingEntity.UpdatedAt = DateTime.UtcNow;

            var updated = await _repositoryContext.DciServiceScoreRepository.UpdateAsync(existingEntity, ct) > 0;

            return updated ? updated.ToOkApiResponse() : updated.ToNotFoundApiResponse();

        }
        catch (Exception e)
        {
            _logger.LogError(e, "[DciServiceScoreService::UpdateDciServiceScoreAsync] Error updating DciServiceScore => RequestPayload => {Request}",
                request.Serialize());
            return ApiResponse<bool>.Default.ToServerErrorApiResponse();
            
        }
    }

    public async Task<IApiResponse<bool>> DeleteDciServiceScoreAsync(string id, CancellationToken ct = default)
    {

        try
        {
            _logger.LogDebug(
                "[DciServiceScoreService::DeleteDciServiceScoreAsync] Received Request to delete DciServiceScore => RequestPayload => {Id}",
                id);
            
            var existingEntity = await _repositoryContext.DciServiceScoreRepository.GetByIdAsync(id, ct);
            
            if (existingEntity == null)
            {
                _logger.LogWarning("DciServiceScore not found with ID: {Id}", id);
                return ApiResponse<bool>.Default.ToNotFoundApiResponse();
            }
            
            var deleted = await _repositoryContext.DciServiceScoreRepository.DeleteAsync(existingEntity, ct) > 0;

            return deleted ? deleted.ToOkApiResponse() : deleted.ToNotFoundApiResponse();

        }
        catch (Exception e)
        {
            
            _logger.LogError(e, "[DciServiceScoreService::DeleteDciServiceScoreAsync] Error deleting DciServiceScore => RequestPayload => {Id}",
                id);
            return ApiResponse<bool>.Default.ToServerErrorApiResponse();
        }
    }

    public async Task<bool> CurateDciProductRankingAsync(List<RepositoryDciScore> rankingData, DateTime publicationDate, string publicationWeek, DateTime publicationStartDate, DateTime publicationEndDate, CancellationToken ct = default)
    {
        _logger.LogInformation("[DciServiceScoreService::CurateDciProductRankingAsync] Starting DCI product ranking curation");
        
        try
        {
            var dciServiceScores = await _repositoryContext.DciServiceScoreRepository
                .GetQueryable()
                .AsNoTracking()
                .Where(x => x.PublicationDate != null && x.PublicationDate == publicationDate)
                .CountAsync(ct);

            if (dciServiceScores == 0)
            {
                _logger.LogWarning("[DciServiceScoreService::CurateDciProductRankingAsync] No DCI service scores found for publication date: {PublicationDate}", publicationDate.Date);
                return false;
            }
            
            if (rankingData.Count == 0)
            {
                _logger.LogWarning("[DciServiceScoreService::CurateDciProductRankingAsync] No repository scores found for publication date: {PublicationDate}", publicationDate.Date);
                return false;
            }

            var productTeamScores = rankingData
                .GroupBy(x => new { x.ProductTeamId, x.ProductTeamName, x.ProductGroupId, x.ProductGroupName })
                .Select(g => new
                {
                    g.Key.ProductTeamId,
                    g.Key.ProductTeamName,
                    g.Key.ProductGroupId,
                    g.Key.ProductGroupName,
                    AverageScore = g.Average(s => s.DciScore),
                    RepositoryType = g.First().RepositoryType
                })
                .ToList();

            var dciRankingData = new DciRankings
            {
                Frontend = productTeamScores
                    .Where(x => x.RepositoryType == ValidationConstants.RepositoryType.Frontend)
                    .Select(x => new DciRankingItem
                    {
                        ProductTeamId = x.ProductTeamId,
                        ProductTeamName = x.ProductTeamName,
                        ProductGroupId = x.ProductGroupId,
                        ProductGroupName = x.ProductGroupName,
                        Rating = x.AverageScore,
                        Status = Miscellaneous.GetDciScoreStatus(x.AverageScore)
                    }).ToList(),
                Backend = productTeamScores
                    .Where(x => x.RepositoryType == ValidationConstants.RepositoryType.Backend)
                    .Select(x => new DciRankingItem
                    {
                        ProductTeamId = x.ProductTeamId,
                        ProductTeamName = x.ProductTeamName,
                        ProductGroupId = x.ProductGroupId,
                        ProductGroupName = x.ProductGroupName,
                        Rating = x.AverageScore,
                        Status = Miscellaneous.GetDciScoreStatus(x.AverageScore)
                    }).ToList(),
                Overall = productTeamScores
                    .Select(x => new DciRankingItem
                    {
                        ProductTeamId = x.ProductTeamId,
                        ProductTeamName = x.ProductTeamName,
                        ProductGroupId = x.ProductGroupId,
                        ProductGroupName = x.ProductGroupName,
                        Rating = x.AverageScore,
                        Status = Miscellaneous.GetDciScoreStatus(x.AverageScore)
                    }).ToList()
            };

            var dciBreakdown = new DciBreakDown()
            {
                RepositoryBreakDown = new DciRepositoryBreakDown
                {
                    Items = rankingData.Select(x => new DciRepositoryBreakDownItem
                    {
                        RepositoryId = x.RepositoryId,
                        RepositoryName = x.RepositoryName,
                        CurrentScore = x.DciScore,
                        ProductTeamId = x.ProductTeamId,
                        ProductTeamName = x.ProductTeamName,
                        ProductGroupId = x.ProductGroupId,
                        ProductGroupName = x.ProductGroupName
                    }).ToList()
                },
                ProductTeamBreakDown = new DciProductTeamBreakDown
                {
                    Items = productTeamScores.Select(x => new DciProductTeamBreakDownItem
                    {
                        ProductTeamId = x.ProductTeamId,
                        ProductTeamName = x.ProductTeamName,
                        ProductGroupId = x.ProductGroupId,
                        ProductGroupName = x.ProductGroupName,
                        CurrentScore = x.AverageScore,
                    }).ToList()
                },
                ProductGroupBreakDown = new DciProductGroupBreakDown
                {
                    Items = productTeamScores.GroupBy(x => x.ProductGroupId)
                        .Select(g => new DciProductGroupBreakDownItem
                        {
                            ProductGroupId = g.Key,
                            ProductGroupName = g.First().ProductGroupName,
                            CurrentScore = g.Average(s => s.AverageScore),
                        }).ToList()
                }
            };


            var dciRanking = new DciProductsRanking
            {
                PublicationDate = publicationDate,
                PublicationWeek = publicationWeek,
                PublicationStartDate = publicationStartDate,
                PublicationEndDate = publicationEndDate,
                CurrentScore = productTeamScores.Count > 0 ? productTeamScores.Average(x => x.AverageScore) : 0,
                Rankings = dciRankingData,
                DciBreakDown = dciBreakdown,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            foreach (var teamScore in productTeamScores)
            {
                var rankingItem = new DciRankingItem
                {
                    ProductTeamId = teamScore.ProductTeamId,
                    ProductTeamName = teamScore.ProductTeamName,
                    ProductGroupId = teamScore.ProductGroupId,
                    ProductGroupName = teamScore.ProductGroupName,
                    Rating = teamScore.AverageScore,
                    Status = Miscellaneous.GetDciScoreStatus(teamScore.AverageScore)
                };

                dciRanking.Rankings.Overall.Add(rankingItem);

                switch (teamScore.RepositoryType)
                {
                    case ValidationConstants.RepositoryType.Backend:
                        dciRanking.Rankings.Backend.Add(rankingItem);
                        break;
                    case ValidationConstants.RepositoryType.Frontend:
                        dciRanking.Rankings.Frontend.Add(rankingItem);
                        break;
                }
            }

            var savedCount = await _repositoryContext.DciProductsRankingRepository.AddAsync(dciRanking, ct);

            if (savedCount > 0)
            {
                _logger.LogInformation("[DciServiceScoreService::CurateDciProductRankingAsync] Successfully curated DCI product ranking for week: {PublicationWeek}", publicationWeek);
                return true;
            }

            _logger.LogError("[DciServiceScoreService::CurateDciProductRankingAsync] Failed to save DCI product ranking");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DciServiceScoreService::CurateDciProductRankingAsync] Error occurred while curating DCI product ranking");
            return false;
        }
    }
}
