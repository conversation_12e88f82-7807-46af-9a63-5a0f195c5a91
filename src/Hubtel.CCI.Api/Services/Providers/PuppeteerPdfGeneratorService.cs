using Hubtel.CCI.Api.Services.Interfaces;
using PuppeteerSharp;
using PuppeteerSharp.Media;

namespace Hubtel.CCI.Api.Services.Providers;

public class PuppeteerPdfGeneratorService : IPdfGenerator, IAsyncDisposable
{
    private readonly IBrowser _browser = Puppeteer.LaunchAsync(new LaunchOptions
        {
            Headless = true,
            Args =
            [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage", // Add this for Docker/Linux environments
                "--font-render-hinting=none" // Better font rendering
            ],
            DefaultViewport = new ViewPortOptions
            {
                Width = 1920,
                Height = 1080,
                DeviceScaleFactor = 1
            }
        })
        .GetAwaiter().GetResult();

    public async Task<byte[]> GeneratePdfAsync(string html, PdfOptions? options = null, CancellationToken ct = default)
    {
        await using var page = await _browser.NewPageAsync();
    
        // Set a desktop-like viewport before loading content
        await page.SetViewportAsync(new ViewPortOptions
        {
            Width = 1440,
            Height = 900,
            DeviceScaleFactor = 2,
            IsMobile = false,
            HasTouch = false,
            IsLandscape = false
        });
        
        await page.EmulateMediaTypeAsync(MediaType.Screen);

        await page.SetContentAsync(html, new NavigationOptions { 
            Timeout = 0,
            WaitUntil = new[] { WaitUntilNavigation.Networkidle0 }
        });
        
        await page.WaitForSelectorAsync("canvas", new WaitForSelectorOptions
        {
            Visible = true,
            Timeout = 5000
        });

        await page.WaitForExpressionAsync("window.chartsRendered === true", new WaitForFunctionOptions
        {
            PollingInterval = 100,
            Timeout = 5000
        });

        var pdfOptions = new PdfOptions
        {
            Width = "1440px",
            Height = "900px",
            PrintBackground = true,
            Scale = 0.90m,
            MarginOptions = new MarginOptions
            {
                Top = "0mm",
                Bottom = "0mm",
                Left = "0mm",
                Right = "0mm"
            },
            PreferCSSPageSize = false
        };

        return await page.PdfDataAsync(pdfOptions);
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            await _browser.CloseAsync();
        }
        finally
        {
            GC.SuppressFinalize(this);
        }
    }
}