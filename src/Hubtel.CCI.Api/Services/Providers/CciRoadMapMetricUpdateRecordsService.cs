using System.Globalization;
using Flurl;
using Flurl.Http;
using Flurl.Util;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Helpers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Exception = System.Exception;

namespace Hubtel.CCI.Api.Services.Providers;

public class CciRoadMapMetricUpdateRecordsService(
    ILogger<CciRoadMapMetricUpdateRecordsService> logger,
    IRepositoryContext repositoryContext,
    IOptions<SonarQubeConfig> sonarQubeConfig): ICciRoadMapMetricUpdateRecordsService
{

    public async Task<IApiResponse<CciRoadMapMetricUpdateRecord?>> GetRecentCciRoadMapMetricUpdateRecordAsync(CancellationToken ct = default)
    {
        try
        {
            logger.LogDebug("[CciRoadMapMetricUpdateRecordsService::GetRecentCciRoadMapMetricUpdateRecordAsync] Getting recent cciRoadMapMetricUpdateRecord");
            
            var cciRoadMapMetricUpdateRecord = await repositoryContext.CciRoadMapMetricUpdateRecordRepository
                .GetQueryable()
                .OrderByDescending(x => x.CreatedAt)
                .Take(1)
                .FirstOrDefaultAsync(ct);
            
            if (cciRoadMapMetricUpdateRecord == null)
                return IApiResponse<CciRoadMapMetricUpdateRecord>.Default.ToNotFoundApiResponse("No recent cciRoadMapMetricUpdateRecord found");
            
            return cciRoadMapMetricUpdateRecord.ToOkApiResponse("Successfully got recent cciRoadMapMetricUpdateRecord");
                
        }
        catch (Exception ex)
        {
            logger.LogError(ex,"[CciRoadMapMetricUpdateRecordsService::GetRecentCciRoadMapMetricUpdateRecordAsync] " +
                               "An error occurred while getting recent cciRoadMapMetricUpdateRecord, " +
                               "Message: {Message}", ex.Message);
            return IApiResponse<CciRoadMapMetricUpdateRecord>.Default.ToFailedDependencyApiResponse("An error occurred while getting recent cciRoadMapMetricUpdateRecord");
            
        }
    }

    public async Task<IApiResponse<bool>> ProcessCciRoadMapMetricUpdateRecordAsync(DateTime runDate, DateTime runStartTime, CancellationToken ct = default)
    {
        try
        {
            logger.LogDebug("[CciRoadMapMetricUpdateRecordsService::ProcessCciRoadMapMetricUpdateRecordAsync] " +
                            "Processing cciRoadMapMetricUpdateRecord with runDate: {RunDate}, runStartTime: {RunStartTime}",
                runDate, runStartTime);
            
            var roadmaps = await repositoryContext.CciRoadMapRepository
                .GetQueryable()
                .Where(x => runDate  <= x.TargetDate)
                .ToListAsync(ct);
            
            if (roadmaps.Count == 0)
                return ApiResponse<bool>.Default.ToNotFoundApiResponse("No roadmaps found for processing");
            
            var repositoryIds = roadmaps.Select(x => x.RepositoryId).Distinct().ToList();
            
            var repositories = await repositoryContext.RepositoryRepository
                .GetQueryable()
                .Where(x => repositoryIds.Contains(x.Id))
                .ToListAsync(ct);
            
            if (repositories.Count == 0)
                return ApiResponse<bool>.Default.ToNotFoundApiResponse("No repositories found for processing");
            
            logger.LogDebug("[CciRoadMapMetricUpdateRecordsService::ProcessCciRoadMapMetricUpdateRecordAsync] Found " +
                            "{RepositoriesCount} repositories for processing", repositories.Count);
            
            var repositoryMetricsResponse = await FetchCciRepositoryScores(repositories);
            
            if (repositoryMetricsResponse.Data?.Count == 0)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Failed to fetch cci repository scores");
            
            var repositoryMetrics = repositoryMetricsResponse.Data!;
            

            foreach (var roadmap in roadmaps)
            {
                var roadMapMetrics = repositoryMetrics.TryGetValue(roadmap.RepositoryId, out var metrics)
                    ? metrics
                    : new RepositoryMetrics();
                
                UpdateRoadMapMetrics(roadmap, roadMapMetrics);
            }
            
            var updated = await repositoryContext.CciRoadMapRepository.UpdateRangeAsync(roadmaps, ct);
            
            if (updated == 0)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Failed to update roadmaps");

            var cciRoadMapMetricUpdateRecord = new CciRoadMapMetricUpdateRecord
            {
                RunDate = runDate,
                RunStartTime = runStartTime,
                RunEndTime = DateTime.UtcNow,
                CciRoadMaps = new CciRoadMaps()
                {
                    Items = roadmaps
                }
            };
            
            var added = await repositoryContext.CciRoadMapMetricUpdateRecordRepository.AddAsync(cciRoadMapMetricUpdateRecord, ct);
            
            if (added == 0)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Failed to add cciRoadMapMetricUpdateRecord");
            
            
            return true.ToOkApiResponse("Successfully processed cciRoadMapMetricUpdateRecord");
        }
        catch (Exception ex)
        {
            logger.LogError(ex,"[CciRoadMapMetricUpdateRecordsService::ProcessCciRoadMapMetricUpdateRecordAsync] " +
                               "An error occurred while processing cciRoadMapMetricUpdateRecord, " +
                               "Message: {Message}", ex.Message);
            return false.ToFailedDependencyApiResponse("An error occurred while processing cciRoadMapMetricUpdateRecord");
        }
    }

    public async Task<IApiResponse<bool>> ProcessCciRoadMapMetricUpdateRecordAsyncNew(DateTime runDate, DateTime runStartTime,
        CancellationToken ct = default)
    {
        try
        {
            logger.LogDebug("[CciRoadMapMetricUpdateRecordsService::ProcessCciRoadMapMetricUpdateRecordAsyncNew] " +
                            "Processing cciRoadMapMetricUpdateRecord with runDate: {RunDate}, runStartTime: {RunStartTime}",
                runDate, runStartTime);
            
            var roadmaps = await repositoryContext.CciRoadMapRecordRepository
                .GetQueryable()
                .ToListAsync(ct);
            
            if (roadmaps.Count == 0)
                return ApiResponse<bool>.Default.ToNotFoundApiResponse("No roadmaps found for processing");
            
            var repositoryIds = roadmaps.Select(x => x.Repository.Id).Distinct().ToList();
            
            var repositories = await repositoryContext.RepositoryRepository
                .GetQueryable()
                .Where(x => repositoryIds.Contains(x.Id))
                .ToListAsync(ct);
            
            if (repositories.Count == 0)
                return ApiResponse<bool>.Default.ToNotFoundApiResponse("No repositories found for processing");
            
            logger.LogDebug("[CciRoadMapMetricUpdateRecordsService::ProcessCciRoadMapMetricUpdateRecordAsync] Found " +
                            "{RepositoriesCount} repositories for processing", repositories.Count);
            
            var repositoryMetricsResponse = await FetchCciRepositoryScores(repositories);
            
            if (repositoryMetricsResponse.Data?.Count == 0)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Failed to fetch cci repository scores");
            
            var repositoryMetrics = repositoryMetricsResponse.Data!;

            var recentPublicationDate = Miscellaneous.GetCurrentPublicationTargetDay();
            var cciScores = await repositoryContext.CciRepositoryScoreRepository
                .GetQueryable()
                .Where(x => repositoryIds.Contains(x.RepositoryId!))
                .Where(x => x.PublicationStartDate <= recentPublicationDate && x.PublicationEndDate >= recentPublicationDate)
                .ToListAsync(ct);

            

            foreach (var roadmap in roadmaps)
            {
                var roadMapMetrics = repositoryMetrics.TryGetValue(roadmap.Repository.Id, out var metrics)
                    ? metrics
                    : new RepositoryMetrics();
                
                var cciScore = cciScores.FirstOrDefault(x => x.RepositoryId == roadmap.Repository.Id);
                roadMapMetrics.FinalAverage = cciScore?.FinalAverage ?? 0;
                roadmap.UpdatedAt = DateTime.UtcNow;
                
                UpdateRoadMapRecordMetrics(roadmap, roadMapMetrics);
            }
            
            var updated = await repositoryContext.CciRoadMapRecordRepository.UpdateRangeAsync(roadmaps, ct);
            
            if (updated == 0)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Failed to update roadmaps");

            var cciRoadMapMetricUpdateRecord = new CciRoadMapMetricUpdateRecord
            {
                RunDate = runDate,
                RunStartTime = runStartTime,
                RunEndTime = DateTime.UtcNow,
                CciRoadMapRecords = new CciRoadMapRecords()
                {
                    Items = roadmaps
                }
            };
            
            var added = await repositoryContext.CciRoadMapMetricUpdateRecordRepository.AddAsync(cciRoadMapMetricUpdateRecord, ct);
            
            if (added == 0)
                return ApiResponse<bool>.Default.ToFailedDependencyApiResponse("Failed to add cciRoadMapMetricUpdateRecord");
            
            
            return true.ToOkApiResponse("Successfully processed cciRoadMapMetricUpdateRecord");
        }
        catch (Exception ex)
        {
            logger.LogError(ex,"[CciRoadMapMetricUpdateRecordsService::ProcessCciRoadMapMetricUpdateRecordAsync] " +
                               "An error occurred while processing cciRoadMapMetricUpdateRecord, " +
                               "Message: {Message}", ex.Message);
            return false.ToFailedDependencyApiResponse("An error occurred while processing cciRoadMapMetricUpdateRecord");
        }
    }


    private static void UpdateRoadMapMetrics(CciRoadMap roadmap, RepositoryMetrics roadMapMetrics)
    {
        roadmap.Bugs = roadMapMetrics.Bugs;
        roadmap.CodeSmells = roadMapMetrics.CodeSmells;
        roadmap.Coverage = roadMapMetrics.Coverage;
        roadmap.DuplicatedLines = roadMapMetrics.DuplicatedLinesDensity;
        roadmap.Vulnerabilities = roadMapMetrics.Vulnerabilities;
        roadmap.SecurityHotspots = roadMapMetrics.SecurityHotspots;
    }
    
    private static void UpdateRoadMapRecordMetrics(
        CciRoadMapRecord record,
        RepositoryMetrics metrics
    )
    {

        ApplyMetric(record.Bugs, metrics.Bugs);
        ApplyMetric(record.CodeSmells, metrics.CodeSmells);
        ApplyMetric(record.Coverage,  metrics.Coverage);
        ApplyMetric(record.DuplicatedLines, metrics.DuplicatedLinesDensity);
        ApplyMetric(record.Vulnerabilities, metrics.Vulnerabilities);
        ApplyMetric(record.SecurityHotspots, metrics.SecurityHotspots);

        record.OverallQuality.Current = metrics.FinalAverage;
    }
    
    
    /// <summary>
    /// Updates only the “new” pieces: EndWeek and CurrentValue.
    /// Leaves StartWeek, StartValue and TargetValue as‐is.
    /// </summary>
    private static void ApplyMetric(
        MetricData? slice,
        decimal newValue
    )
    {
        if (slice is null)
            return;
        slice.CurrentValue = newValue;
        // slice.TargetValue, slice.StartWeek, slice.StartValue, slice.EngineerAssigned remain unchanged
    }
    
    
    public async Task<IApiResponse<Dictionary<string, RepositoryMetrics>>> FetchCciRepositoryScores(
    List<Repository> repositories)
{

        logger.LogDebug("[CciRoadMapMetricUpdateRecordsService::FetchCciRepositoryScores] Fetching cci repository scores");

        var repositoryMetrics = new Dictionary<string, RepositoryMetrics>();

        foreach (var repo in repositories)
        {
            var metrics = await FetchMetricsForRepository(repo);
            if (metrics != null)
            {
                repositoryMetrics[repo.Id] = metrics;
            }
        }

        return repositoryMetrics.ToOkApiResponse("Successfully fetched cci repository scores");
}

private async Task<RepositoryMetrics?> FetchMetricsForRepository(Repository repo)
{
    const string metricsEndpoint = "/api/measures/component";
    var sonarQubeUrl = sonarQubeConfig.Value.Host;
    var token = sonarQubeConfig.Value.Token;

    try
    {
        var response = await sonarQubeUrl
            .AppendPathSegment(metricsEndpoint)
            .SetQueryParams(new
            {
                component = repo.SonarQubeKey,
                metricKeys = "bugs,vulnerabilities,cognitive_complexity,code_smells,coverage,duplicated_lines_density,reliability_rating,reopened_issues,security_hotspots,security_rating"
            })
            .WithBasicAuth(token, "")
            .GetJsonAsync<SonarQubeMetricsResponse>();

        if (response?.Component?.Measures == null) return null;

        return new RepositoryMetrics
        {
            Bugs = ParseMetric(response.Component.Measures, "bugs"),
            Vulnerabilities = ParseMetric(response.Component.Measures, "vulnerabilities"),
            CognitiveComplexity = ParseMetric(response.Component.Measures, "cognitive_complexity"),
            CodeSmells = ParseMetric(response.Component.Measures, "code_smells"),
            Coverage = ParseMetric(response.Component.Measures, "coverage"),
            DuplicatedLinesDensity = ParseMetric(response.Component.Measures, "duplicated_lines_density"),
            ReliabilityRating = ParseMetric(response.Component.Measures, "reliability_rating"),
            ReopenedIssues = ParseMetric(response.Component.Measures, "reopened_issues"),
            SecurityHotspots = ParseMetric(response.Component.Measures, "security_hotspots"),
            SecurityRating = ParseMetric(response.Component.Measures, "security_rating")
        };
    }
    catch (FlurlHttpException httpEx)
    {
        var errorMessage = await httpEx.GetResponseStringAsync();
        logger.LogWarning(httpEx,"[CciRoadMapMetricUpdateRecordsService] Failed to fetch metrics for Repo: {Repo}, Key: {RepoKey}. StatusCode: {StatusCode}, Error: {ErrorMessage}",
            repo.Name, repo.SonarQubeKey, httpEx.StatusCode, errorMessage);
    }

    return null;
}

private static decimal ParseMetric(List<SonarQubeMeasure> measures, string metric)
{
    return (decimal)(double.TryParse(measures.Find(m => m.Metric == metric)?.Value, out var value) ? value : 0.0);
}
}