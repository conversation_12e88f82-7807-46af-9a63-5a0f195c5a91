using Arch.EntityFrameworkCore.UnitOfWork.Collections;
using Hubtel.CCI.Api.Actors.Messages;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Hubtel.OpenTelemetry.Instrumentation.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class ServicesStatisticsService:IServicesStatisticsService
{
    private readonly IRepositoryContext _repositoryContext;
    private readonly ILogger<ServicesStatisticsService> _logger;
    private readonly IAzureProxyService _azureProxyService;

    public ServicesStatisticsService(IRepositoryContext repositoryContext, ILogger<ServicesStatisticsService> logger, IAzureProxyService azureProxyService)
    {
        _repositoryContext = repositoryContext;
        _logger = logger;
        _azureProxyService = azureProxyService;
    }


    public async Task<PagedResult<Service>> GetServicesForStatisticsAggregation(int page, int pageSize)
    {
        try
        {
            var query = await _repositoryContext.ServiceRepository.GetQueryable()
                .ToPagedListAsync(Math.Abs(page - 1), pageSize);
            return new PagedResult<Service>
            {
                Results = query.Items.ToList(),
                TotalPages = query.TotalPages,
                TotalCount = query.TotalCount,
                PageIndex = page,
                PageSize = page,
            };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred while retrieving services for statistics aggregation");
            return new PagedResult<Service>
            {
                Results = new List<Service>(),
                TotalCount = 0,
                PageIndex = page,
                PageSize = pageSize,
                TotalPages = 0
            };
        }
    }

    
    public async Task<List<Service>> GetServicesForRepository(string repositoryId)
    {
        try
        {
            var services = await _repositoryContext.ServiceRepository.GetQueryable()
                .Where(item => string.Equals(item.RepositoryId, repositoryId))
                .ToListAsync();
            return services;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred while retrieving services for repository {RepositoryId}", repositoryId);
            return [];
        }
    }
    
    public async Task<PagedResult<Repository>> GetRepositoriesForStatisticsAggregation(int page, int pageSize)
    {
        try
        {
            var query = await _repositoryContext.RepositoryRepository.GetQueryable()
                .Where(item => string.Equals(item.Status, ValidationConstants.RepositoryServiceStatus.Active))
                .ToPagedListAsync(Math.Abs(page - 1), pageSize);
            return new PagedResult<Repository>
            {
                Results = query.Items.ToList(),
                TotalPages = query.TotalPages,
                TotalCount = query.TotalCount,
                PageIndex = page,
                PageSize = page,
            };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred while retrieving repositories for statistics aggregation");
            return new PagedResult<Repository>
            {
                Results = new List<Repository>(),
                TotalCount = 0,
                PageIndex = page,
                PageSize = pageSize,
                TotalPages = 0
            };
        }
    }

    public async Task<int> AggregateServiceStatsAsync(Service service,Repository repository, DeploymentAggregatorActorMessage message)
    {
        try
        {
            _logger.LogInformation("Received request to aggregate service statistics:{Service}", service.ToJsonString());
            
            var serviceDeploymentTrails = await _repositoryContext.ServiceDeploymentTrailRepository
                .GetQueryable()
                .Where(item => string.Equals(item.RepositoryServiceId, service.Id))
                .ToListAsync();
            
            var projectName = repository.Url.ToProjectName();
            var releaseDefinitionId=service.ServiceReleaseUrl!.GetReleaseDefinitionId();
            
            if (int.TryParse(service.ServiceReleaseDefinitionId, out var definitionId))
            {
                releaseDefinitionId = definitionId;
            }

            if (releaseDefinitionId == null) return 0;

            var serviceDeploymentMetrics = await _azureProxyService.GetRepositoryServiceDeploymentMetricsSummaryAsync(
                projectName,
                releaseDefinitionId.Value,
                10
            );

            var numberRollbacks= serviceDeploymentTrails.Count(item =>
                item.Status == ServiceDeploymentTrailStatus.DefaultFailed);
            
            var numberSuccessfulDeployments = serviceDeploymentTrails.Count(item =>
                item.Status == ServiceDeploymentTrailStatus.DefaultSuccessful);

            var productInfo = await _repositoryContext.GetProductGroupByRepositoryFilterAsync(repository.Id,
                repository.SonarQubeKey,repository
            );

            if (productInfo == null)
            {
                _logger.LogInformation(
                    "No product info found for repository {RepositoryId} with SonarQubeKey {SonarQubeKey}",
                    repository.Id, repository.SonarQubeKey);
                return 0;
            }

            var serviceStat = new ServiceStatistic
            {
                ServiceId = service.Id,
                RepositoryId = service.RepositoryId,
                RepositoryName = service.RepositoryName,
                RepositoryType = service.RepositoryType,
                ServiceName = service.Name,
                Status = repository.Status,
                PublicationDate = message.PublicationDate,
                PublicationStartDate = message.PublicationStartDate,
                PublicationEndDate = message.PublicationEndDate,
                PublicationWeek = message.PublicationWeek,
                NumberOfFailedDeployments =serviceDeploymentMetrics?.TotalFailedDeployments?? numberRollbacks,
                NumberOfSuccessfulDeployments =
                    serviceDeploymentMetrics?.TotalSuccessfulDeployments ?? numberSuccessfulDeployments,
                NumberOfDeployments =
                    serviceDeploymentMetrics?.TotalDeployments ?? serviceDeploymentTrails.Count,
                NumberOfRollbacks = serviceDeploymentMetrics?.TotalFailedDeployments?? numberRollbacks,
                RepositoryUrl = repository.Url,
                RepositorySonarQubeKey = repository?.SonarQubeKey,
                ProductGroupId = productInfo.ProductGroupId,
                ProductGroupName = productInfo.ProductGroupName,
                ProductTeamId = productInfo.ProductTeams.Items.FirstOrDefault()?.Id,
                ProductTeamName = productInfo.ProductTeams.Items.FirstOrDefault()?.Name,
            };

            _logger.LogInformation(
                "Aggregating service stats for service: {ServiceName} with stats: {ServiceStat}, ServiceDefinitionId: {ServiceDefinitionId},DeploymentMetric: {DeploymentMetric}",
                service.Name, serviceStat.ToJsonString(), releaseDefinitionId, serviceDeploymentMetrics.ToJsonString());
            var res=await _repositoryContext.ServiceStatisticRepository.AddAsync(serviceStat);
            return res;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred while aggregating service stats for service");
            return 0; // or handle the error as needed
        }
    }
}