using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Hubtel.OpenTelemetry.Instrumentation.Extensions;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Services.Providers;

public class DcpService:IDcpService
{
    private readonly ILogger<DcpService> _logger;
    private readonly IRepositoryContext _repositoryContext;
    private const string NotFoundMessage = "Deployment Request not found";
    private const string InternalServerErrorMessage = "Sorry,something went wrong";
    public DcpService(ILogger<DcpService> logger, IRepositoryContext repositoryContext)
    {
        _logger = logger;
        _repositoryContext = repositoryContext;
    }

    public async Task<IApiResponse<DeploymentRequest>> CreateDcpRequestAsync(CreateDcpRequest dcpRequest)
    {
        try
        {
            _logger.LogInformation("Creating DCP request: {Request}", dcpRequest.ToJsonString());
            var userEmail= dcpRequest.AuthData?.EmailAddress;
            var engineer = await _repositoryContext.EngineerRepository.GetQueryable().FirstOrDefaultAsync(item =>
                string.Equals(item.Email, userEmail));
            
            var newRequest = dcpRequest.Adapt<DeploymentRequest>();
            newRequest.Status = DeploymentRequestStatus.NotStarted;
            newRequest.EngineerRequestId = engineer?.Id;
            newRequest.EngineerRequestName = engineer?.Name??userEmail;
            
            await _repositoryContext.DeploymentRequestRepository.AddAsync(newRequest);

            return newRequest.ToCreatedApiResponse("Deployment Request Added Successfully");
        }
        catch (Exception e)
        {
           _logger.LogError(e,"Error creating DCP request:{Request}", dcpRequest.ToJsonString());

           return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(
               "Sorry,an error occurred while adding request");
        }
    }

    public async Task<IApiResponse<DeploymentRequest>> UpdateDcpRequestAsync(string id, DcpRequest dcpRequest)
    {
        try
        {
            _logger.LogInformation("Updating DCP request: {Request}", dcpRequest.ToJsonString());
            var existingRequest = await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(id);
            if (existingRequest == null)
            {
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse(
                    NotFoundMessage);
            }

            existingRequest = dcpRequest.Adapt(existingRequest);
            existingRequest.UpdatedAt = DateTime.UtcNow;
            await _repositoryContext.DeploymentRequestRepository.UpdateAsync(existingRequest);

            return existingRequest.ToOkApiResponse("Deployment Request Updated Successfully");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating DCP request:{Request}", dcpRequest.ToJsonString());
            return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(
                "Sorry, an error occurred while updating request");
        }
    }

    public async Task<IApiResponse<DeploymentRequest>> GetDcpRequestByIdAsync(string id)
    {
        try
        {
            _logger.LogInformation("Retrieving DCP request by ID: {Id}", id);
            var request = await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(id);
            if (request == null)
            {
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse(
                    NotFoundMessage);
            }
            
            var servicesIds= request.ServicesToBeDeployed.Select(item => item.ServiceId).ToList();
            
            if(servicesIds.Count == 0) return request.ToOkApiResponse("Deployment Request Retrieved Successfully");
            
            var deploymentTrails= await _repositoryContext.ServiceDeploymentTrailRepository.GetQueryable()
                .Where(item => servicesIds.Contains(item.RepositoryServiceId)&&string.Equals(item.DeploymentRequestId, request.Id))
                .ToListAsync();

            request.ServicesToBeDeployed.ForEach(item =>
            {
                var serviceDeploymentTrails = deploymentTrails
                    .Where(trail => string.Equals(trail.RepositoryServiceId, item.ServiceId))
                    .Select(t => t.Adapt<ServiceDeploymentTrailResponse>())
                    .ToList();
                item.DeploymentTrails = serviceDeploymentTrails;
            });

            return request.ToOkApiResponse("Deployment Request Retrieved Successfully");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error retrieving DCP request by ID: {Id}", id);
            return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(
                "Sorry, an error occurred while retrieving request");
        }
    }

    public async Task<IApiResponse<PagedResult<DeploymentRequest>>> FilterDcpRequestsAsync(DcpRequestFilter filter)
    {
        try
        {
           
            _logger.LogInformation("Filtering DCP requests with filter: {Filter}", filter.ToJsonString());
            var query = _repositoryContext.DeploymentRequestRepository.GetQueryable()
                .AsNoTracking()
                .ApplyDeploymentRequestFilter(filter);
            var pagedListData = await query.GetPagedAsync(filter.PageIndex, filter.PageSize);
            var response = new PagedResult<DeploymentRequest>
            {
                TotalCount = pagedListData.TotalCount,
                TotalPages = pagedListData.TotalPages,
                PageIndex = pagedListData.PageIndex,
                PageSize = filter.PageSize,
                Results = pagedListData.Results,
                LowerBound = pagedListData.LowerBound,
                UpperBound = pagedListData.UpperBound,
            };
            return response.ToOkApiResponse();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error filtering DCP requests with filter: {Filter}", filter.ToJsonString());
            return ApiResponse<PagedResult<DeploymentRequest>>.Default.ToServerErrorApiResponse(
                "Sorry, an error occurred while filtering requests");
        }
    }

    public async Task<IApiResponse<DeploymentRequest>> CancelDcpRequestAsync(string id, CancelDcpRequest cancelDcpRequest)
    {
        try
        {
            var dcpRequest = await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(id);
            if (dcpRequest == null)
            {
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse(
                    NotFoundMessage);
            }

            _logger.LogInformation("Cancelling DCP request: {Request}", cancelDcpRequest.ToJsonString());
            dcpRequest.Status = DeploymentRequestStatus.Cancelled;
            dcpRequest.CancellationReason = cancelDcpRequest.CancellationReason;
            dcpRequest.UpdatedAt = DateTime.UtcNow;
            await _repositoryContext.DeploymentRequestRepository.UpdateAsync(dcpRequest);

            return dcpRequest.ToOkApiResponse("Deployment Request Cancelled Successfully");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error cancelling DCP request:{Request}", cancelDcpRequest.ToJsonString());
            return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(
                "Sorry, an error occurred while cancelling request");
        }
    }

    public async Task<IApiResponse<DeploymentRequest>> UpdateServiceToBeDeployedAsync(string requestId,
        ServiceToBeDeployedInput request)
    {
        try
        {
            _logger.LogInformation("Received request to update service to be deployed: {Service},{RequestId}",
                request.ToJsonString(), requestId);
            var deploymentRequest = await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId);
            if (deploymentRequest == null)
            {
                _logger.LogWarning("Deployment request not found for ID: {RequestId}", requestId);
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse(NotFoundMessage);
            }

            var serviceInfo = await _repositoryContext.ServiceRepository.GetByIdAsync(request.ServiceId!);
            if (serviceInfo == null)
            {
                _logger.LogWarning("Service not found for ID: {ServiceId}", request.ServiceId);
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse("Service not found");
            }

            var existingService = deploymentRequest.ServicesToBeDeployed
                .FirstOrDefault(item => string.Equals(item.ServiceId, request.ServiceId));


            var newServiceToBeDeployed = request.Adapt<ServiceToBeDeployed>();
            newServiceToBeDeployed.ServiceName = serviceInfo.Name;
            newServiceToBeDeployed.RepositoryId = serviceInfo.RepositoryId;
            newServiceToBeDeployed.RepositoryName = serviceInfo.RepositoryName;

            existingService ??= newServiceToBeDeployed;
            existingService.ServiceName = serviceInfo.Name;
            existingService.RepositoryId = serviceInfo.RepositoryId;
            existingService.RepositoryName = serviceInfo.RepositoryName;
            
            // remove service from the list 
            deploymentRequest.ServicesToBeDeployed.RemoveAll(item => string.Equals(item.ServiceId, request.ServiceId));
            
            // add the updated service to the list
            deploymentRequest.ServicesToBeDeployed.Add(existingService);
            deploymentRequest.UpdatedAt = DateTime.UtcNow;

            await _repositoryContext.DeploymentRequestRepository.UpdateAsync(deploymentRequest);
            
            _logger.LogInformation("Updating deployment request with new service: {Service},{RequestId}",
                existingService.ToJsonString(), requestId);
            
            return deploymentRequest.ToOkApiResponse("Service to be deployed updated successfully");


        }
        catch (Exception e)
        {
            _logger.LogError(e, " Error updating service to be deployed: {Service},{RequestId}",
                request.ToJsonString(), requestId);
            return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(InternalServerErrorMessage);
        }
    }

    public async Task<IApiResponse<DeploymentRequest>> RemoveServiceToBeDeployedAsync(string requestId, string serviceId)
    {
        try
        {
            _logger.LogInformation("Received request to remove service to be deployed: {ServiceId}, {RequestId}",
            serviceId, requestId);
            var deploymentRequest = await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId);
            if (deploymentRequest == null)
            {
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse(NotFoundMessage);
            }

            var serviceToRemove = deploymentRequest.ServicesToBeDeployed
                .FirstOrDefault(item => string.Equals(item.ServiceId, serviceId));
            if (serviceToRemove == null)
            {
                return deploymentRequest.ToOkApiResponse();
            }

            deploymentRequest.ServicesToBeDeployed.Remove(serviceToRemove);
            deploymentRequest.UpdatedAt = DateTime.UtcNow;

            await _repositoryContext.DeploymentRequestRepository.UpdateAsync(deploymentRequest);

            _logger.LogInformation("Service removed from deployment request: {ServiceId}, {RequestId}",
                serviceId, requestId);

            return deploymentRequest.ToOkApiResponse("Service to be deployed removed successfully");
        }
        catch (Exception e)
        {
            _logger.LogError(e, " Error removing service to be deployed: {ServiceId}, {RequestId}",
                serviceId, requestId);
            return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(InternalServerErrorMessage);
        }
    }

    public async Task<IApiResponse<DeploymentRequest>> UpdateBulkServicesToBeDeployedAsync(string requestId,
        List<ServiceToBeDeployedInput> requests)
    {
        try
        {
            _logger.LogInformation("Received request to update bulk services to be deployed: {RequestId}, {Services}",
                requestId, requests.ToJsonString());
            var deploymentRequest = await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(requestId);
            if (deploymentRequest == null)
            {
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse(NotFoundMessage);
            }
            
            var servicesIds= requests.Select(item => item.ServiceId).ToList();
            var servicesInfo= await _repositoryContext.ServiceRepository.GetQueryable()
                .Where(item => servicesIds.Contains(item.Id))
                .ToListAsync();

            if (servicesInfo.Count != requests.Count)
            {
                return ApiResponse<DeploymentRequest>.Default.ToNotFoundApiResponse("Some services not found");
            }
            
            // Clear existing services to be deployed
            deploymentRequest.ServicesToBeDeployed.RemoveAll(item =>
                servicesIds.Contains(item.ServiceId));

            List<ServiceToBeDeployed> newServicesToBeDeployed = [];
            foreach (var serviceRequest in requests)
            {
                var service= serviceRequest.Adapt<ServiceToBeDeployed>();
                var serviceInfo = servicesInfo.FirstOrDefault(item => string.Equals(item.Id, service.ServiceId))!;
                service.ServiceName = serviceInfo.Name;
                service.RepositoryId = serviceInfo.RepositoryId;
                service.RepositoryName = serviceInfo.RepositoryName;
                newServicesToBeDeployed.Add(service);
            }
            
            // Add new services to be deployed
            deploymentRequest.ServicesToBeDeployed.AddRange(newServicesToBeDeployed);
            deploymentRequest.UpdatedAt = DateTime.UtcNow;
            await _repositoryContext.DeploymentRequestRepository.UpdateAsync(deploymentRequest);
            _logger.LogInformation("Bulk services updated in deployment request: {RequestId}, {Services}",
                requestId, requests.ToJsonString());
            return deploymentRequest.ToOkApiResponse("Bulk services to be deployed updated successfully");

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating bulk services to be deployed: {RequestId}, {Services}",
                requestId, requests.ToJsonString());
            return ApiResponse<DeploymentRequest>.Default.ToServerErrorApiResponse(InternalServerErrorMessage);
        }
    }

    public async Task<IApiResponse<ServiceDeploymentTrailResponse>> AddServiceDeploymentTrailAsync(ServiceDeploymentTrailRequest request)
    {
        try
        {
            var deploymentRequest =
                await _repositoryContext.DeploymentRequestRepository.GetByIdAsync(request.DeploymentRequestId!);
            if (deploymentRequest == null)
            {
                return ApiResponse<ServiceDeploymentTrailResponse>.Default.ToNotFoundApiResponse(NotFoundMessage);
            }

            var serviceToBeDeployed =
                deploymentRequest.ServicesToBeDeployed.FirstOrDefault(item =>
                    string.Equals(item.ServiceId, request.RepositoryServiceId));
            if (serviceToBeDeployed == null)
            {
                return ApiResponse<ServiceDeploymentTrailResponse>.Default.ToNotFoundApiResponse(
                    "Service to be deployed not found in the request");
            }
            
            var newDeploymentTrail= request.Adapt<ServiceDeploymentTrail>();
           
            await _repositoryContext.ServiceDeploymentTrailRepository.AddAsync(newDeploymentTrail);

            _logger.LogInformation("Adding service deployment trail: {Request}\n{Data}", request.ToJsonString(),
                newDeploymentTrail.ToJsonString());

            var response = newDeploymentTrail.Adapt<ServiceDeploymentTrailResponse>();

            return response.ToCreatedApiResponse("Service deployment trail added successfully");

        }
        catch (Exception e)
        {
            _logger.LogError(e," Error adding service deployment trail: {Request}",
                request.ToJsonString());
            return ApiResponse<ServiceDeploymentTrailResponse>.Default.ToServerErrorApiResponse(
                InternalServerErrorMessage);
        }
    }

    public async Task<IApiResponse<int>> DeleteDcpRequestByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var res = await _repositoryContext.DeploymentRequestRepository.GetQueryable()
                .Where(item => item.CreatedAt >= startDate && item.CreatedAt <= endDate)
                .ExecuteUpdateAsync(item => item
                    .SetProperty(f => f.IsDeleted, true)
                    .SetProperty(f => f.UpdatedAt, DateTime.UtcNow));
            
            _logger.LogInformation("Deleted {Count} DCP requests between {StartDate} and {EndDate}", res, startDate, endDate);
            return res.ToOkApiResponse("DCP requests deleted successfully");

        }
        catch (Exception e)
        {
            _logger.LogError(e," Error deleting DCP requests by date range: {StartDate} - {EndDate}",
                startDate, endDate);
            return ApiResponse<int>.Default.ToServerErrorApiResponse(InternalServerErrorMessage);
        }
    }

    public async Task<IApiResponse<int>> DeleteDcpRequestByIdsAsync(DeleteDcpRequest request)
    {
        try
        {
            var res = await _repositoryContext.DeploymentRequestRepository.DeleteBulkByIdsAsync(request.RequestIds);
            _logger.LogInformation("Deleting DCP requests by IDs: {Ids},Res:{Res}", request.RequestIds.ToJsonString(),
                res);
            return res.ToOkApiResponse("DCP requests deleted successfully");
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}