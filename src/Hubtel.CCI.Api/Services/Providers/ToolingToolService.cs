using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Graph.Models;
using RazorLight;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Hubtel.CCI.Api.Services.Providers
{
    public class ToolingToolService : IToolingToolsService
    {
        private readonly IRepositoryContext _repositoryContext;
        private readonly ILogger<ToolingToolService> _logger;
        private readonly IHostEnvironment _hostEnvironment;

        public ToolingToolService(IRepositoryContext repositoryContext, ILogger<ToolingToolService> logger, IHostEnvironment hostEnvironment)
        {
            _repositoryContext = repositoryContext;
            _logger = logger;
            _hostEnvironment = hostEnvironment;
        }
        public async Task<IApiResponse<CreateToolResponse>> CreateToolAsync(CreateToolRequest request, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:CreateToolAsync]: Request payload ---> {RequestPayload}",
                new { request }.Serialize());

            var createToolModel = request.Adapt<ToolingTool>();
            var savedCount = await _repositoryContext.ToolingToolsRepository.AddAsync(createToolModel, ct);
            if (savedCount <= 0)
                return ApiResponse<CreateToolResponse>.Default.ToFailedDependencyApiResponse(
                    "Could not create Tool! Please try again"
                );

            return createToolModel.Adapt<CreateToolResponse>().ToCreatedApiResponse();
        }

        public async Task<IApiResponse<object>> DeleteToolAsync(string id, CancellationToken ct)
        {
            if(string.IsNullOrEmpty(id))
                return ApiResponse<object>.Default.ToBadRequestApiResponse("Tool ID cannot be null or empty");

            _logger.LogDebug("[ToolingToolService:DeleteToolAsync]: Request payload ---> {RequestPayload}", id);
            var tool = await _repositoryContext.ToolingToolsRepository.GetByIdAsync(id, ct);
            if (tool is null)
            {
                _logger.LogWarning("Tool not found with ID: {Id}", id);
                return ApiResponse<object>.Default.ToNotFoundApiResponse("Tool not found! Please try again");
            }
            var deleted = await _repositoryContext.ToolingToolsRepository.DeleteAsync(tool, ct);
            if (deleted <= 0)
            {
                _logger.LogError("Failed to delete Tool with ID: {Id}", id);
                return ApiResponse<object>.Default.ToFailedDependencyApiResponse(
                    "Could not delete Tool! Please try again"
                );
            }
            _logger.LogInformation("Tool with ID: {Id} deleted successfully", id);
            return ApiResponse<object>.Default.ToNoContentApiResponse("Tool deleted successfully");
        }

        public async Task<IApiResponse<GetToolResponse>> GetToolAsync(string id, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:GetToolAsync]: Request payload ---> {RequestPayload}",
                new { id }.Serialize());
            var tool =await _repositoryContext.ToolingToolsRepository.FindOneAsync(x => x.Id == id, ct);
            if (tool == null)
            {
                return ApiResponse<GetToolResponse>.Default.ToNotFoundApiResponse("Tool not found! Please try again");
            }
            return tool.Adapt<GetToolResponse>().ToOkApiResponse();
        }


        public async Task<IApiResponse<PagedResult<GetToolResponse>>> GetToolsAsync(SearchToolFilter filter, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:GetToolsAsync]: Request payload ---> {RequestPayload}",
                new { filter }.Serialize());
            var query = _repositoryContext.ToolingToolsRepository.GetQueryable().AsNoTracking();

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
                query = query.Where(c => c.Name.Contains(filter.SearchTerm,StringComparison.CurrentCultureIgnoreCase));

            if (!string.IsNullOrWhiteSpace(filter.Domain))
                query = query.Where(c => c.Domain == Enum.Parse<Data.Entities.Domain>(filter.Domain));

            var pagedResult = await query.OrderByDescending(x => x.Name)
                .GetPagedAsync(filter.PageIndex, filter.PageSize, ct);

            var results = pagedResult.Results.Select(x => x.Adapt<GetToolResponse>()).ToList();

            var response = new PagedResult<GetToolResponse>
            {
                Results = results,
                UpperBound = pagedResult.UpperBound,
                LowerBound = pagedResult.LowerBound,
                PageIndex = pagedResult.PageIndex,
                PageSize = pagedResult.PageSize,
                TotalCount = pagedResult.TotalCount,
                TotalPages = pagedResult.TotalPages
            };

            return response.ToOkApiResponse();
        }

        public async Task<IApiResponse<UpdateToolResponse>> UpdateToolAsync(string id, UpdateToolRequest request, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:UpdateToolAsync]: Request payload ---> {RequestPayload}",
                new { id, request }.Serialize());

            var tool = await _repositoryContext.ToolingToolsRepository.GetByIdAsync(id, ct);

            if (tool is null)
            {
                _logger.LogWarning("Tool not found with ID: {Id}", id);
                return ApiResponse<UpdateToolResponse>.Default.ToNotFoundApiResponse();
            }

            var newUpdatedTool = request.Adapt(tool);
            var updated= await _repositoryContext.ToolingToolsRepository.UpdateAsync(newUpdatedTool,ct);
            if (updated <= 0)
            {
                _logger.LogError("Failed to update Tool with ID: {Id}", id);
                return ApiResponse<UpdateToolResponse>.Default.ToFailedDependencyApiResponse(
                    "Could not update Tool! Please try again"
                );
            }
            _logger.LogInformation("Tool with ID: {Id} updated successfully", id);
            return tool.Adapt<UpdateToolResponse>().ToOkApiResponse();

        }

        public async Task<IApiResponse<PagedResult<GetToolingReportsResponse>>> GetToolingReportsAsync(GetToolingReportsRequest request, CancellationToken ct)
        {
            var reportRepoContext = _repositoryContext.ToolingReportRepository.GetQueryable();
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                reportRepoContext = reportRepoContext.Where(x => x.ProductGroupName.Contains(request.SearchTerm,StringComparison.CurrentCultureIgnoreCase));
            }
            if (!string.IsNullOrEmpty(request.Domain))
            {
                reportRepoContext = reportRepoContext.Where(x => x.Domain == Enum.Parse<Data.Entities.Domain>(request.Domain));
            }
            if (request.Date is not null)
            {
                reportRepoContext = reportRepoContext.Where(x => x.CreatedAt == request.Date);
            }

            var pagedResult = await reportRepoContext.OrderByDescending(x => x.CreatedAt)
                .GetPagedAsync(request.PageIndex, request.PageSize, ct);

            var results = pagedResult.Results.Select(x => x.Adapt<GetToolingReportsResponse>()).ToList();

            var response = new PagedResult<GetToolingReportsResponse>
            {
                Results = results,
                UpperBound = pagedResult.UpperBound,
                LowerBound = pagedResult.LowerBound,
                PageIndex = pagedResult.PageIndex,
                PageSize = pagedResult.PageSize,
                TotalCount = pagedResult.TotalCount,
                TotalPages = pagedResult.TotalPages
            };

            return response.ToOkApiResponse();
        }

        public async Task<IApiResponse<UpdateToolingReportResponse>> UpdateToolingReportAsync(string id, UpdateToolingReportRequest request, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:UpdateToolingReportAsync]: Request payload ---> {RequestPayload}",
               new { id, request }.Serialize());

            var tool = await _repositoryContext.ToolingReportRepository.GetByIdAsync(id, ct);

            if (tool is null)
            {
                _logger.LogWarning("Report not found with ID: {Id}", id);
                return ApiResponse<UpdateToolingReportResponse>.Default.ToNotFoundApiResponse();
            }

            var newUpdatedTool = request.Adapt(tool);
            var updated = await _repositoryContext.ToolingReportRepository.UpdateAsync(newUpdatedTool, ct);
            if (updated <= 0)
            {
                _logger.LogError("Failed to update Report with ID: {Id}", id);
                return ApiResponse<UpdateToolingReportResponse>.Default.ToFailedDependencyApiResponse(
                    "Could not update Tool! Please try again"
                );
            }
            _logger.LogInformation("Report with ID: {Id} updated successfully", id);
            return tool.Adapt<UpdateToolingReportResponse>().ToOkApiResponse();
        }

        public async Task<IApiResponse<GetToolingReportsResponse>> GetToolingReportAsync(string id, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:GetToolingReportAsync]: Request payload ---> {RequestPayload}",
                new { id }.Serialize());
            var report = await _repositoryContext.ToolingReportRepository.FindOneAsNoTrackingAsync(x => x.Id == id,ct);

            if (report == null)
            {
                _logger.LogWarning("Report not found with ID: {Id}", id);
                return ApiResponse<GetToolingReportsResponse>.Default.ToNotFoundApiResponse("Report not found! Please try again");
            }

            var response = report.Adapt<GetToolingReportsResponse>();

            return response.ToOkApiResponse();
        }

        public async Task<IApiResponse<string>> GenerateAndSendToolingReport(string id, CancellationToken ct)
        {
            _logger.LogDebug("[ToolingToolService:GenerateAndSendToolingReport]: Triggering report generation and sending process");

         string templatePath = Path.Combine(_hostEnvironment.ContentRootPath, "Templates");
         var engine = new RazorLightEngineBuilder()
                            .UseFileSystemProject(templatePath)
                             .UseMemoryCachingProvider()
                             .Build();

            var model = await _repositoryContext.ToolingReportRepository.FindOneAsNoTrackingAsync(x => x.Id == "d72b4447856e4fbd9fb48a47f67ee76c",ct); 
            if (model == null) 
            {
                _logger.LogError("No tooling report found to generate");
                return "".ToFailedDependencyApiResponse("No tooling report found to generate");
            }


            string result = await engine.CompileRenderAsync("ToolingReport.cshtml", model);
            return result.ToOkApiResponse("Report generation and sending process triggered successfully");
        }
    }
}
