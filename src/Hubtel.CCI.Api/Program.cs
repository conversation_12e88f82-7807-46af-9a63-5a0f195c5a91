using System.Reflection;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using FluentValidation;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Repositories;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;
using Hubtel.CCI.Api.Dtos.Requests.CciRoadMap;
using Hubtel.CCI.Api.Dtos.Requests.Engineer;
using Hubtel.CCI.Api.Dtos.Requests.ProductGroup;
using Hubtel.CCI.Api.Dtos.Requests.ProductTeam;
using Hubtel.CCI.Api.Dtos.Requests.Repository;
using Hubtel.CCI.Api.Dtos.Requests.RepositoryService;
using Hubtel.CCI.Api.Dtos.Requests.SonarQube;
using Hubtel.CCI.Api.Extensions;
using Hubtel.CCI.Api.Handlers;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Repositories.Interfaces;
using Hubtel.CCI.Api.Repositories.Providers;
using Hubtel.CCI.Api.Services.Cascaders;
using Hubtel.CCI.Api.Services.Interfaces;
using Hubtel.CCI.Api.Services.Providers;
using Hubtel.CCI.Api.Validators.CciRepositoryScore;
using Hubtel.CCI.Api.Validators.CciRoadMap;
using Hubtel.CCI.Api.Validators.Engineer;
using Hubtel.CCI.Api.Validators.ProductGroup;
using Hubtel.CCI.Api.Validators.ProductTeam;
using Hubtel.CCI.Api.Validators.Repository;
using Hubtel.CCI.Api.Validators.RepositoryService;
using Hubtel.CCI.Api.Validators.SonarQube;
using Hubtel.OpenTelemetry.Instrumentation.Extensions;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using PuppeteerSharp;
using Hubtel.CCI.Api.Data;


const string corsPolicyName = "Hubtel.CCI.Api.CCIPolicy";

var actorSystemName = Regex.Replace(Assembly.GetExecutingAssembly().GetName().Name ?? "ActorSystemName",
    @"[^a-zA-Z\s]+", "", RegexOptions.None, TimeSpan.FromMilliseconds(100));

var builder = WebApplication.CreateBuilder(args);

var services = builder.Services;
var config = builder.Configuration;


services.AddMemoryCache();
// services
services.AddScoped<IValidationService, ValidationService>();
services.AddScoped<IRepositoryContext, RepositoryContext>();
services.AddScoped<IEngineerService, EngineerService>();
services.AddScoped<IProductTeamService, ProductTeamService>();
services.AddScoped<IProductGroupService, ProductGroupService>();
services.AddScoped<IArsenalService, ArsenalService>();
services.AddScoped<ICciArsenalScoreService, CciArsenalScoreService>();
services.AddScoped<IEngineerCascader, EngineerCascader>();
services.AddScoped<IArsenalCascader, ArsenalCascader>();
services.AddScoped<IProductTeamCascader, ProductTeamCascader>();
services.AddScoped<ILookupService, LookupService>();
services.AddScoped<ISonarQubeService, SonarQubeService>();
services.AddScoped<ICciRoadMapService, CciRoadMapService>();
services.AddScoped<ICciRoadMapMetricUpdateRecordsService, CciRoadMapMetricUpdateRecordsService>();
services.AddScoped<IBypassedPrService, BypassedPrService>();
services.AddScoped<ICciEngineerScoreService, CciEngineerScoreService>();
services.AddScoped<IAzureService, AzureService>();
services.AddScoped<ITeamsNotificationService, TeamsNotificationService>();
services.AddScoped<IArsenalServiceService, ArsenalServiceService>();
services.AddScoped<IDcpService, DcpService>();
services.AddScoped<IReportDeliveryRuleService, ReportDeliveryRuleService>();
services.AddScoped<IIssueTrackerService, IssueTrackerService>();
services.AddScoped<IToolingToolsService, ToolingToolService>();
services.AddScoped<IDciServiceScoreService, DciServiceScoreService>();
services.AddScoped<IServicesStatisticsService, ServicesStatisticsService>();
services.AddSingleton<IPdfGenerator, PuppeteerPdfGeneratorService>();

// validators
services.AddScoped<IValidator<CreateEngineerRequest>, CreateEngineerRequestValidator>();
services.AddScoped<IValidator<UpdateEngineerRequest>, UpdateEngineerRequestValidator>();
services.AddScoped<IValidator<CreateRepositoryRequest>, CreateRepositoryRequestValidator>();
services.AddScoped<IValidator<UpdateRepositoryRequest>, UpdateRepositoryRequestValidator>();
services.AddScoped<IValidator<CreateProductTeamRequest>, CreateProductTeamRequestValidator>();
services.AddScoped<IValidator<UpdateProductTeamRequest>, UpdateProductTeamRequestValidator>();
services.AddScoped<IValidator<UpdateProductGroupRequest>, UpdateProductGroupRequestValidator>();
services.AddScoped<IValidator<CreateProductGroupRequest>, CreateProductGroupRequestValidator>();
services.AddScoped<IValidator<ImportAzureRepositoryRequest>, ImportAzureRepositoryRequestValidator>();
services.AddScoped<IValidator<CreateCciRepositoryScoreRequest>, CreateCciRepositoryScoreRequestValidator>();
services.AddScoped<IValidator<UpdateCciRepositoryScoreRequest>, UpdateCciRepositoryScoreRequestValidator>();
services.AddScoped<IValidator<PublishCciProductsRankingRequest>, PublishCciProductsRankingRequestValidator>();
services.AddScoped<IValidator<CreateBulkCciRepositoryScoresRequest>, CreateBulkCciRepositoryScoreRequestValidator>();
services.AddScoped<IValidator<CreateCciRoadMapRequest>, CreateCciRoadMapRequestValidator>();
services.AddScoped<IValidator<CreateRepositoryServiceRequest>, CreateRepositoryServiceRequestValidator>();
services.AddScoped<IValidator<UpdateRepositoryServiceRequest>, UpdateRepositoryServiceRequestValidator>();
services.AddHubtelRedisSdk(c =>
    config.GetSection(nameof(RedisConfiguration)).Bind(c));
    

var connectionString = config.GetConnectionString("DbConnection");
var dataSourceBuilder = new NpgsqlDataSourceBuilder(connectionString);
dataSourceBuilder.UseJsonNet();
var dataSource = dataSourceBuilder.Build();

services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(dataSource));


services.AddHubtelGelfLogging(config);
// kinda want to handle the validation on my own
// services.AddValidatorsFromAssemblyContaining<Program>()
// services.AddFluentValidationAutoValidation()

services.Configure<LookupConfig>(options => config.GetSection(nameof(LookupConfig)).Bind(options));
services.Configure<SonarQubeConfig>(options => config.GetSection(nameof(SonarQubeConfig)).Bind(options));
services.Configure<AzureOrgConfig>(options => config.GetSection(nameof(AzureOrgConfig)).Bind(options));
services.Configure<TeamsBotAppConfig>(options => config.GetSection(nameof(TeamsBotAppConfig)).Bind(options));


services.AddHubtelBearerAuth(c => config.GetSection(nameof(BearerTokenConfig)).Bind(c));
services.AddHubtelBasicAuth();

services.AddAuthorizationBuilder()
    .AddPolicy("DevDocsReadPolicy", policy =>
        policy.Requirements.Add(new PermissionRequirement("dev-docs.cci.read")))
    .AddPolicy("DevDocsWritePolicy", policy =>
        policy.Requirements.Add(new PermissionRequirement("dev-docs.cci.write")))
    .AddPolicy("DevDocsPublishPolicy", policy =>
        policy.Requirements.Add(new PermissionRequirement("dev-docs.cci.publish")))
    .AddPolicy("DevDocsDeletePolicy", policy =>
        policy.Requirements.Add(new PermissionRequirement("dev-docs.cci.delete")));

services.AddSingleton<IAuthorizationHandler, PermissionHandler>();

services.AddHubtelSwaggerGen(config, AuthScheme.Bearer);

if (builder.Environment.IsDevelopment() || builder.Environment.IsProduction())
{
    services.AddHubtelOpenTelemetry(config);
}

services.AddHealthChecks();

services.AddCors(options => options
    .AddPolicy(corsPolicyName, policy => policy
        .AllowAnyOrigin()
        .AllowAnyHeader()
        .AllowAnyMethod()));

services.AddHubtelControllers();

services.AddHttpLogging(options =>
{
    options.LoggingFields = HttpLoggingFields.All;
    options.RequestBodyLogLimit = 4096;
    options.ResponseBodyLogLimit = 4096;
});


services.AddHubtelApiVersioning(1);

services.AddHubtelActorSystem(actorSystemName);
services.AddScoped<IActorService<MainActor>, ActorService<MainActor>>();

services.AddExceptionHandler<GlobalExceptionHandler>();
services.AddProblemDetails();
services.AddCustomMapsterMappings();

var app = builder.Build();

await app.RunMigrationsAsync();

app.UseHubtelActorSystem();

app.UseHttpLogging();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseHubtelSwagger();

app.UseExceptionHandler();
app.UseRouting();

app.UseCors(corsPolicyName);

app.UseAuthentication();

app.UseAuthorization();

app.MapHealthChecks("/health");
app.MapControllers();

Platform platform;
if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
{
    platform = RuntimeInformation.ProcessArchitecture == Architecture.Arm64
        ? Platform.MacOSArm64
        : Platform.MacOS; 
}
else
{
    platform = Platform.Linux;
}

await new BrowserFetcher(new BrowserFetcherOptions()
{
    Browser  = SupportedBrowser.Chrome,
    Platform = platform,
}).DownloadAsync();

await app.RunAsync();

namespace Hubtel.CCI.Api
{
    public interface IApiMarker
    {
    }
}
