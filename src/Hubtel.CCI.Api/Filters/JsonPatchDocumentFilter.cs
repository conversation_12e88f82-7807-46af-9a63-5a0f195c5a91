using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Hubtel.CCI.Api.Filters;

public class JsonPatchDocumentFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var httpMethod = context.ApiDescription.HttpMethod?.ToLowerInvariant();
        if (httpMethod != "patch")
        {
            return;
        }

        var patchDocType = context.ApiDescription.ParameterDescriptions
            .FirstOrDefault(p => p.Type.IsGenericType && p.Type.GetGenericTypeDefinition() == typeof(Microsoft.AspNetCore.JsonPatch.JsonPatchDocument<>));

        if (patchDocType != null)
        {
            // Remove the existing request body if it's the problematic one
            operation.RequestBody = new OpenApiRequestBody
            {
                Description = "A JSON Patch document to apply updates.",
                Required = true,
                Content =
                {
                    ["application/json-patch+json"] = new OpenApiMediaType
                    {
                        Schema = new OpenApiSchema
                        {
                            Type = "array",
                            Items = new OpenApiSchema
                            {
                                Type = "object",
                                Properties =
                                {
                                    ["op"] = new OpenApiSchema { Type = "string", Description = "The operation to perform (add, remove, replace, move, copy, test)." },
                                    ["path"] = new OpenApiSchema { Type = "string", Description = "The JSON Pointer to the target location." },
                                    ["value"] = new OpenApiSchema { Type = "object", Description = "The value to be used by the operation (if applicable)." },
                                    ["from"] = new OpenApiSchema { Type = "string", Description = "The JSON Pointer to the source location (for move and copy operations)." }
                                },
                                Required = new HashSet<string> { "op", "path" }
                            }
                        }
                    }
                }
            };
        }
    }
}