using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Models;

public class PublicationInfo
{
    public string? ProductTeamName { get; set; }
    public string? ProductTeamId { get; set; }
    public string? ProductGroupId { get; set; }
    public string? ProductGroupName { get; set; }
    public string? PublicationWeek { get; set; }
    public DateTime? PublicationDate { get; set; }
    public DateTime? PublicationStartDate { get; set; }
    public DateTime? PublicationEndDate { get; set; }
    public List<CciRepositoryScore> Backend { get; set; } = new();
    public List<CciRepositoryScore> Frontend { get; set; } = new();
}