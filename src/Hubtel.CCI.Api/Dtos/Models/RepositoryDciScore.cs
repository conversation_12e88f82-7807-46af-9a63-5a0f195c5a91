namespace Hubtel.CCI.Api.Dtos.Models;

/// <summary>
/// Represents the DCI score of a repository.
/// </summary>
public class RepositoryDciScore
{
    /// <summary>
    /// Gets or sets the id of the repository.
    /// </summary>
    public string RepositoryId { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the name of the repository.
    /// </summary>
    public string RepositoryName { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the URL of the repository.
    /// </summary>
    public string RepositoryUrl { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the type of the repository.
    /// </summary>
    public string RepositoryType { get; set; } = null!;
    
    
    /// <summary>
    /// Gets or sets the DCI score of the repository.
    /// </summary>
    public string ProductGroupId { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the name of the product group.
    /// </summary>
    public string ProductGroupName { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the id of the product team.
    /// </summary>
    public string ProductTeamId { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string ProductTeamName { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the DCI score of the repository.
    /// </summary>
    public decimal DciScore { get; set; }
}