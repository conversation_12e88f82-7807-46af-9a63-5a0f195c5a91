using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Models;

public class ProcessServiceBatchRequest
{
    public ProcessServiceBatchRequest(List<Service> batch, Repository repository, ProductGroupDetails repoExtraInfo, string publicationWeek, DateTime publicationDate, DateTime publicationStartDate, DateTime publicationEndDate, ServiceDciScoreComputationStatistics serviceStats, CancellationToken ct)
    {
        Batch = batch;
        Repository = repository;
        RepoExtraInfo = repoExtraInfo;
        PublicationWeek = publicationWeek;
        PublicationDate = publicationDate;
        PublicationStartDate = publicationStartDate;
        PublicationEndDate = publicationEndDate;
        ServiceStats = serviceStats;
        Ct = ct;
    }

    public List<Service> Batch { get; private set; }
    public Repository Repository { get; private set; }
    public ProductGroupDetails RepoExtraInfo { get; private set; }
    public string PublicationWeek { get; private set; }
    public DateTime PublicationDate { get; private set; }
    public DateTime PublicationStartDate { get; private set; }
    public DateTime PublicationEndDate { get; private set; }
    public ServiceDciScoreComputationStatistics ServiceStats { get; private set; }
    public CancellationToken Ct { get; private set; }
}