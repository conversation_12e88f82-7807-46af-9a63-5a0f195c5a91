using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Models;


/// <summary>
/// Represents a request to create a DCI score item.
/// </summary>
public class CreateDciScoreItemRequest
{

    /// <summary>
    /// Gets the repository.
    /// </summary>
    public required Repository Repository { get; set; }
    
    /// <summary>
    /// Gets the repository extra information.
    /// </summary>
    public required ProductGroupDetails RepoExtraInfo { get;  set; }
    
    /// <summary>
    /// Gets the product team.
    /// </summary>
    public required ProductTeamItem ProductTeam { get;  set; }
    
    /// <summary>
    /// Gets the service.
    /// </summary>
    public required Service Service { get;  set; }
    
    /// <summary>
    /// Gets the DCI data.
    /// </summary>
    public required (DciInput, DciResult) DciData { get;  set; }
    
    /// <summary>
    /// Gets the publication week.
    /// </summary>
    public required string PublicationWeek { get;  set; }
    
    /// <summary>
    /// Gets the publication date.
    /// </summary>
    public DateTime PublicationDate { get;  set; }
    
    /// <summary>
    /// Gets the publication start date.
    /// </summary>
    public DateTime PublicationStartDate { get;  set; }
    
    /// <summary>
    /// Gets the publication end date.
    /// </summary>
    public DateTime PublicationEndDate { get;  set; }
}