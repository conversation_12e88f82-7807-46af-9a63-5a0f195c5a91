using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Models;


/// <summary>
/// Represents a request to create a DCI score item.
/// </summary>
public class CreateDciScoreItemRequest
{
    public CreateDciScoreItemRequest(Repository repository, ProductGroupDetails repoExtraInfo, ProductTeamItem productTeam, Service service, (DciInput, DciResult) dciData, string publicationWeek, DateTime publicationDate, DateTime publicationStartDate, DateTime publicationEndDate)
    {
        Repository = repository;
        RepoExtraInfo = repoExtraInfo;
        ProductTeam = productTeam;
        Service = service;
        DciData = dciData;
        PublicationWeek = publicationWeek;
        PublicationDate = publicationDate;
        PublicationStartDate = publicationStartDate;
        PublicationEndDate = publicationEndDate;
    }

    /// <summary>
    /// Gets the repository.
    /// </summary>
    public Repository Repository { get; private set; }
    
    /// <summary>
    /// Gets the repository extra information.
    /// </summary>
    public ProductGroupDetails RepoExtraInfo { get; private set; }
    
    /// <summary>
    /// Gets the product team.
    /// </summary>
    public ProductTeamItem ProductTeam { get; private set; }
    
    /// <summary>
    /// Gets the service.
    /// </summary>
    public Service Service { get; private set; }
    
    /// <summary>
    /// Gets the DCI data.
    /// </summary>
    public (DciInput, DciResult) DciData { get; private set; }
    
    /// <summary>
    /// Gets the publication week.
    /// </summary>
    public string PublicationWeek { get; private set; }
    
    /// <summary>
    /// Gets the publication date.
    /// </summary>
    public DateTime PublicationDate { get; private set; }
    
    /// <summary>
    /// Gets the publication start date.
    /// </summary>
    public DateTime PublicationStartDate { get; private set; }
    
    /// <summary>
    /// Gets the publication end date.
    /// </summary>
    public DateTime PublicationEndDate { get; private set; }
}