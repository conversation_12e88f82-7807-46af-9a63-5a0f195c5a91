namespace Hubtel.CCI.Api.Dtos.Models.Azure;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;

public class AzureWorkItemDetailsListItemInfo
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("rev")]
    public int? Rev { get; set; }

    [JsonProperty("fields")]
    public AzureWorkItemFields? Fields { get; set; }

    [JsonProperty("multilineFieldsFormat")]
    public Dictionary<string, object>? MultilineFieldsFormat { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}

public class AzureWorkItemFields
{
    [JsonProperty("System.AreaPath")]
    public string? SystemAreaPath { get; set; }

    [JsonProperty("System.TeamProject")]
    public string? SystemTeamProject { get; set; }

    [JsonProperty("System.IterationPath")]
    public string? SystemIterationPath { get; set; }

    [JsonProperty("System.WorkItemType")]
    public string? SystemWorkItemType { get; set; }

    [JsonProperty("System.State")]
    public string? SystemState { get; set; }

    [JsonProperty("System.Reason")]
    public string? SystemReason { get; set; }

    [JsonProperty("System.CreatedDate")]
    public DateTime? SystemCreatedDate { get; set; }

    [JsonProperty("System.CreatedBy")]
    public AzureWorkItemDetailsListItemInfoIdentity? SystemCreatedBy { get; set; }

    [JsonProperty("System.ChangedDate")]
    public DateTime? SystemChangedDate { get; set; }

    [JsonProperty("System.ChangedBy")]
    public AzureWorkItemDetailsListItemInfoIdentity? SystemChangedBy { get; set; }

    [JsonProperty("System.CommentCount")]
    public int? SystemCommentCount { get; set; }

    [JsonProperty("System.Title")]
    public string? SystemTitle { get; set; }

    // Microsoft.VSTS.Common.* fields
    [JsonProperty("Microsoft.VSTS.Common.StateChangeDate")]
    public DateTime? VstsCommonStateChangeDate { get; set; }

    [JsonProperty("Microsoft.VSTS.Common.Priority")]
    public int? VstsCommonPriority { get; set; }
}

public class AzureWorkItemDetailsListItemInfoIdentity
{
    [JsonProperty("displayName")]
    public string? DisplayName { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public AzureWorkItemDetailsListItemInfoIdentityLinks? Links { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("uniqueName")]
    public string? UniqueName { get; set; }

    [JsonProperty("imageUrl")]
    public string? ImageUrl { get; set; }

    [JsonProperty("descriptor")]
    public string? Descriptor { get; set; }
}

public class AzureWorkItemDetailsListItemInfoIdentityLinks
{
    [JsonProperty("avatar")]
    public AzureWorkItemDetailsListItemInfoAvatarLink? Avatar { get; set; }
}

public class AzureWorkItemDetailsListItemInfoAvatarLink
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}
