using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Dtos.Models.Azure;

public class AzureReleaseInfo
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("createdOn")]
    public DateTime? CreatedOn { get; set; }

    [JsonProperty("modifiedOn")]
    public DateTime? ModifiedOn { get; set; }

    [JsonProperty("modifiedBy")]
    public Identity? ModifiedBy { get; set; }

    [JsonProperty("createdBy")]
    public Identity? CreatedBy { get; set; }

    [JsonProperty("createdFor")]
    public Identity? CreatedFor { get; set; }

    [JsonProperty("variables")]
    public Dictionary<string, object>? Variables { get; set; }

    [JsonProperty("variableGroups")]
    public List<object>? VariableGroups { get; set; }

    [JsonProperty("releaseDefinition")]
    public ReleaseDefinition? ReleaseDefinition { get; set; }

    [JsonProperty("releaseDefinitionRevision")]
    public int? ReleaseDefinitionRevision { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("reason")]
    public string? Reason { get; set; }

    [JsonProperty("releaseNameFormat")]
    public string? ReleaseNameFormat { get; set; }

    [JsonProperty("keepForever")]
    public bool? KeepForever { get; set; }

    [JsonProperty("definitionSnapshotRevision")]
    public int? DefinitionSnapshotRevision { get; set; }

    [JsonProperty("logsContainerUrl")]
    public string? LogsContainerUrl { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public ReleaseLinks? Links { get; set; }

    [JsonProperty("tags")]
    public List<string>? Tags { get; set; }

    [JsonProperty("triggeringArtifactAlias")]
    public string? TriggeringArtifactAlias { get; set; }

    [JsonProperty("projectReference")]
    public ProjectReference? ProjectReference { get; set; }

    [JsonProperty("properties")]
    public Dictionary<string, object>? Properties { get; set; }
}

public class Identity
{
    [JsonProperty("displayName")]
    public string? DisplayName { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public IdentityLinks? Links { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("uniqueName")]
    public string? UniqueName { get; set; }

    [JsonProperty("imageUrl")]
    public string? ImageUrl { get; set; }

    [JsonProperty("descriptor")]
    public string? Descriptor { get; set; }
}

public class IdentityLinks
{
    [JsonProperty("avatar")]
    public AvatarLink? Avatar { get; set; }
}

public class AvatarLink
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}

public class ReleaseDefinition
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("path")]
    public string? Path { get; set; }

    [JsonProperty("projectReference")]
    public object? ProjectReference { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public DefinitionLinks? Links { get; set; }
}

public class DefinitionLinks
{
    [JsonProperty("self")]
    public Link? Self { get; set; }

    [JsonProperty("web")]
    public Link? Web { get; set; }
}

public class ReleaseLinks
{
    [JsonProperty("self")]
    public Link? Self { get; set; }

    [JsonProperty("web")]
    public Link? Web { get; set; }
}

public class Link
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}

public class ProjectReference
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }
}
