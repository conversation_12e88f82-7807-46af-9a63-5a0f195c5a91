namespace Hubtel.CCI.Api.Dtos.Models.Azure;

using Newtonsoft.Json;

public class AzureWorkItemQuery
{
    [JsonProperty("queryType")]
    public string? QueryType { get; set; }

    [JsonProperty("queryResultType")]
    public string? QueryResultType { get; set; }

    [JsonProperty("asOf")]
    public DateTime? AsOf { get; set; }

    [JsonProperty("columns")]
    public List<AzureFieldColumn>? Columns { get; set; }

    [JsonProperty("workItems")]
    public List<AzureWorkItemRef>? WorkItems { get; set; }
}

public class AzureFieldColumn
{
    [JsonProperty("referenceName")]
    public string? ReferenceName { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}

public class AzureWorkItemRef
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}
