using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Dtos.Models.Azure;

public class AzureRepositoryResponse
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("project")]
    public ProjectInfo? Project { get; set; }

    [JsonProperty("defaultBranch")]
    public string? DefaultBranch { get; set; }

    [JsonProperty("size")]
    public long? Size { get; set; }

    [JsonProperty("remoteUrl")]
    public string? RemoteUrl { get; set; }

    [JsonProperty("sshUrl")]
    public string? SshUrl { get; set; }

    [JsonProperty("webUrl")]
    public string? WebUrl { get; set; }

    [JsonProperty("_links")]
    public RepositoryLinks? Links { get; set; }

    [JsonProperty("isDisabled")]
    public bool? IsDisabled { get; set; }

    [JsonProperty("isInMaintenance")]
    public bool? IsInMaintenance { get; set; }
}

public class ProjectInfo
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("state")]
    public string? State { get; set; }

    [JsonProperty("revision")]
    public int? Revision { get; set; }

    [JsonProperty("visibility")]
    public string? Visibility { get; set; }

    [JsonProperty("lastUpdateTime")]
    public DateTime? LastUpdateTime { get; set; }
}

public class RepositoryLinks
{
    [JsonProperty("self")]
    public LinkItem? Self { get; set; }

    [JsonProperty("project")]
    public LinkItem? Project { get; set; }

    [JsonProperty("web")]
    public LinkItem? Web { get; set; }

    [JsonProperty("ssh")]
    public LinkItem? Ssh { get; set; }

    [JsonProperty("commits")]
    public LinkItem? Commits { get; set; }

    [JsonProperty("refs")]
    public LinkItem? Refs { get; set; }

    [JsonProperty("pullRequests")]
    public LinkItem? PullRequests { get; set; }

    [JsonProperty("items")]
    public LinkItem? Items { get; set; }

    [JsonProperty("pushes")]
    public LinkItem? Pushes { get; set; }
}

public class LinkItem
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}