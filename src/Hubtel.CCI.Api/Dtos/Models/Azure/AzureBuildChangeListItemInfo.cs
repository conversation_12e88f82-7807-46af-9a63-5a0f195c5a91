namespace Hubtel.CCI.Api.Dtos.Models.Azure;

using Newtonsoft.Json;
using System;

public class AzureBuildChangeListItemInfo
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("author")]
    public ChangeAuthor? Author { get; set; }

    [JsonProperty("timestamp")]
    public DateTime? Timestamp { get; set; }

    [JsonProperty("location")]
    public string? Location { get; set; }

    [JsonProperty("messageTruncated")]
    public bool? MessageTruncated { get; set; }

    [JsonProperty("displayUri")]
    public string? DisplayUri { get; set; }

    [JsonProperty("pusher")]
    public string? Pusher { get; set; }
}

public class ChangeAuthor
{
    [JsonProperty("displayName")]
    public string? DisplayName { get; set; }

    [JsonProperty("_links")]
    public ChangeAuthorLinks? Links { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("uniqueName")]
    public string? UniqueName { get; set; }
}

public class ChangeAuthorLinks
{
    [JsonProperty("avatar")]
    public AzureBuildChangeListItemInfoAvatarLink? Avatar { get; set; }
}

public class AzureBuildChangeListItemInfoAvatarLink
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}
