namespace Hubtel.CCI.Api.Dtos.Models.Azure;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;

public class AzureReleaseDetail
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("createdOn")]
    public DateTime? CreatedOn { get; set; }

    [JsonProperty("modifiedOn")]
    public DateTime? ModifiedOn { get; set; }

    [JsonProperty("modifiedBy")]
    public AzureReleaseDetailIdentity? ModifiedBy { get; set; }

    [JsonProperty("createdBy")]
    public AzureReleaseDetailIdentity? CreatedBy { get; set; }

    [JsonProperty("createdFor")]
    public AzureReleaseDetailIdentity? CreatedFor { get; set; }

    [JsonProperty("environments")]
    public List<ReleaseEnvironment>? Environments { get; set; }

    [JsonProperty("variables")]
    public Dictionary<string, VariableValue>? Variables { get; set; }

    [JsonProperty("variableGroups")]
    public List<object>? VariableGroups { get; set; }

    [JsonProperty("artifacts")]
    public List<Artifact>? Artifacts { get; set; }

    [JsonProperty("releaseDefinition")]
    public AzureReleaseDetailReleaseDefinition? ReleaseDefinition { get; set; }

    [JsonProperty("releaseDefinitionRevision")]
    public int? ReleaseDefinitionRevision { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("reason")]
    public string? Reason { get; set; }

    [JsonProperty("releaseNameFormat")]
    public string? ReleaseNameFormat { get; set; }

    [JsonProperty("keepForever")]
    public bool? KeepForever { get; set; }

    [JsonProperty("definitionSnapshotRevision")]
    public int? DefinitionSnapshotRevision { get; set; }

    [JsonProperty("logsContainerUrl")]
    public string? LogsContainerUrl { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public AzureReleaseDetailReleaseLinks? Links { get; set; }

    [JsonProperty("tags")]
    public List<string>? Tags { get; set; }

    [JsonProperty("triggeringArtifactAlias")]
    public string? TriggeringArtifactAlias { get; set; }

    [JsonProperty("projectReference")]
    public AzureReleaseDetailProjectReference? ProjectReference { get; set; }

    [JsonProperty("properties")]
    public Dictionary<string, object>? Properties { get; set; }
}

/* ------------ Common Small Types ------------ */

public class VariableValue
{
    [JsonProperty("value")]
    public string? Value { get; set; }
}

public class AzureReleaseDetailLinkItem
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}

public class AzureReleaseDetailReleaseLinks
{
    [JsonProperty("self")]
    public LinkItem? Self { get; set; }

    [JsonProperty("web")]
    public LinkItem? Web { get; set; }
}

public class AzureReleaseDetailProjectReference
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }
}

public class AzureReleaseDetailIdentity
{
    [JsonProperty("displayName")]
    public string? DisplayName { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public AzureReleaseDetailIdentityLinks? Links { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("uniqueName")]
    public string? UniqueName { get; set; }

    [JsonProperty("imageUrl")]
    public string? ImageUrl { get; set; }

    [JsonProperty("descriptor")]
    public string? Descriptor { get; set; }
}

public class AzureReleaseDetailIdentityLinks
{
    [JsonProperty("avatar")]
    public AzureReleaseDetailAvatarLink? Avatar { get; set; }
}

public class AzureReleaseDetailAvatarLink
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}

/* ------------ Artifacts ------------ */

public class Artifact
{
    [JsonProperty("sourceId")]
    public string? SourceId { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("alias")]
    public string? Alias { get; set; }

    [JsonProperty("definitionReference")]
    public Dictionary<string, DefinitionReferenceItem>? DefinitionReference { get; set; }

    [JsonProperty("isPrimary")]
    public bool? IsPrimary { get; set; }

    [JsonProperty("isRetained")]
    public bool? IsRetained { get; set; }
}

public class DefinitionReferenceItem
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }
}

/* ------------ Release Definition ------------ */

public class AzureReleaseDetailReleaseDefinition
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("path")]
    public string? Path { get; set; }

    [JsonProperty("projectReference")]
    public object? ProjectReference { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public ReleaseLinks? Links { get; set; }
}

/* ------------ Release Environment (top-level in environments[]) ------------ */

public class ReleaseEnvironment
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("releaseId")]
    public int? ReleaseId { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("variables")]
    public Dictionary<string, object>? Variables { get; set; }

    [JsonProperty("variableGroups")]
    public List<object>? VariableGroups { get; set; }

    [JsonProperty("preDeployApprovals")]
    public List<Approval>? PreDeployApprovals { get; set; }

    [JsonProperty("postDeployApprovals")]
    public List<Approval>? PostDeployApprovals { get; set; }

    [JsonProperty("preApprovalsSnapshot")]
    public ApprovalsSnapshot? PreApprovalsSnapshot { get; set; }

    [JsonProperty("postApprovalsSnapshot")]
    public ApprovalsSnapshot? PostApprovalsSnapshot { get; set; }

    [JsonProperty("deploySteps")]
    public List<DeployStep>? DeploySteps { get; set; }

    [JsonProperty("requestedBy")]
    public Identity? RequestedBy { get; set; }

    [JsonProperty("requestedFor")]
    public Identity? RequestedFor { get; set; }

    [JsonProperty("queuedOn")]
    public DateTime? QueuedOn { get; set; }

    [JsonProperty("lastModifiedBy")]
    public Identity? LastModifiedBy { get; set; }

    [JsonProperty("lastModifiedOn")]
    public DateTime? LastModifiedOn { get; set; }

    [JsonProperty("hasStarted")]
    public bool? HasStarted { get; set; }

    [JsonProperty("tasks")]
    public List<object>? Tasks { get; set; }

    [JsonProperty("runPlanId")]
    public string? RunPlanId { get; set; }

    [JsonProperty("issues")]
    public List<object>? Issues { get; set; }

    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("definitionEnvironmentId")]
    public int? DefinitionEnvironmentId { get; set; }

    [JsonProperty("environmentOptions")]
    public EnvironmentOptions? EnvironmentOptions { get; set; }

    [JsonProperty("demands")]
    public List<object>? Demands { get; set; }

    [JsonProperty("conditions")]
    public List<Condition>? Conditions { get; set; }

    [JsonProperty("createdOn")]
    public DateTime? CreatedOn { get; set; }

    [JsonProperty("modifiedOn")]
    public DateTime? ModifiedOn { get; set; }

    [JsonProperty("workflowTasks")]
    public List<object>? WorkflowTasks { get; set; }

    [JsonProperty("deployPhasesSnapshot")]
    public List<DeployPhaseSnapshot>? DeployPhasesSnapshot { get; set; }

    [JsonProperty("owner")]
    public Identity? Owner { get; set; }

    [JsonProperty("schedules")]
    public List<object>? Schedules { get; set; }

    [JsonProperty("release")]
    public MinimalReleaseRef? Release { get; set; }

    [JsonProperty("releaseDefinition")]
    public ReleaseDefinition? ReleaseDefinition { get; set; }

    [JsonProperty("releaseCreatedBy")]
    public Identity? ReleaseCreatedBy { get; set; }

    [JsonProperty("triggerReason")]
    public string? TriggerReason { get; set; }

    [JsonProperty("timeToDeploy")]
    public double? TimeToDeploy { get; set; }

    [JsonProperty("processParameters")]
    public Dictionary<string, object>? ProcessParameters { get; set; }

    [JsonProperty("preDeploymentGatesSnapshot")]
    public GatesSnapshot? PreDeploymentGatesSnapshot { get; set; }

    [JsonProperty("postDeploymentGatesSnapshot")]
    public GatesSnapshot? PostDeploymentGatesSnapshot { get; set; }
}

/* ------------ Environment Sub-Types ------------ */

public class Approval
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("revision")]
    public int? Revision { get; set; }

    [JsonProperty("approver")]
    public Identity? Approver { get; set; }

    [JsonProperty("approvedBy")]
    public Identity? ApprovedBy { get; set; }

    [JsonProperty("approvalType")]
    public string? ApprovalType { get; set; }

    [JsonProperty("createdOn")]
    public DateTime? CreatedOn { get; set; }

    [JsonProperty("modifiedOn")]
    public DateTime? ModifiedOn { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("comments")]
    public string? Comments { get; set; }

    [JsonProperty("isAutomated")]
    public bool? IsAutomated { get; set; }

    [JsonProperty("isNotificationOn")]
    public bool? IsNotificationOn { get; set; }

    [JsonProperty("trialNumber")]
    public int? TrialNumber { get; set; }

    [JsonProperty("attempt")]
    public int? Attempt { get; set; }

    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("release")]
    public MinimalReleaseRef? Release { get; set; }

    [JsonProperty("releaseDefinition")]
    public ReleaseDefinition? ReleaseDefinition { get; set; }

    [JsonProperty("releaseEnvironment")]
    public MinimalReleaseEnvironmentRef? ReleaseEnvironment { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}

public class MinimalReleaseRef
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public ReleaseLinks? Links { get; set; }
}

public class MinimalReleaseEnvironmentRef
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public Dictionary<string, object>? Links { get; set; }
}

public class ApprovalsSnapshot
{
    [JsonProperty("approvals")]
    public List<ApprovalSnapshotItem>? Approvals { get; set; }

    [JsonProperty("approvalOptions")]
    public ApprovalOptions? ApprovalOptions { get; set; }
}

public class ApprovalSnapshotItem
{
    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("isAutomated")]
    public bool? IsAutomated { get; set; }

    [JsonProperty("isNotificationOn")]
    public bool? IsNotificationOn { get; set; }

    [JsonProperty("approver")]
    public Identity? Approver { get; set; }

    [JsonProperty("id")]
    public int? Id { get; set; }
}

public class ApprovalOptions
{
    [JsonProperty("requiredApproverCount")]
    public int? RequiredApproverCount { get; set; }

    [JsonProperty("releaseCreatorCanBeApprover")]
    public bool? ReleaseCreatorCanBeApprover { get; set; }

    [JsonProperty("autoTriggeredAndPreviousEnvironmentApprovedCanBeSkipped")]
    public bool? AutoTriggeredAndPreviousEnvironmentApprovedCanBeSkipped { get; set; }

    [JsonProperty("enforceIdentityRevalidation")]
    public bool? EnforceIdentityRevalidation { get; set; }

    [JsonProperty("timeoutInMinutes")]
    public int? TimeoutInMinutes { get; set; }

    [JsonProperty("executionOrder")]
    public string? ExecutionOrder { get; set; }
}

public class EnvironmentOptions
{
    [JsonProperty("emailNotificationType")]
    public string? EmailNotificationType { get; set; }

    [JsonProperty("emailRecipients")]
    public string? EmailRecipients { get; set; }

    [JsonProperty("skipArtifactsDownload")]
    public bool? SkipArtifactsDownload { get; set; }

    [JsonProperty("timeoutInMinutes")]
    public int? TimeoutInMinutes { get; set; }

    [JsonProperty("enableAccessToken")]
    public bool? EnableAccessToken { get; set; }

    [JsonProperty("publishDeploymentStatus")]
    public bool? PublishDeploymentStatus { get; set; }

    [JsonProperty("badgeEnabled")]
    public bool? BadgeEnabled { get; set; }

    [JsonProperty("autoLinkWorkItems")]
    public bool? AutoLinkWorkItems { get; set; }

    [JsonProperty("pullRequestDeploymentEnabled")]
    public bool? PullRequestDeploymentEnabled { get; set; }
}

public class Condition
{
    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("conditionType")]
    public string? ConditionType { get; set; }

    [JsonProperty("value")]
    public string? Value { get; set; }

    [JsonProperty("result")]
    public bool? Result { get; set; }
}

/* ------------ Deploy Step / Phase Runtime ------------ */

public class DeployStep
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("deploymentId")]
    public int? DeploymentId { get; set; }

    [JsonProperty("attempt")]
    public int? Attempt { get; set; }

    [JsonProperty("reason")]
    public string? Reason { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("operationStatus")]
    public string? OperationStatus { get; set; }

    [JsonProperty("releaseDeployPhases")]
    public List<ReleaseDeployPhase>? ReleaseDeployPhases { get; set; }

    [JsonProperty("requestedBy")]
    public Identity? RequestedBy { get; set; }

    [JsonProperty("requestedFor")]
    public Identity? RequestedFor { get; set; }

    [JsonProperty("queuedOn")]
    public DateTime? QueuedOn { get; set; }

    [JsonProperty("lastModifiedBy")]
    public Identity? LastModifiedBy { get; set; }

    [JsonProperty("lastModifiedOn")]
    public DateTime? LastModifiedOn { get; set; }

    [JsonProperty("hasStarted")]
    public bool? HasStarted { get; set; }

    [JsonProperty("tasks")]
    public List<object>? Tasks { get; set; }

    [JsonProperty("runPlanId")]
    public string? RunPlanId { get; set; }

    [JsonProperty("issues")]
    public List<object>? Issues { get; set; }
}

public class ReleaseDeployPhase
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("phaseId")]
    public string? PhaseId { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("phaseType")]
    public string? PhaseType { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("runPlanId")]
    public string? RunPlanId { get; set; }

    [JsonProperty("deploymentJobs")]
    public List<DeploymentJobContainer>? DeploymentJobs { get; set; }

    [JsonProperty("manualInterventions")]
    public List<object>? ManualInterventions { get; set; }

    [JsonProperty("startedOn")]
    public DateTime? StartedOn { get; set; }
}

public class DeploymentJobContainer
{
    [JsonProperty("job")]
    public DeploymentJob? Job { get; set; }

    [JsonProperty("tasks")]
    public List<DeploymentTaskItem>? Tasks { get; set; }
}

public class DeploymentJob
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("timelineRecordId")]
    public string? TimelineRecordId { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("dateStarted")]
    public DateTime? DateStarted { get; set; }

    [JsonProperty("dateEnded")]
    public DateTime? DateEnded { get; set; }

    [JsonProperty("startTime")]
    public DateTime? StartTime { get; set; }

    [JsonProperty("finishTime")]
    public DateTime? FinishTime { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("issues")]
    public List<object>? Issues { get; set; }

    [JsonProperty("agentName")]
    public string? AgentName { get; set; }

    [JsonProperty("logUrl")]
    public string? LogUrl { get; set; }
}

public class DeploymentTaskItem
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("timelineRecordId")]
    public string? TimelineRecordId { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("dateStarted")]
    public DateTime? DateStarted { get; set; }

    [JsonProperty("dateEnded")]
    public DateTime? DateEnded { get; set; }

    [JsonProperty("startTime")]
    public DateTime? StartTime { get; set; }

    [JsonProperty("finishTime")]
    public DateTime? FinishTime { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("percentComplete")]
    public int? PercentComplete { get; set; }

    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("issues")]
    public List<object>? Issues { get; set; }

    [JsonProperty("task")]
    public TaskMeta? Task { get; set; }

    [JsonProperty("agentName")]
    public string? AgentName { get; set; }

    [JsonProperty("logUrl")]
    public string? LogUrl { get; set; }
}

public class TaskMeta
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("version")]
    public string? Version { get; set; }
}

/* ------------ Deploy Phases Snapshot (definition snapshot) ------------ */

public class DeployPhaseSnapshot
{
    [JsonProperty("deploymentInput")]
    public DeploymentInput? DeploymentInput { get; set; }

    [JsonProperty("rank")]
    public int? Rank { get; set; }

    [JsonProperty("phaseType")]
    public string? PhaseType { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("refName")]
    public string? RefName { get; set; }

    [JsonProperty("workflowTasks")]
    public List<WorkflowTask>? WorkflowTasks { get; set; }
}

public class DeploymentInput
{
    [JsonProperty("healthPercent")]
    public int? HealthPercent { get; set; }

    [JsonProperty("deploymentHealthOption")]
    public string? DeploymentHealthOption { get; set; }

    [JsonProperty("tags")]
    public List<string>? Tags { get; set; }

    [JsonProperty("skipArtifactsDownload")]
    public bool? SkipArtifactsDownload { get; set; }

    [JsonProperty("artifactsDownloadInput")]
    public ArtifactsDownloadInput? ArtifactsDownloadInput { get; set; }

    [JsonProperty("queueId")]
    public int? QueueId { get; set; }

    [JsonProperty("demands")]
    public List<object>? Demands { get; set; }

    [JsonProperty("enableAccessToken")]
    public bool? EnableAccessToken { get; set; }

    [JsonProperty("timeoutInMinutes")]
    public int? TimeoutInMinutes { get; set; }

    [JsonProperty("jobCancelTimeoutInMinutes")]
    public int? JobCancelTimeoutInMinutes { get; set; }

    [JsonProperty("condition")]
    public string? Condition { get; set; }

    [JsonProperty("overrideInputs")]
    public Dictionary<string, object>? OverrideInputs { get; set; }
}

public class ArtifactsDownloadInput
{
    [JsonProperty("downloadInputs")]
    public List<object>? DownloadInputs { get; set; }
}

public class WorkflowTask
{
    [JsonProperty("environment")]
    public Dictionary<string, object>? Environment { get; set; }

    [JsonProperty("taskId")]
    public string? TaskId { get; set; }

    [JsonProperty("version")]
    public string? Version { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("refName")]
    public string? RefName { get; set; }

    [JsonProperty("enabled")]
    public bool? Enabled { get; set; }

    [JsonProperty("alwaysRun")]
    public bool? AlwaysRun { get; set; }

    [JsonProperty("continueOnError")]
    public bool? ContinueOnError { get; set; }

    [JsonProperty("timeoutInMinutes")]
    public int? TimeoutInMinutes { get; set; }

    [JsonProperty("retryCountOnTaskFailure")]
    public int? RetryCountOnTaskFailure { get; set; }

    [JsonProperty("definitionType")]
    public string? DefinitionType { get; set; }

    [JsonProperty("overrideInputs")]
    public Dictionary<string, object>? OverrideInputs { get; set; }

    [JsonProperty("condition")]
    public string? Condition { get; set; }

    [JsonProperty("inputs")]
    public Dictionary<string, string>? Inputs { get; set; }
}

/* ------------ Gates (empty snapshots in sample) ------------ */

public class GatesSnapshot
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("gatesOptions")]
    public object? GatesOptions { get; set; }

    [JsonProperty("gates")]
    public List<object>? Gates { get; set; }
}
