namespace Hubtel.CCI.Api.Dtos.Models.Azure;

using Newtonsoft.Json;
using System;
using System.Collections.Generic;

public class AzurePullRequestInfo
{
    [JsonProperty("repository")]
    public PullRequestRepository? Repository { get; set; }

    [JsonProperty("pullRequestId")]
    public int? PullRequestId { get; set; }

    [JsonProperty("codeReviewId")]
    public int? CodeReviewId { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("createdBy")]
    public AzurePullRequestInfoIdentity? CreatedBy { get; set; }

    [JsonProperty("creationDate")]
    public DateTime? CreationDate { get; set; }

    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("sourceRefName")]
    public string? SourceRefName { get; set; }

    [JsonProperty("targetRefName")]
    public string? TargetRefName { get; set; }

    [JsonProperty("mergeStatus")]
    public string? MergeStatus { get; set; }

    [JsonProperty("isDraft")]
    public bool? IsDraft { get; set; }

    [JsonProperty("mergeId")]
    public string? MergeId { get; set; }

    [JsonProperty("lastMergeSourceCommit")]
    public CommitRef? LastMergeSourceCommit { get; set; }

    [JsonProperty("lastMergeTargetCommit")]
    public CommitRef? LastMergeTargetCommit { get; set; }

    [JsonProperty("lastMergeCommit")]
    public CommitRef? LastMergeCommit { get; set; }

    [JsonProperty("reviewers")]
    public List<Reviewer>? Reviewers { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("completionOptions")]
    public CompletionOptions? CompletionOptions { get; set; }

    [JsonProperty("supportsIterations")]
    public bool? SupportsIterations { get; set; }

    [JsonProperty("autoCompleteSetBy")]
    public AzurePullRequestInfoIdentity? AutoCompleteSetBy { get; set; }
}

public class PullRequestRepository
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("project")]
    public RepoProjectInfo? Project { get; set; }
}

public class RepoProjectInfo
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("state")]
    public string? State { get; set; }

    [JsonProperty("visibility")]
    public string? Visibility { get; set; }

    [JsonProperty("lastUpdateTime")]
    public DateTime? LastUpdateTime { get; set; }
}

public class AzurePullRequestInfoIdentity
{
    [JsonProperty("displayName")]
    public string? DisplayName { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("_links")]
    public AzurePullRequestInfoIdentityLinks? Links { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("uniqueName")]
    public string? UniqueName { get; set; }

    [JsonProperty("imageUrl")]
    public string? ImageUrl { get; set; }

    [JsonProperty("descriptor")]
    public string? Descriptor { get; set; }
}

public class AzurePullRequestInfoIdentityLinks
{
    [JsonProperty("avatar")]
    public AzurePullRequestInfoAvatarLink? Avatar { get; set; }
}

public class AzurePullRequestInfoAvatarLink
{
    [JsonProperty("href")]
    public string? Href { get; set; }
}

public class CommitRef
{
    [JsonProperty("commitId")]
    public string? CommitId { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}

public class Reviewer
{
    // Azure DevOps reviewer objects can have many fields; keep these flexible & nullable.
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("displayName")]
    public string? DisplayName { get; set; }

    [JsonProperty("uniqueName")]
    public string? UniqueName { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("imageUrl")]
    public string? ImageUrl { get; set; }

    [JsonProperty("isRequired")]
    public bool? IsRequired { get; set; }

    [JsonProperty("hasDeclined")]
    public bool? HasDeclined { get; set; }

    [JsonProperty("isFlagged")]
    public bool? IsFlagged { get; set; }

    [JsonProperty("vote")]
    public int? Vote { get; set; }

    [JsonProperty("_links")]
    public IdentityLinks? Links { get; set; }
}

public class CompletionOptions
{
    [JsonProperty("mergeCommitMessage")]
    public string? MergeCommitMessage { get; set; }

    [JsonProperty("deleteSourceBranch")]
    public bool? DeleteSourceBranch { get; set; }

    [JsonProperty("mergeStrategy")]
    public string? MergeStrategy { get; set; }

    [JsonProperty("transitionWorkItems")]
    public bool? TransitionWorkItems { get; set; }

    // Use object? for flexibility; Azure may return ints/strings here.
    [JsonProperty("autoCompleteIgnoreConfigIds")]
    public List<object>? AutoCompleteIgnoreConfigIds { get; set; }
}
