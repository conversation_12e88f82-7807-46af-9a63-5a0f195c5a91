namespace Hubtel.CCI.Api.Dtos.Models;

/// <summary>
/// Represents a request to calculate deployment metrics.
/// </summary>
public class CalculateDeploymentMetricsRequest
{
    /// <summary>
    /// Gets or sets the azure project name.
    /// </summary>
    public string Project { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the release definition ID for azure.
    /// </summary>
    public int ReleaseDefinitionId { get; set; }
    
    /// <summary>
    /// Gets or sets the repository ID for azure.
    /// </summary>
    public string RepositoryId { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the service name.
    /// </summary>
    public string ServiceName { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the start time for release period under observation.
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// Gets or sets the end time for release period under observation.
    /// </summary>
    public DateTime EndTime { get; set; }
    
    /// <summary>
    /// Gets or sets the number of releases to consider within the periond(start time to end time).
    /// </summary>
    public int Top { get; set; } = 5;
}