using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Requests.Azure;

public class FetchAzureDeploymentsRequest: SearchFilter
{
    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }
    
    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }
    
    /// <summary>
    /// Gets or sets the project name.
    /// </summary>
    public string? ProjectName { get; set; }
    
    
    public bool HasDeploymentRecords { get; set; } = true;
    
}