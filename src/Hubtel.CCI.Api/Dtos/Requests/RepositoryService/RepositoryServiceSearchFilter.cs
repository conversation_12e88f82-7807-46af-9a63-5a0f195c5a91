using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Requests.RepositoryService;

/// <summary>
/// Represents the request object for searching repository services.
/// </summary>
public class RepositoryServiceSearchFilter : SearchFilter
{
    /// <summary>
    /// Gets or sets the repository IDs.
    /// </summary>
    public List<string>? RepositoryIds { get; set; }
    
    /// <summary>
    /// Gets or sets the repository ID.
    /// </summary>
    public string? RepositoryId { get; set; }
    
    /// <summary>
    /// Gets or sets the repository type.
    /// </summary>
    public string? RepositoryType { get; set; }
    
    /// <summary>
    /// Gets or sets the status of the repository service.
    /// </summary>
    public string? Status { get; set; }
}