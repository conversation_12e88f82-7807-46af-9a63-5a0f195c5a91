using System.ComponentModel.DataAnnotations;

namespace Hubtel.CCI.Api.Dtos.Requests.RepositoryService;

/// <summary>
/// Request model for updating an existing repository service
/// </summary>
public class UpdateRepositoryServiceRequest
{
    /// <summary>
    /// The name of the service.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Optional description of the service.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The unique identifier of the repository associated with the service.
    /// </summary>
    public string? RepositoryId { get; set; }

    /// <summary>
    /// The name of the repository associated with the service.
    /// </summary>
    public string? RepositoryName { get; set; }
    

    /// <summary>
    /// The current release tag of the service.
    /// </summary>
    public string? TagName { get; set; }

    /// <summary>
    /// The type/category of the service (e.g., API, WebApp, etc.).
    /// </summary>
    public string? ServiceType { get; set; }
    
    /// <summary>
    /// The type of the repository associated with the service.
    /// </summary>
    public string? RepositoryType { get; set; }

    /// <summary>
    /// The ID of the engineer responsible for the service.
    /// </summary>
    public string? EngineerResponsibleId { get; set; }

    /// <summary>
    /// The name of the engineer responsible for the service.
    /// </summary>
    public string? EngineerResponsibleName { get; set; }

    /// <summary>
    /// The ID of the engineer accountable for the service.
    /// </summary>
    public string? EngineerAccountableId { get; set; }

    /// <summary>
    /// The name of the engineer accountable for the service.
    /// </summary>
    public string? EngineerAccountableName { get; set; }

    /// <summary>
    /// Indicates whether the service is inward or outward facing.
    /// </summary>
    public string? ServiceFacingDirection { get; set; }

    /// <summary>
    /// Person or team to consult for the service.
    /// </summary>
    public string? WhoToConsult { get; set; }

    /// <summary>
    /// Person or team to inform about the service.
    /// </summary>
    public string? WhoToInform { get; set; }

    /// <summary>
    /// Status of Continuous Deployment setup for the service.
    /// </summary>
    public string? CdSetupStatus { get; set; }

    /// <summary>
    /// Indicates if the service sends money.
    /// </summary>
    public bool? ServiceSendsMoney { get; set; }

    /// <summary>
    /// Indicates if the service receives money.
    /// </summary>
    public bool? ServiceReceivesMoney { get; set; }

    /// <summary>
    /// Types of end users for this service (e.g., Customers, Businesses).
    /// </summary>
    public List<string>? EndUsersTypes { get; set; }

    /// <summary>
    /// Indicates if the service has more than 1000 end users.
    /// </summary>
    public bool? HasMoreThanThousandEndUsers { get; set; }

    /// <summary>
    /// Indicates if the service can misrepresent data.
    /// </summary>
    public bool? CanMisrepresentData { get; set; }

    /// <summary>
    /// Indicates if the service is publicly available in the market.
    /// </summary>
    public bool? ServiceInMarket { get; set; } = false;

    /// <summary>
    /// Indicates if this service will affect other services when changed.
    /// </summary>
    public bool? WillAffectOtherServices { get; set; } = false;

    /// <summary>
    /// Number of downstream services that depend on this service.
    /// </summary>
    public int? DownstreamServicesCount { get; set; }

    /// <summary>
    /// Indicates if the service receives the MER component.
    /// </summary>
    public bool? ReceiveMerComponent { get; set; }

    /// <summary>
    /// Indicates if the service sends the MER component.
    /// </summary>
    public bool? SendsMerComponent { get; set; }

    /// <summary>
    /// Exposure level when receiving the MER component (Limited or Unlimited).
    /// </summary>
    public string? ReceiveMerExposure { get; set; }

    /// <summary>
    /// Exposure level when sending the MER component (Limited or Unlimited).
    /// </summary>
    public string? SendsMerExposure { get; set; }

    /// <summary>
    /// Indicates the likelihood of a data breach (High or Low).
    /// </summary>
    public string? PossibilityOfDataBreach { get; set; }

    /// <summary>
    /// Total calculated risk score based on metadata answers.
    /// </summary>
    public decimal? TotalRiskScore { get; set; }

    /// <summary>
    /// Risk classification derived from the total risk score.
    /// </summary>
    public string? RiskClassification { get; set; }

    /// <summary>
    /// Deployment strategy used by the service (e.g., Blue/Green, Canary).
    /// </summary>
    public string? DeploymentStrategy { get; set; }

    /// <summary>
    /// Required competence level of engineers working on the service (e.g., D1-D4).
    /// </summary>
    public string? EngineerCompetenceRequirement { get; set; }

    /// <summary>
    /// Code review requirement level for the service (e.g., 1-5).
    /// </summary>
    public string? CodeReviewRequirement { get; set; }

    /// <summary>
    /// URL to the release pipeline or artifact for the service.
    /// </summary>
    [Url]
    public string? ServiceReleaseUrl { get; set; }

    /// <summary>
    /// ID of the release pipeline definition in Azure DevOps.
    /// </summary>
    public string? ServiceReleaseDefinitionId { get; set; }
    
    public string? Status { get; set; } = null!;
}