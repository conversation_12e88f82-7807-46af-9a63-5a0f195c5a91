namespace Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;

/// <summary>
/// Represents the request object for creating a new CciRepositoryScore.
/// </summary>
public class CreateCciRepositoryScoreRequest
{
    /// <summary>
    /// Gets or sets the SonarQube key of the repository.
    /// </summary>
    public string RepositorySonarQubeKey { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? ProductTeamName { get; set; }

    /// <summary>
    /// Gets or sets the name of the repository.
    /// </summary>
    public string? RepositoryName { get; set; }

    /// <summary>
    /// Gets or sets the URL of the repository.
    /// </summary>
    public string? RepositoryUrl { get; set; }

    /// <summary>
    /// Gets or sets the type of the repository.
    /// </summary>
    public string? RepositoryType { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product team.
    /// </summary>
    public string? ProductTeamId { get; set; }

    /// <summary>
    /// Gets or sets the name of the product group.
    /// </summary>
    public string? ProductGroupName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product group.
    /// </summary>
    public string? ProductGroupId { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether SonarQube metrics were acquired.
    /// </summary>
    public bool SonarQubeMetricsAcquired { get; set; } = true;

    /// <summary>
    /// Gets or sets the final average score.
    /// </summary>
    public decimal? FinalAverage { get; set; }

    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication end date.
    /// </summary>
    public DateTime? PublicationEndDate { get; set; }

    /// <summary>
    /// Gets or sets the publication start date.
    /// </summary>
    public DateTime? PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the average score.
    /// </summary>
    public decimal? Average { get; set; }

    /// <summary>
    /// Gets or sets the computation value for code smells.
    /// </summary>
    public decimal? CodeSmellsComputation { get; set; }

    /// <summary>
    /// Gets or sets the code coverage percentage.
    /// </summary>
    public decimal? Coverage { get; set; }

    /// <summary>
    /// Gets or sets the number of bugs.
    /// </summary>
    public decimal? Bugs { get; set; }

    /// <summary>
    /// Gets or sets the computation value for bugs.
    /// </summary>
    public decimal? BugsComputation { get; set; }

    /// <summary>
    /// Gets or sets the number of code smells.
    /// </summary>
    public decimal? CodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the computation value for code coverage.
    /// </summary>
    public decimal? CoverageComputation { get; set; }

    /// <summary>
    /// Gets or sets the duplicated lines density percentage.
    /// </summary>
    public decimal? DuplicatedLinesDensity { get; set; }

    /// <summary>
    /// Gets or sets the computation value for duplicated lines density.
    /// </summary>
    public decimal? DuplicatedLinesDensityComputation { get; set; }

    /// <summary>
    /// Gets or sets the number of vulnerabilities.
    /// </summary>
    public decimal? Vulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the computation value for vulnerabilities.
    /// </summary>
    public decimal? VulnerabilitiesComputation { get; set; }

    /// <summary>
    /// Gets or sets the number of security hotspots.
    /// </summary>
    public decimal? SecurityHotspots { get; set; }

    /// <summary>
    /// Gets or sets the computation value for security hotspots.
    /// </summary>
    public decimal? SecurityHotspotsComputation { get; set; }

    /// <summary>
    /// Gets or sets the security rating.
    /// </summary>
    public decimal? SecurityRating { get; set; }

    /// <summary>
    /// Gets or sets the computation value for the security rating.
    /// </summary>
    public decimal? SecurityRatingComputation { get; set; }

    /// <summary>
    /// Gets or sets the reliability rating.
    /// </summary>
    public decimal? ReliabilityRating { get; set; }

    /// <summary>
    /// Gets or sets the computation value for reopened issues.
    /// </summary>
    public decimal? ReopenedIssuesComputation { get; set; }

    /// <summary>
    /// Gets or sets the cognitive complexity value.
    /// </summary>
    public decimal? CognitiveComplexity { get; set; }

    /// <summary>
    /// Gets or sets the computation value for cognitive complexity.
    /// </summary>
    public decimal? CognitiveComplexityComputation { get; set; }

    /// <summary>
    /// Gets or sets the computation value for the reliability rating.
    /// </summary>
    public decimal? ReliabilityRatingComputation { get; set; }

    /// <summary>
    /// Gets or sets the number of reopened issues.
    /// </summary>
    public decimal? ReopenedIssues { get; set; }

    /// <summary>
    /// Gets or sets the framework upgrade score computation value for the repository
    /// </summary>
    public decimal? FrameworkUpgradeComputation { get; set; }


    /// <summary>
    /// Gets or sets the Semantic score computation value for the repository
    /// </summary>
    public decimal? SemanticScoreComputation { get; set; }

    /// <summary>
    /// Gets or sets the id of the repository.
    /// </summary>
    public string? RepositoryId { get; set; }
    
    /// <summary>
    /// Gets or sets the status of the repository.
    /// </summary>
    public string? Status { get; set; }
        
    /// <summary>
    /// Gets or sets the relative cognitive complexity value.
    /// </summary>
    public decimal? RelativeCognitiveComplexity { get; set; }
    
    
    /// <summary>
    /// Gets or sets the computation value for relative cognitive complexity.
    /// </summary>
    public decimal? RelativeCognitiveComplexityComputation { get; set; }
    
    /// <summary>
    /// Gets or sets the number of non commented lines of code.
    /// </summary>
    public decimal? NonCommentedLinesOfCode { get; set; }
    
    /// <summary>
    /// Gets or sets the lines of code.
    /// </summary>
    public decimal? LinesOfCode { get; set; }
}