using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;


/// <summary>
/// Represents a request to get CCI repository table scores based on products.
/// </summary>
public class GetCciRepositoryTableScoresPaginatedRequest: SearchFilter
{
    /// <summary>
    /// Gets or sets the start date of the publication period.
    /// Defaults to the start date of the previous publication week.
    /// </summary>
    public DateTime? PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the publication period.
    /// Defaults to the end date of the previous publication week.
    /// </summary>
    public DateTime? PublicationEndDate { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets the product team ID.
    /// </summary>
    public string? ProductTeamId { get; set; }
    
    
    /// <summary>
    /// Gets or sets the repository type
    /// </summary>
    public string? RepositoryType { get; set; }
    
    /// <summary>
    /// Gets or sets the search term for filtering results.
    /// </summary>
    public string? Search { get; set; }
    
    
    /// <summary>
    /// Gets or sets the repository ID.
    /// </summary>
    public string? RepositoryId { get; set; }
}
