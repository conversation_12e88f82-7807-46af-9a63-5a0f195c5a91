namespace Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;

/// <summary>
/// Represents a request to get CCI products rankings.
/// </summary>
public class GetCciProductsRankingsRequest
{
    /// <summary>
    /// Gets or sets the start date of the publication period.
    /// Defaults to the start date of the previous publication week.
    /// </summary>
    public DateTime? PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the publication period.
    /// Defaults to the end date of the previous publication week.
    /// </summary>
    public DateTime? PublicationEndDate { get; set; }

    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }
}