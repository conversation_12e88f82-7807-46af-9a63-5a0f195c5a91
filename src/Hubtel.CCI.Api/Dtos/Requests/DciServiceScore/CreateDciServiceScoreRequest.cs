using System.ComponentModel.DataAnnotations;

namespace Hubtel.CCI.Api.Dtos.Requests.DciServiceScore;

/// <summary>
/// Represents the request object for creating a new DCI service score.
/// </summary>
public class CreateDciServiceScoreRequest
{
    /// <summary>
    /// Gets or sets the SonarQube key of the repository.
    /// </summary>
    [Required]
    public string RepositorySonarQubeKey { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the repository.
    /// </summary>
    public string? RepositoryName { get; set; }

    /// <summary>
    /// Gets or sets the id of the repository.
    /// </summary>
    public string? RepositoryId { get; set; }

    /// <summary>
    /// Gets or sets the URL of the repository.
    /// </summary>
    public string? RepositoryUrl { get; set; }

    /// <summary>
    /// Gets or sets the type of the repository.
    /// </summary>
    public string? RepositoryType { get; set; }

    /// <summary>
    /// Gets or sets the stability score.
    /// </summary>
    public decimal? StabilityScore { get; set; }

    /// <summary>
    /// Gets or sets the process compliance score.
    /// </summary>
    public decimal? ProcessComplianceScore { get; set; }

    /// <summary>
    /// Gets or sets the speed score.
    /// </summary>
    public decimal? SpeedScore { get; set; }

    /// <summary>
    /// Gets or sets the success rate score.
    /// </summary>
    public decimal? SuccessRateScore { get; set; }

    /// <summary>
    /// Gets or sets the issue severity score.
    /// </summary>
    public decimal? IssueSeverityScore { get; set; }

    /// <summary>
    /// Gets or sets the code confidence index score.
    /// </summary>
    public decimal? CodeConfidenceIndexScore { get; set; }
}