using Hubtel.CCI.Api.Data.Entities;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Hubtel.CCI.Api.Dtos.Requests.ToolingReport
{
    public class UpdateIssueTrackerRequest
    {
        public string ReportedBy { get; set; } = null!;
        public string[] ProductGroup { get; set; } = [];
        public string[] ProductTeam { get; set; } = [];
        public string Tool { get; set; } = null!;
        public List<string> ServicesAffected { get; set; } = new();
        [Required]
        [JsonRequired]
        public Domain Domain { get; set; }
        public string IncidentDescription { get; set; } = null!;
        public string ActionTaken { get; set; } = null!;
        [JsonRequired]
        public IssueStatus Status { get; set; }
    }
}
