using Hubtel.CCI.Api.Data.Entities;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Hubtel.CCI.Api.Dtos.Requests.ToolingReport
{
    public class CreateIssueTrackerRequest
    {
        [Required]
        public string ReportedBy { get; set; } = null!;
        [Required]
        public string RecordedBy { get; set; } = null!;
        [Required, MinLength(1)]
        public List<string> ProductGroup { get; set; } = new();
        [Required, MinLength(1)]
        public List<string> ProductTeam { get; set; } = new();
        [JsonPropertyName("tool")]
        public string ToolName { get; set; } = null!;
        [Required,MinLength(1)]
        public List<string> ServicesAffected { get; set; } = new();
        [Required]
        [JsonRequired]
        public IssueStatus Status { get; set; }
        [Required]
        public string IncidentDescription { get; set; } = null!;
        [Required]
        public string ActionTaken { get; set; } = null!;
        [Required]
        [JsonRequired]
        public Domain Domain { get; set; }
        [Required]
        [JsonRequired]
        public string AssignedTo { get; set; } = null!;
        [Required]
        public IncidentSeverity Severity { get; set; } = IncidentSeverity.Low;
    }
}
