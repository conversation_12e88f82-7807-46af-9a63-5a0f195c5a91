using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Requests.ToolingReport
{
    public class GetIssueTrackerRequest:BaseFilter
    {
        public List<string> ProductGroup { get; set; } = new();
        public List<string> ProductTeam { get; set; } = new();
        public string? Tool { get; set; }
        public string? ReportedBy { get; set; }
        public string? Domain { get; set; } 
        public string? Status { get; set; }
        public List<string> Services { get; set; } = new();
        public string ToolVersion { get; set; } = "";
        public int PageNumber { get; set; } = 1;
    }
}
