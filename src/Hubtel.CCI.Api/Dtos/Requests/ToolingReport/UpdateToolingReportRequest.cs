using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Requests.ToolingReport;

public class UpdateToolingReportRequest
{
    public GeneralOverview GeneralOverview { get; set; } = null!;
    public ExecutiveSummary ExecutiveSummary { get; set; } = null!;
    public KeyMetricsOverview KeyMetricsOverview { get; set; } = null!;
    public List<DetailedProjectStatus> DetailedProjectStatus { get; set; } = new();
    public IssuesSummary IssuesSummary { get; set; } = null!;
    public PriorityActions PriorityActions { get; set; } = null!;
    public Recommendations Recommendation { get; set; } = new();
    public List<string> PlugAndPlaySolutionsInUse { get; set; } = new();
}