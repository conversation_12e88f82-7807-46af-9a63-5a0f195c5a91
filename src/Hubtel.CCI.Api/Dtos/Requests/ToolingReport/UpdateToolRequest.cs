using Hubtel.CCI.Api.Data.Entities;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Hubtel.CCI.Api.Dtos.Requests.ToolingReport
{
    public class UpdateToolRequest
    {
        [Required]
        public required string Name { get; set; }
        /// <summary>
        /// Description of the tool
        /// </summary>
        public string Description { get; set; } = string.Empty;
        /// <summary>
        /// Version of the tool
        /// </summary>
        public string Version { get; set; } = string.Empty;
        /// <summary>
        /// URL to the tool's documentation
        /// </summary>
        public string DocumentationUrl { get; set; } = string.Empty;
        [Required]
        [JsonRequired]
        public Domain Domain { get; set; }
    }
}
