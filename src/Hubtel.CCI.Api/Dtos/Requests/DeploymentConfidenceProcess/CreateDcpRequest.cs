using System.ComponentModel.DataAnnotations;
using Hubtel.CCI.Api.Attributes;
using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Dtos.Models;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;


public class CreateDcpRequest
{
    [Required(AllowEmptyStrings = false)] public string? Description { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductGroupId { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductGroupName { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductTeamName { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductTeamId { get; set; }

    [AllowedValues(DeploymentAreaOfImpact.DefaultBackend, DeploymentAreaOfImpact.DefaultFrontend,
        DeploymentAreaOfImpact.DefaultBoth)]
    public string? AreaOfImpact { get; set; }

    [Required(AllowEmptyStrings = false)] public string? SystemBehaviorOfChange { get; set; }
    public bool HasControlledEnv { get; set; } = false;

    [IsAllowedValue(DeploymentRequestPriority.Low,
        DeploymentRequestPriority.Medium,
        DeploymentRequestPriority.High)]
    public string? Priority { get; set; }

    [MinLength(1)] public List<AffectedRepository> AffectedRepositories { get; set; } = new();
    [MinLength(1)] public List<ServiceToBeDeployed> ServicesToBeDeployed { get; set; } = new();
    [SwaggerIgnore] public AuthClaim? AuthData { get; set; }

}