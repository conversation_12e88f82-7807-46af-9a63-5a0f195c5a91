using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Hubtel.CCI.Api.Attributes;
using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Dtos.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
public class Service
{
    public string? Id { get; set; } 

    public string? ServiceReleaseUrl { get; set; }
    public string? ServiceReleaseId { get; set; }
    public string? RepositoryId { get; set; }
    public string? RepositoryName { get; set; }
    public string? ServiceName { get; set; }
    public string? ServiceTagName { get; set; }
    public ClassificationInfo ClassificationInfo { get; set; } = new ClassificationInfo();
    public DateTime? UpdatedAt { get; set; }
    public DateTime? CreatedAt { get; set; }
}

public class DcpRequest
{
    [Required(AllowEmptyStrings = false)] public string? Description { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductGroupId { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductGroupName { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductTeamName { get; set; }
    [Required(AllowEmptyStrings = false)] public string? ProductTeamId { get; set; }
    [Required(AllowEmptyStrings = false)] public string? EngineerRequestId { get; set; }
    [Required(AllowEmptyStrings = false)] public string? EngineerRequestName { get; set; }

    [AllowedValues(DeploymentAreaOfImpact.DefaultBackend, DeploymentAreaOfImpact.DefaultFrontend,
        DeploymentAreaOfImpact.DefaultBoth)]
    public string? AreaOfImpact { get; set; }

    [Required(AllowEmptyStrings = false)] public string? SystemBehaviorOfChange { get; set; }

    public bool HasControlledEnv { get; set; } = false;

    [IsAllowedValue(DeploymentRequestPriority.High, DeploymentRequestPriority.Medium, DeploymentRequestPriority.Low)]
    public string? Priority { get; set; }

    public string? CicdEngineerResponsibleId { get; set; }
    public string? CicdEngineerResponsibleName { get; set; }
    public DateTime? DcpPlanningMeetingStartTime { get; set; }
    public DateTime? DcpPlanningMeetingEndTime { get; set; }
    public DateTime? DcpWorkingSessionStartTime { get; set; }
    public DateTime? DcpWorkingSessionEndTime { get; set; }
    public DateTime? DcpWorkingSessionReviewStartTime { get; set; }
    public DateTime? DcpWorkingSessionReviewEndTime { get; set; }
    public DateTime? DcpConfidenceReadinessReviewStartTime { get; set; }
    public DateTime? DcpConfidenceReadinessReviewEndTime { get; set; }
    public string? DeploymentConfidenceReportLink { get; set; }
    public string? CancellationReason { get; set; }
    public string? DeltaDiscretion { get; set; }
    public List<AffectedRepository> AffectedRepositories { get; set; } = new();
    public List<ServiceToBeDeployed> ServicesToBeDeployed { get; set; } = new();

    [IsAllowedValue(DeploymentRequestStatus.NotStarted,
        DeploymentRequestStatus.InProgress,
        DeploymentRequestStatus.Completed,
        DeploymentRequestStatus.Rejected,
        DeploymentRequestStatus.Cancelled)]
    public string? Status { get; set; }

    [SwaggerIgnore] public AuthClaim? AuthData { get; set; }
}

public class ClassificationInfo
{
    public int ServiceScore { get; set; } = 0;
    public string? Category { get; set; }
}

public class AffectedRepository
{
    public string? Name { get; set; }
    public string? Id { get; set; }
    public string? LinkToPullRequest { get; set; }
    public string? FeatureArea { get; set; }
}

public class ServiceToBeDeployed
{
    [Required(AllowEmptyStrings = false)] 
    public string? ServiceId { get; set; }
    public string? ServiceName { get; set; }
    public string? RepositoryId { get; set; }
    public string? RepositoryName { get; set; }
    public List<ServiceDeploymentTrailResponse> DeploymentTrails { get; set; } = [];

}

public class ServiceToBeDeployedInput
{
    [Required(AllowEmptyStrings = false)] 
    public string? ServiceId { get; set; }
}