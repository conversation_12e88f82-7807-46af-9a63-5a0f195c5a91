using System.ComponentModel.DataAnnotations;
using Hubtel.CCI.Api.Attributes;
using Hubtel.CCI.Api.Constants;

namespace Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;

public class ServiceDeploymentTrailRequest
{
    [Required(AllowEmptyStrings = false)] public string? DeploymentRequestId { get; set; }
    [Required(AllowEmptyStrings = false)] public string? RepositoryServiceId { get; set; }

    [IsAllowedValue(ServiceDeploymentTrailStatus.DefaultFailed, ServiceDeploymentTrailStatus.DefaultSuccessful)]
    public string? Status { get; set; } = ServiceDeploymentTrailStatus.DefaultSuccessful;

    public string? Description { get; set; }
}