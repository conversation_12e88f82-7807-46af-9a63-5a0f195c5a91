using System.Text.Json.Serialization;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;

public class DcpRequestFilter:BaseFilter
{
    public string? ProductGroupId { get; set; }
    public string? ProductGroupName { get; set; }
    public string? ProductTeamName { get; set; }
    public string? ProductTeamId { get; set; }
    public string? EngineerRequestId { get; set; }
    public string? EngineerRequestName { get; set; }
    public bool? HasControlledEnv { get; set; }
    public string? Priority { get; set; }
    public List<string> Status { get; set; } = [];
    public string? AreaOfImpact { get; set; }
    [SwaggerIgnore] public AuthClaim? AuthUser { get; set; }
}