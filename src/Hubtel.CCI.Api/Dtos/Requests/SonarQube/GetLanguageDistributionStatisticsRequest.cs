using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Requests.SonarQube;

public class GetLanguageDistributionStatisticsRequest: SearchFilter
{
    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; } 
}