using Hubtel.CCI.Api.CommonConstants;

namespace Hubtel.CCI.Api.Dtos.Requests.ReportDeliveryRule;

public class CreateReportDeliveryRule
{
    public string ProductGroupId { get; set; } = string.Empty;
    public List<string> ProductTeamIds { get; set; } = new();
    /// <summary>
    /// "Backend", "Frontend", or null for any/overall
    /// </summary>
    public string? Scope  { get; set; }

    public string RecipientEmail { get; set; } = string.Empty;
    public string ReportType { get; set; } = ValidationConstants.ReportType.ProductGroupWeeklyReport;
}