using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Requests.CciRoadMapRecord;

public class CreateCciRoadMapRecordRequest
{
    public MetricData? Bugs { get; set; }
    public MetricData? CodeSmells { get; set; }
    public MetricData? Coverage { get; set; }
    public MetricData? DuplicatedLines { get; set; }
    public MetricData? Vulnerabilities { get; set; }
    public MetricData? SecurityHotspots { get; set; }
    public DateTime? StartDate { get; set; }
    public QualityProgress OverallQuality { get; set; } = new();
    public RepositoryInfo Repository { get; set; } = new();
}