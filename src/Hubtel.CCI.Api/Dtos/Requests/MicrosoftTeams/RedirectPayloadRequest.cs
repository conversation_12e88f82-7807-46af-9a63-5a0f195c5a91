using System.ComponentModel.DataAnnotations;
using System.Web;

namespace Hubtel.CCI.Api.Dtos.Requests.MicrosoftTeams;


public class RedirectPayloadRequest
{
    [Required(ErrorMessage = "RedirectUrl Is Required")]
    public string RedirectUrl { get; set; } = string.Empty;
    
    public string ExtractCodeFromUrl()
    {
        var query = new Uri(RedirectUrl).Query;
        var parsed = HttpUtility.ParseQueryString(query);
        return parsed["code"] ?? string.Empty;
    }
}