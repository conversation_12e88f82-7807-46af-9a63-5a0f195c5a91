using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.CciRoadMap;

/// <summary>
/// Represents the request object for creating a product team roadmap.
/// </summary>
public class GetCciRoadMapResponse: BaseResponse
{
    /// <summary>
    /// Gets or sets the ID of the repository.
    /// </summary>
    public string RepositoryId { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the repository.
    /// </summary>
    public string? RepositoryName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product team.
    /// </summary>
    public string ProductTeamId { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? ProductTeamName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the assigned engineer.
    /// </summary>
    public string EngineerAssignedId { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the assigned engineer.
    /// </summary>
    public string? EngineerName { get; set; }

    /// <summary>
    /// Gets or sets the start date of the roadmap activity.
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Gets or sets the target completion date for the roadmap activity.
    /// </summary>
    public DateTime? TargetDate { get; set; }

    /// <summary>
    /// Gets or sets the number of bugs identified.
    /// </summary>
    public decimal? Bugs { get; set; }

    /// <summary>
    /// Gets or sets the number of code smells identified.
    /// </summary>
    public decimal? CodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the code coverage percentage
    /// </summary>
    public decimal? Coverage { get; set; }

    /// <summary>
    /// Gets or sets the duplicated lines density percentage.
    /// </summary>
    public decimal? DuplicatedLines { get; set; }

    /// <summary>
    /// Gets or sets the number of vulnerabilities identified.
    /// </summary>
    public decimal? Vulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the number of security hotspots identified.
    /// </summary>
    public decimal? SecurityHotspots { get; set; }
    
    /// <summary>
    /// Gets or sets the number of target bugs
    /// </summary>
    public decimal? TargetBugs { get; set; }

    /// <summary>
    /// Gets or sets the number of target code smells
    /// </summary>
    public decimal? TargetCodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the target code coverage percentage.
    /// </summary>
    public decimal? TargetCoverage { get; set; }

    /// <summary>
    /// Gets or sets the target duplicated lines density percentage.
    /// </summary>
    public decimal? TargetDuplicatedLines { get; set; }

    /// <summary>
    /// Gets or sets the target number of vulnerabilities.
    /// </summary>
    public decimal? TargetVulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the target number of security hotspots.
    /// </summary>
    public decimal? TargetSecurityHotspots { get; set; }   
    
    
    /// <summary>
    /// Gets or sets the starting number of bugs identified.
    /// </summary>
    public decimal? StartingBugs { get; set; }

    /// <summary>
    /// Gets or sets the starting number of code smells identified.
    /// </summary>
    public decimal? StartingCodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the starting code coverage percentage
    /// </summary>
    public decimal? StartingCoverage { get; set; }

    /// <summary>
    /// Gets or sets the starting duplicated lines density percentage.
    /// </summary>
    public decimal? StartingDuplicatedLines { get; set; }

    /// <summary>
    /// Gets or sets the number of starting vulnerabilities identified.
    /// </summary>
    public decimal? StartingVulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the number of starting security hotspots identified.
    /// </summary>
    public decimal? StartingSecurityHotspots { get; set; }
}