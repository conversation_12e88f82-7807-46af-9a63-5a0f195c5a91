using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Dtos.Responses.SonarQube;

public class SonarQubeProjectsResponse
{
    [JsonProperty("paging")] public SonarQubePaging Paging { get; set; } = new();

    [JsonProperty("components")] public List<SonarQubeProject> Components { get; set; } = [];
}

public class SonarQubePaging
{
    [JsonProperty("pageIndex")]
    public int PageIndex { get; set; }

    [JsonProperty("pageSize")]
    public int PageSize { get; set; }

    [JsonProperty("total")]
    public int Total { get; set; }
}

public class SonarQubeProject
{
    [JsonProperty("key")] public string Key { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("qualifier")]
    public string? Qualifier { get; set; }

    [JsonProperty("visibility")]
    public string? Visibility { get; set; }

    [JsonProperty("lastAnalysisDate")]
    public string? LastAnalysisDate { get; set; }

    [JsonProperty("revision")]
    public string? Revision { get; set; }

    [JsonProperty("managed")]
    public bool Managed { get; set; }
}