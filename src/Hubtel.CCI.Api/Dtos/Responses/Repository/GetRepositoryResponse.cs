using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.Repository;

/// <summary>
/// Represents the response object for getting a repository's details.
/// This class inherits from the BaseResponse class.
/// </summary>
public class GetRepositoryResponse : BaseResponse
{
    /// <summary>
    /// Gets or sets the name of the repository item.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the URL of the repository item.
    /// </summary>
    public string Url { get; set; } = null!;

    /// <summary>
    /// Gets or sets the type of the repository item.
    /// </summary>
    public string Type { get; set; } = null!;

    /// <summary>
    /// Gets or sets the SonarQube key of the repository item.
    /// </summary>
    public string SonarQubeKey { get; set; } = null!;

    /// <summary>
    /// Gets or sets the description of the repository item.
    /// </summary>
    public string Description { get; set; } = null!;

    /// <summary>
    /// Gets or sets the framework upgrade computation score value for repository item.
    /// </summary>
    public decimal FrameworkUpgradeComputation { get; set; }

    /// <summary>
    /// Gets or sets the semantic score computation score value for repository item.
    /// </summary>
    public decimal SemanticScoreComputation { get; set; }

    /// <summary>
    /// Gets or sets the Status of the repository
    /// </summary>
    public string Status { get; set; } = null!;
}