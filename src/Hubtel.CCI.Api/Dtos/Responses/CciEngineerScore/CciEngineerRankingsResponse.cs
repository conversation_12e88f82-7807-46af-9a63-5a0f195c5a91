using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;

public class CciEngineerRankingsResponse: BaseResponse
{
    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication end date.
    /// </summary>
    public DateTime? PublicationEndDate { get; set; }

    /// <summary>
    /// Gets or sets the publication start date.
    /// </summary>
    public DateTime? PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the publication current cci score.
    /// </summary>
    public decimal? CurrentScore { get; set; }

    /// <summary>
    /// Gets or sets the publication rankings for overall, backend and frontend product teams.
    /// </summary>
    public EngineerRankings Rankings { get; set; } = new();
}