using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Dtos.Responses.Common;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;

namespace Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;

public class CciEngineerSonarQubeMetricsResponse: BaseResponse
{
    public string? EngineerName { get; set; }

    public string? EngineerEmail { get; set; }
    
    public string? RepositoryId { get; set; }
    
    public string? PublicationWeek { get; set; }
    
    public DateTime PublicationDate { get; set; }
    
    public PullRequest PullRequest { get; set; } = new();
    
    public AzureRepositoryDto AzureRepository { get; set; } = new();

    public SonarQubeComponent SonarQubeMetrics { get; set; } = new();
}