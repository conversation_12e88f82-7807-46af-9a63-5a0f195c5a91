namespace Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;

public class CciEngineerSqMetricsOverview
{
    public decimal UniqueEngineerCount { get; set; }
    public decimal UniqueRepositoryCount { get; set; }
    public List<EngineerSummary> Engineers { get; set; } = [];
    public DateTime? PublicationDate { get; set; }
    public string? PublicationWeek { get; set; }
}

public class EngineerSummary
{
    public string? EngineerName { get; set; }
    public string? EngineerEmail { get; set; }
    public decimal UniqueRepositoryCount { get; set; }
    public decimal UniquePullRequestCount { get; set; }
    public List<PullRequestSummary> PullRequests { get; set; } = new();
}

public class PullRequestSummary
{
    public int PullRequestId { get; set; }
    public string? PullRequestUrl { get; set; }
    public string? RepositoryName { get; set; }
    public string? RepositoryId { get; set; }
    public decimal LinesOfCode { get; set; }
    public decimal Coverage { get; set; }
}