using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;

public class GetCciEngineerSqMetricsTableScoresFilter: BaseFilter
{
    /// <summary>
    /// Gets or sets the date of the publication period.
    /// Defaults to the date of the previous publication week.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }
    
    /// <summary>
    /// Gets or sets the engineer's email
    /// </summary>
    public string? EngineerEmail { get; set; }

    
    /// <summary>
    /// Gets or sets the search term for filtering results.
    /// </summary>
    public string? Search { get; set; }
}