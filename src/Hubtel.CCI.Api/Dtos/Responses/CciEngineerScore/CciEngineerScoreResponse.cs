using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;

public class CciEngineerScoreResponse: BaseResponse
{
    /// <summary>
    /// The Publication date of the Engineer Score.
    /// </summary>
    public DateTime PublicationDate { get; set; }
    
    
    public string? PublicationWeek { get; set; }
    
    /// <summary>
    /// The total number of pull requests across all repositories.
    /// </summary>
    public decimal TotalPullRequests { get; set; }
    
    /// <summary>
    /// The CCI Score for the engineer.
    /// </summary>
    public decimal Score { get; set; }
    
    /// <summary>
    /// The total number of bugs across all pull requests.
    /// </summary>
    public decimal TotalBugs { get; set; }
    
    /// <summary>
    /// The total number of Code Smells across all pull requests.
    /// </summary>
    public decimal TotalCodeSmells { get; set; }
    
    /// <summary>
    /// The total number of Vulnerabilities across all pull requests.
    /// </summary>
    public decimal TotalVulnerabilities { get; set; }
    
    /// <summary>
    /// The total Lines Of Codes across all pull requests.
    /// </summary>
    public decimal TotalLinesOfCode { get; set; }
    
    /// <summary>
    /// The average coverage across all pull requests.
    /// </summary>
    public decimal AverageCoverage { get; set; }
    
    /// <summary>
    /// The average duplication across all pull requests.
    /// </summary>
    public decimal AverageDuplications { get; set; }
    
    /// <summary>
    /// The id of The Engineer
    /// </summary>
    public string? EngineerId { get; set; } 
    
    /// <summary>
    /// The name of the Engineer
    /// </summary>
    public string? EngineerName { get; set; }
    
    /// <summary>
    /// The email of the engineer
    /// </summary>
    public string? EngineerEmail { get; set; }
    
    /// <summary>
    /// The Metrics Of The Engineer
    /// </summary>
    public EngineerPrMetrics EngineerPrMetrics { get; set; } = new();
}