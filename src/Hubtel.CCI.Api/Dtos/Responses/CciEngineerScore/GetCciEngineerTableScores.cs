using Hubtel.CCI.Api.Dtos.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.CciEngineerScore;


/// <summary>
/// Represents a request to get CCI engineer table scores based on products.
/// </summary>
public class GetCciEngineerTableScoresFilter: BaseFilter
{
    /// <summary>
    /// Gets or sets the date of the publication.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }
    
    
    public string? ProductGroupId { get; set; }
    
    
    public string? Domain { get; set; }

    
    /// <summary>
    /// Gets or sets the search term for filtering results.
    /// </summary>
    public string? Search { get; set; }
}