using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Responses.ToolingReport
{
    public class UpdateToolResponse
    {
        public string Id { get; set; } = null!;

        /// <summary>
        /// Name of the tool
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the tool
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Version of the tool
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// URL to the tool's documentation
        /// </summary>
        public string DocumentationUrl { get; set; } = string.Empty;

        /// <summary>
        /// Domain of the tool
        /// </summary>
        public Domain Domain { get; set; }
    }
}
