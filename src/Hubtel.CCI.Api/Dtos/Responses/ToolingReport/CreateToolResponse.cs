using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Responses.ToolingReport
{
    public class CreateToolResponse
    {
        /// <summary>
        /// Gets or sets the ID of the created tool.
        /// </summary>
        public string Id { get; set; } = null!;
        /// <summary>
        /// Gets or sets the name of the created tool.
        /// </summary>
        
        public required string Name { get; set; }
        /// <summary>
        /// Description of the tool
        /// </summary>
        public string Description { get; set; } = string.Empty;
        /// <summary>
        /// Version of the tool
        /// </summary>
        public string Version { get; set; } = string.Empty;
        /// <summary>
        /// URL to the tool's documentation
        /// </summary>
        public string DocumentationUrl { get; set; } = string.Empty;
        
        public Domain Domain { get; set; }
    }
}
