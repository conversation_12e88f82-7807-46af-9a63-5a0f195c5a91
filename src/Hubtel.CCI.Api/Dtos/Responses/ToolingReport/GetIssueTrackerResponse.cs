using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Responses.ToolingReport
{
    public class GetIssueTrackerResponse
    {
        public string Id { get; set; } = null!;
        public string ReportedBy { get; set; } = null!;
        public List<string> ProductGroup { get; set; } = null!;
        public List<string> ProductTeam { get; set; } = null!;
        public List<string> ServicesAffected { get; set; } = new();
        public string Tool { get; set; } = null!;
        public string Domain { get; set; } = null!;
        public string IncidentDescription { get; set; } = null!;
        public string ActionTaken { get; set; } = null!;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public IssueStatus Status { get; set; } 
        public string ToolVersion { get; set; } = "";
        public string AssignedTo { get; set; } = null!;
        public IncidentSeverity Severity { get; set; } 

    }
}
