using Hubtel.CCI.Api.Dtos.Responses.Common;
using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Responses.ToolingReport
{
    public class CreateIssueTrackerResponse : BaseResponse
    {
        public string ReportedBy { get; set; } = null!;
        public string RecordedBy { get; set; } = null!;
        public List<string> ProductGroup { get; set; } = new();
        public List<string> ProductTeam { get; set; } = new();
        public string Tool { get; set; } = null!;
        public List<string> ServicesAffected { get; set; } = new();
        public IssueStatus Status { get; set; }
        public string IncidentDescription { get; set; } = null!;
        public string ActionTaken { get; set; } = null!;
        public Domain Domain { get; set; }
        public string? AssignedTo { get; set; }
        public IncidentSeverity Severity { get; set; }
        public string ToolVersion { get; set; } = "";
    }
}
