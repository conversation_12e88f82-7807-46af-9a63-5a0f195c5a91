using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.CciRoadMapRecord;

public class GetCciRoadMapRecordResponse: BaseResponse
{
    public MetricData? Bugs { get; set; }
    public MetricData? CodeSmells { get; set; }
    public MetricData? Coverage { get; set; }
    public MetricData? DuplicatedLines { get; set; }
    public MetricData? Vulnerabilities { get; set; }
    public MetricData? SecurityHotspots { get; set; }
    public DateTime? StartDate { get; set; }
    public QualityProgress OverallQuality { get; set; } = new();
    public ProductInfo Product { get; set; } = new();
    public RepositoryInfo Repository { get; set; } = new();
}