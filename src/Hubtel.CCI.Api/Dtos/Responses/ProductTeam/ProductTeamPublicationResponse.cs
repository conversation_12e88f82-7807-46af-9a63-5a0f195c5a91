using Hubtel.CCI.Api.Data.Entities;

namespace Hubtel.CCI.Api.Dtos.Responses.ProductTeam;

class ProductTeamPublicationDto
{
    public DateTime? PublicationDate { get; set; }
    public RankingItem? Rankings { get; set; } = new();
}

public class TrendItem
{
    public DateTime PublicationDate { get; set; }
    public RankingItem? Rankings { get; set; }
}

public class GrowthItem
{
    public DateTime PublicationDate { get; set; }
    public decimal NonCommentedLinesOfCode { get; set; }
}

public class ProductSectionStats
{
    public decimal Bugs { get; set; }
    public decimal CodeSmells { get; set; }
    public decimal Vulnerabilities { get; set; }
    public decimal CodeCoverage { get; set; }
    public decimal Duplication { get; set; }
    public decimal TotalLinesOfCode { get; set; }
    public decimal RepositoryCount { get; set; }
    public decimal CciAverage { get; set; }
    public decimal? Rank { get; set; }
    public string? Status { get; set; }
    
    public decimal TotalProducts { get; set; }
    
    public List<RankingItem>? RankingList { get; set; }
    public List<TrendItem> PastEightDaysRankItem { get; set; } = new();
    public List<TrendItem> PastEightWeeksRankItem { get; set; } = new();
    public List<TrendItem> PastEightMonthsRankItem { get; set; } = new();
    public List<GrowthItem> PastEightDaysCodeGrowth { get; set; } = new();
    public List<GrowthItem> PastEightWeeksCodeGrowth { get; set; } = new();
    public List<GrowthItem> PastEightMonthsCodeGrowth { get; set; } = new();
}

public class ProductTeamPublicationResponse
{
    public string ProductTeamId { get; set; } = string.Empty;
    public string ProductTeamName { get; set; } = string.Empty;
    public string? ProductGroupId { get; set; }
    public string? ProductGroupName { get; set; }
    public ProductSectionStats Backend { get; set; } = new();
    public ProductSectionStats Frontend { get; set; } = new();
}