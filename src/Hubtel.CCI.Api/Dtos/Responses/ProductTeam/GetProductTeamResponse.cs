using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.ProductTeam;

/// <summary>
/// Represents the response object for getting a product team's details.
/// This class inherits from the BaseResponse class.
/// </summary>
public class GetProductTeamResponse : BaseResponse
{
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the members of the product team.
    /// </summary>
    public List<Member> Members { get; set; } = new();

    /// <summary>
    /// Gets or sets the repositories associated with the product team.
    /// </summary>
    public List<RepositoryItem> Repositories { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the Status of the product team
    /// </summary>
    public string Status { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the Tag of the product team
    /// </summary>
    public string Tag { get; set; } = null!;
    
    
    /// <summary>
    /// Gets or sets the Ranking of the product team
    /// </summary>
    public int Ranking { get; set; }
    
    
}