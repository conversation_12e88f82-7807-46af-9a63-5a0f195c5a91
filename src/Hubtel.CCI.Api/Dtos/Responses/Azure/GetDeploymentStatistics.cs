namespace Hubtel.CCI.Api.Dtos.Responses.Azure;

public class GetDeploymentStatistics
{
    public decimal TotalDeployments { get; set; }
    
    public decimal TotalSuccessful { get; set; }
    
    public decimal TotalFailed { get; set; }
    
    public decimal TotalRollbacks { get; set; }
    
    public decimal TotalAzureFailed { get; set; }

    public List<DeploymentGroupedStats> TopDefinitionsByDeploymentCount { get; set; } = [];
    
    public List<DeploymentGroupedStats> TopDefinitionsBySuccessCount { get; set; } = [];
    
    public List<DeploymentGroupedStats> TopDefinitionsByFailedCount { get; set; } = [];
    
    public List<DeploymentGroupedStats> TopDefinitionsByRollbackCount { get; set; } = [];
}



public class DeploymentGroupedStats
{
    public string? DefinitionId { get; set; }
    
    public string? DefinitionName { get; set; }
    
    public string? ProjectName { get; set; }
    
    public decimal DeploymentCount { get; set; }
    
    public decimal RollbackCount { get; set; }
    
    public decimal SuccessfulCount { get; set; }
    
    public decimal FailedCount { get; set; }
    
    public decimal FailedAzureCount { get; set; }
}