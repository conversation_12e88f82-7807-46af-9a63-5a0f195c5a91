using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.Azure;

public class GetAzureReleaseDeploymentRecords: BaseResponse
{
    public DateTime PublicationDate { get; set; }

    public string PublicationWeek { get; set; } = "";
    
    public string DefinitionId { get; set; } = "";
    
    public string? DefinitionName { get; set; } = "";
    
    public string DefinitionProjectName { get; set; } = "";
    
    public string CompositeDefinitionId { get; set; } = "";
    
    public decimal TotalDeployments { get; set; }
    
    public decimal TotalDeploymentsSuccess { get; set; }
    
    public decimal TotalDeploymentsFailed { get; set; }
    
    public decimal TotalDeploymentsInRolledBack { get; set; }
}