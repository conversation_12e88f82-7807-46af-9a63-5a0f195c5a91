using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Dtos.Responses.Azure;

public class AzureReleaseDefinitionResponse
{
    public int Count { get; set; }
    public List<ReleaseDefinition> Value { get; set; } = [];
}

public class ReleaseDefinition
{
    public string? Source { get; set; } 
    public int Revision { get; set; }
    public string? Description { get; set; }
    public Identity CreatedBy { get; set; }  = new();
    public DateTime CreatedOn { get; set; }
    public Identity ModifiedBy { get; set; } = new();
    public DateTime ModifiedOn { get; set; }
    public bool IsDeleted { get; set; }
    public bool IsDisabled { get; set; }
    public string? ReleaseNameFormat { get; set; }
    public string? Comment { get; set; }
    public int Id { get; set; }
    public string? Name { get; set; }
    public string? Path { get; set; }
    public string? Url { get; set; }
    
    [JsonProperty("_links")]
    public Links Links { get; set; } = new();
}

public class Identity
{
    public string? DisplayName { get; set; }
    public string? Url { get; set; }
    public AvatarLinks Links { get; set; } = new();
    public string? Id { get; set; }
    public string? UniqueName { get; set; }
    public string? ImageUrl { get; set; }
    public string? Descriptor { get; set; }
}

public class AvatarLinks
{
    public Avatar Avatar { get; set; } = new();
}


public class ReleaseLinks
{
    public LinkRef Self { get; set; } = new();
    public LinkRef Web { get; set; } = new();
}

public class LinkRef
{
    public string? Href { get; set; }
}