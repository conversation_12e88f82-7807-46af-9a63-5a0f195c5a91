using Hubtel.CCI.Api.Dtos.Responses.Common;

namespace Hubtel.CCI.Api.Dtos.Responses.Azure;

public class GetAzureReleaseDefinitionResponse: BaseResponse
{
    public string? Source { get; set; } 
    public int Revision { get; set; }
    public string? Description { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
    public bool IsDeletedAzure { get; set; }
    public bool IsDisabled { get; set; }
    public string? ReleaseNameFormat { get; set; }
    public string? Comment { get; set; }
    public int DefinitionId { get; set; }
    public string? Name { get; set; }
    public string? Path { get; set; }
    public string? Url { get; set; }
        
    public string? CompositeDefinitionId { get; set; }
        
    public string? ProjectName { get; set; }
}