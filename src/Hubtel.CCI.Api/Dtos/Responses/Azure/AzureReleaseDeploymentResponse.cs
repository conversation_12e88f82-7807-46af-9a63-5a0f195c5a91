using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Dtos.Responses.Azure;

public class AzureReleaseDeploymentResponse
{
    public int Count { get; set; }
    public List<ReleaseDeployment> Value { get; set; } = new();
}


public class ReleaseDeployment
{
    public int Id { get; set; }
    public AzureRelease Release { get; set; } = new();
    public ReleaseDefinitionDeployment ReleaseDefinition { get; set; } = new();
    public ReleaseEnvironment ReleaseEnvironment { get; set; } = new();
    public ProjectReference? ProjectReference { get; set; }
    public int DefinitionEnvironmentId { get; set; }
    public int Attempt { get; set; }
    public string Reason { get; set; } = "";
    public string DeploymentStatus { get; set; } = "";
    public string OperationStatus { get; set; } = "";
    public Identity RequestedBy { get; set; } = new();
    public Identity RequestedFor { get; set; } = new();
    public DateTime QueuedOn { get; set; }
    public DateTime StartedOn { get; set; }
    public DateTime CompletedOn { get; set; }
    public DateTime LastModifiedOn { get; set; }
    public Identity LastModifiedBy { get; set; } = new();
    public List<DeploymentCondition> Conditions { get; set; } = new();
    public List<Approval> PreDeployApprovals { get; set; } = new();
    public List<Approval> PostDeployApprovals { get; set; } = new();
}


public class AzureRelease
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string Url { get; set; } = "";
    public List<Artifact> Artifacts { get; set; } = new();
    public string WebAccessUri { get; set; } = "";
    
    [JsonProperty("_links")]
    public LinkSet Links { get; set; } = new();
}

public class Artifact
{
    public string SourceId { get; set; } = "";
    public string Type { get; set; } = "";
    public string Alias { get; set; } = "";
    public DefinitionReference DefinitionReference { get; set; } = new();
    public bool IsPrimary { get; set; }
    public bool IsRetained { get; set; }
}

public class DefinitionReference
{
    public ArtifactDefinitionRef ArtifactSourceDefinitionUrl { get; set; } = new();
    public ArtifactDefinitionRef Branches { get; set; } = new();
    public ArtifactDefinitionRef BuildUri { get; set; } = new();
    public ArtifactDefinitionRef Definition { get; set; } = new();
    public ArtifactDefinitionRef IsMultiDefinitionType { get; set; } = new();
    public ArtifactDefinitionRef IsXamlBuildArtifactType { get; set; } = new();
    public ArtifactDefinitionRef Project { get; set; } = new();

    [JsonProperty("repository.provider")]
    public ArtifactDefinitionRef RepositoryProvider { get; set; } = new();

    public ArtifactDefinitionRef Repository { get; set; } = new();
    public ArtifactDefinitionRef RequestedFor { get; set; } = new();
    public ArtifactDefinitionRef RequestedForId { get; set; } = new();
    public ArtifactDefinitionRef SourceVersion { get; set; } = new();
    public ArtifactDefinitionRef Version { get; set; } = new();
    public ArtifactDefinitionRef ArtifactSourceVersionUrl { get; set; } = new();
    public ArtifactDefinitionRef Branch { get; set; } = new();
}

public class ArtifactDefinitionRef
{
    public string Id { get; set; } = "";
    public string? Name { get; set; }
}


public class ReleaseDefinitionDeployment
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string Path { get; set; } = "";
    public ProjectReference? ProjectReference { get; set; }
    public string Url { get; set; } = "";
    
    [JsonProperty("_links")]
    public LinkSet Links { get; set; } = new();
}

public class ProjectReference
{
    public string Id { get; set; } = "";
    public string? Name { get; set; }
}

public class ReleaseEnvironment
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string Url { get; set; } = "";
    
    [JsonProperty("_links")]
    public LinkSet Links { get; set; } = new();
}

public class Approval
{
    public int Id { get; set; }
    public int Revision { get; set; }
    public string ApprovalType { get; set; } = "";
    public DateTime CreatedOn { get; set; }
    public DateTime ModifiedOn { get; set; }
    public string Status { get; set; } = "";
    public string Comments { get; set; } = "";
    public bool IsAutomated { get; set; }
    public bool IsNotificationOn { get; set; }
    public int TrialNumber { get; set; }
    public int Attempt { get; set; }
    public int Rank { get; set; }
    public AzureRelease Release { get; set; } = new();
    public ReleaseDefinition ReleaseDefinition { get; set; } = new();
    public ReleaseEnvironment ReleaseEnvironment { get; set; } = new();
    public string Url { get; set; } = "";
}

public class DeploymentCondition
{
    public string Name { get; set; } = "";
    public string ConditionType { get; set; } = "";
    public string Value { get; set; } = "";
    public bool Result { get; set; }
}


public class LinkSet
{
    public LinkRef? Self { get; set; }
    public LinkRef? Web { get; set; }
    public LinkRef? Avatar { get; set; }
}