namespace Hubtel.CCI.Api.Dtos.Responses.Azure;

public class RepositoryListResponse
{
    public List<AzureRepository> Value { get; set; } = new();
}

public class AzureRepository
{
    public string Id { get; set; } = string.Empty;
    public string? Name { get; set; }
    
    public string? WebUrl { get; set; }
}

public class AzureRepositoryDto
{
    public Guid Id { get; set; }
    public string AzureRepoId { get; set; } = string.Empty;
    public string? Name { get; set; }
    public string? ProjectName { get; set; }
    public string? Organization { get; set; }
    
    public string? WebUrl { get; set; }
}