using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

/// <summary>
/// Represents a repository model.
/// </summary>
public class MsTeamsBotToken : BaseModel
{
    /// <summary>
    /// Gets or sets the AccessToken of the Bot User Token Object.
    /// </summary>
    public string AccessToken { get; set; } = null!;

    /// <summary>
    /// Gets or sets the ExpiresOn of the Bot User Token Object.
    /// </summary>
    public DateTimeOffset ExpiresOn { get; set; }

    /// <summary>
    /// Gets or sets the Scopes of the Bot User Token Object.
    /// </summary>
    public List<string> Scopes { get; set; } = [];

    /// <summary>
    /// Gets or sets the UserName of the Bot User Token Object.
    /// </summary>
    public string UserName { get; set; } = null!;

    /// <summary>
    /// Gets or sets the HomeAccountId of the Bot User Token Object.
    /// </summary>
    public string HomeAccountId { get; set; } = null!;
    
    /// <summary>
    /// Gets or sets the Identifier of the Bot User Token Object.
    /// </summary>
    public string Identifier { get; set; } = null!;
    
    
    /// <summary>
    /// Gets or sets the Identifier of the Bot User Token Object.
    /// </summary>
    public string SerializedTokenCache { get; set; } = null!;
}