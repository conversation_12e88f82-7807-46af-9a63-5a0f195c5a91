using Hubtel.CCI.Api.Data.Entities.Common;
using Hubtel.CCI.Api.Dtos.Responses.Azure;

namespace Hubtel.CCI.Api.Data.Entities;

public class AzureReleaseDefinition: BaseModel
{

        public string? Source { get; set; } 
        public int Revision { get; set; }
        public string? Description { get; set; }
        public Identity CreatedBy { get; set; }  = new();
        public DateTime CreatedOn { get; set; }
        public Identity ModifiedBy { get; set; } = new();
        public DateTime ModifiedOn { get; set; }
        public bool IsDeletedAzure { get; set; }
        public bool IsDisabled { get; set; }
        public string? ReleaseNameFormat { get; set; }
        public string? Comment { get; set; }
        public int DefinitionId { get; set; }
        public string? Name { get; set; }
        public string? Path { get; set; }
        public string? Url { get; set; }
        
        public string? CompositeDefinitionId { get; set; }
        
        public string? ProjectName { get; set; }

        public Links Links { get; set; } = new();
}