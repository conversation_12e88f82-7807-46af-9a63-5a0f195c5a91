using Hubtel.CCI.Api.Data.Entities.Common;
using System.Text.Json.Serialization;

namespace Hubtel.CCI.Api.Data.Entities
{
    public class IssueTracker : BaseModel
    {
        public string ReportedBy { get; set; } = null!;
        public string RecordedBy { get; set; } = null!;
        public List<string> ProductGroup { get; set; } = new();
        public List<string> ProductTeam { get; set; } = new();
        public ToolingTool Tool { get; set; } = null!;
        public List<string> ServicesAffected { get; set; } = new();
        public IssueStatus Status { get; set; } 
        public string IncidentDescription { get; set; } = null!;
        public string ActionTaken { get; set; } = null!;
        public Domain Domain { get; set; }
        public string? AssignedTo { get; set; }
        public IncidentSeverity Severity { get; set; }
        public string ToolVersion { get; set; } = "";
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum IssueStatus
    {
        Open,
        InProgress,
        Resolved,
        Closed,
        Abandoned
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum Domain
    {
        Frontend,
        Backend
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum IncidentSeverity
    {
        Critical = 1,
        High = 2,
        Medium = 3,
        Low = 4,
        Informational = 5,
        Abandoned = 6
    }
}
