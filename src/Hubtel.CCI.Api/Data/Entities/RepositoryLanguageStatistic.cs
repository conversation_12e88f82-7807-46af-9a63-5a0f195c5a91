using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class RepositoryLanguageStatistic: BaseModel
{
    public DateTime PublicationDate { get; set; }
    
    public string? PublicationWeek { get; set; }
    
    public decimal TotalLinesOfCode { get; set; }
    
    public decimal NewLinesOfCode { get; set; }
    
    public decimal NumberOfProjects { get; set; }
    
    public decimal UniqueLanguages { get; set; }
    
    public List<PublicationLanguageDistribution> LanguageDistributions { get; set; } = [];
    
    public List<PublicationLanguageDistribution> NewLanguageDistributions { get; set; } = [];


    public PublicationLanguageRanking LanguageRanking { get; set; } = new();
    
    public PublicationLanguageRanking NewLanguageRanking { get; set; } = new();

}


public class PublicationLanguageDistribution
{
    public string Language { get; set; } = string.Empty;
    public decimal TotalLoc { get; set; }
    public decimal Percentage { get; set; }
}

public class PublicationLanguageRanking
{
    /// <summary>
    /// Gets or sets the list of backend language distribution items.
    /// </summary>
    public List<PublicationLanguageDistribution> Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of frontend language distribution items.
    /// </summary>
    public List<PublicationLanguageDistribution> Frontend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of overall ranking items
    /// </summary>
    public List<PublicationLanguageDistribution> Overall { get; set; } = new();
}