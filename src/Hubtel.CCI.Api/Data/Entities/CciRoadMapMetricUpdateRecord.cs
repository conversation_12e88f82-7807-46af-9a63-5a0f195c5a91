using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class CciRoadMapMetricUpdateRecord: BaseModel
{
    
    /// <summary>
    /// Gets or sets the run date for this record.
    /// </summary>
    public DateTime RunDate { get; set; }
    
    /// <summary>
    /// Gets or sets the run start time for this record.
    /// </summary>
    public DateTime RunStartTime { get; set; }
    
    /// <summary>
    /// Gets or sets the run end time for this record.
    /// </summary>
    public DateTime RunEndTime { get; set; }

    /// <summary>
    /// Gets or sets the cci road maps processed in this update record.
    /// </summary>
    public CciRoadMaps CciRoadMaps { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the cci road map records processed in this update record.
    /// </summary>
    public CciRoadMapRecords CciRoadMapRecords { get; set; } = new();
}


/// <summary>
/// Represents a collection of cci road maps.
/// </summary>
public class CciRoadMaps
{
    /// <summary>
    /// Gets or sets the list of cci road map items.
    /// </summary>
    public List<CciRoadMap> Items { get; set; } = [];
}


/// <summary>
/// Represents a collection of cci road map records.
/// </summary>
public class CciRoadMapRecords
{
    /// <summary>
    /// Gets or sets the list of cci road map records.
    /// </summary>
    public List<CciRoadMapRecord> Items { get; set; } = [];
}