using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class RepositoryLanguageDistribution: BaseModel
{
    /// <summary>
    /// ProjectKey is the SonarQube key for the project.
    /// </summary>
    public string? ProjectKey { get; set; }
    
    /// <summary>
    /// ProjectName is the name of the project in SonarQube.
    /// </summary>
    public string? ProjectName { get; set; }
    
    /// <summary>
    /// NclocLanguageDistribution is a string representation of the language distribution in the project.
    /// </summary>
    public string? NclocLanguageDistribution { get; set; } // e.g. "cs=8000;js=1500;html=734"
    
    /// <summary>
    /// TotalNcloc is the total number of lines of code in the project.
    /// </summary>
    public decimal TotalNcloc { get; set; }
    
    /// <summary>
    /// TotalNcloc is the total number of new lines of code in the project.
    /// </summary>
    public decimal NewNcloc { get; set; }

    public List<LanguageDistribution> LanguageDistributions { get; set; } = [];
    
    public List<LanguageDistribution> NewLanguageDistributionsDelta { get; set; } = [];
    
    /// <summary>
    /// CaptureDate is the date when the language distribution was captured.
    /// </summary>
    public DateTime CaptureDate { get; set; }
    
    /// <summary>
    /// PublicationDate is the date when the language distribution was published.
    /// </summary>
    public DateTime? PublicationDate { get; set; }
    
    /// <summary>
    /// PublicationWeek is the week of the year when the language distribution was published.
    /// </summary>
    public string? PublicationWeek { get; set; }
    
    
    /// <summary>
    /// RepositoryId is the unique identifier for the repository in the context of the application.
    /// </summary>
    public string? RepositoryId { get; set; }
    
    
    /// <summary>
    /// RepositoryType is the type of the repository (e.g., Frontend, Backend.).
    /// </summary>
    public string? RepositoryType { get; set; }
    
    /// <summary>
    /// IsTracked indicates whether the repository is being tracked in the CCI Portal.
    /// </summary>
    public bool IsTracked { get; set; } = false;
    
    /// <summary>
    /// ProductTeamName is the name of the product team associated with the repository.
    /// </summary>
    public string? ProductTeamName { get; set; }
    
    /// <summary>
    /// ProductTeamId is the unique identifier for the product team associated with the repository.
    /// </summary>
    public string? ProductTeamId { get; set; }
    
    /// <summary>
    /// ProductGroupName is the name of the product group associated with the repository.
    /// </summary>
    public string? ProductGroupName { get; set; }
    
    /// <summary>
    /// ProductGroupId is the unique identifier for the product group associated with the repository.
    /// </summary>
    public string? ProductGroupId { get; set; }
}


public class LanguageDistribution
{
    public string Language { get; set; } = string.Empty;
    public decimal Ncloc { get; set; }
    public decimal Percentage { get; set; }
}