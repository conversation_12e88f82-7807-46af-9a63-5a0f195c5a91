using Hubtel.CCI.Api.Data.Entities.Common;
using Hubtel.CCI.Api.Dtos.Responses.Azure;

namespace Hubtel.CCI.Api.Data.Entities;

public class AzureReleaseDefinitionDeployment : BaseModel
{
    public DateTime PublicationDate { get; set; }

    public string PublicationWeek { get; set; } = "";
    
    public string DefinitionId { get; set; } = "";
    
    public string? DefinitionName { get; set; } = "";
    
    public string DefinitionProjectName { get; set; } = "";
    
    public string CompositeDefinitionId { get; set; } = "";
    
    public decimal TotalDeployments { get; set; }
    
    public decimal TotalDeploymentsSuccess { get; set; }
    
    public decimal TotalDeploymentsFailed { get; set; }
    
    
    public decimal TotalDeploymentsFailedAzure { get; set; }

    
    public decimal TotalDeploymentsInRolledBack { get; set; }
    
    public List<AzureReleaseDeployment> ReleaseDeployments { get; set; } = [];
    public List<AzureReleaseDeployment> SuccessfulDeployments { get; set; } = [];

    public List<AzureReleaseDeployment> RolledBackDeployments { get; set; } = [];
    
    public List<AzureReleaseDeployment> AzureFailedDeployments { get; set; } = [];


}
public class AzureReleaseDeployment
{
    public int ReleaseId { get; set; }
    public AzureRelease Release { get; set; } = new();
    public ReleaseDefinitionDeployment ReleaseDefinition { get; set; } = new();
    public ReleaseEnvironment ReleaseEnvironment { get; set; } = new();
    public ProjectReference? ProjectReference { get; set; }
    public int DefinitionEnvironmentId { get; set; }
    public int Attempt { get; set; }
    public string Reason { get; set; } = "";
    public string DeploymentStatus { get; set; } = "";
    public string OperationStatus { get; set; } = "";
    public Identity RequestedBy { get; set; } = new();
    public Identity RequestedFor { get; set; } = new();
    public DateTime QueuedOn { get; set; }
    public DateTime StartedOn { get; set; }
    public DateTime CompletedOn { get; set; }
    public DateTime LastModifiedOn { get; set; }
    public Identity LastModifiedBy { get; set; } = new();
    public List<DeploymentCondition> Conditions { get; set; } = new();
    public List<Approval> PreDeployApprovals { get; set; } = new();
    public List<Approval> PostDeployApprovals { get; set; } = new();

    public bool IsRollBack { get; set; } = false;
    
    public string? SourceVersion { get; set; }
}
