using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities
{
    public class ToolingTool : BaseModel
    {
        public string Name { get; set; } = null!;
        public Domain Domain { get; set; } 
        public string Description { get; set; } = string.Empty;
        public string ToolType { get; set; } = string.Empty;
        public ICollection<IssueTracker> Issues { get; set; } = [];
        public string DocumentationUrl { get; set; } = string.Empty;
        public string LatestVersion { get; set; } = string.Empty;

    }
}
