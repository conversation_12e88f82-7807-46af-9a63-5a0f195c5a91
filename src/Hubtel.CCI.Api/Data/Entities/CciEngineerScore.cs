using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class CciEngineerScore: BaseModel
{
    /// <summary>
    /// The Publication date of the Engineer Score.
    /// </summary>
    public DateTime PublicationDate { get; set; }
    
    
    public string? PublicationWeek { get; set; }
    
    /// <summary>
    /// The total number of pull requests across all repositories.
    /// </summary>
    public decimal TotalPullRequests { get; set; }
    
    /// <summary>
    /// The CCI Score for the engineer.
    /// </summary>
    public decimal Score { get; set; }
    
    /// <summary>
    /// The total number of bugs across all pull requests.
    /// </summary>
    public decimal TotalBugs { get; set; }
    
    /// <summary>
    /// The total number of Code Smells across all pull requests.
    /// </summary>
    public decimal TotalCodeSmells { get; set; }
    
    /// <summary>
    /// The total number of Vulnerabilities across all pull requests.
    /// </summary>
    public decimal TotalVulnerabilities { get; set; }
    
    /// <summary>
    /// The total number of Security Hotspots across all pull requests.
    /// </summary>
    public decimal TotalSecurityHotspots { get; set; }

    
    /// <summary>
    /// The total Lines Of Codes across all pull requests.
    /// </summary>
    public decimal TotalLinesOfCode { get; set; }
    
    /// <summary>
    /// The average coverage across all pull requests.
    /// </summary>
    public decimal AverageCoverage { get; set; }
    
    /// <summary>
    /// The average duplication across all pull requests.
    /// </summary>
    public decimal AverageDuplications { get; set; }
    
    
    public decimal AverageComplexity { get; set; }
    
    /// <summary>
    /// The id of The Engineer
    /// </summary>
    public string? EngineerId { get; set; } 
    
    /// <summary>
    /// The name of the Engineer
    /// </summary>
    public string? EngineerName { get; set; }
    
    /// <summary>
    /// The email of the engineer
    /// </summary>
    public string? EngineerEmail { get; set; }
    
    /// <summary>
    /// The type of the engineer
    /// </summary>
    public string? EngineerDomain { get; set; }
    
    /// <summary>
    /// The Metrics Of The Engineer
    /// </summary>
    public EngineerPrMetrics EngineerPrMetrics { get; set; } = new();
}

public class EngineerPrMetrics
{
    /// <summary>
    /// A list of SqMetrics where the SonarQube Key represents a repository and the value is a list of EngineerSonarQubeMetrics.
    /// </summary>
    public List<EngineerSqMetrics> EngineerSonarQubeMetrics { get; set; } = new();
    
    /// <summary>
    /// A dictionary where the key is the SonarQube Key representing a repository and the value is the CCI Score
    /// </summary>
    public List<EngineerRepoCciScores> CciScore { get; set; } = new();
}

public class EngineerSqMetrics
{
    /// <summary>
    /// The SonarQube Key representing a repository.
    /// </summary>
    public string SonarQubeKey { get; set; } = null!;
    
    /// <summary>
    /// A list of EngineerSonarQubeMetrics for the given SonarQube Key, representing individual PRs.
    /// </summary>
    public List<EngineerSonarQubeMetrics> EngineerSonarQubeMetrics { get; set; } = new();
}

public class EngineerRepoCciScores
{
    /// <summary>
    /// The SonarQube Key representing a repository.
    /// </summary>
    public string SonarQubeKey { get; set; } = null!;
    
    /// <summary>
    /// The CCI Score for the given repository.
    /// </summary>
    public decimal Score { get; set; }
}

