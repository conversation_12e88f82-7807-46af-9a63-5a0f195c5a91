using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities
{
    public class IssueTrackerAuditLog : BaseModel
    {
        public int ChangelogId { get; set; }
        public int IssueId { get; set; }
        public IssueTracker Issue { get; set; } = null!;
        public string ChangeType { get; set; } = string.Empty;
        public DateTime ChangedAt { get; set; }
        public string FieldName { get; set; } = string.Empty;
        public string OldValue { get; set; } = string.Empty;
        public string NewValue { get; set; } = string.Empty;
        public string FullSnapshotJson { get; set; } = string.Empty;
    }
}