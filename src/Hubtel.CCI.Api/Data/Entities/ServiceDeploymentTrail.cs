using System.ComponentModel.DataAnnotations.Schema;
using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Data.Entities.Common;
// ReSharper disable All

namespace Hubtel.CCI.Api.Data.Entities;

public class ServiceDeploymentTrail:BaseModel
{
    public string? DeploymentRequestId { get; set; }
    public string? RepositoryServiceId { get; set; }
    public string? Status { get; set; }= ServiceDeploymentTrailStatus.DefaultSuccessful;
    public string? Description { get; set; }
    
    [ForeignKey(nameof(DeploymentRequestId))]
    public DeploymentRequest? DeploymentRequest { get; set; }
    
    [ForeignKey(nameof(RepositoryServiceId))]
    public Service? RepositoryService { get; set; }
}