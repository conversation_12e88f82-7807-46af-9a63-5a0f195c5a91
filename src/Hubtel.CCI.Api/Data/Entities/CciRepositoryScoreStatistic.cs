using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

/// <summary>
/// Represents the CCI repository score statistic.
/// </summary>
public class CciRepositoryScoreStatistic : BaseModel
{
    /// <summary>
    /// Gets or sets the publication week in ISO format (YYYY-WW).
    /// </summary>
    public string? PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the score statistic.
    /// </summary>
    public ScoreStatistic ScoreStatistic { get; set; } = new();
}

/// <summary>
/// Represents an overview of the score statistics.
/// </summary>
public class Overview
{
    /// <summary>
    /// Gets or sets the current score.
    /// </summary>
    public decimal CurrentScore { get; set; }

    /// <summary>
    /// Gets or sets the total number of bugs.
    /// </summary>
    public decimal TotalBugs { get; set; }

    /// <summary>
    /// Gets or sets the total number of code smells.
    /// </summary>
    public decimal TotalCodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the total number of vulnerabilities.
    /// </summary>
    public decimal TotalVulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the total number of security hotspots.
    /// </summary>
    public decimal TotalSecurityHotspots { get; set; }

    /// <summary>
    /// Gets or sets the total number of duplications.
    /// </summary>
    public decimal AverageDuplications { get; set; }

    /// <summary>
    /// Gets or sets the average code coverage.
    /// </summary>
    public decimal AverageCodeCoverage { get; set; }

    /// <summary>
    /// Gets or sets the total cognitive complexity.
    /// </summary>
    public decimal TotalCognitiveComplexity { get; set; }
    
    
    /// <summary>
    /// Gets or sets the total relative cognitive complexity.
    /// </summary>
    public decimal TotalRelativeCognitiveComplexity { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of  non-space/non-commented lines of code.
    /// </summary>
    public decimal TotalNonCommentedLinesOfCode { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of lines of code.
    /// </summary>
    public decimal TotalLinesOfCode { get; set; }
}

/// <summary>
/// Represents a highlight with a name and score.
/// </summary>
public class Highlight
{
    /// <summary>
    /// Gets or sets the name of the highlight.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the score of the highlight.
    /// </summary>
    public decimal Score { get; set; }
}

/// <summary>
/// Represents the highlights with the highest and lowest scores.
/// </summary>
public class Highlights
{
    /// <summary>
    /// Gets or sets the highlight with the highest score.
    /// </summary>
    public Highlight Highest { get; set; } = new();

    /// <summary>
    /// Gets or sets the highlight with the lowest score.
    /// </summary>
    public Highlight Lowest { get; set; } = new();
}

/// <summary>
/// Represents a metric with a name and score.
/// </summary>
public class ProductTeamMetricItem
{
    /// <summary>
    /// Gets or sets the Id of the product team metric.
    /// </summary>
    public string Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the product team metric.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the score of the metric.
    /// </summary>
    public decimal Score { get; set; }
}

/// <summary>
/// Represents the performance by various metrics.
/// </summary>
public class PerformanceByMetrics
{
    /// <summary>
    /// Gets or sets the list of bug metrics.
    /// </summary>
    public List<ProductTeamMetricItem> Bugs { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of code smell metrics.
    /// </summary>
    public List<ProductTeamMetricItem> CodeSmells { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of vulnerability metrics.
    /// </summary>
    public List<ProductTeamMetricItem> Vulnerabilities { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of security hotspot metrics.
    /// </summary>
    public List<ProductTeamMetricItem> SecurityHotspots { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of duplication metrics.
    /// </summary>
    public List<ProductTeamMetricItem> Duplications { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of code coverage metrics.
    /// </summary>
    public List<ProductTeamMetricItem> CodeCoverage { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of cognitive complexity metrics.
    /// </summary>
    public List<ProductTeamMetricItem> CognitiveComplexity { get; set; } = new();
}

/// <summary>
/// Represents an item in the score ranking.
/// </summary>
public class ScoreRankingItem
{
    /// <summary>
    /// Gets or sets the Id of the product team metric.
    /// </summary>
    public string Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the item.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the score of the item.
    /// </summary>
    public decimal Score { get; set; }

    /// <summary>
    /// Gets or sets the number of bugs.
    /// </summary>
    public decimal Bugs { get; set; }

    /// <summary>
    /// Gets or sets the number of code smells.
    /// </summary>
    public decimal CodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the number of vulnerabilities.
    /// </summary>
    public decimal Vulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the number of security hotspots.
    /// </summary>
    public decimal SecurityHotspots { get; set; }

    /// <summary>
    /// Gets or sets the average number of duplications.
    /// </summary>
    public decimal AverageDuplications { get; set; }

    /// <summary>
    /// Gets or sets the average code coverage.
    /// </summary>
    public decimal AverageCodeCoverage { get; set; }

    /// <summary>
    /// Gets or sets the cognitive complexity.
    /// </summary>
    public decimal CognitiveComplexity { get; set; }
    
    
    /// <summary>
    /// Gets or sets the relative cognitive complexity.
    /// </summary>
    public decimal RelativeCognitiveComplexity { get; set; }
    
    
    /// <summary>
    /// Gets or sets the number of  non-space/non-commented lines of code.
    /// </summary>
    public decimal NonCommentedLinesOfCode { get; set; }
    
    /// <summary>
    /// Gets or sets the number of lines of code.
    /// </summary>
    public decimal LinesOfCode { get; set; }
}

/// <summary>
/// Represents the score ranking.
/// </summary>
public class ScoreRanking
{
    /// <summary>
    /// Gets or sets the list of backend score ranking items.
    /// </summary>
    public List<ScoreRankingItem> Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of frontend score ranking items.
    /// </summary>
    public List<ScoreRankingItem> Frontend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of overall score ranking items.
    /// </summary>
    public List<ScoreRankingItem> Overall { get; set; } = new();
}

/// <summary>
/// Represents an overview by scope item.
/// </summary>
public class OverviewByScopeItem
{
    /// <summary>
    /// Gets or sets the number of repositories.
    /// </summary>
    public int Repositories { get; set; }

    /// <summary>
    /// Gets or sets the score.
    /// </summary>
    public decimal Score { get; set; }

    /// <summary>
    /// Gets or sets the total number of bugs.
    /// </summary>
    public decimal TotalBugs { get; set; }

    /// <summary>
    /// Gets or sets the total number of code smells.
    /// </summary>
    public decimal TotalCodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the total number of vulnerabilities.
    /// </summary>
    public decimal TotalVulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the total number of security hotspots.
    /// </summary>
    public decimal TotalSecurityHotspots { get; set; }

    /// <summary>
    /// Gets or sets the average number of duplications.
    /// </summary>
    public decimal AverageDuplications { get; set; }

    /// <summary>
    /// Gets or sets the average code coverage.
    /// </summary>
    public decimal AverageCodeCoverage { get; set; }

    /// <summary>
    /// Gets or sets the total cognitive complexity.
    /// </summary>
    public decimal TotalCognitiveComplexity { get; set; }
    
    /// <summary>
    /// Gets or sets the total relative cognitive complexity.
    /// </summary>
    public decimal TotalRelativeCognitiveComplexity { get; set; }
    
    
    /// <summary>
    /// Gets or sets the total number of  non-space/non-commented lines of code.
    /// </summary>
    public decimal TotalNonCommentedLinesOfCode { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of lines of code.
    /// </summary>
    public decimal TotalLinesOfCode { get; set; }
}

/// <summary>
/// Represents the overview by scope.
/// </summary>
public class OverviewByScope
{
    /// <summary>
    /// Gets or sets the backend overview by scope item.
    /// </summary>
    public OverviewByScopeItem Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the frontend overview by scope item.
    /// </summary>
    public OverviewByScopeItem Frontend { get; set; } = new();

    /// <summary>
    /// Gets or sets the overall overview by scope item.
    /// </summary>
    public OverviewByScopeItem Overall { get; set; } = new();
}


/// <summary>
/// Represents the weekly final average trend.
/// </summary>
public class WeeklyFinalAverageTrend
{
    /// <summary>
    /// Gets or sets the frontend trend.
    /// </summary>
    public List<WeeklyFinalAverageTrendItem> Frontend { get; set; } = [];
    
    /// <summary>
    /// Gets or sets the backend trend.
    /// </summary>
    public List<WeeklyFinalAverageTrendItem> Backend { get; set; } = [];
    
    /// <summary>
    /// Gets or sets the overall trend.
    /// </summary>
    public List<WeeklyFinalAverageTrendItem> Overall { get; set; } = [];
}

/// <summary>
/// Represents an item in the weekly final average trend.
/// </summary>
public class WeeklyFinalAverageTrendItem
{
    /// <summary>
    /// Gets or sets the datetime for the average trend item.
    /// </summary>
    public DateTime Date { get; set; }
    
    /// <summary>
    /// Gets or sets the average score for the trend item.
    /// </summary>
    public decimal AverageScore { get; set; }
}

/// <summary>
/// Represents the issue distribution.
/// </summary>
public class IssueDistribution
{
    public IssueDistributionItem Backend { get; set; } = new();
    public IssueDistributionItem Frontend { get; set; } = new();
    public IssueDistributionItem Overall { get; set; } = new();
}

/// <summary>
/// Represents the issue distribution by scope.
/// </summary>
public class IssueDistributionItem
{
    /// <summary>
    /// Gets or sets the score of the bugs.
    /// </summary>
    public decimal Bugs { get; set; }
    
    /// <summary>
    /// Gets or sets the score of the code smells.
    /// </summary>
    public decimal CodeSmells { get; set; }
    
    /// <summary>
    /// Gets or sets the score of the vulnerabilities.
    /// </summary>
    public decimal Vulnerabilities { get; set; }
    
    /// <summary>
    /// Gets or sets the score of the security hotspots.
    /// </summary>
    public decimal SecurityHotspots { get; set; }
}

/// <summary>
/// Represents the score statistic.
/// </summary>
public class ScoreStatistic
{
    /// <summary>
    /// Gets or sets the overview.
    /// </summary>
    public Overview Overview { get; set; } = new();

    /// <summary>
    /// Gets or sets the highlights.
    /// </summary>
    public Highlights Highlights { get; set; } = new();

    /// <summary>
    /// Gets or sets the performance by metrics.
    /// </summary>
    public PerformanceByMetrics PerformanceByMetrics { get; set; } = new();

    /// <summary>
    /// Gets or sets the score ranking.
    /// </summary>
    public ScoreRanking Ranking { get; set; } = new();

    /// <summary>
    /// Gets or sets the overview by scope.
    /// </summary>
    public OverviewByScope OverviewByScope { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the weekly final average trend.
    /// </summary>
    public WeeklyFinalAverageTrend WeeklyFinalAverageTrend { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the issue distribution.
    /// </summary>
    public IssueDistribution IssueDistribution { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the monthly final average trend, using the average for the final week
    /// </summary>
    public WeeklyFinalAverageTrend MonthlyFinalAverageTrend { get; set; } = new();
}