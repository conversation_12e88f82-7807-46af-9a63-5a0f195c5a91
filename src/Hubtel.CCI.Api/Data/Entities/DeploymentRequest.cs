using System.ComponentModel.DataAnnotations.Schema;
using Hubtel.CCI.Api.Data.Entities.Common;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
// ReSharper disable All
namespace Hubtel.CCI.Api.Data.Entities;

public class DeploymentRequest:BaseModel
{
    public string? Description { get; set; }
    public string? ProductGroupId { get; set; }
    public string? ProductGroupName { get; set; }
    public string? ProductTeamName { get; set; }
    public string? ProductTeamId { get; set; }
    public string? EngineerRequestId { get; set; }
    public string? EngineerRequestName { get; set; }
    public string? AreaOfImpact { get; set; }
    public string? SystemBehaviorOfChange { get; set; }
    public bool HasControlledEnv { get; set; } = false;
    public string? Priority { get; set; }
    public string? CicdEngineerResponsibleId { get; set; }
    public string? CicdEngineerResponsibleName { get; set; }
    public DateTime? DcpPlanningMeetingStartTime { get; set; }
    public DateTime? DcpPlanningMeetingEndTime { get; set; }
    public DateTime? DcpWorkingSessionStartTime { get; set; }
    public DateTime? DcpWorkingSessionEndTime { get; set; }
    public DateTime? DcpWorkingSessionReviewStartTime { get; set; }
    public DateTime? DcpWorkingSessionReviewEndTime { get; set; }
    public DateTime? DcpConfidenceReadinessReviewStartTime { get; set; }
    public DateTime? DcpConfidenceReadinessReviewEndTime { get; set; }
    public string? DeploymentConfidenceReportLink { get; set; }
    public string? CancellationReason { get; set; }
    public string? DeltaDiscretion { get; set; }
    [Column(TypeName = "jsonb")]
    public List<AffectedRepository> AffectedRepositories { get; set; } = new();
    [Column(TypeName = "jsonb")]
    public List<ServiceToBeDeployed> ServicesToBeDeployed { get; set; } = new();
    public string? Status { get; set; }
}
