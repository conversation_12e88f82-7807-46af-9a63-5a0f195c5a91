using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

/// <summary>
/// Represents a repository model.
/// </summary>
public class Repository : BaseModel
{
    /// <summary>
    /// Gets or sets the name of the repository item.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the URL of the repository item.
    /// </summary>
    public string Url { get; set; } = null!;

    /// <summary>
    /// Gets or sets the type of the repository item.
    /// </summary>
    public string Type { get; set; } = null!;

    /// <summary>
    /// Gets or sets the SonarQube key of the repository item.
    /// </summary>
    public string SonarQubeKey { get; set; } = null!;

    /// <summary>
    /// Gets or sets the description of the repository item.
    /// </summary>
    public string Description { get; set; } = null!;

    /// <summary>
    /// Gets or sets the framework upgrade score computation value for the repository
    /// </summary>
    public decimal? FrameworkUpgradeComputation { get; set; }
    
    
    /// <summary>
    /// Gets or sets the Azure DevOps repository ID
    /// </summary>
    public string? AzureRepositoryId { get; set; }


    /// <summary>
    /// Gets or sets the Semantic score computation value for the repository
    /// </summary>
    public decimal? SemanticScoreComputation { get; set; }

    /// <summary>
    /// Gets or sets the Status of the repository
    /// </summary>
    public string Status { get; set; } = null!;
}