using Hubtel.CCI.Api.Data.Entities.Common;
using Hubtel.CCI.Api.Dtos.Responses.Azure;
using Hubtel.CCI.Api.Dtos.Responses.SonarQube;

namespace Hubtel.CCI.Api.Data.Entities;

public class EngineerSonarQubeMetrics: BaseModel
{
    public string? EngineerName { get; set; }

    public string? EngineerEmail { get; set; }
    
    public string? RepositoryId { get; set; }
    
    public string? PublicationWeek { get; set; }
    
    public string? RepositoryType { get; set; }
    
    public DateTime PublicationDate { get; set; }
    
    public PullRequest PullRequest { get; set; } = new();
    
    public AzureRepositoryDto AzureRepository { get; set; } = new();

    public SonarQubeComponent SonarQubeMetrics { get; set; } = new();
    
}

// store pullrequest, azure repo, sonarqube metrics together with Engineer name and publication date in EngineerSonarQubeMetrics table