using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class DciProductsRanking : BaseModel
{
    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication end date.
    /// </summary>
    public DateTime? PublicationEndDate { get; set; }

    /// <summary>
    /// Gets or sets the publication start date.
    /// </summary>
    public DateTime? PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the publication current dci score.
    /// </summary>
    public decimal? CurrentScore { get; set; }

    /// <summary>
    /// Gets or sets the publication rankings for overall, backend and frontend product teams.
    /// </summary>
    public DciRankings Rankings { get; set; } = new();


    public DciBreakDown DciBreakDown { get; set; } = new();

}

public class DciBreakDown
{
    public DciProductGroupBreakDown ProductGroupBreakDown { get; set; } = new();
    
    public DciProductTeamBreakDown ProductTeamBreakDown { get; set; } = new();
    
    public DciRepositoryBreakDown RepositoryBreakDown { get; set; } = new();
}


public class DciProductGroupBreakDown
{
    /// <summary>
    /// Gets or sets the list of product group breakdowns.
    /// </summary>
    public List<DciProductGroupBreakDownItem> Items { get; set; } = new();
}


public class DciProductGroupBreakDownItem
{
    /// <summary>
    /// Gets or sets the name of the product group.
    /// </summary>
    public string? ProductGroupName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product group.
    /// </summary>
    public string? ProductGroupId { get; set; }

    /// <summary>
    /// Gets or sets the current score of the product group.
    /// </summary>
    public decimal? CurrentScore { get; set; }
}


public class DciProductTeamBreakDown
{
    /// <summary>
    /// Gets or sets the list of product team breakdowns.
    /// </summary>
    public List<DciProductTeamBreakDownItem> Items { get; set; } = new();
}

public class DciProductTeamBreakDownItem
{
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? ProductTeamName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product team.
    /// </summary>
    public string? ProductTeamId { get; set; }
    
    /// <summary>
    /// Gets or sets the name of the product group.
    /// </summary>
    public string? ProductGroupName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product group.
    /// </summary>
    public string? ProductGroupId { get; set; }

    /// <summary>
    /// Gets or sets the current score of the product team.
    /// </summary>
    public decimal? CurrentScore { get; set; }
}

public class DciRepositoryBreakDown
{
    /// <summary>
    /// Gets or sets the list of repository breakdowns.
    /// </summary>
    public List<DciRepositoryBreakDownItem> Items { get; set; } = new();
}

public class DciRepositoryBreakDownItem
{
    /// <summary>
    /// Gets or sets the name of the repository.
    /// </summary>
    public string? RepositoryName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the repository.
    /// </summary>
    public string? RepositoryId { get; set; }

    /// <summary>
    /// Gets or sets the current score of the repository.
    /// </summary>
    public decimal? CurrentScore { get; set; }
    
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? ProductTeamName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product team.
    /// </summary>
    public string? ProductTeamId { get; set; }
    
    /// <summary>
    /// Gets or sets the name of the product group.
    /// </summary>
    public string? ProductGroupName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product group.
    /// </summary>
    public string? ProductGroupId { get; set; }
}


public class DciRankings
{
    /// <summary>
    /// Gets or sets the list of backend product team rankings.
    /// </summary>
    public List<DciRankingItem> Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of overall product team rankings.
    /// </summary>
    public List<DciRankingItem> Overall { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of frontend product team rankings.
    /// </summary>
    public List<DciRankingItem> Frontend { get; set; } = new();
}


public class DciRankingItem
{
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? ProductTeamName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product team.
    /// </summary>
    public string? ProductTeamId { get; set; }

    /// <summary>
    /// Gets or sets the name of the product group.
    /// </summary>
    public string? ProductGroupName { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product group.
    /// </summary>
    public string? ProductGroupId { get; set; }

    /// <summary>
    /// Gets or sets the status of the product cci score
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Gets or sets the rating score of the product team
    /// </summary>
    public decimal? Rating { get; set; }
}