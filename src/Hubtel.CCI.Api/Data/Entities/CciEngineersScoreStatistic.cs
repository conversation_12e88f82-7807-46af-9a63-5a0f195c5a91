using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class CciEngineersScoreStatistic: BaseModel
{
    public DateTime PublicationDate { get; set; }
    
    public string? PublicationWeek { get; set; }

    public EngineersScoreStatistics EngineersScoreStatistics { get; set; } = new();
}


public class EngineersStatisticOverview
{
    public decimal TotalBugs { get; set; }
    public decimal TotalCodeSmells { get; set; }
    public decimal TotalVulnerabilities { get; set; }
    public decimal TotalLinesOfCode { get; set; }
    public decimal TotalPrs { get; set; }
    public decimal TotalActiveEngineers { get; set; }
    public decimal AverageCoverage { get; set; }
    
    public decimal OverallRating { get; set; }
    public decimal AverageDuplications { get; set; }
}


/// <summary>
/// Represents a metric with a name and score.
/// </summary>
public class EngineerMetricItem
{
    /// <summary>
    /// Gets or sets the Id of the engineer metric.
    /// </summary>
    public string Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the engineer metric.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the score of the metric.
    /// </summary>
    public decimal Score { get; set; }
}

/// <summary>
/// Represents the performance by various metrics.
/// </summary>
public class EngineersPerformanceByMetrics
{
    /// <summary>
    /// Gets or sets the list of bug metrics.
    /// </summary>
    public List<EngineerMetricItem> Bugs { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of code smell metrics.
    /// </summary>
    public List<EngineerMetricItem> CodeSmells { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of vulnerability metrics.
    /// </summary>
    public List<EngineerMetricItem> Vulnerabilities { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the list of lines of code metrics.
    /// </summary>
    public List<EngineerMetricItem> LinesOfCode { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of security hotspot metrics.
    /// </summary>
    public List<EngineerMetricItem> SecurityHotspots { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of duplication metrics.
    /// </summary>
    public List<EngineerMetricItem> Duplications { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of code coverage metrics.
    /// </summary>
    public List<EngineerMetricItem> CodeCoverage { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of cognitive complexity metrics.
    /// </summary>
    public List<EngineerMetricItem> CognitiveComplexity { get; set; } = new();
}


/// <summary>
/// Represents an item in the score ranking.
/// </summary>
public class EngineerScoreRankingItem
{
    /// <summary>
    /// Gets or sets the Id of the product team metric.
    /// </summary>
    public string Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the item.
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the score of the item.
    /// </summary>
    public decimal Score { get; set; }

    /// <summary>
    /// Gets or sets the number of bugs.
    /// </summary>
    public decimal Bugs { get; set; }

    /// <summary>
    /// Gets or sets the number of code smells.
    /// </summary>
    public decimal CodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the number of vulnerabilities.
    /// </summary>
    public decimal Vulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the number of security hotspots.
    /// </summary>
    public decimal SecurityHotspots { get; set; }

    /// <summary>
    /// Gets or sets the average number of duplications.
    /// </summary>
    public decimal AverageDuplications { get; set; }
    
    /// <summary>
    /// Gets or sets the lines of code.
    /// </summary>
    public decimal LinesOfCode { get; set; }

    /// <summary>
    /// Gets or sets the average code coverage.
    /// </summary>
    public decimal AverageCodeCoverage { get; set; }

    /// <summary>
    /// Gets or sets the cognitive complexity.
    /// </summary>
    public decimal CognitiveComplexity { get; set; }
}

/// <summary>
/// Represents the score ranking.
/// </summary>
public class EngineerScoreRanking
{
    /// <summary>
    /// Gets or sets the list of backend score ranking items.
    /// </summary>
    public List<EngineerScoreRankingItem> Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of frontend score ranking items.
    /// </summary>
    public List<EngineerScoreRankingItem> Frontend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of overall score ranking items.
    /// </summary>
    public List<EngineerScoreRankingItem> Overall { get; set; } = new();
}


/// <summary>
/// Represents an overview by scope item.
/// </summary>
public class EngineerOverviewByScopeItem
{
    /// <summary>
    /// Gets or sets the number of pull requests.
    /// </summary>
    public decimal CompletedPullRequests { get; set; }
    
    /// <summary>
    /// Gets or sets the number of engineers.
    /// </summary>
    public decimal Engineers { get; set; }

    /// <summary>
    /// Gets or sets the score.
    /// </summary>
    public decimal AverageScore { get; set; }

    /// <summary>
    /// Gets or sets the total number of bugs.
    /// </summary>
    public decimal TotalBugs { get; set; }

    /// <summary>
    /// Gets or sets the total number of code smells.
    /// </summary>
    public decimal TotalCodeSmells { get; set; }

    /// <summary>
    /// Gets or sets the total number of vulnerabilities.
    /// </summary>
    public decimal TotalVulnerabilities { get; set; }

    /// <summary>
    /// Gets or sets the total number of security hotspots.
    /// </summary>
    public decimal TotalSecurityHotspots { get; set; }
    
    /// <summary>
    /// Gets or sets the total lines of code.
    /// </summary>
    public decimal TotalLinesOfCode { get; set; }

    /// <summary>
    /// Gets or sets the average number of duplications.
    /// </summary>
    public decimal AverageDuplications { get; set; }

    /// <summary>
    /// Gets or sets the average code coverage.
    /// </summary>
    public decimal AverageCodeCoverage { get; set; }

    /// <summary>
    /// Gets or sets the total cognitive complexity.
    /// </summary>
    public decimal TotalCognitiveComplexity { get; set; }
}

/// <summary>
/// Represents the engineer overview by scope.
/// </summary>
public class EngineerOverviewByScope
{
    /// <summary>
    /// Gets or sets the backend overview by scope item.
    /// </summary>
    public EngineerOverviewByScopeItem Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the frontend overview by scope item.
    /// </summary>
    public EngineerOverviewByScopeItem Frontend { get; set; } = new();

    /// <summary>
    /// Gets or sets the overall overview by scope item.
    /// </summary>
    public EngineerOverviewByScopeItem Overall { get; set; } = new();
}

public class EngineersScoreStatistics
{
    public EngineersStatisticOverview Overview { get; set; } = new();
    
    public EngineersPerformanceByMetrics PerformanceByMetrics { get; set; } = new();
    
    public EngineerScoreRanking ScoreRanking { get; set; } = new();
    
    public EngineerOverviewByScope OverviewByScope { get; set; } = new();
}