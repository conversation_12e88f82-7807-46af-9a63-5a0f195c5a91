using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class CciRoadMapRecord: BaseModel
{
    public MetricData? Bugs { get; set; }
    public MetricData? CodeSmells { get; set; }
    public MetricData? Coverage { get; set; }
    public MetricData? DuplicatedLines { get; set; }
    public MetricData? Vulnerabilities { get; set; }
    public MetricData? SecurityHotspots { get; set; }
    public DateTime? StartDate { get; set; }
    public QualityProgress OverallQuality { get; set; } = new();
    public ProductInfo Product { get; set; } = new();
    public RepositoryInfo Repository { get; set; } = new();
}

public class MetricData
{
    public decimal StartWeek { get; set; }
    public decimal EndWeek { get; set; }
    public decimal StartValue { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public EngineerInfo EngineerAssigned { get; set; } = new();
}

public class EngineerInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
}

public class QualityProgress
{
    public decimal Start { get; set; }
    public decimal Current { get; set; }
    public decimal Target { get; set; }
}

public class ProductInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
}

public class RepositoryInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
}
