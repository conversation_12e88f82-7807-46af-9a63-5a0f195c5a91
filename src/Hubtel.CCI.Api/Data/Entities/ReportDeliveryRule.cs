using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class ReportDeliveryRule: BaseModel
{
    public string? ProductGroupId { get; set; }
    
    public string? ProductGroupName { get; set; }
    public List<string> ProductTeamIds { get; set; } = new();
    /// <summary>
    /// "Backend", "Frontend", or null for any/overall
    /// </summary>
    public string? Scope         { get; set; }

    public string RecipientEmail { get; set; } = string.Empty;
    public string ReportType { get; set; } = ValidationConstants.ReportType.ProductGroupWeeklyReport;
}