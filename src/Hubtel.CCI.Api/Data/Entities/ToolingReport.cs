using System.Text.Json.Serialization;
using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities
{
    public class ToolingReport : BaseModel
    {
        public string ProductGroupName { get; set; } = null!;
        public Domain Domain { get; set; }
        public GeneralOverview GeneralOverview { get; set; } = null!;
        public ExecutiveSummary ExecutiveSummary { get; set; } = null!;
        public KeyMetricsOverview KeyMetricsOverview { get; set; } = null!;
        public List<DetailedProjectStatus> DetailedProjectStatus { get; set; } = new();
        public IssuesSummary IssuesSummary { get; set; } = null!;
        public PriorityActions PriorityActions { get; set; } = null!;
        public Recommendations Recommendation { get; set; } = new();
        public List<string> PlugAndPlaySolutionsInUse { get; set; } = new();
    }

    public class GeneralOverview
    {
        public int Projects { get; set; } = 0;
        public int Repositories { get; set; } = 0;
        public int NumberOfProducts { get; set; } = 0;
        public int TotalIssues { get; set; } = 0;
        public int SecurityIssues { get; set; } = 0;
    }
    public class ExecutiveSummary
    {
        public List<string> CurrentState { get; set; } = new();
        public List<string> KeyIssues { get; set; } = new();
    }

    public class KeyMetricsOverview
    {
        public int Repositories { get; set; } = 0;
        public int ActiveProjects { get; set; } = 0;
        public int CriticalIssues { get; set; } = 0;

    }

    public class DetailedProjectStatus
    {
        public string Tool { get; set; } = null!;
        public string Version { get; set; } = null!;
        public int Projects { get; set; } = 0;
        public VersionStatus Status { get; set; }
    }

    public class IssuesSummary
    {
        public List<SecurityIssues> SecurityIssues { get; set; } = new();
        public List<string> IssuesOverview { get; set; } = new();
    }

    public class SecurityIssues
    {
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public List<string> AffectedServices { get; set; } = new();
        public int CountOfAffectedServices { get; set; } 
    }

    public class PriorityActions
    {
        public List<Actions> Actions { get; set; } = new();
    }

    public class Actions
    {
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Duration { get; set; } = null!;
    }

    public class Recommendations
    {
        public string Overview { get; set; } = null!;
        public List<string> ExpectedBenefits { get; set; } = new();
        public List<string> SuccessMetrics { get; set; } = new();
    }

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum VersionStatus
    {
        Outdated,
        Latest
    }
}
