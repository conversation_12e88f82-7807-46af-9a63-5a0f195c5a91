using Hubtel.CCI.Api.Data.Entities.Common;

namespace Hubtel.CCI.Api.Data.Entities;

public class CciEngineerRanking: BaseModel
{
    /// <summary>
    /// Gets or sets the publication week.
    /// </summary>
    public string? PublicationWeek { get; set; }

    /// <summary>
    /// Gets or sets the publication date.
    /// </summary>
    public DateTime? PublicationDate { get; set; }

    /// <summary>
    /// Gets or sets the publication end date.
    /// </summary>
    public DateTime? PublicationEndDate { get; set; }

    /// <summary>
    /// Gets or sets the publication start date.
    /// </summary>
    public DateTime? PublicationStartDate { get; set; }

    /// <summary>
    /// Gets or sets the publication current cci score.
    /// </summary>
    public decimal? CurrentScore { get; set; }

    /// <summary>
    /// Gets or sets the publication rankings for overall, backend and frontend product teams.
    /// </summary>
    public EngineerRankings Rankings { get; set; } = new();
}

public class EngineerRankings
{
    /// <summary>
    /// Gets or sets the list of backend product team rankings.
    /// </summary>
    public List<EngineerRankingItem> Backend { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of overall product team rankings.
    /// </summary>
    public List<EngineerRankingItem> Overall { get; set; } = new();

    /// <summary>
    /// Gets or sets the list of frontend product team rankings.
    /// </summary>
    public List<EngineerRankingItem> Frontend { get; set; } = new();
}

public class EngineerRankingItem
{
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? EngineerName { get; set; }
    
    /// <summary>
    /// Gets or sets the name of the product team.
    /// </summary>
    public string? EngineerEmail { get; set; }

    /// <summary>
    /// Gets or sets the ID of the product team.
    /// </summary>
    public string? EngineerId { get; set; }


    /// <summary>
    /// Gets or sets the status of the product cci score
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Gets or sets the rating score of the engineer
    /// </summary>
    public decimal? Rating { get; set; }
}