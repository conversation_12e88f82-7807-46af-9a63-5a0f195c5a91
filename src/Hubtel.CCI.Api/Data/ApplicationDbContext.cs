using System.Reflection;
using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
    }

    public DbSet<Engineer> Engineers => Set<Engineer>();
    public DbSet<DeploymentRequest> DeploymentRequests => Set<DeploymentRequest>();
    public DbSet<ServiceDeploymentTrail> ServiceDeploymentTrails => Set<ServiceDeploymentTrail>();
    public DbSet<ProductTeam> ProductTeams => Set<ProductTeam>();
    public DbSet<ProductGroup> ProductGroups => Set<ProductGroup>();
    public DbSet<Repository> Repositories => Set<Repository>();
    public DbSet<CciRepositoryScore> CciRepositoryScores => Set<CciRepositoryScore>();
    public DbSet<CciProductsRanking> CciProductsRankings => Set<CciProductsRanking>();
    public DbSet<CciProductTeamsScoreTrend> CciProductTeamsScoreTrends => Set<CciProductTeamsScoreTrend>();
    public DbSet<CciRepositoryScoreStatistic> CciRepositoryScoreStatistics => Set<CciRepositoryScoreStatistic>();
    public DbSet<CciRoadMap> CciRoadMaps => Set<CciRoadMap>();
    
    public DbSet<CciRoadMapMetricUpdateRecord> CciRoadMapMetricUpdateRecords => Set<CciRoadMapMetricUpdateRecord>();
    
    public DbSet<BypassedPr> BypassedPrs => Set<BypassedPr>();
    
    public DbSet<RepositorySyncLogBp> RepositorySyncLogBps => Set<RepositorySyncLogBp>();
    
    public DbSet<EngineerSonarQubeMetrics> EngineerSonarQubeMetrics => Set<EngineerSonarQubeMetrics>();
    
    public DbSet<CciEngineersScoreStatistic> CciEngineersScoreStatistics => Set<CciEngineersScoreStatistic>();
    
    public DbSet<CciEngineerScore> CciEngineerScores => Set<CciEngineerScore>();
    
    public DbSet<CciRoadMapRecord> CciRoadMapRecords => Set<CciRoadMapRecord>();
    
    public DbSet<AzureReleaseDefinition> AzureReleaseDefinitions => Set<AzureReleaseDefinition>();
    
    public DbSet<AzureReleaseDefinitionDeployment> AzureReleaseDefinitionDeployments => Set<AzureReleaseDefinitionDeployment>();

    public DbSet<CciEngineerRanking> CciEngineerRankings => Set<CciEngineerRanking>();
    
    public DbSet<RepositoryLanguageDistribution> RepositoryLanguageDistributions => Set<RepositoryLanguageDistribution>();
    
    public DbSet<RepositoryLanguageStatistic> RepositoryLanguageStatistics => Set<RepositoryLanguageStatistic>();
    
    public DbSet<ReportDeliveryRule> ReportDeliveryRules => Set<ReportDeliveryRule>();
    public DbSet<Service> Services => Set<Service>();
    public DbSet<ServiceStatistic> ServiceStatistics => Set<ServiceStatistic>();
    
    public DbSet<DciServiceScore> DciServiceScores => Set<DciServiceScore>();
    
    public DbSet<DciProductsRanking> DciProductsRankings => Set<DciProductsRanking>();
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        
        base.OnModelCreating(modelBuilder);
    }
}