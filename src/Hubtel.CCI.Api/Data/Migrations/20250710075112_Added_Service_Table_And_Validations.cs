using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_Service_Table_And_Validations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Services",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    RepositoryId = table.Column<string>(type: "text", nullable: false),
                    RepositoryName = table.Column<string>(type: "text", nullable: false),
                    TagName = table.Column<string>(type: "text", nullable: true),
                    ServiceType = table.Column<string>(type: "text", nullable: true),
                    RepositoryType = table.Column<string>(type: "text", nullable: true),
                    EngineerResponsibleId = table.Column<string>(type: "text", nullable: true),
                    EngineerResponsibleName = table.Column<string>(type: "text", nullable: true),
                    EngineerAccountableId = table.Column<string>(type: "text", nullable: true),
                    EngineerAccountableName = table.Column<string>(type: "text", nullable: true),
                    ServiceFacingDirection = table.Column<string>(type: "text", nullable: true),
                    WhoToConsult = table.Column<string>(type: "text", nullable: true),
                    WhoToInform = table.Column<string>(type: "text", nullable: true),
                    CdSetupStatus = table.Column<string>(type: "text", nullable: true),
                    ServiceSendsMoney = table.Column<bool>(type: "boolean", nullable: false),
                    ServiceReceivesMoney = table.Column<bool>(type: "boolean", nullable: false),
                    EndUsersTypes = table.Column<List<string>>(type: "text[]", nullable: false),
                    HasMoreThanThousandEndUsers = table.Column<bool>(type: "boolean", nullable: true),
                    CanMisrepresentData = table.Column<bool>(type: "boolean", nullable: true),
                    ServiceInMarket = table.Column<bool>(type: "boolean", nullable: true),
                    WillAffectOtherServices = table.Column<bool>(type: "boolean", nullable: true),
                    DownstreamServicesCount = table.Column<int>(type: "integer", nullable: false),
                    ReceiveMerComponent = table.Column<bool>(type: "boolean", nullable: false),
                    SendsMerComponent = table.Column<bool>(type: "boolean", nullable: false),
                    ReceiveMerExposure = table.Column<string>(type: "text", nullable: true),
                    SendsMerExposure = table.Column<string>(type: "text", nullable: true),
                    PossibilityOfDataBreach = table.Column<string>(type: "text", nullable: true),
                    TotalRiskScore = table.Column<decimal>(type: "numeric", nullable: true),
                    RiskClassification = table.Column<string>(type: "text", nullable: true),
                    DeploymentStrategy = table.Column<string>(type: "text", nullable: true),
                    EngineerCompetenceRequirement = table.Column<string>(type: "text", nullable: true),
                    CodeReviewRequirement = table.Column<string>(type: "text", nullable: true),
                    ServiceReleaseUrl = table.Column<string>(type: "text", nullable: true),
                    ServiceReleaseDefinitionId = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Services", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Services");
        }
    }
}
