using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_Product_Scope_And_AsIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CciProductTeamsScoreTrend",
                table: "CciProductTeamsScoreTrend");

            migrationBuilder.DropIndex(
                name: "IX_CciProductTeamsScoreTrend_ProductTeamId_PublicationWeek",
                table: "CciProductTeamsScoreTrend");

            migrationBuilder.RenameTable(
                name: "CciProductTeamsScoreTrend",
                newName: "CciProductTeamsScoreTrends");

            migrationBuilder.RenameIndex(
                name: "IX_CciProductTeamsScoreTrend_PublicationWeek",
                table: "CciProductTeamsScoreTrends",
                newName: "IX_CciProductTeamsScoreTrends_PublicationWeek");

            migrationBuilder.RenameIndex(
                name: "IX_CciProductTeamsScoreTrend_PublicationDate",
                table: "CciProductTeamsScoreTrends",
                newName: "IX_CciProductTeamsScoreTrends_PublicationDate");

            migrationBuilder.AddColumn<string>(
                name: "ProductGroupName",
                table: "CciProductTeamsScoreTrends",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProductScope",
                table: "CciProductTeamsScoreTrends",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProductTeamName",
                table: "CciProductTeamsScoreTrends",
                type: "text",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_CciProductTeamsScoreTrends",
                table: "CciProductTeamsScoreTrends",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_CciProductTeamsScoreTrends_ProductScope",
                table: "CciProductTeamsScoreTrends",
                column: "ProductScope");

            migrationBuilder.CreateIndex(
                name: "IX_CciProductTeamsScoreTrends_ProductTeamId_PublicationWeek_Pr~",
                table: "CciProductTeamsScoreTrends",
                columns: new[] { "ProductTeamId", "PublicationWeek", "ProductScope" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CciProductTeamsScoreTrends",
                table: "CciProductTeamsScoreTrends");

            migrationBuilder.DropIndex(
                name: "IX_CciProductTeamsScoreTrends_ProductScope",
                table: "CciProductTeamsScoreTrends");

            migrationBuilder.DropIndex(
                name: "IX_CciProductTeamsScoreTrends_ProductTeamId_PublicationWeek_Pr~",
                table: "CciProductTeamsScoreTrends");

            migrationBuilder.DropColumn(
                name: "ProductGroupName",
                table: "CciProductTeamsScoreTrends");

            migrationBuilder.DropColumn(
                name: "ProductScope",
                table: "CciProductTeamsScoreTrends");

            migrationBuilder.DropColumn(
                name: "ProductTeamName",
                table: "CciProductTeamsScoreTrends");

            migrationBuilder.RenameTable(
                name: "CciProductTeamsScoreTrends",
                newName: "CciProductTeamsScoreTrend");

            migrationBuilder.RenameIndex(
                name: "IX_CciProductTeamsScoreTrends_PublicationWeek",
                table: "CciProductTeamsScoreTrend",
                newName: "IX_CciProductTeamsScoreTrend_PublicationWeek");

            migrationBuilder.RenameIndex(
                name: "IX_CciProductTeamsScoreTrends_PublicationDate",
                table: "CciProductTeamsScoreTrend",
                newName: "IX_CciProductTeamsScoreTrend_PublicationDate");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CciProductTeamsScoreTrend",
                table: "CciProductTeamsScoreTrend",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_CciProductTeamsScoreTrend_ProductTeamId_PublicationWeek",
                table: "CciProductTeamsScoreTrend",
                columns: new[] { "ProductTeamId", "PublicationWeek" });
        }
    }
}
