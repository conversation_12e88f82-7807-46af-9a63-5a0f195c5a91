using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_New_Language_Additions_Statistics : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "NewLanguageDistributions",
                table: "RepositoryLanguageStatistics",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");

            migrationBuilder.AddColumn<string>(
                name: "NewLanguageRanking",
                table: "RepositoryLanguageStatistics",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'{\"Backend\":[],\"Frontend\":[],\"Overall\":[]}'::jsonb");

            migrationBuilder.AddColumn<decimal>(
                name: "NewLinesOfCode",
                table: "RepositoryLanguageStatistics",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NewLanguageDistributions",
                table: "RepositoryLanguageStatistics");

            migrationBuilder.DropColumn(
                name: "NewLanguageRanking",
                table: "RepositoryLanguageStatistics");

            migrationBuilder.DropColumn(
                name: "NewLinesOfCode",
                table: "RepositoryLanguageStatistics");
        }
    }
}
