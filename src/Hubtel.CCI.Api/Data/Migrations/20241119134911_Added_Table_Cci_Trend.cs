using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_Table_Cci_Trend : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciProductTeamsScoreTrend",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ProductTeamId = table.Column<string>(type: "text", nullable: true),
                    ProductGroupId = table.Column<string>(type: "text", nullable: true),
                    AverageScore = table.Column<decimal>(type: "numeric", nullable: true),
                    TrendDirection = table.Column<string>(type: "text", nullable: true),
                    ScoreChange = table.Column<decimal>(type: "numeric", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciProductTeamsScoreTrend", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CciProductTeamsScoreTrend_ProductTeamId_PublicationWeek",
                table: "CciProductTeamsScoreTrend",
                columns: new[] { "ProductTeamId", "PublicationWeek" });

            migrationBuilder.CreateIndex(
                name: "IX_CciProductTeamsScoreTrend_PublicationDate",
                table: "CciProductTeamsScoreTrend",
                column: "PublicationDate");

            migrationBuilder.CreateIndex(
                name: "IX_CciProductTeamsScoreTrend_PublicationWeek",
                table: "CciProductTeamsScoreTrend",
                column: "PublicationWeek");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciProductTeamsScoreTrend");
        }
    }
}
