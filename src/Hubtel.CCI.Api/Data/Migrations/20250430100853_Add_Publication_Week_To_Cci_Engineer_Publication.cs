using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Publication_Week_To_Cci_Engineer_Publication : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PublicationWeek",
                table: "EngineerSonarQubeMetrics",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PublicationWeek",
                table: "CciEngineersScoreStatistics",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PublicationWeek",
                table: "CciEngineerScores",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PublicationWeek",
                table: "EngineerSonarQubeMetrics");

            migrationBuilder.DropColumn(
                name: "PublicationWeek",
                table: "CciEngineersScoreStatistics");

            migrationBuilder.DropColumn(
                name: "PublicationWeek",
                table: "CciEngineerScores");
        }
    }
}
