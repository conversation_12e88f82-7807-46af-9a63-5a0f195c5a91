using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_CCI_RoadMap_Record_V2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciRoadMapRecords",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Bugs = table.Column<string>(type: "jsonb", nullable: true, defaultValueSql: "'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb"),
                    CodeSmells = table.Column<string>(type: "jsonb", nullable: true, defaultValueSql: "'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb"),
                    Coverage = table.Column<string>(type: "jsonb", nullable: true, defaultValueSql: "'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb"),
                    DuplicatedLines = table.Column<string>(type: "jsonb", nullable: true, defaultValueSql: "'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb"),
                    Vulnerabilities = table.Column<string>(type: "jsonb", nullable: true, defaultValueSql: "'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb"),
                    SecurityHotspots = table.Column<string>(type: "jsonb", nullable: true, defaultValueSql: "'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb"),
                    StartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OverallQuality = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Start\":0.0,\"Current\":0.0,\"Target\":0.0}'::jsonb"),
                    Product = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Id\":\"\",\"Name\":\"\"}'::jsonb"),
                    Repository = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Id\":\"\",\"Name\":\"\",\"Type\":\"\"}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciRoadMapRecords", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciRoadMapRecords");
        }
    }
}
