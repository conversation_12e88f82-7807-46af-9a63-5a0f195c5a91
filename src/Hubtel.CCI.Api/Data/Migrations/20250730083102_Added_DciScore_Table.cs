using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_DciScore_Table : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DciServiceScores",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RepositoryName = table.Column<string>(type: "text", nullable: true),
                    RepositoryId = table.Column<string>(type: "text", nullable: true),
                    RepositoryUrl = table.Column<string>(type: "text", nullable: true),
                    RepositoryType = table.Column<string>(type: "text", nullable: true),
                    RepositorySonarQubeKey = table.Column<string>(type: "text", nullable: false),
                    ProductTeamName = table.Column<string>(type: "text", nullable: true),
                    ProductTeamId = table.Column<string>(type: "text", nullable: true),
                    ProductGroupName = table.Column<string>(type: "text", nullable: true),
                    ProductGroupId = table.Column<string>(type: "text", nullable: true),
                    ServiceId = table.Column<string>(type: "text", nullable: true),
                    ServiceName = table.Column<string>(type: "text", nullable: true),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationStartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    FinalScore = table.Column<decimal>(type: "numeric", nullable: true),
                    StabilityScore = table.Column<decimal>(type: "numeric", nullable: true),
                    ProcessComplianceScore = table.Column<decimal>(type: "numeric", nullable: true),
                    SpeedScore = table.Column<decimal>(type: "numeric", nullable: true),
                    SuccessRateScore = table.Column<decimal>(type: "numeric", nullable: true),
                    IssueSeverityScore = table.Column<decimal>(type: "numeric", nullable: true),
                    CodeConfidenceIndexScore = table.Column<decimal>(type: "numeric", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DciServiceScores", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DciServiceScores_PublicationDate",
                table: "DciServiceScores",
                column: "PublicationDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DciServiceScores");
        }
    }
}
