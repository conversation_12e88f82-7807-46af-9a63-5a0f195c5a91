using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Azure_Deployment_Definition_Releases_Model : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ProjectName",
                table: "AzureReleaseDefinitions",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AzureReleaseDefinitionDeployments",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    PublicationWeek = table.Column<string>(type: "text", nullable: false),
                    DefinitionId = table.Column<string>(type: "text", nullable: false),
                    DefinitionName = table.Column<string>(type: "text", nullable: true),
                    DefinitionProjectName = table.Column<string>(type: "text", nullable: false),
                    CompositeDefinitionId = table.Column<string>(type: "text", nullable: false),
                    TotalDeployments = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalDeploymentsSuccess = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalDeploymentsFailed = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalDeploymentsInRolledBack = table.Column<decimal>(type: "numeric", nullable: false),
                    ReleaseDeployments = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AzureReleaseDefinitionDeployments", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AzureReleaseDefinitionDeployments_CompositeDefinitionId",
                table: "AzureReleaseDefinitionDeployments",
                column: "CompositeDefinitionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AzureReleaseDefinitionDeployments");

            migrationBuilder.DropColumn(
                name: "ProjectName",
                table: "AzureReleaseDefinitions");
        }
    }
}
