using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_Cci_Stats_Table : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciRepositoryScoreStatistics",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ScoreStatistic = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Overview\":{\"CurrentScore\":0,\"TotalBugs\":0,\"TotalCodeSmells\":0,\"TotalVulnerabilities\":0,\"TotalSecurityHotspots\":0,\"TotalDuplications\":0,\"AverageCodeCoverage\":0,\"TotalCognitiveComplexity\":0},\"Highlights\":{\"Highest\":{\"Name\":null,\"Score\":0},\"Lowest\":{\"Name\":null,\"Score\":0}},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"Ranking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"Repositories\":0,\"Score\":0,\"TotalCodeSmells\":0,\"TotalVulnerabilities\":0,\"TotalSecurityHotspots\":0,\"AverageDuplications\":0,\"AverageCodeCoverage\":0,\"TotalCognitiveComplexity\":0},\"Frontend\":{\"Repositories\":0,\"Score\":0,\"TotalCodeSmells\":0,\"TotalVulnerabilities\":0,\"TotalSecurityHotspots\":0,\"AverageDuplications\":0,\"AverageCodeCoverage\":0,\"TotalCognitiveComplexity\":0},\"Overall\":{\"Repositories\":0,\"Score\":0,\"TotalCodeSmells\":0,\"TotalVulnerabilities\":0,\"TotalSecurityHotspots\":0,\"AverageDuplications\":0,\"AverageCodeCoverage\":0,\"TotalCognitiveComplexity\":0}}}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciRepositoryScoreStatistics", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CciRepositoryScoreStatistics_PublicationDate",
                table: "CciRepositoryScoreStatistics",
                column: "PublicationDate");

            migrationBuilder.CreateIndex(
                name: "IX_CciRepositoryScoreStatistics_PublicationWeek",
                table: "CciRepositoryScoreStatistics",
                column: "PublicationWeek");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciRepositoryScoreStatistics");
        }
    }
}
