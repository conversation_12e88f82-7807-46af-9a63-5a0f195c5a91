using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_New_Loc_For_Language_Distribution_Publication : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "NewLanguageDistributionsDelta",
                table: "RepositoryLanguageDistributions",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");

            migrationBuilder.AddColumn<decimal>(
                name: "NewN<PERSON><PERSON>",
                table: "RepositoryLanguageDistributions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NewLanguageDistributionsDelta",
                table: "RepositoryLanguageDistributions");

            migrationBuilder.DropColumn(
                name: "NewNcloc",
                table: "RepositoryLanguageDistributions");
        }
    }
}
