using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_CciProductRanking_Entity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciProductsRankings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationStartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CurrentScore = table.Column<decimal>(type: "numeric", nullable: true),
                    Rankings = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Backend\":[],\"Overall\":[],\"Frontend\":[]}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciProductsRankings", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciProductsRankings");
        }
    }
}
