using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Repositories.Migrations
{
    /// <inheritdoc />
    public partial class FixTheIssuesSummaryDefaultValueSql : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "IssuesSummary",
                table: "ToolingReport",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'{\"SecurityIssues\":[],\"IssuesOverview\":[]}'::jsonb",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldDefaultValueSql: "'[]'::jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "IssuesSummary",
                table: "ToolingReport",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldDefaultValueSql: "'{\"SecurityIssues\":[],\"IssuesOverview\":[]}'::jsonb");
        }
    }
}
