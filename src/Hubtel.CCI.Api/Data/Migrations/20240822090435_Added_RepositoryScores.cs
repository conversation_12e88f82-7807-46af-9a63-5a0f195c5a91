using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_RepositoryScores : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciRepositoryScores",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RepositoryName = table.Column<string>(type: "text", nullable: true),
                    RepositoryUrl = table.Column<string>(type: "text", nullable: true),
                    RepositoryType = table.Column<string>(type: "text", nullable: true),
                    RepositorySonarQubeKey = table.Column<string>(type: "text", nullable: false),
                    ProductTeamName = table.Column<string>(type: "text", nullable: true),
                    ProductTeamId = table.Column<string>(type: "text", nullable: true),
                    ProductGroupName = table.Column<string>(type: "text", nullable: true),
                    ProductGroupId = table.Column<string>(type: "text", nullable: true),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationStartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    SonarQubeMetricsAcquired = table.Column<bool>(type: "boolean", nullable: false),
                    FinalAverage = table.Column<decimal>(type: "numeric", nullable: true),
                    Average = table.Column<decimal>(type: "numeric", nullable: true),
                    Bugs = table.Column<decimal>(type: "numeric", nullable: true),
                    BugsComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    CodeSmells = table.Column<decimal>(type: "numeric", nullable: true),
                    CodeSmellsComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    Coverage = table.Column<decimal>(type: "numeric", nullable: true),
                    CoverageComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    DuplicatedLinesDensity = table.Column<decimal>(type: "numeric", nullable: true),
                    DuplicatedLinesDensityComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    Vulnerabilities = table.Column<decimal>(type: "numeric", nullable: true),
                    VulnerabilitiesComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    SecurityHotspots = table.Column<decimal>(type: "numeric", nullable: true),
                    SecurityHotspotsComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    SecurityRating = table.Column<decimal>(type: "numeric", nullable: true),
                    SecurityRatingComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    ReliabilityRating = table.Column<decimal>(type: "numeric", nullable: true),
                    ReliabilityRatingComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    ReopenedIssues = table.Column<decimal>(type: "numeric", nullable: true),
                    ReopenedIssuesComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    CognitiveComplexity = table.Column<decimal>(type: "numeric", nullable: true),
                    CognitiveComplexityComputation = table.Column<decimal>(type: "numeric", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciRepositoryScores", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciRepositoryScores");
        }
    }
}
