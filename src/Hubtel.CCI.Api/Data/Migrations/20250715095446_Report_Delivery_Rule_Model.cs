using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Report_Delivery_Rule_Model : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReportDeliveryRules",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ProductGroupId = table.Column<string>(type: "text", nullable: true),
                    ProductTeamIds = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    Scope = table.Column<string>(type: "text", nullable: true),
                    RecipientEmail = table.Column<string>(type: "text", nullable: false),
                    ReportType = table.Column<string>(type: "text", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportDeliveryRules", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReportDeliveryRules_RecipientEmail",
                table: "ReportDeliveryRules",
                column: "RecipientEmail");

            migrationBuilder.CreateIndex(
                name: "IX_ReportDeliveryRules_ReportType",
                table: "ReportDeliveryRules",
                column: "ReportType");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReportDeliveryRules");
        }
    }
}
