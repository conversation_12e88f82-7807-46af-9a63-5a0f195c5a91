using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Engineer_Cci_Metrics_Models : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CciEngineerScores",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    TotalPullRequests = table.Column<decimal>(type: "numeric", nullable: false),
                    Score = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalBugs = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalCodeSmells = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalVulnerabilities = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalLinesOfCode = table.Column<decimal>(type: "numeric", nullable: false),
                    AverageCoverage = table.Column<decimal>(type: "numeric", nullable: false),
                    AverageDuplications = table.Column<decimal>(type: "numeric", nullable: false),
                    EngineerId = table.Column<string>(type: "text", nullable: true),
                    EngineerName = table.Column<string>(type: "text", nullable: true),
                    EngineerEmail = table.Column<string>(type: "text", nullable: true),
                    EngineerPrMetrics = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"EngineerSonarQubeMetrics\":[],\"CciScore\":[]}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciEngineerScores", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CciEngineersScoreStatistics",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    EngineersScoreStatistics = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Overview\":{\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalLinesOfCode\":0.0,\"TotalPrs\":0.0,\"TotalActiveEngineers\":0.0,\"AverageCoverage\":0.0,\"AverageDuplications\":0.0},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"ScoreRanking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"CompletedPullRequests\":0.0,\"Engineers\":0.0,\"AverageScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Frontend\":{\"CompletedPullRequests\":0.0,\"Engineers\":0.0,\"AverageScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Overall\":{\"CompletedPullRequests\":0.0,\"Engineers\":0.0,\"AverageScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0}}}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciEngineersScoreStatistics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EngineerSonarQubeMetrics",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    EngineerName = table.Column<string>(type: "text", nullable: true),
                    EngineerEmail = table.Column<string>(type: "text", nullable: true),
                    RepositoryId = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    PullRequest = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Repository\":{\"Id\":null,\"Name\":null,\"Url\":null,\"Project\":{\"Id\":null,\"Name\":null,\"State\":null,\"Visibility\":null,\"LastUpdateTime\":\"0001-01-01T00:00:00\"}},\"PullRequestId\":0,\"CodeReviewId\":0,\"Status\":null,\"CreatedBy\":{\"DisplayName\":null,\"Url\":null,\"Links\":{\"Avatar\":{\"Href\":null}},\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null,\"Descriptor\":null},\"CreationDate\":\"0001-01-01T00:00:00\",\"ClosedDate\":\"0001-01-01T00:00:00\",\"Title\":null,\"Description\":null,\"SourceRefName\":null,\"TargetRefName\":null,\"MergeStatus\":null,\"IsDraft\":false,\"MergeId\":null,\"LastMergeSourceCommit\":{\"CommitId\":null,\"Url\":null},\"LastMergeTargetCommit\":{\"CommitId\":null,\"Url\":null},\"LastMergeCommit\":{\"CommitId\":null,\"Url\":null},\"Reviewers\":[],\"Url\":null,\"CompletionOptions\":{\"MergeCommitMessage\":null,\"MergeStrategy\":null,\"BypassPolicy\":false,\"BypassReason\":null,\"AutoCompleteIgnoreConfigIds\":[]},\"SupportsIterations\":false,\"CompletionQueueTime\":\"0001-01-01T00:00:00\"}'::jsonb"),
                    AzureRepository = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    SonarQubeMetrics = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Key\":null,\"Name\":null,\"Measures\":[]}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EngineerSonarQubeMetrics", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CciEngineerScores_PublicationDate",
                table: "CciEngineerScores",
                column: "PublicationDate");

            migrationBuilder.CreateIndex(
                name: "IX_CciEngineersScoreStatistics_PublicationDate",
                table: "CciEngineersScoreStatistics",
                column: "PublicationDate");

            migrationBuilder.CreateIndex(
                name: "IX_EngineerSonarQubeMetrics_PublicationDate",
                table: "EngineerSonarQubeMetrics",
                column: "PublicationDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciEngineerScores");

            migrationBuilder.DropTable(
                name: "CciEngineersScoreStatistics");

            migrationBuilder.DropTable(
                name: "EngineerSonarQubeMetrics");
        }
    }
}
