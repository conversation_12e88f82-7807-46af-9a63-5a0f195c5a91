using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Cci_RoadMap_SQ_Update_Record : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ScoreStatistic",
                table: "CciRepositoryScoreStatistics",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'{\"Overview\":{\"CurrentScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Highlights\":{\"Highest\":{\"Name\":null,\"Score\":0.0},\"Lowest\":{\"Name\":null,\"Score\":0.0}},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"Ranking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Frontend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Overall\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0}},\"WeeklyFinalAverageTrend\":{\"Frontend\":[],\"Backend\":[],\"Overall\":[]},\"IssueDistribution\":{\"Backend\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0},\"Frontend\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0},\"Overall\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0}},\"MonthlyFinalAverageTrend\":{\"Frontend\":[],\"Backend\":[],\"Overall\":[]}}'::jsonb",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldDefaultValueSql: "'{\"Overview\":{\"CurrentScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Highlights\":{\"Highest\":{\"Name\":null,\"Score\":0.0},\"Lowest\":{\"Name\":null,\"Score\":0.0}},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"Ranking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Frontend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Overall\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0}}}'::jsonb");

            migrationBuilder.CreateTable(
                name: "CciRoadMapMetricUpdateRecords",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RunDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    RunStartTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    RunEndTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CciRoadMaps = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Items\":[]}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CciRoadMapMetricUpdateRecords", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CciRoadMapMetricUpdateRecords_RunDate",
                table: "CciRoadMapMetricUpdateRecords",
                column: "RunDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CciRoadMapMetricUpdateRecords");

            migrationBuilder.AlterColumn<string>(
                name: "ScoreStatistic",
                table: "CciRepositoryScoreStatistics",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'{\"Overview\":{\"CurrentScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Highlights\":{\"Highest\":{\"Name\":null,\"Score\":0.0},\"Lowest\":{\"Name\":null,\"Score\":0.0}},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"Ranking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Frontend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Overall\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0}}}'::jsonb",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldDefaultValueSql: "'{\"Overview\":{\"CurrentScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Highlights\":{\"Highest\":{\"Name\":null,\"Score\":0.0},\"Lowest\":{\"Name\":null,\"Score\":0.0}},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"Ranking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Frontend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Overall\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0}},\"WeeklyFinalAverageTrend\":{\"Frontend\":[],\"Backend\":[],\"Overall\":[]},\"IssueDistribution\":{\"Backend\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0},\"Frontend\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0},\"Overall\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0}},\"MonthlyFinalAverageTrend\":{\"Frontend\":[],\"Backend\":[],\"Overall\":[]}}'::jsonb");
        }
    }
}
