using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Update_Cci_Road_Map_Metric_Update_Record : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CciRoadMapRecords",
                table: "CciRoadMapMetricUpdateRecords",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'{\"Items\":[]}'::jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CciRoadMapRecords",
                table: "CciRoadMapMetricUpdateRecords");
        }
    }
}
