using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Bypassed_PRs_Entity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BypassedPrs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PullRequestId = table.Column<int>(type: "integer", nullable: false),
                    CodeReviewId = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    RepositoryId = table.Column<string>(type: "text", nullable: true),
                    Url = table.Column<string>(type: "text", nullable: true),
                    Repository = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Id\":null,\"Name\":null,\"Url\":null,\"Project\":{\"Id\":null,\"Name\":null}}'::jsonb"),
                    Reviewers = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    CreatedBy = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"DisplayName\":null,\"Url\":null,\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null}'::jsonb"),
                    CreationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ClosedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    MergeStatus = table.Column<string>(type: "text", nullable: true),
                    CompletionOptions = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"MergeCommitMessage\":null,\"MergeStrategy\":null,\"BypassPolicy\":false,\"BypassReason\":null}'::jsonb"),
                    ClosedBy = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"DisplayName\":null,\"Url\":null,\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null}'::jsonb"),
                    CompletionQueueTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ArtifactId = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BypassedPrs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RepositorySyncLogBps",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RepositoryId = table.Column<string>(type: "text", nullable: true),
                    RepositoryName = table.Column<string>(type: "text", nullable: true),
                    Url = table.Column<string>(type: "text", nullable: true),
                    AzureRepoId = table.Column<string>(type: "text", nullable: true),
                    ProjectName = table.Column<string>(type: "text", nullable: true),
                    LastSyncedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RepositorySyncLogBps", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BypassedPrs_ClosedDate",
                table: "BypassedPrs",
                column: "ClosedDate");

            migrationBuilder.CreateIndex(
                name: "IX_BypassedPrs_CreationDate",
                table: "BypassedPrs",
                column: "CreationDate");

            migrationBuilder.CreateIndex(
                name: "IX_RepositorySyncLogBps_Url",
                table: "RepositorySyncLogBps",
                column: "Url");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BypassedPrs");

            migrationBuilder.DropTable(
                name: "RepositorySyncLogBps");
        }
    }
}
