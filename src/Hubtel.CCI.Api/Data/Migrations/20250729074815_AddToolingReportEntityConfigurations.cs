using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Repositories.Migrations
{
    /// <inheritdoc />
    public partial class AddToolingReportEntityConfigurations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ToolingReport",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ProductGroup = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: false),
                    Domain = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    GeneralOverview = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Projects\":0,\"Repositories\":0,\"NumberOfProducts\":0,\"TotalIssues\":0,\"SecurityIssues\":0}'::jsonb"),
                    ExecutiveSummary = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"CurrentState\":[],\"KeyIssues\":[]}'::jsonb"),
                    KeyMetricsOverview = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Repositories\":0,\"ActiveProjects\":0,\"CriticalIssues\":0}'::jsonb"),
                    DetailedProjectStatus = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    IssuesSummary = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    PriorityActions = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'[]'::jsonb"),
                    Recommendation = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Overview\":null,\"ExpectedBenefits\":[],\"SuccessMetrics\":[]}'::jsonb"),
                    PlugAndPlaySolutionsInUse = table.Column<List<string>>(type: "text[]", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ToolingReport", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ToolingReport");
        }
    }
}
