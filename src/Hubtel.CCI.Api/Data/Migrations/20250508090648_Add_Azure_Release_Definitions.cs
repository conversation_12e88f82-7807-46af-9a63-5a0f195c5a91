using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Azure_Release_Definitions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AzureReleaseDefinitions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Source = table.Column<string>(type: "text", nullable: true),
                    Revision = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"DisplayName\":null,\"Url\":null,\"Links\":{\"Avatar\":{\"Href\":null}},\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null,\"Descriptor\":null}'::jsonb"),
                    CreatedOn = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ModifiedBy = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"DisplayName\":null,\"Url\":null,\"Links\":{\"Avatar\":{\"Href\":null}},\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null,\"Descriptor\":null}'::jsonb"),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeletedAzure = table.Column<bool>(type: "boolean", nullable: false),
                    IsDisabled = table.Column<bool>(type: "boolean", nullable: false),
                    ReleaseNameFormat = table.Column<string>(type: "text", nullable: true),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    DefinitionId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Path = table.Column<string>(type: "text", nullable: true),
                    Url = table.Column<string>(type: "text", nullable: true),
                    CompositeDefinitionId = table.Column<string>(type: "text", nullable: true),
                    Links = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Avatar\":{\"Href\":null}}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AzureReleaseDefinitions", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AzureReleaseDefinitions_CompositeDefinitionId",
                table: "AzureReleaseDefinitions",
                column: "CompositeDefinitionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AzureReleaseDefinitions");
        }
    }
}
