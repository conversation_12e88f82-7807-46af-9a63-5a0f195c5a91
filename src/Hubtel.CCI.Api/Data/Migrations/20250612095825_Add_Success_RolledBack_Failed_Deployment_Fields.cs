using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Success_RolledBack_Failed_Deployment_Fields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AzureFailedDeployments",
                table: "AzureReleaseDefinitionDeployments",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");

            migrationBuilder.AddColumn<string>(
                name: "RolledBackDeployments",
                table: "AzureReleaseDefinitionDeployments",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");

            migrationBuilder.AddColumn<string>(
                name: "SuccessfulDeployments",
                table: "AzureReleaseDefinitionDeployments",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AzureFailedDeployments",
                table: "AzureReleaseDefinitionDeployments");

            migrationBuilder.DropColumn(
                name: "RolledBackDeployments",
                table: "AzureReleaseDefinitionDeployments");

            migrationBuilder.DropColumn(
                name: "SuccessfulDeployments",
                table: "AzureReleaseDefinitionDeployments");
        }
    }
}
