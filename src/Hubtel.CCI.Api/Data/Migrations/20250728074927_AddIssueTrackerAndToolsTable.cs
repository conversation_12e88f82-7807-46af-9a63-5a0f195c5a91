using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddIssueTrackerAndToolsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ToolingTool",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Domain = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    ToolType = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: false),
                    DocumentationUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    LatestVersion = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ToolingTool", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "IssueTracker",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ReportedBy = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: false),
                    RecordedBy = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: false),
                    ProductGroup = table.Column<List<string>>(type: "text[]", nullable: false),
                    ProductTeam = table.Column<List<string>>(type: "text[]", nullable: false),
                    ToolId = table.Column<string>(type: "text", nullable: false),
                    ServicesAffected = table.Column<List<string>>(type: "text[]", nullable: false),
                    Status = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IncidentDescription = table.Column<string>(type: "text", nullable: false),
                    ActionTaken = table.Column<string>(type: "text", nullable: false),
                    Domain = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AssignedTo = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: true),
                    Severity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ToolVersion = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IssueTracker", x => x.Id);
                    table.ForeignKey(
                        name: "FK_IssueTracker_ToolingTool_ToolId",
                        column: x => x.ToolId,
                        principalTable: "ToolingTool",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_IssueTracker_ToolId",
                table: "IssueTracker",
                column: "ToolId");

            migrationBuilder.CreateIndex(
                name: "IX_ToolingTool_Name",
                table: "ToolingTool",
                column: "Name",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "IssueTracker");

            migrationBuilder.DropTable(
                name: "ToolingTool");
        }
    }
}
