// <auto-generated />
using System;
using System.Collections.Generic;
using Hubtel.CCI.Api.Data;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250801114616_Added_DCI_Ranking_Tables")]
    partial class Added_DCI_Ranking_Tables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.AzureReleaseDefinition", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Comment")
                        .HasColumnType("text");

                    b.Property<string>("CompositeDefinitionId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"DisplayName\":null,\"Url\":null,\"Links\":{\"Avatar\":{\"Href\":null}},\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null,\"Descriptor\":null}'::jsonb");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("DefinitionId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeletedAzure")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDisabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Links")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Avatar\":{\"Href\":null}}'::jsonb");

                    b.Property<string>("ModifiedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"DisplayName\":null,\"Url\":null,\"Links\":{\"Avatar\":{\"Href\":null}},\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null,\"Descriptor\":null}'::jsonb");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .HasColumnType("text");

                    b.Property<string>("ProjectName")
                        .HasColumnType("text");

                    b.Property<string>("ReleaseNameFormat")
                        .HasColumnType("text");

                    b.Property<int>("Revision")
                        .HasColumnType("integer");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompositeDefinitionId");

                    b.ToTable("AzureReleaseDefinitions");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.AzureReleaseDefinitionDeployment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AzureFailedDeployments")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("CompositeDefinitionId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("DefinitionId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DefinitionName")
                        .HasColumnType("text");

                    b.Property<string>("DefinitionProjectName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReleaseDeployments")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("RolledBackDeployments")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("SuccessfulDeployments")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<decimal>("TotalDeployments")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalDeploymentsFailed")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalDeploymentsFailedAzure")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalDeploymentsInRolledBack")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalDeploymentsSuccess")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("CompositeDefinitionId");

                    b.ToTable("AzureReleaseDefinitionDeployments");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.BypassedPr", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ArtifactId")
                        .HasColumnType("text");

                    b.Property<string>("ClosedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"DisplayName\":null,\"Url\":null,\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null}'::jsonb");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("CodeReviewId")
                        .HasColumnType("integer");

                    b.Property<string>("CompletionOptions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"MergeCommitMessage\":null,\"MergeStrategy\":null,\"BypassPolicy\":false,\"BypassReason\":null}'::jsonb");

                    b.Property<DateTime?>("CompletionQueueTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"DisplayName\":null,\"Url\":null,\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null}'::jsonb");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MergeStatus")
                        .HasColumnType("text");

                    b.Property<int>("PullRequestId")
                        .HasColumnType("integer");

                    b.Property<string>("Repository")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Id\":null,\"Name\":null,\"Url\":null,\"Project\":{\"Id\":null,\"Name\":null}}'::jsonb");

                    b.Property<string>("RepositoryId")
                        .HasColumnType("text");

                    b.Property<string>("Reviewers")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ClosedDate");

                    b.HasIndex("CreationDate");

                    b.ToTable("BypassedPrs");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciEngineerRanking", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("CurrentScore")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationEndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationStartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("Rankings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Backend\":[],\"Overall\":[],\"Frontend\":[]}'::jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.ToTable("CciEngineerRankings");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciEngineerScore", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal>("AverageComplexity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("AverageCoverage")
                        .HasColumnType("numeric");

                    b.Property<decimal>("AverageDuplications")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("EngineerDomain")
                        .HasColumnType("text");

                    b.Property<string>("EngineerEmail")
                        .HasColumnType("text");

                    b.Property<string>("EngineerId")
                        .HasColumnType("text");

                    b.Property<string>("EngineerName")
                        .HasColumnType("text");

                    b.Property<string>("EngineerPrMetrics")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"EngineerSonarQubeMetrics\":[],\"CciScore\":[]}'::jsonb");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<decimal>("Score")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalBugs")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalCodeSmells")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalLinesOfCode")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalPullRequests")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalSecurityHotspots")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalVulnerabilities")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.ToTable("CciEngineerScores");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciEngineersScoreStatistic", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("EngineersScoreStatistics")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Overview\":{\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalLinesOfCode\":0.0,\"TotalPrs\":0.0,\"TotalActiveEngineers\":0.0,\"AverageCoverage\":0.0,\"OverallRating\":0.0,\"AverageDuplications\":0.0},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"LinesOfCode\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"ScoreRanking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"CompletedPullRequests\":0.0,\"Engineers\":0.0,\"AverageScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"TotalLinesOfCode\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Frontend\":{\"CompletedPullRequests\":0.0,\"Engineers\":0.0,\"AverageScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"TotalLinesOfCode\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0},\"Overall\":{\"CompletedPullRequests\":0.0,\"Engineers\":0.0,\"AverageScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"TotalLinesOfCode\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0}}}'::jsonb");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.ToTable("CciEngineersScoreStatistics");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciProductTeamsScoreTrend", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal?>("AverageScore")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ProductGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupName")
                        .HasColumnType("text");

                    b.Property<string>("ProductScope")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamId")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<decimal?>("ScoreChange")
                        .HasColumnType("numeric");

                    b.Property<string>("TrendDirection")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("ProductScope");

                    b.HasIndex("PublicationDate");

                    b.HasIndex("PublicationWeek");

                    b.HasIndex("ProductTeamId", "PublicationWeek", "ProductScope");

                    b.ToTable("CciProductTeamsScoreTrends");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciProductsRanking", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("CurrentScore")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationEndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationStartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("Rankings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Backend\":[],\"Overall\":[],\"Frontend\":[]}'::jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.HasIndex("PublicationWeek");

                    b.HasIndex("PublicationStartDate", "PublicationEndDate");

                    b.ToTable("CciProductsRankings");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciRepositoryScore", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal?>("Average")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Bugs")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BugsComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CodeSmells")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CodeSmellsComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CognitiveComplexity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CognitiveComplexityComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Coverage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CoverageComputation")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("DuplicatedLinesDensity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("DuplicatedLinesDensityComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("FinalAverage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("FrameworkUpgradeComputation")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("LinesOfCode")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("NonCommentedLinesOfCode")
                        .HasColumnType("numeric");

                    b.Property<string>("ProductGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupName")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamId")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationEndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationStartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<decimal?>("RelativeCognitiveComplexity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("RelativeCognitiveComplexityComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ReliabilityRating")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ReliabilityRatingComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ReopenedIssues")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ReopenedIssuesComputation")
                        .HasColumnType("numeric");

                    b.Property<string>("RepositoryId")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryName")
                        .HasColumnType("text");

                    b.Property<string>("RepositorySonarQubeKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RepositoryType")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryUrl")
                        .HasColumnType("text");

                    b.Property<decimal?>("SecurityHotspots")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("SecurityHotspotsComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("SecurityRating")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("SecurityRatingComputation")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("SemanticScoreComputation")
                        .HasColumnType("numeric");

                    b.Property<bool>("SonarQubeMetricsAcquired")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("Vulnerabilities")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("VulnerabilitiesComputation")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.HasIndex("PublicationWeek");

                    b.HasIndex("RepositoryId");

                    b.HasIndex("PublicationStartDate", "PublicationEndDate");

                    b.ToTable("CciRepositoryScores");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciRepositoryScoreStatistic", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("ScoreStatistic")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Overview\":{\"CurrentScore\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0,\"TotalRelativeCognitiveComplexity\":0.0,\"TotalNonCommentedLinesOfCode\":0.0,\"TotalLinesOfCode\":0.0},\"Highlights\":{\"Highest\":{\"Name\":null,\"Score\":0.0},\"Lowest\":{\"Name\":null,\"Score\":0.0}},\"PerformanceByMetrics\":{\"Bugs\":[],\"CodeSmells\":[],\"Vulnerabilities\":[],\"SecurityHotspots\":[],\"Duplications\":[],\"CodeCoverage\":[],\"CognitiveComplexity\":[]},\"Ranking\":{\"Backend\":[],\"Frontend\":[],\"Overall\":[]},\"OverviewByScope\":{\"Backend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0,\"TotalRelativeCognitiveComplexity\":0.0,\"TotalNonCommentedLinesOfCode\":0.0,\"TotalLinesOfCode\":0.0},\"Frontend\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0,\"TotalRelativeCognitiveComplexity\":0.0,\"TotalNonCommentedLinesOfCode\":0.0,\"TotalLinesOfCode\":0.0},\"Overall\":{\"Repositories\":0,\"Score\":0.0,\"TotalBugs\":0.0,\"TotalCodeSmells\":0.0,\"TotalVulnerabilities\":0.0,\"TotalSecurityHotspots\":0.0,\"AverageDuplications\":0.0,\"AverageCodeCoverage\":0.0,\"TotalCognitiveComplexity\":0.0,\"TotalRelativeCognitiveComplexity\":0.0,\"TotalNonCommentedLinesOfCode\":0.0,\"TotalLinesOfCode\":0.0}},\"WeeklyFinalAverageTrend\":{\"Frontend\":[],\"Backend\":[],\"Overall\":[]},\"IssueDistribution\":{\"Backend\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0},\"Frontend\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0},\"Overall\":{\"Bugs\":0.0,\"CodeSmells\":0.0,\"Vulnerabilities\":0.0,\"SecurityHotspots\":0.0}},\"MonthlyFinalAverageTrend\":{\"Frontend\":[],\"Backend\":[],\"Overall\":[]}}'::jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.HasIndex("PublicationWeek");

                    b.ToTable("CciRepositoryScoreStatistics");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciRoadMap", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal?>("Bugs")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CodeSmells")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Coverage")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("DuplicatedLines")
                        .HasColumnType("numeric");

                    b.Property<string>("EngineerAssignedId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EngineerName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ProductTeamId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamName")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RepositoryName")
                        .HasColumnType("text");

                    b.Property<decimal?>("SecurityHotspots")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("StartingBugs")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StartingCodeSmells")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StartingCoverage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StartingDuplicatedLines")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StartingSecurityHotspots")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StartingVulnerabilities")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TargetBugs")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TargetCodeSmells")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TargetCoverage")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("TargetDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("TargetDuplicatedLines")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TargetSecurityHotspots")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TargetVulnerabilities")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("Vulnerabilities")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.ToTable("CciRoadMaps");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciRoadMapMetricUpdateRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CciRoadMapRecords")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Items\":[]}'::jsonb");

                    b.Property<string>("CciRoadMaps")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Items\":[]}'::jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("RunDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("RunEndTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("RunStartTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("RunDate");

                    b.ToTable("CciRoadMapMetricUpdateRecords");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.CciRoadMapRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Bugs")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb");

                    b.Property<string>("CodeSmells")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb");

                    b.Property<string>("Coverage")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("DuplicatedLines")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("OverallQuality")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Start\":0.0,\"Current\":0.0,\"Target\":0.0}'::jsonb");

                    b.Property<string>("Product")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Id\":\"\",\"Name\":\"\"}'::jsonb");

                    b.Property<string>("Repository")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Id\":\"\",\"Name\":\"\",\"Type\":\"\"}'::jsonb");

                    b.Property<string>("SecurityHotspots")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Vulnerabilities")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"StartWeek\":0.0,\"EndWeek\":0.0,\"StartValue\":0.0,\"CurrentValue\":0.0,\"TargetValue\":0.0,\"EngineerAssigned\":{\"Id\":\"\",\"Name\":\"\"}}'::jsonb");

                    b.HasKey("Id");

                    b.ToTable("CciRoadMapRecords");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.DciProductsRanking", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("CurrentScore")
                        .HasColumnType("numeric");

                    b.Property<string>("DciBreakDown")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"ProductGroupBreakDown\":{\"Items\":[]},\"ProductTeamBreakDown\":{\"Items\":[]},\"RepositoryBreakDown\":{\"Items\":[]}}'::jsonb");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationEndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationStartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("Rankings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Backend\":[],\"Overall\":[],\"Frontend\":[]}'::jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.HasIndex("PublicationWeek");

                    b.HasIndex("PublicationStartDate", "PublicationEndDate");

                    b.ToTable("DciProductsRankings");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.DciServiceScore", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal?>("CodeConfidenceIndexScore")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("FinalScore")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("IssueSeverityScore")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ProcessComplianceScore")
                        .HasColumnType("numeric");

                    b.Property<string>("ProductGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupName")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamId")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationEndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("PublicationStartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryId")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryName")
                        .HasColumnType("text");

                    b.Property<string>("RepositorySonarQubeKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RepositoryType")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryUrl")
                        .HasColumnType("text");

                    b.Property<string>("ServiceId")
                        .HasColumnType("text");

                    b.Property<string>("ServiceName")
                        .HasColumnType("text");

                    b.Property<decimal?>("SpeedScore")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StabilityScore")
                        .HasColumnType("numeric");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<decimal?>("SuccessRateScore")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.ToTable("DciServiceScores");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.DeploymentRequest", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<List<AffectedRepository>>("AffectedRepositories")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("AreaOfImpact")
                        .HasColumnType("text");

                    b.Property<string>("CancellationReason")
                        .HasColumnType("text");

                    b.Property<string>("CicdEngineerResponsibleId")
                        .HasColumnType("text");

                    b.Property<string>("CicdEngineerResponsibleName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpConfidenceReadinessReviewEndTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpConfidenceReadinessReviewStartTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpPlanningMeetingEndTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpPlanningMeetingStartTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpWorkingSessionEndTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpWorkingSessionReviewEndTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpWorkingSessionReviewStartTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("DcpWorkingSessionStartTime")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DeltaDiscretion")
                        .HasColumnType("text");

                    b.Property<string>("DeploymentConfidenceReportLink")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EngineerRequestId")
                        .HasColumnType("text");

                    b.Property<string>("EngineerRequestName")
                        .HasColumnType("text");

                    b.Property<bool>("HasControlledEnv")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Priority")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupName")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamId")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamName")
                        .HasColumnType("text");

                    b.Property<List<ServiceToBeDeployed>>("ServicesToBeDeployed")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("SystemBehaviorOfChange")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.ToTable("DeploymentRequests");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.Engineer", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Domain")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("JobLevel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Engineers");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.EngineerSonarQubeMetrics", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AzureRepository")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("EngineerEmail")
                        .HasColumnType("text");

                    b.Property<string>("EngineerName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("PullRequest")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Repository\":{\"Id\":null,\"Name\":null,\"Url\":null,\"Project\":{\"Id\":null,\"Name\":null,\"State\":null,\"Visibility\":null,\"LastUpdateTime\":\"0001-01-01T00:00:00\"}},\"PullRequestId\":0,\"CodeReviewId\":0,\"Status\":null,\"CreatedBy\":{\"DisplayName\":null,\"Url\":null,\"Links\":{\"Avatar\":{\"Href\":null}},\"Id\":null,\"UniqueName\":null,\"ImageUrl\":null,\"Descriptor\":null},\"CreationDate\":\"0001-01-01T00:00:00\",\"ClosedDate\":\"0001-01-01T00:00:00\",\"Title\":null,\"Description\":null,\"SourceRefName\":null,\"TargetRefName\":null,\"MergeStatus\":null,\"IsDraft\":false,\"MergeId\":null,\"LastMergeSourceCommit\":{\"CommitId\":null,\"Url\":null},\"LastMergeTargetCommit\":{\"CommitId\":null,\"Url\":null},\"LastMergeCommit\":{\"CommitId\":null,\"Url\":null},\"Reviewers\":[],\"Url\":null,\"CompletionOptions\":{\"MergeCommitMessage\":null,\"MergeStrategy\":null,\"BypassPolicy\":false,\"BypassReason\":null,\"AutoCompleteIgnoreConfigIds\":[]},\"SupportsIterations\":false,\"CompletionQueueTime\":\"0001-01-01T00:00:00\"}'::jsonb");

                    b.Property<string>("RepositoryId")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryType")
                        .HasColumnType("text");

                    b.Property<string>("SonarQubeMetrics")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Key\":null,\"Name\":null,\"Measures\":[]}'::jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PublicationDate");

                    b.ToTable("EngineerSonarQubeMetrics");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.MsTeamsBotToken", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccessToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTimeOffset>("ExpiresOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HomeAccountId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<List<string>>("Scopes")
                        .IsRequired()
                        .HasColumnType("text[]");

                    b.Property<string>("SerializedTokenCache")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("MsTeamsBotToken");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.ProductGroup", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ProductTeams")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Items\":[]}'::jsonb");

                    b.Property<string>("Supervisors")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Items\":[]}'::jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("ProductGroups");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.ProductTeam", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Members")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Items\":[]}'::jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Ranking")
                        .HasColumnType("integer");

                    b.Property<string>("Repositories")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Items\":[]}'::jsonb");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Tag")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("ProductTeams");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.ReportDeliveryRule", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ProductGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupName")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamIds")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("RecipientEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReportType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Scope")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("RecipientEmail");

                    b.HasIndex("ReportType");

                    b.ToTable("ReportDeliveryRules");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.Repository", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AzureRepositoryId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("FrameworkUpgradeComputation")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("SemanticScoreComputation")
                        .HasColumnType("numeric");

                    b.Property<string>("SonarQubeKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Repositories");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.RepositoryLanguageDistribution", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CaptureDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTracked")
                        .HasColumnType("boolean");

                    b.Property<string>("LanguageDistributions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("NclocLanguageDistribution")
                        .HasColumnType("text");

                    b.Property<string>("NewLanguageDistributionsDelta")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<decimal>("NewNcloc")
                        .HasColumnType("numeric");

                    b.Property<string>("ProductGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ProductGroupName")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamId")
                        .HasColumnType("text");

                    b.Property<string>("ProductTeamName")
                        .HasColumnType("text");

                    b.Property<string>("ProjectKey")
                        .HasColumnType("text");

                    b.Property<string>("ProjectName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryId")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryType")
                        .HasColumnType("text");

                    b.Property<decimal>("TotalNcloc")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("RepositoryLanguageDistributions");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.RepositoryLanguageStatistic", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LanguageDistributions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("LanguageRanking")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Backend\":[],\"Frontend\":[],\"Overall\":[]}'::jsonb");

                    b.Property<string>("NewLanguageDistributions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<string>("NewLanguageRanking")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{\"Backend\":[],\"Frontend\":[],\"Overall\":[]}'::jsonb");

                    b.Property<decimal>("NewLinesOfCode")
                        .HasColumnType("numeric");

                    b.Property<decimal>("NumberOfProjects")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("PublicationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PublicationWeek")
                        .HasColumnType("text");

                    b.Property<decimal>("TotalLinesOfCode")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UniqueLanguages")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("RepositoryLanguageStatistics");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.RepositorySyncLogBp", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AzureRepoId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastSyncedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("ProjectName")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryId")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Url");

                    b.ToTable("RepositorySyncLogBps");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.Service", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<bool?>("CanMisrepresentData")
                        .HasColumnType("boolean");

                    b.Property<string>("CdSetupStatus")
                        .HasColumnType("text");

                    b.Property<string>("CodeReviewRequirement")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("DeploymentStrategy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("DownstreamServicesCount")
                        .HasColumnType("integer");

                    b.Property<List<string>>("EndUsersTypes")
                        .IsRequired()
                        .HasColumnType("text[]");

                    b.Property<string>("EngineerAccountableId")
                        .HasColumnType("text");

                    b.Property<string>("EngineerAccountableName")
                        .HasColumnType("text");

                    b.Property<string>("EngineerCompetenceRequirement")
                        .HasColumnType("text");

                    b.Property<string>("EngineerResponsibleId")
                        .HasColumnType("text");

                    b.Property<string>("EngineerResponsibleName")
                        .HasColumnType("text");

                    b.Property<bool?>("HasMoreThanThousandEndUsers")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PossibilityOfDataBreach")
                        .HasColumnType("text");

                    b.Property<bool>("ReceiveMerComponent")
                        .HasColumnType("boolean");

                    b.Property<string>("ReceiveMerExposure")
                        .HasColumnType("text");

                    b.Property<string>("RepositoryId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RepositoryName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RepositoryType")
                        .HasColumnType("text");

                    b.Property<string>("RiskClassification")
                        .HasColumnType("text");

                    b.Property<bool>("SendsMerComponent")
                        .HasColumnType("boolean");

                    b.Property<string>("SendsMerExposure")
                        .HasColumnType("text");

                    b.Property<string>("ServiceFacingDirection")
                        .HasColumnType("text");

                    b.Property<bool?>("ServiceInMarket")
                        .HasColumnType("boolean");

                    b.Property<bool>("ServiceReceivesMoney")
                        .HasColumnType("boolean");

                    b.Property<string>("ServiceReleaseDefinitionId")
                        .HasColumnType("text");

                    b.Property<string>("ServiceReleaseUrl")
                        .HasColumnType("text");

                    b.Property<bool>("ServiceSendsMoney")
                        .HasColumnType("boolean");

                    b.Property<string>("ServiceType")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("TagName")
                        .HasColumnType("text");

                    b.Property<decimal?>("TotalRiskScore")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("WhoToConsult")
                        .HasColumnType("text");

                    b.Property<string>("WhoToInform")
                        .HasColumnType("text");

                    b.Property<bool?>("WillAffectOtherServices")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("Services");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.ServiceDeploymentTrail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DeploymentRequestId")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("RepositoryServiceId")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("DeploymentRequestId");

                    b.HasIndex("RepositoryServiceId");

                    b.ToTable("ServiceDeploymentTrails");
                });

            modelBuilder.Entity("Hubtel.CCI.Api.Data.Entities.ServiceDeploymentTrail", b =>
                {
                    b.HasOne("Hubtel.CCI.Api.Data.Entities.DeploymentRequest", "DeploymentRequest")
                        .WithMany()
                        .HasForeignKey("DeploymentRequestId");

                    b.HasOne("Hubtel.CCI.Api.Data.Entities.Service", "RepositoryService")
                        .WithMany()
                        .HasForeignKey("RepositoryServiceId");

                    b.Navigation("DeploymentRequest");

                    b.Navigation("RepositoryService");
                });
#pragma warning restore 612, 618
        }
    }
}
