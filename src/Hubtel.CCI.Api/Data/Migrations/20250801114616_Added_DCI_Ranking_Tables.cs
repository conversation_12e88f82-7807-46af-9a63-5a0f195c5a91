using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.CCI.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_DCI_Ranking_Tables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DciProductsRankings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PublicationWeek = table.Column<string>(type: "text", nullable: true),
                    PublicationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    PublicationStartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CurrentScore = table.Column<decimal>(type: "numeric", nullable: true),
                    Rankings = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"Backend\":[],\"Overall\":[],\"Frontend\":[]}'::jsonb"),
                    DciBreakDown = table.Column<string>(type: "jsonb", nullable: false, defaultValueSql: "'{\"ProductGroupBreakDown\":{\"Items\":[]},\"ProductTeamBreakDown\":{\"Items\":[]},\"RepositoryBreakDown\":{\"Items\":[]}}'::jsonb"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DciProductsRankings", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DciProductsRankings_PublicationDate",
                table: "DciProductsRankings",
                column: "PublicationDate");

            migrationBuilder.CreateIndex(
                name: "IX_DciProductsRankings_PublicationStartDate_PublicationEndDate",
                table: "DciProductsRankings",
                columns: new[] { "PublicationStartDate", "PublicationEndDate" });

            migrationBuilder.CreateIndex(
                name: "IX_DciProductsRankings_PublicationWeek",
                table: "DciProductsRankings",
                column: "PublicationWeek");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DciProductsRankings");
        }
    }
}
