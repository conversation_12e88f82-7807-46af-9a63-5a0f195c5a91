using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class AzureReleaseDefinitionDeploymentConfiguration: IEntityTypeConfiguration<AzureReleaseDefinitionDeployment>
{
    private const string Jsonb = "jsonb";

    public void Configure(EntityTypeBuilder<AzureReleaseDefinitionDeployment> builder)
    {
        builder.<PERSON><PERSON>ey(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

        builder
            .Property(o => o.ReleaseDeployments)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<AzureReleaseDeployment>()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AzureReleaseDeployment>>(v)!
            );
        
        builder
            .Property(o => o.SuccessfulDeployments)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<AzureReleaseDeployment>()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AzureReleaseDeployment>>(v)!
            );
        
        builder
            .Property(o => o.RolledBackDeployments)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<AzureReleaseDeployment>()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AzureReleaseDeployment>>(v)!
            );
        
        builder
            .Property(o => o.AzureFailedDeployments)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<AzureReleaseDeployment>()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AzureReleaseDeployment>>(v)!
            );
        

        builder.HasIndex(c => c.CompositeDefinitionId);
        

    }
}