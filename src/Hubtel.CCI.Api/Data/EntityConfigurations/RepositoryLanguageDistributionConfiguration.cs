using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class RepositoryLanguageDistributionConfiguration : IEntityTypeConfiguration<RepositoryLanguageDistribution>
{
    public void Configure(EntityTypeBuilder<RepositoryLanguageDistribution> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
        builder
            .Property(o => o.LanguageDistributions)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<LanguageDistribution>()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<LanguageDistribution>>(v)!
            );
        
        builder
            .Property(o => o.NewLanguageDistributionsDelta)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<LanguageDistribution>()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<LanguageDistribution>>(v)!
            );
    }
}