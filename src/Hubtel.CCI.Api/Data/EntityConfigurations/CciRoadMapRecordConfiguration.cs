using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class CciRoadMapRecordConfiguration: IEntityTypeConfiguration<CciRoadMapRecord>
{
    private const string Jsonb = "jsonb";

    public void Configure(EntityTypeBuilder<CciRoadMapRecord> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

        builder
            .Property(o => o.Bugs)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new MetricData()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<MetricData>(v)!
            );
        
        builder
            .Property(o => o.CodeSmells)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new MetricData()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<MetricData>(v)!
            );
        
        builder
            .Property(o => o.Coverage)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new MetricData()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<MetricData>(v)!
            );
        
        builder
            .Property(o => o.DuplicatedLines)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new MetricData()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<MetricData>(v)!
            );
        
        builder
            .Property(o => o.Vulnerabilities)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new MetricData()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<MetricData>(v)!
            );
        
        builder
            .Property(o => o.SecurityHotspots)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new MetricData()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<MetricData>(v)!
            );
        
        builder
            .Property(o => o.OverallQuality)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new QualityProgress()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<QualityProgress>(v)!
            );
        
        builder
            .Property(o => o.Product)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new ProductInfo()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ProductInfo>(v)!
            );
        
        builder
            .Property(o => o.Repository)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new RepositoryInfo()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<RepositoryInfo>(v)!
            );

    }
}