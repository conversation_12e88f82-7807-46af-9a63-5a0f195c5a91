using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Hubtel.CCI.Api.Data.EntityConfigurations
{
    public class ToolingToolsConfiguration : IEntityTypeConfiguration<ToolingTool>
    {
        public void Configure(EntityTypeBuilder<ToolingTool> builder)
        {
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.HasIndex(x => x.Name)
                .IsUnique();

            builder.Property(x => x.Domain).HasConversion<EnumToStringConverter<Domain>>();

            builder.Property(x=> x.ToolType)
                .IsRequired()
                .HasMaxLength(300);

            builder.Property(x => x.DocumentationUrl).HasMaxLength(500);

            builder.Property(x => x.LatestVersion)
                .IsRequired()
                .HasMaxLength(100);

        }
    }
}
