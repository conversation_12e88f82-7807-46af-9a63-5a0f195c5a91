using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class RepositoryLanguageStatisticConfiguration : IEntityTypeConfiguration<RepositoryLanguageStatistic>
{
    private const string Jsonb = "jsonb";

    public void Configure(EntityTypeBuilder<RepositoryLanguageStatistic> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
        builder
            .Property(o => o.LanguageDistributions)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<PublicationLanguageDistribution>()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<PublicationLanguageDistribution>>(v)!
            );
        
        builder
            .Property(o => o.NewLanguageDistributions)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<PublicationLanguageDistribution>()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<PublicationLanguageDistribution>>(v)!
            );
        
        builder
            .Property(o => o.LanguageRanking)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new PublicationLanguageRanking()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<PublicationLanguageRanking>(v)!
            );
        
        builder
            .Property(o => o.NewLanguageRanking)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new PublicationLanguageRanking()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<PublicationLanguageRanking>(v)!
            );
    }
}