using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class ProductTeamConfiguration : IEntityTypeConfiguration<ProductTeam>
{
    public void Configure(EntityTypeBuilder<ProductTeam> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

        builder
            .Property(o => o.Members)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new Members()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Members>(v)!
            );

        builder
            .Property(o => o.Repositories)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new Entities.Repositories()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Entities.Repositories>(v)!
            );
    }
}