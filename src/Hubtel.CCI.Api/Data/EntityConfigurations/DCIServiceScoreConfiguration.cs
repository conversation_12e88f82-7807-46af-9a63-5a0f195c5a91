using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class DciServiceScoreConfiguration: IEntityTypeConfiguration<DciServiceScore>
{
    public void Configure(EntityTypeBuilder<DciServiceScore> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
        
        builder.HasIndex(c => c.PublicationDate);

    }
}