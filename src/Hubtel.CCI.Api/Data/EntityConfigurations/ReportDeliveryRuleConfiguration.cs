using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class ReportDeliveryRuleConfiguration : IEntityTypeConfiguration<ReportDeliveryRule>
{
    public void Configure(EntityTypeBuilder<ReportDeliveryRule> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

        builder.HasIndex(o => o.RecipientEmail);

        builder.HasIndex(o => o.ReportType);
        
        builder
            .Property(r => r.ProductTeamIds)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<string>()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)!
            );
    }
}