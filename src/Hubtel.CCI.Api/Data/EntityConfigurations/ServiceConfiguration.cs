using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class ServiceConfiguration: IEntityTypeConfiguration<Service>
{
    public void Configure(EntityTypeBuilder<Service> builder)
    {
        builder.HasKey(o => o.Id);

        builder.HasQueryFilter(c => !c.Is<PERSON>eleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
    }   
}