using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class CciEngineerRankingConfiguration : IEntityTypeConfiguration<CciEngineerRanking>
{
    private const string Jsonb = "jsonb";

    public void Configure(EntityTypeBuilder<CciEngineerRanking> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

        builder
            .Property(o => o.Rankings)
            .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new EngineerRankings()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<EngineerRankings>(v)!
            );
        

        builder.HasIndex(c => c.PublicationDate);

    }
}