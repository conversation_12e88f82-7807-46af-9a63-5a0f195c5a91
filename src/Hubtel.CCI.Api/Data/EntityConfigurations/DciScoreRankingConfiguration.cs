using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class DciScoreRankingConfiguration: IEntityTypeConfiguration<DciProductsRanking>
{
    public void Configure(EntityTypeBuilder<DciProductsRanking> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
        
        builder
            .Property(o => o.Rankings)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new DciRankings()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<DciRankings>(v)!
            );
        
        builder
            .Property(o => o.DciBreakDown)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new DciBreakDown()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<DciBreakDown>(v)!
            );
        
        builder
            .HasIndex(c => c.PublicationWeek);

        builder
            .HasIndex(c => c.PublicationDate);

        builder
            .HasIndex(c => new { c.PublicationStartDate, c.PublicationEndDate });
    }
}