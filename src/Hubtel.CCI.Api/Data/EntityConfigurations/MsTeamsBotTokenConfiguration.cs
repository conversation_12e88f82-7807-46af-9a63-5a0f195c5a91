using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class MsTeamsBotTokenConfiguration : IEntityTypeConfiguration<MsTeamsBotToken>
{
    public void Configure(EntityTypeBuilder<MsTeamsBotToken> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
    }
}