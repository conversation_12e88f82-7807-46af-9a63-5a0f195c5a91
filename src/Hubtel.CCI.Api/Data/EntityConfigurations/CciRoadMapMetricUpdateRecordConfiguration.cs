using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations;

public class CciRoadMapMetricUpdateRecordConfiguration : IEntityTypeConfiguration<CciRoadMapMetricUpdateRecord>
{
    public void Configure(EntityTypeBuilder<CciRoadMapMetricUpdateRecord> builder)
    {
        builder.<PERSON><PERSON><PERSON>(o => o.Id);

        builder.HasQueryFilter(c => !c.IsDeleted);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

        builder
            .Property(o => o.CciRoadMaps)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new CciRoadMaps()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<CciRoadMaps>(v)!
            );
        
        
        builder
            .Property(o => o.CciRoadMapRecords)
            .HasColumnType("jsonb")
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new CciRoadMapRecords()) + "'::jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<CciRoadMapRecords>(v)!
            );

        builder.HasIndex(c => c.RunDate);
    }
}