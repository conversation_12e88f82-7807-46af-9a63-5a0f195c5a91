using Hubtel.CCI.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Newtonsoft.Json;

namespace Hubtel.CCI.Api.Data.EntityConfigurations
{
    public class ToolingReportEntityConfigurations : IEntityTypeConfiguration<ToolingReport>
    {
        private const string Jsonb = "jsonb";
        private const string ArrayColumnType = "text[]";
        public void Configure(EntityTypeBuilder<ToolingReport> builder)
        {
            builder.<PERSON><PERSON><PERSON>(o => o.Id);
            builder.HasQueryFilter(c => !c.IsDeleted);
            builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
            builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");

            builder.Property( x=> x.ProductGroupName).IsRequired().HasMaxLength(300);

            builder.Property(x => x.Domain)
                .IsRequired()
                .HasConversion<EnumToStringConverter<Domain>>()
                .HasMaxLength(100);

            builder.Property(x=> x.GeneralOverview)
                .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new GeneralOverview()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<GeneralOverview>(v)!
            );

            builder.Property(x=> x.ExecutiveSummary)
                   .HasColumnType(Jsonb)
            .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new ExecutiveSummary()) + $"'::{Jsonb}")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ExecutiveSummary>(v)!
            );

            builder.Property(x=> x.KeyMetricsOverview)
                      .HasColumnType(Jsonb)
                    .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new KeyMetricsOverview()) + $"'::{Jsonb}")
                    .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<KeyMetricsOverview>(v)!
            );

            builder.Property(x => x.DetailedProjectStatus)
                       .HasColumnType(Jsonb)
                    .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new List<DetailedProjectStatus>()) + $"'::{Jsonb}")
                    .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<DetailedProjectStatus>>(v)!
            );
            builder.Property(x => x.IssuesSummary)
                .HasColumnType(Jsonb)
                .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new IssuesSummary()) + $"'::{Jsonb}")
                .HasConversion(
                    v => JsonConvert.SerializeObject(v),
                    v => JsonConvert.DeserializeObject<IssuesSummary>(v)!
                );

            builder.Property(x => x.PriorityActions)
                .HasColumnType(Jsonb)
                .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new PriorityActions()) + $"'::{Jsonb}")
                .HasConversion(
                    v => JsonConvert.SerializeObject(v),
                    v => JsonConvert.DeserializeObject<PriorityActions>(v)!
                );
            builder.Property(x => x.Recommendation)
                .HasColumnType(Jsonb)
                .HasDefaultValueSql("'" + JsonConvert.SerializeObject(new Recommendations()) + $"'::{Jsonb}")
                .HasConversion(
                    v => JsonConvert.SerializeObject(v),
                    v => JsonConvert.DeserializeObject<Recommendations>(v)!
                );

            builder.Property(o=>o.PlugAndPlaySolutionsInUse)
                .IsRequired().HasColumnType(ArrayColumnType);


        }
    }
}
