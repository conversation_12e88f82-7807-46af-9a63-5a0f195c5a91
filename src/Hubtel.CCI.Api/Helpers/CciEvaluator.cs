using Hubtel.CCI.Api.Dtos.Models;

namespace Hubtel.CCI.Api.Helpers;

public static class CciEvaluator
{
    public static decimal EvaluateThreshold(decimal value, List<(decimal Threshold, decimal Percentage)> thresholds)
    {
        foreach (var (threshold, percentage) in thresholds)
        {
            if (value <= threshold)
                return percentage;
        }
        return 0m;
    }

    public static decimal EvaluateCoverage(decimal coverage)
    {
        var thresholds = new List<(decimal, decimal)>
        {
            (70m, 100m),
            (50m, 80m),
            (30m, 60m),
            (20m, 40m),
            (15m, 20m),
            (10m, 10m),
            (0m, 0m)
        };
        foreach (var (threshold, percentage) in thresholds)
        {
            if (coverage >= threshold)
                return percentage;
        }
        return 0m;
    }

    public static decimal EvaluateBugs(decimal count) => EvaluateThreshold(count, new List<(decimal, decimal)>
    {
        (0m, 100m), (5m, 80m), (10m, 60m), (20m, 40m), (30m, 20m), (40m, 10m), (50m, 0m)
    });

    public static decimal EvaluateVulnerabilities(decimal count) => EvaluateThreshold(count, new List<(decimal, decimal)>
    {
        (0m, 100m), (5m, 80m), (10m, 60m), (20m, 40m), (30m, 20m), (40m, 10m), (50m, 0m)
    });

    public static decimal EvaluateCognitiveComplexity(decimal value) => EvaluateThreshold(value, new List<(decimal, decimal)>
    {
        (500m, 100m), (1000m, 80m), (1500m, 60m), (5000m, 40m), (10000m, 20m), (15000m, 10m), (20000m, 0m)
    });
    
    public static decimal EvaluateCognitiveComplexityRelative(decimal value) => EvaluateThreshold(value, new List<(decimal, decimal)>
    {
        (50m, 100m), (75m, 80m), (100m, 60m), (150m, 40m), (200m, 20m), (250m, 0m), (300m, 0m)
    });

    public static decimal EvaluateCodeSmells(decimal count) => EvaluateThreshold(count, new List<(decimal, decimal)>
    {
        (0m, 100m), (50m, 80m), (100m, 60m), (200m, 40m), (500m, 20m), (700m, 10m), (1000m, 0m)
    });

    public static decimal EvaluateDuplicatedLinesDensity(decimal density) => EvaluateThreshold(density, new List<(decimal, decimal)>
    {
        (5m, 100m), (10m, 80m), (15m, 60m), (20m, 40m), (35m, 20m), (40m, 10m), (50m, 0m)
    });

    public static decimal EvaluateReliabilityRating(decimal rating) => EvaluateThreshold(rating, new List<(decimal, decimal)>
    {
        (1m, 100m), (2m, 80m), (3m, 60m), (4m, 40m), (5m, 0m)
    });

    public static decimal EvaluateReopenedIssues(decimal count) => EvaluateThreshold(count, new List<(decimal, decimal)>
    {
        (0m, 100m), (3m, 80m), (6m, 60m), (8m, 40m), (10m, 20m), (30m, 0m)
    });

    public static decimal EvaluateSecurityHotspots(decimal count) => EvaluateThreshold(count, new List<(decimal, decimal)>
    {
        (0m, 100m), (5m, 80m), (10m, 60m), (15m, 40m), (20m, 20m), (50m, 0m)
    });

    public static decimal EvaluateSecurityRating(decimal rating) => EvaluateThreshold(rating, new List<(decimal, decimal)>
    {
        (1m, 100m), (2m, 75m), (3m, 50m), (4m, 25m), (5m, 0m)
    });

    public static decimal ComputeEngineerFinalAverage(
        string type,
        CciMetrics metrics)
    {
        if (type == "Frontend")
        {
            return (
                0.1m * (EvaluateCoverage(metrics.Coverage) / 100m) +
                0.05m * (metrics.SemanticScore / 100m) +
                0.05m * (metrics.FrameworkUpgrade / 100m) +
                0.4m * ((EvaluateBugs(metrics.Bugs) / 100m + EvaluateVulnerabilities(metrics.Vulnerabilities) / 100m) / 2m) +
                0.4m * (( EvaluateCodeSmells(metrics.CodeSmells) / 100m + EvaluateCognitiveComplexityRelative(metrics.RelativeCognitiveComplexity) / 100m + EvaluateDuplicatedLinesDensity(metrics.DuplicatedLinesDensity) / 100m) / 3m)
            ) * 100m;
        }
        else
        {
            return (
                0.45m * (EvaluateCoverage(metrics.Coverage) / 100m) +
                0.05m * (metrics.SemanticScore / 100m) +
                0.05m * (metrics.FrameworkUpgrade / 100m) +
                0.2m * ((EvaluateBugs(metrics.Bugs) / 100m + EvaluateVulnerabilities(metrics.Vulnerabilities) / 100m) / 2m) +
                0.25m * ((EvaluateCodeSmells(metrics.CodeSmells) / 100m + EvaluateCognitiveComplexityRelative(metrics.RelativeCognitiveComplexity) / 100m + EvaluateDuplicatedLinesDensity(metrics.DuplicatedLinesDensity) / 100m) / 3m)
            ) * 100m;
        }
    }
    
}