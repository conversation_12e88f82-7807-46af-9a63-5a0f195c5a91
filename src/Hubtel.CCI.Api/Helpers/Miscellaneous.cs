using System.Globalization;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Models;

namespace Hubtel.CCI.Api.Helpers;

public static class Miscellaneous
{
    /// <summary>
    /// Curates the product teams ranking based on the provided response data.
    /// </summary>
    /// <param name="response">A dictionary containing publication information keyed by product team ID.</param>
    /// <returns>A <see cref="CciProductsRanking"/> object containing the curated rankings.</returns>
    public static CciProductsRanking CurateProductTeamsRanking(Dictionary<string, PublicationInfo> response)
    {
        var ranking = new CciProductsRanking();

        foreach (var key in response.Keys)
        {
            var publicationInfo = response[key];
            var hasBackendScope = publicationInfo.Backend.Count > 0;
            var hasFrontendScope = publicationInfo.Frontend.Count > 0;
            var hasBothScopes = hasBackendScope && hasFrontendScope;
            var backendAverageScore = publicationInfo.Backend.Count == 0
                ? 0
                : publicationInfo.Backend.Average(x => x.FinalAverage) ?? 0;
            var frontendAverageScore = publicationInfo.Frontend.Count == 0
                ? 0
                : publicationInfo.Frontend.Average(x => x.FinalAverage) ?? 0;
            var singularScore = GetSingularScopeScore(hasBackendScope, hasFrontendScope, backendAverageScore,
                frontendAverageScore);
            var overallScore = hasBothScopes ? (backendAverageScore + frontendAverageScore) / 2 : singularScore;

            if (hasBackendScope)
            {
                ranking.Rankings.Backend.Add(new RankingItem
                {
                    ProductGroupId = publicationInfo.ProductGroupId,
                    ProductGroupName = publicationInfo.ProductGroupName,
                    ProductTeamName = publicationInfo.ProductTeamName,
                    ProductTeamId = publicationInfo.ProductTeamId,
                    Rating = backendAverageScore,
                    Status = GetCciScoreStatus(backendAverageScore)
                });
            }

            if (hasFrontendScope)
            {
                ranking.Rankings.Frontend.Add(new RankingItem
                {
                    ProductGroupId = publicationInfo.ProductGroupId,
                    ProductGroupName = publicationInfo.ProductGroupName,
                    ProductTeamName = publicationInfo.ProductTeamName,
                    ProductTeamId = publicationInfo.ProductTeamId,
                    Rating = frontendAverageScore,
                    Status = GetCciScoreStatus(frontendAverageScore)
                });
            }

            ranking.Rankings.Overall.Add(new RankingItem
            {
                ProductGroupId = publicationInfo.ProductGroupId,
                ProductGroupName = publicationInfo.ProductGroupName,
                ProductTeamName = publicationInfo.ProductTeamName,
                ProductTeamId = publicationInfo.ProductTeamId,
                Rating = overallScore,
                Status = GetCciScoreStatus(overallScore)
            });
        }

        ranking.CreatedAt = DateTime.UtcNow;
        ranking.UpdatedAt = DateTime.UtcNow;
        ranking.PublicationDate = response.FirstOrDefault().Value.PublicationDate;
        ranking.PublicationWeek = response.FirstOrDefault().Value.PublicationWeek;
        ranking.PublicationStartDate = response.FirstOrDefault().Value.PublicationStartDate;
        ranking.PublicationEndDate = response.FirstOrDefault().Value.PublicationEndDate;
        ranking.CurrentScore = ranking.Rankings.Overall.Count == 0
            ? 0
            : ranking.Rankings.Overall.Average(x => x.Rating ?? 0);
        return ranking;
    }
    
    
    
    public static CciEngineerRanking CurateEngineersRanking(List<CciEngineerScore> response)
    {
        var ranking = new CciEngineerRanking();

        foreach (var engineerCci in response)
        {
            var hasBackendScope = engineerCci.EngineerDomain == ValidationConstants.RepositoryType.Backend;
            var hasFrontendScope = engineerCci.EngineerDomain == ValidationConstants.RepositoryType.Frontend;
            
            // The engineer can have only one scope
            if (hasBackendScope)
            {
                ranking.Rankings.Backend.Add(new EngineerRankingItem()
                {
                    EngineerId = engineerCci.EngineerId,
                    EngineerName = engineerCci.EngineerName,
                    EngineerEmail = engineerCci.EngineerEmail,
                    Rating = engineerCci.Score,
                    Status = GetCciScoreStatus(engineerCci.Score)
                });
            }
            
            if (hasFrontendScope)
            {
                ranking.Rankings.Frontend.Add(new EngineerRankingItem()
                {
                    EngineerId = engineerCci.EngineerId,
                    EngineerName = engineerCci.EngineerName,
                    EngineerEmail = engineerCci.EngineerEmail,
                    Rating = engineerCci.Score,
                    Status = GetCciScoreStatus(engineerCci.Score)
                });
            }
            
            ranking.Rankings.Overall.Add(new EngineerRankingItem()
            {
                EngineerId = engineerCci.EngineerId,
                EngineerName = engineerCci.EngineerName,
                EngineerEmail = engineerCci.EngineerEmail,
                Rating = engineerCci.Score,
                Status = GetCciScoreStatus(engineerCci.Score)
            });
        }
        
        ranking.CreatedAt = DateTime.UtcNow;
        ranking.UpdatedAt = DateTime.UtcNow;
        ranking.PublicationDate = response.FirstOrDefault()?.PublicationDate;
        ranking.PublicationWeek = response.FirstOrDefault()?.PublicationWeek;
        ranking.PublicationStartDate = response.FirstOrDefault()?.PublicationDate.Date.AddHours(0).AddMinutes(0).AddSeconds(0);
        ranking.PublicationEndDate = response.FirstOrDefault()?.PublicationDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        ranking.CurrentScore = ranking.Rankings.Overall.Count == 0
            ? 0
            : ranking.Rankings.Overall.Average(x => x.Rating ?? 0);
        return ranking;
    }

    /// <summary>
    /// Calculates the start and end dates of the previous week.
    /// </summary>
    /// <returns>A tuple containing the start date (Monday) and end date (Friday) of the previous week.</returns>
    public static PreviousPublicationWeekStartAndEndDates GetPreviousPublicationWeekStartAndEndDates()
    {
        var today = DateTime.Today;

        var currentDayOfWeek = (int)today.DayOfWeek;

        var currentWeekMonday = today.AddDays(-(currentDayOfWeek - 1));

        var previousWeekMonday = currentWeekMonday.AddDays(-7).Date;

        var previousWeekFriday = previousWeekMonday.AddDays(4).Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        return new PreviousPublicationWeekStartAndEndDates
            { StartDate = previousWeekMonday, EndDate = previousWeekFriday };
    }
    
    
    
    /// <summary>
    /// Returns the previous publication day (the day we last published scores *for*), 
    /// skipping weekends. Assumes we publish today's scores for the previous weekday.
    /// </summary>
    public static DateTime GetPreviousPublicationDay()
    {
        var today = DateTime.Today;

        var forDay = GetPreviousWeekday(today);

        var previousPublicationDay = GetPreviousWeekday(forDay);

        return previousPublicationDay;
    }
    
    /// <summary>
    /// Gets the current publication target day based on the current day of the week.
    /// </summary>
    /// <returns></returns>
    public static DateTime GetCurrentPublicationTargetDay()
    {
        var today = DateTime.Today;

        return today.DayOfWeek switch
        {
            DayOfWeek.Monday => today.AddDays(-3),  // Monday publishes Friday's data
            DayOfWeek.Sunday => today.AddDays(-2),  // Sunday publishes Friday's data
            DayOfWeek.Saturday => today.AddDays(-1), // Saturday publishes Friday's data
            _ => today.AddDays(-1) // Tuesday–Friday: publish for previous weekday
        };
    }


    /// <summary>
    /// Gets the current publication week based on the provided date.
    /// </summary>
    /// <param name="date"></param>
    /// <returns> A string representing the week the date is in  </returns>
    public static string GetCurrentPublicationWeek(DateTime date)
    {
        CultureInfo culture = CultureInfo.InvariantCulture;

        // Use CalendarWeekRule.FirstFourDayWeek for ISO 8601-like behavior
        var calendar = culture.Calendar;
        var weekRule = culture.DateTimeFormat.CalendarWeekRule;
        var firstDayOfWeek = culture.DateTimeFormat.FirstDayOfWeek;

        var weekNumber = calendar.GetWeekOfYear(date, weekRule, firstDayOfWeek);

        var result = $"{date.Year:D4}-{weekNumber:D2}";
        
        return result;
    }

    /// <summary>
    /// Returns the previous weekday (Mon–Fri), skipping Saturday and Sunday.
    /// </summary>
    private static DateTime GetPreviousWeekday(DateTime from)
    {
        var day = from.AddDays(-1);

        return day.DayOfWeek switch
        {
            DayOfWeek.Sunday => day.AddDays(-2),
            DayOfWeek.Saturday => day.AddDays(-1),
            _ => day
        };
    }

    /// <summary>
    /// Gets the CCI score status based on the provided value.
    /// </summary>
    /// <param name="value">The CCI score value.</param>
    /// <returns>The status corresponding to the CCI score value.</returns>
    private static string GetCciScoreStatus(decimal value)
    {
        return value switch
        {
            >= 0 and <= 20 => "Needs Immediate Attention",
            > 20 and <= 40 => "Low Confidence",
            > 40 and <= 60 => "Below Average Confidence",
            > 60 and <= 80 => "Moderate Confidence",
            > 80 and <= 90 => "High Confidence",
            > 90 and <= 100 => "Outstanding Confidence",
            _ => throw new ArgumentOutOfRangeException(nameof(value), "Value must be between 0 and 100.")
        };
    }
    
    
    public static string GetDciScoreStatus(decimal value)
    {
        var baseLabel = value switch
        {
            >= 0.9m => "Excellent",
            >= 0.75m => "Good", 
            >= 0.6m => "Moderate",
            >= 0.4m => "Low",
            _ => "Critical"
        };
        
        return baseLabel;
    }


    /// <summary>
    /// Gets the previous publication week based on the current publication week.
    /// </summary>
    /// <param name="currentPublicationWeek">The current publication week in the format "YYYY-WW".</param>
    /// <returns>The previous publication week in the format "YYYY-WW".</returns>
    public static string GetPreviousPublicationWeek(string currentPublicationWeek)
    {
        var currentWeek = currentPublicationWeek.Split('-');
        var year = int.Parse(currentWeek[0]);
        var week = int.Parse(currentWeek[1]);

        if (week == 1)
        {
            year--;
            week = 52;
        }
        else
        {
            week--;
        }

        return $"{year}-{week:D2}";
    }
    
    /// <summary>
    /// Gets the start and end dates of the previous publication day.
    /// </summary>
    /// <param name="from"></param>
    /// <returns></returns>
    public static (DateTime Start, DateTime End) GetPreviousPublicationDayRange(DateTime from)
    {
        var prev = GetPreviousWeekday(from);
        return (prev.Date, prev.Date.AddHours(23).AddMinutes(59).AddSeconds(59));
    }


    /// <summary>
    /// Calculates the trend direction based on the current and previous values.
    /// </summary>
    /// <param name="currentValue">The current value.</param>
    /// <param name="previousValue">The previous value.</param>
    /// <returns>The difference between the current value and the previous value, indicating the trend direction.</returns>
    public static string GetTrendDirection(decimal currentValue, decimal previousValue)
    {
        var value = currentValue - previousValue;

        return value switch
        {
            > 0 => "up",
            < 0 => "down",
            _ => "stable"
        };
    }


    /// <summary>
    /// Calculates the singular scope score based on the backend and frontend scope flags and their respective average scores.
    /// </summary>
    /// <param name="hasBackendScope">Indicates if the backend scope is present.</param>
    /// <param name="hasFrontendScope">Indicates if the frontend scope is present.</param>
    /// <param name="backendAverageScore">The average score for the backend scope.</param>
    /// <param name="frontendAverageScore">The average score for the frontend scope.</param>
    /// <returns>The singular scope score based on the provided scopes and their average scores.</returns>
    private static decimal GetSingularScopeScore(bool hasBackendScope, bool hasFrontendScope,
        decimal backendAverageScore, decimal frontendAverageScore)
    {
        var singularScore = hasBackendScope switch
        {
            true when !hasFrontendScope => backendAverageScore,
            false when hasFrontendScope => frontendAverageScore,
            _ => 0
        };

        return singularScore;
    }
    
    
    public static string ToPascalCase(this string text)
    {
        if (string.IsNullOrEmpty(text) || text.Length < 2)
            return text.ToUpper();

        return char.ToUpper(text[0]) + text.Substring(1);
    }
}