using FluentValidation;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Dtos.Requests.Engineer;
using Hubtel.CCI.Api.Extensions;

namespace Hubtel.CCI.Api.Validators.Engineer;

/// <summary>
///     Class validates a request to update an engineer
/// </summary>
public class UpdateEngineerRequestValidator : AbstractValidator<UpdateEngineerRequest>
{
    public UpdateEngineerRequestValidator()
    {
        RuleFor(x => x.Name);
        RuleFor(x => x.Email).EmailAddress().When(x => x.Email != null)
            .WithMessage("Email must be a valid email address");
        RuleFor(x => x.Domain)
            .ForEach(x =>
                x.Must(y => y.IsValidMemberOfConstant(typeof(ValidationConstants.EngineerDomainTypes)))
                    .WithMessage(typeof(ValidationConstants.EngineerDomainTypes).ToValidationString()))
            .When(x => x.Domain != null);
        RuleFor(x => x.JobLevel)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.EngineerJobLevelTypes)))
            .WithMessage(typeof(ValidationConstants.EngineerJobLevelTypes).ToValidationString());
    }
}