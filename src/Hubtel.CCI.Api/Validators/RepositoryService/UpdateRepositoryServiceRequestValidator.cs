using FluentValidation;
using Hubtel.CCI.Api.CommonConstants;
using Hubtel.CCI.Api.Dtos.Requests.RepositoryService;
using Hubtel.CCI.Api.Extensions;

namespace Hubtel.CCI.Api.Validators.RepositoryService;

/// <summary>
///     Class validates a request to update a repository service
/// </summary>
public class UpdateRepositoryServiceRequestValidator : AbstractValidator<UpdateRepositoryServiceRequest>
{
    public UpdateRepositoryServiceRequestValidator()
    {
        RuleFor(x => x.Name);
        RuleFor(x => x.RepositoryId);
        RuleFor(x => x.RepositoryName);
        RuleFor(x => x.TagName);
        RuleFor(x => x.RepositoryType)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.RepositoryType)))
            .When(x => x.RepositoryType != null)
            .WithMessage(typeof(ValidationConstants.RepositoryType).ToValidationString());

        RuleFor(x => x.ServiceType)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.ServiceType)))
            .WithMessage(typeof(ValidationConstants.ServiceType).ToValidationString());

        RuleFor(x => x.EngineerResponsibleId);
        RuleFor(x => x.EngineerResponsibleName);
        RuleFor(x => x.EngineerAccountableId);
        RuleFor(x => x.EngineerAccountableName);
                RuleFor(x => x.ServiceFacingDirection)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.ServiceFacingDirection)))
            .WithMessage(typeof(ValidationConstants.ServiceFacingDirection).ToValidationString());
        RuleFor(x => x.WhoToConsult);
        RuleFor(x => x.WhoToInform);
        RuleFor(x => x.CdSetupStatus);
        RuleFor(x => x.ServiceSendsMoney);
        RuleFor(x => x.ServiceReceivesMoney);
        RuleFor(x => x.EndUsersTypes)
            .ForEach(x => x.Must(y => y.IsValidMemberOfConstant(typeof(ValidationConstants.EndUsersTypes))
            ));
        RuleFor(x => x.HasMoreThanThousandEndUsers);
        RuleFor(x => x.Status)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.RepositoryServiceStatus)))
            .WithMessage(typeof(ValidationConstants.RepositoryServiceStatus).ToValidationString());
        RuleFor(x => x.CanMisrepresentData);
        RuleFor(x => x.ServiceInMarket);
        RuleFor(x => x.WillAffectOtherServices);
        RuleFor(x => x.DownstreamServicesCount);
        RuleFor(x => x.ReceiveMerComponent);
        RuleFor(x => x.SendsMerComponent);
        RuleFor(x => x.ReceiveMerExposure)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.ReceiveMerExposureTypes)))
            .WithMessage(typeof(ValidationConstants.ReceiveMerExposureTypes).ToValidationString());
        RuleFor(x => x.SendsMerExposure)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.SendsMerExposureTypes)))
            .WithMessage(typeof(ValidationConstants.SendsMerExposureTypes).ToValidationString());
        RuleFor(x => x.PossibilityOfDataBreach);
        RuleFor(x => x.TotalRiskScore);
        RuleFor(x => x.RiskClassification)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.RiskClassificationTypes)))
            .WithMessage(typeof(ValidationConstants.RiskClassificationTypes).ToValidationString());
        RuleFor(x => x.DeploymentStrategy)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.DeploymentStrategyTypes)))
            .WithMessage(typeof(ValidationConstants.DeploymentStrategyTypes).ToValidationString());
        RuleFor(x => x.EngineerCompetenceRequirement)
            .Must(x => x == null || x.IsValidMemberOfConstant(typeof(ValidationConstants.EngineerCompetenceRequirementTypes)))
            .WithMessage(typeof(ValidationConstants.EngineerCompetenceRequirementTypes).ToValidationString());

        RuleFor(x => x.ServiceFacingDirection);
        RuleFor(x => x.WhoToConsult);
        RuleFor(x => x.WhoToInform);
        RuleFor(x => x.CdSetupStatus);
        RuleFor(x => x.ServiceSendsMoney);
        RuleFor(x => x.ServiceReceivesMoney);
        RuleFor(x => x.EndUsersTypes);
        RuleFor(x => x.HasMoreThanThousandEndUsers);
        RuleFor(x => x.CanMisrepresentData);
        RuleFor(x => x.ServiceInMarket);
        RuleFor(x => x.WillAffectOtherServices);
        RuleFor(x => x.DownstreamServicesCount);
        RuleFor(x => x.ReceiveMerComponent);
        RuleFor(x => x.SendsMerComponent);
        RuleFor(x => x.ReceiveMerExposure);
        RuleFor(x => x.SendsMerExposure);
        RuleFor(x => x.PossibilityOfDataBreach);
        RuleFor(x => x.TotalRiskScore);
        RuleFor(x => x.RiskClassification);
        RuleFor(x => x.DeploymentStrategy);
        RuleFor(x => x.EngineerCompetenceRequirement);
        RuleFor(x => x.CodeReviewRequirement);
        RuleFor(x => x.ServiceReleaseDefinitionId);
        RuleFor(x => x.ServiceReleaseUrl);
        RuleFor(x => x.ServiceReleaseUrl);
    }
}