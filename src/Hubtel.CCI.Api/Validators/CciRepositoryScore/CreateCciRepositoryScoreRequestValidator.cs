using FluentValidation;
using Hubtel.CCI.Api.Dtos.Requests.CciRepositoryScore;

namespace Hubtel.CCI.Api.Validators.CciRepositoryScore;

/// <summary>
///     Class validates a request to create a CciRepositoryScore
/// </summary>
public class CreateCciRepositoryScoreRequestValidator : AbstractValidator<CreateCciRepositoryScoreRequest>
{
    public CreateCciRepositoryScoreRequestValidator()
    {
        RuleFor(x => x.RepositorySonarQubeKey)
            .NotNull()
            .NotEmpty().WithMessage("RepositorySonarQubeKey must not be empty")
            .NotNull().WithMessage("RepositorySonarQubeKey cannot be null");

        RuleFor(x => x.RepositoryId)
            .NotNull()
            .NotEmpty().WithMessage("RepositoryId must not be empty")
            .NotNull().WithMessage("RepositoryId cannot be null");

        RuleFor(x => x.SonarQubeMetricsAcquired)
            .NotNull().WithMessage("SonarQubeMetricsAcquired cannot be null");

        RuleFor(x => x.FinalAverage)
            .InclusiveBetween(0, 100).WithMessage("FinalAverage must be a positive number");

        RuleFor(x => x.Average)
            .InclusiveBetween(0, 100).WithMessage("Average must be a positive number");

        RuleFor(x => x.CodeSmellsComputation)
            .InclusiveBetween(0, 100).WithMessage("CodeSmellsComputation must be a positive number");

        RuleFor(x => x.Coverage)
            .InclusiveBetween(0, 100).WithMessage("Coverage must be between 0 and 100");

        RuleFor(x => x.Bugs)
            .GreaterThanOrEqualTo(0).WithMessage("Bugs must be a positive number");

        RuleFor(x => x.BugsComputation)
            .InclusiveBetween(0, 100).WithMessage("BugsComputation must be a positive number");

        RuleFor(x => x.CodeSmells)
            .GreaterThanOrEqualTo(0).WithMessage("CodeSmells must be a positive number");

        RuleFor(x => x.CoverageComputation)
            .InclusiveBetween(0, 100).WithMessage("CoverageComputation must be a positive number");

        RuleFor(x => x.DuplicatedLinesDensity)
            .InclusiveBetween(0, 100).WithMessage("DuplicatedLinesDensity must be between 0 and 100");

        RuleFor(x => x.DuplicatedLinesDensityComputation)
            .InclusiveBetween(0, 100).WithMessage("DuplicatedLinesDensityComputation must be a positive number");

        RuleFor(x => x.Vulnerabilities)
            .GreaterThanOrEqualTo(0).WithMessage("Vulnerabilities must be a positive number");

        RuleFor(x => x.VulnerabilitiesComputation)
            .InclusiveBetween(0, 100).WithMessage("VulnerabilitiesComputation must be a positive number");

        RuleFor(x => x.SecurityHotspots)
            .GreaterThanOrEqualTo(0).WithMessage("SecurityHotspots must be a positive number");

        RuleFor(x => x.SecurityHotspotsComputation)
            .InclusiveBetween(0, 100).WithMessage("SecurityHotspotsComputation must be a positive number");

        RuleFor(x => x.SecurityRating)
            .InclusiveBetween(0, 5).WithMessage("SecurityRating must be between 0 and 5");

        RuleFor(x => x.SecurityRatingComputation)
            .InclusiveBetween(0, 100).WithMessage("SecurityRatingComputation must be a positive number");

        RuleFor(x => x.ReliabilityRating)
            .InclusiveBetween(0, 5).WithMessage("ReliabilityRating must be between 0 and 5");

        RuleFor(x => x.ReopenedIssuesComputation)
            .InclusiveBetween(0, 100).WithMessage("ReopenedIssuesComputation must be a positive number");

        RuleFor(x => x.CognitiveComplexity)
            .GreaterThanOrEqualTo(0).WithMessage("CognitiveComplexity must be a positive number");

        RuleFor(x => x.CognitiveComplexityComputation)
            .InclusiveBetween(0, 100).WithMessage("CognitiveComplexityComputation must be a positive number");

        RuleFor(x => x.ReliabilityRatingComputation)
            .InclusiveBetween(0, 100).WithMessage("ReliabilityRatingComputation must be a positive number");

        RuleFor(x => x.SemanticScoreComputation)
            .InclusiveBetween(0, 100).WithMessage("SemanticScoreComputation must be a positive number");

        RuleFor(x => x.FrameworkUpgradeComputation)
            .InclusiveBetween(0, 100).WithMessage("FrameworkUpgradeComputation must be a positive number");

        RuleFor(x => x.ReopenedIssues)
            .GreaterThanOrEqualTo(0).WithMessage("ReopenedIssues must be a positive number");

        RuleFor(x => x.PublicationDate)
            .LessThanOrEqualTo(DateTime.Now).WithMessage("PublicationDate cannot be in the future");

        RuleFor(x => x.PublicationEndDate)
            .GreaterThanOrEqualTo(x => x.PublicationStartDate)
            .WithMessage("PublicationEndDate must be after PublicationStartDate");
        
        RuleFor(x => x.Status)
            .NotNull()
            .NotEmpty().WithMessage("Status must not be empty")
            .NotNull().WithMessage("Status cannot be null");
        
        RuleFor(x => x.NonCommentedLinesOfCode)
            .GreaterThanOrEqualTo(0).WithMessage("NonCommentedLinesOfCode must be a positive number");

        RuleFor(x => x.LinesOfCode)
            .GreaterThanOrEqualTo(0).WithMessage("LinesOfCode must be a positive number");
        
        RuleFor(x => x.CognitiveComplexityComputation)
            .GreaterThanOrEqualTo(0).WithMessage("CognitiveComplexityComputation must be a positive number");

        RuleFor(x => x.RelativeCognitiveComplexityComputation)
            .GreaterThanOrEqualTo(0).WithMessage("RelativeCognitiveComplexityComputation must be a positive number");
    }
}