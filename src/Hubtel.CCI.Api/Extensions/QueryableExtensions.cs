using System.Linq.Expressions;

namespace Hubtel.CCI.Api.Extensions;

public static class QueryableExtensions
{
    public static IQueryable<T> OrderByDynamic<T>(this IQueryable<T> source, string orderByProperty, bool desc)
    {
        var command = desc ? "OrderByDescending" : "OrderBy";
        var type = typeof(T);
        var property = type.GetProperty(orderByProperty);
        if (property == null) throw new ArgumentException($"Property '{orderByProperty}' not found on type '{type.Name}'");

        var parameter = Expression.Parameter(type, "p");
        var propertyAccess = Expression.MakeMemberAccess(parameter, property);
        var orderByExpression = Expression.Lambda(propertyAccess, parameter);
        var resultExpression = Expression.Call(typeof(Queryable), command, new Type[] { type, property.PropertyType },
            source.Expression, Expression.Quote(orderByExpression));
        return source.Provider.CreateQuery<T>(resultExpression);
    }
    
    //WhereIf Extension Method
    public static IQueryable<T> WhereIf<T>(this IQueryable<T> source, bool condition, Expression<Func<T, bool>> predicate)
    {
        return condition ? source.Where(predicate) : source;
    }
    
}