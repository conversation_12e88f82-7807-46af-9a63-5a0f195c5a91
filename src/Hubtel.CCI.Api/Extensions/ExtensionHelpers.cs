using Hubtel.CCI.Api.Dtos.Common;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.CCI.Api.Extensions;

/// <summary>
/// Provides an extension method for IQueryable to offer pagination functionality.
/// </summary>
public static class ExtensionHelpers
{
    /// <summary>
    /// Extension method for IQueryable to provide pagination functionality.
    /// </summary>
    /// <typeparam name="T">The type of the elements of source.</typeparam>
    /// <param name="query">The IQueryable to paginate.</param>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="pageSize">The number of elements in each page.</param>
    /// <param name="ct">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a PagedResult that has the results for the requested page.</returns>
    public static async Task<PagedResult<T>> GetPagedAsync<T>(this IQueryable<T> query, int page, int pageSize,
        CancellationToken ct = default) where T : class
    {
        var result = new PagedResult<T>
        {
            PageIndex = page,
            PageSize = pageSize,
            TotalCount = await query.CountAsync(ct),
        };

        var pageCount = (double)result.TotalCount / pageSize;
        result.TotalPages = (int)Math.Ceiling(pageCount);
        result.UpperBound = result.TotalCount == 0 ? 0 : (page * pageSize) + 1;
        result.LowerBound = (page * pageSize) < result.TotalCount ? (page * pageSize) : result.TotalCount;

        result.Results = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync(ct);

        return result;
    }
    
    
    
    /// <summary>
    /// Extracts the Azure DevOps project name (the segment after 'hubtel/' and before '/_git').
    /// </summary>
    public static string ToProjectName(this string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            throw new ArgumentException("URL cannot be null or empty.", nameof(url));

        const string marker = "hubtel/";
        const string gitMarker = "/_git";

        var startIndex = url.IndexOf(marker, StringComparison.OrdinalIgnoreCase);
        if (startIndex == -1)
            return string.Empty;

        startIndex += marker.Length;
        var endIndex = url.IndexOf(gitMarker, startIndex, StringComparison.OrdinalIgnoreCase);
        return endIndex == -1 ? string.Empty : url.Substring(startIndex, endIndex - startIndex);
    }
    
    
    /// <summary>
    /// Extracts the release definitionId from an Azure DevOps release URL and parses it as an integer.
    /// Example:
    /// returns 662
    /// </summary>
    public static int? GetReleaseDefinitionId(this string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            throw new ArgumentException("URL cannot be null or empty.", nameof(url));

        var uri = new Uri(url);
        var query = uri.Query; // e.g. ?_a=releases&view=mine&definitionId=662
        var queryParams = System.Web.HttpUtility.ParseQueryString(query);
        var idValue = queryParams["definitionId"];

        if (int.TryParse(idValue, out var id))
            return id;
        return null; 
    }
}