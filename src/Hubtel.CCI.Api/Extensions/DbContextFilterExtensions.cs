using Hubtel.CCI.Api.Constants;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.DeploymentConfidenceProcess;

namespace Hubtel.CCI.Api.Extensions;

public static class DbContextFilterExtensions
{
    public static IQueryable<DeploymentRequest> ApplyDeploymentRequestFilter(this IQueryable<DeploymentRequest> query,
        DcpRequestFilter filter)
    {
        var deploymentRequests = query
            .Where(item=>!item.IsDeleted)
            .WhereIf(!string.IsNullOrWhiteSpace(filter.EngineerRequestId),
                item => string.Equals(item.EngineerRequestId, filter.EngineerRequestId))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.EngineerRequestName),
                item => string.Equals(item.EngineerRequestName, filter.EngineerRequestName))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.ProductGroupId),
                item => string.Equals(item.ProductGroupId, filter.ProductGroupId))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.ProductGroupName),
                item => string.Equals(item.ProductGroupName, filter.ProductGroupName))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.ProductTeamName),
                item => string.Equals(item.ProductTeamName, filter.ProductTeamName))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.ProductTeamId),
                item => string.Equals(item.ProductTeamId, filter.ProductTeamId))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.Priority),
                item => string.Equals(item.Priority, filter.Priority))
            .WhereIf(!string.IsNullOrWhiteSpace(filter.AreaOfImpact),
                item => string.Equals(item.AreaOfImpact, filter.AreaOfImpact))
            .WhereIf(filter.Status.Count>0, item => filter.Status.Contains(item.Status!))
            .WhereIf(filter.HasControlledEnv.HasValue, item => item.HasControlledEnv == filter.HasControlledEnv)
            .OrderBy(x => x.CreatedAt)
            .ThenBy(x =>
                x.Status == DeploymentRequestStatus.InProgress ? 0 :
                x.Status == DeploymentRequestStatus.NotStarted ? 1 :
                x.Status == DeploymentRequestStatus.Completed ? 2 :
                x.Status == DeploymentRequestStatus.Rejected ? 3 :
                x.Status == DeploymentRequestStatus.Cancelled ? 4 : 5);

        return deploymentRequests;
    }

}