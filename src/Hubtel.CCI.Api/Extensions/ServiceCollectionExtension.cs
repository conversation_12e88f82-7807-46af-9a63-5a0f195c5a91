using Akka.Actor;
using Akka.Hosting;
using Confluent.Kafka.Extensions.OpenTelemetry;
using Gelf.Extensions.Logging;
using Hubtel.CCI.Api.Actors;
using Hubtel.CCI.Api.Authentication;
using Hubtel.CCI.Api.Dtos.Common;
using Hubtel.CCI.Api.Dtos.Models;
using Hubtel.CCI.Api.Dtos.Requests.ProductGroup;
using Hubtel.CCI.Api.Dtos.Responses.ToolingReport;
using Hubtel.CCI.Api.Filters;
using Hubtel.CCI.Api.Options;
using Hubtel.CCI.Api.Services.Interfaces;
using Hubtel.OpenTelemetry.Instrumentation.Options;
using Hubtel.Otel.Instrumentation;
using Mapster;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Npgsql;
using OpenTelemetry.Context.Propagation;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using StackExchange.Redis;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using B3Propagator = OpenTelemetry.Extensions.Propagators.B3Propagator;
using Sdk = OpenTelemetry.Sdk;
using Hubtel.CCI.Api.Data.Entities;
using Hubtel.CCI.Api.Dtos.Requests.ToolingReport;

namespace Hubtel.CCI.Api.Extensions;

public static class ServiceCollectionExtension
{
    private const string LoggingSection = "Logging";
    public static IServiceCollection AddHubtelBearerAuth(
        this IServiceCollection services,
        Action<BearerTokenConfig> configure)
    {
        services.Configure(configure);
        var bearerConfig = new BearerTokenConfig();
        configure(bearerConfig);

        services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.Events = new JwtBearerEvents()
                {
                    OnTokenValidated = OnTokenValidated()
                };
                x.SaveToken = true;
                x.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = bearerConfig.Issuer,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey =
                        new SymmetricSecurityKey(Encoding.UTF8.GetBytes(bearerConfig.SigningKey)),
                    ValidAudience = bearerConfig.Audience,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    RequireExpirationTime = true,
                    ValidAlgorithms = new[] { SecurityAlgorithms.HmacSha256 },
                    ClockSkew = TimeSpan.FromMinutes(1)
                };
            });
        return services;
    }

    private static Func<TokenValidatedContext, Task> OnTokenValidated()
    {
        return async ctx =>
        {
            await Task.CompletedTask;


            if (ctx.SecurityToken is not JsonWebToken token)
            {
                ctx.Fail("Invalid Token");
                return;
            }

            var userEmail = token.Claims.FirstOrDefault(c => c.Type == "userName")?.Value;

            if (userEmail is null)
            {
                ctx.Fail("Invalid Token");
                return;
            }

            var lookupService = ctx.HttpContext.RequestServices.GetRequiredService<ILookupService>();

            var lookupResponse = await lookupService.LookupInfoSecUserAsync(userEmail);

            if (lookupResponse.Code is not "200")
            {
                ctx.Fail("Invalid Token");
                return;
            }

            var roles = JsonConvert.DeserializeObject<AuthClaimRole>(lookupResponse.Data!.TokenData.Role);
            var permissions = roles?.Permissions ?? new List<string>();

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, lookupResponse.Data!.TokenData.Email),
                new(ClaimTypes.Name, lookupResponse.Data!.TokenData.UserName),
                new(ClaimTypes.Email, lookupResponse.Data!.TokenData.Email),
                new("permissions", string.Join(",", permissions)),
                new(ClaimTypes.Authentication, ctx.HttpContext.Request.Headers.Authorization[0]?.Split(' ')[1]!),
                new(ClaimTypes.AuthenticationMethod, CommonConstants.AuthScheme.Bearer),
            };

            var identity = new ClaimsIdentity(claims, JwtBearerDefaults.AuthenticationScheme);

            ctx.Principal = new ClaimsPrincipal(identity);

            ctx.Success();
        };
    }

    public static IServiceCollection AddHubtelBasicAuth(this IServiceCollection services)
    {
        services.AddAuthentication(CommonConstants.AuthScheme.Basic)
            .AddScheme<AuthenticationSchemeOptions, BasicAuthHandler>(CommonConstants.AuthScheme.Basic, null);

        return services;
    }

    public static IServiceCollection AddHubtelActorSystem(
        this IServiceCollection services, string actorSystemName)
    {
        services.AddAkka(actorSystemName, (builder) =>
        {
            builder.ConfigureLoggers(loggerConfig =>
            {
                loggerConfig.ClearLoggers();
                loggerConfig.AddLoggerFactory();
            });
            
            builder.WithActors(((system, registry, resolver) =>
            {
                var defaultStrategy = new OneForOneStrategy(
                    3, TimeSpan.FromSeconds(3), ex =>
                    {
                        if (ex is not ActorInitializationException)
                            return Directive.Resume;

                        system.Terminate().Wait(1000);

                        return Directive.Stop;
                    });
                var mainActorProps = resolver
                    .Props<MainActor>()
                    .WithSupervisorStrategy(defaultStrategy);
                var mainActor = system.ActorOf(mainActorProps, nameof(MainActor));
                registry.Register<MainActor>(mainActor);
            }));
        });

        return services;
    }

    public static IServiceCollection AddHubtelGelfLogging(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddLogging(loggingBuilder => loggingBuilder.AddGelf(options =>
        {
            options.AdditionalFields = new Dictionary<string, object?>
            {
                { "facility", configuration.GetSection(LoggingSection)["GELF:Facility"] ?? "Hubtel.CCI.Api" },
                { "Environment", configuration.GetSection(LoggingSection)["GELF:Environment"] ?? "Development" },
                { "machine_name", Environment.MachineName }
            };
            options.Host = configuration.GetSection(LoggingSection)["GELF:Host"];
            options.LogSource = configuration.GetSection(LoggingSection)["GELF:LogSource"];
            options.Port = int.Parse(configuration.GetSection(LoggingSection)["GELF:Port"] ?? "12202");
        }));
        return services;
    }

    public static IServiceCollection AddHubtelSwaggerGen(
        this IServiceCollection services,
        IConfiguration configuration,
        string schemeName)
    {
        services.Configure<ApiDocsConfig>(c => configuration.GetSection(nameof(ApiDocsConfig)).Bind(c));

        services.ConfigureOptions<ConfigureSwaggerOptions>();

        var projName = Assembly.GetExecutingAssembly().GetName().Name;

        services.AddSwaggerGen(c =>
        {
            c.EnableAnnotations();

            c.AddSecurityDefinition(schemeName, new()
            {
                Description = $@"Enter '[schemeName]' [space] and then your token in the text input below.<br/>
                      Example: '{schemeName} 12345abcdef'",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = schemeName
            });
            
            c.CustomSchemaIds(type => type.FullName);

            c.AddSecurityRequirement(new()
            {
                {
                    new()
                    {
                        Reference = new()
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = schemeName
                        },
                        Scheme = "oauth2",
                        Name = schemeName,
                        In = ParameterLocation.Header,
                    },
                    Array.Empty<string>()
                }
            });

            c.DocumentFilter<AdditionalParametersDocumentFilter>();

            c.ResolveConflictingActions(descriptions => { return descriptions.First(); });

            var xmlFile = $"{projName}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            c.IncludeXmlComments(xmlPath);
            c.OperationFilter<JsonPatchDocumentFilter>();
        });

        return services;
    }

    public static IServiceCollection AddHubtelApiVersioning(this IServiceCollection services, int version)
    {
        services.AddApiVersioning(opt =>
        {
            opt.DefaultApiVersion = new ApiVersion(version, 0);
            opt.AssumeDefaultVersionWhenUnspecified = true;
            opt.ReportApiVersions = true;
            opt.ApiVersionReader = ApiVersionReader.Combine(
                new UrlSegmentApiVersionReader(),
                new HeaderApiVersionReader("x-api-version"),
                new MediaTypeApiVersionReader("x-api-version"));
        });

        services.AddVersionedApiExplorer(setup =>
        {
            setup.GroupNameFormat = "'v'VVV";
            setup.SubstituteApiVersionInUrl = true;
        });

        return services;
    }

    public static IServiceCollection AddHubtelControllers(this IServiceCollection services)
    {
        services.Configure<RouteOptions>(options => options.LowercaseUrls = true);

        services.AddControllers(options =>
            {
                options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;
                options.Filters.Add<ActorExceptionFilter>();
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.WriteIndented = true;
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            })
            .ConfigureApiBehaviorOptions(options =>
            {
                options.InvalidModelStateResponseFactory = InvalidModelStateHandler;
            });

        static IActionResult InvalidModelStateHandler(ActionContext context)
        {
            return new BadRequestObjectResult(new ApiResponse<object>(
                Message: "Validation Errors",
                Code: 400,
                Errors: context.ModelState
                    .Where(modelError => modelError.Value?.Errors.Count > 0)
                    .Select(modelError => new ErrorResponse(
                        Field: modelError.Key,
                        ErrorMessage: modelError.Value?.Errors.FirstOrDefault()?.ErrorMessage ?? "Invalid Request"))));
        }

        return services;
    }


    public static IServiceCollection AddAppOpentelemtry(this IServiceCollection services, IConfiguration configuration)
    {
        var otelConfig = configuration.GetSection(nameof(OpenTelemetryConfig)).Get<OpenTelemetryConfig>();
        Sdk.SetDefaultTextMapPropagator(new CompositeTextMapPropagator(
        [
            new TraceContextPropagator(),
            new BaggagePropagator(),
            new B3Propagator()
        ]));

        var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")!;
        services.AddOpenTelemetry()
            .ConfigureResource(resourceBuilder =>
            {
                resourceBuilder.AddService(TelemetryConstants.ServiceName);
                resourceBuilder.AddAttributes([
                    new KeyValuePair<string, object>("deployment.environment", env.ToLower())
                ]);
            })
            .WithTracing((builder) =>
            {
                if (otelConfig!.ShowConsoleTrace)
                {
                    builder.AddConsoleExporter();
                }

                builder.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation(
                        options =>
                        {
                            options.FilterHttpRequestMessage =
                                (httpRequestMessage) =>
                                    !httpRequestMessage.RequestUri!.Host.Contains("visualstudio.com",
                                        StringComparison.OrdinalIgnoreCase);
                            options.RecordException = true;
                        })
                    .AddRedisInstrumentation(options =>
                    {
                        options.FlushInterval = TimeSpan.FromSeconds(2);
                        options.SetVerboseDatabaseStatements = true;
                        options.EnrichActivityWithTimingEvents = true;
                    })
                    .ConfigureRedisInstrumentation((provider, redisInstrumentation) =>
                    {
                        var keys = services
                            .Where(sd => sd.IsKeyedService && sd.ServiceType == typeof(IConnectionMultiplexer))
                            .Select(d => d.ServiceKey)
                            .ToList();
                        if (keys.Count == 0) return;
                        foreach (var connection in
                                 keys.Select(provider.GetRequiredKeyedService<IConnectionMultiplexer>))
                        {
                            redisInstrumentation.AddConnection(connection);
                        }
                    })
                    .AddConfluentKafkaInstrumentation()
                    .AddEntityFrameworkCoreInstrumentation(options => { options.SetDbStatementForText = true; })
                    .AddNpgsql()
                    .AddElasticsearchClientInstrumentation()
                    .AddRabbitMQInstrumentation()
                    .AddSource(TelemetryConstants.ServiceName)
                    .AddOtlpExporter(options =>
                    {
                        options.Endpoint = new Uri($"{otelConfig.Protocol}://{otelConfig.Host}:{otelConfig.Port}");
                    });
            }).WithMetrics(metrics =>
            {
                if (otelConfig!.ShowConsoleMetrics)
                {
                    metrics.AddConsoleExporter();
                }

                metrics.AddOtlpExporter(options =>
                {
                    options.Endpoint = new Uri($"{otelConfig!.Protocol}://{otelConfig.Host}:{otelConfig.Port}");
                });
                metrics.AddHttpClientInstrumentation()
                    .AddAspNetCoreInstrumentation()
                    .AddMeter("Microsoft.AspNetCore.Hosting",
                        "Microsoft.AspNetCore.Diagnostics",
                        "Microsoft.EntityFrameworkCore",
                        TelemetryConstants.ServiceName);
            }).WithLogging(logging =>
            {
                logging.AddOtlpExporter(exporterOptions =>
                {
                    exporterOptions.Endpoint =
                        new Uri($"{otelConfig!.Protocol}://{otelConfig.Host}:{otelConfig.Port}");
                });
            });
        return services;
    }

    public static void AddCustomMapsterMappings(this IServiceCollection services)
    {
        TypeAdapterConfig<IssueTracker, CreateIssueTrackerResponse>.NewConfig()
            .Map(dest => dest.Tool, src => src.Tool.Name);

        TypeAdapterConfig<IssueTracker, GetIssueTrackerResponse>.NewConfig()
            .Map(dest => dest.Tool, src => src.Tool.Name).TwoWays();

        TypeAdapterConfig<CreateProductGroupRequest, ProductGroup>.NewConfig()
            .Map(src => src.Supervisors.Items, dest => dest.Supervisors)
            .Map(src => src.ProductTeams.Items, dest => dest.ProductTeams);

        TypeAdapterConfig<CreateToolRequest,ToolingTool>.NewConfig()
            .Map(dest => dest.LatestVersion, src => src.Version);

        TypeAdapterConfig<CreateToolResponse, ToolingTool>.NewConfig()
            .Map(dest => dest.LatestVersion, src => src.Version);

        TypeAdapterConfig<ToolingTool, GetToolResponse>.NewConfig()
            .Map(dest => dest.Version, src => src.LatestVersion);

        TypeAdapterConfig<UpdateToolRequest, ToolingTool>.NewConfig()
           .Map(dest => dest.LatestVersion, src => src.Version);

        TypeAdapterConfig<ToolingTool, UpdateToolResponse>.NewConfig()
           .Map(dest => dest.Version, src => src.LatestVersion);

        TypeAdapterConfig<UpdateIssueTrackerRequest, IssueTracker>.NewConfig()
            .Ignore(t => t.Tool);

        TypeAdapterConfig<ToolingReport, GetToolingReportsResponse>.NewConfig()
            .Map(dest => dest.GeneralOverview, src => src.GeneralOverview)
            .Map(dest => dest.ExecutiveSummary, src => src.ExecutiveSummary).TwoWays();





    }
}