using System.Security.Claims;
using Hubtel.CCI.Api.Dtos.Models;

namespace Hubtel.CCI.Api.Extensions;

public static class UserIdentityPrincipalExtensions
{
    public static AuthClaim GetAccount(this ClaimsPrincipal principal)
    {
        if (principal?.Identity is not ClaimsIdentity { IsAuthenticated: true } identity)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = identity.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var fullName = identity.FindFirst(ClaimTypes.Name)?.Value;
        var email = identity.FindFirst(ClaimTypes.Email)?.Value;
        var authentication = identity.FindFirst(ClaimTypes.Authentication)?.Value;
        var authenticationMethod = identity.FindFirst(ClaimTypes.AuthenticationMethod)?.Value;
        var permissions = identity.FindFirst(c => c.Type == "permissions")?.Value.Split(',').ToList();

        if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(email))
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        return new AuthClaim()
        {
            Id = userId,
            FullName = fullName ?? string.Empty,
            EmailAddress = email,
            Authentication = authentication ?? string.Empty,
            AuthenticationMethod = authenticationMethod ?? string.Empty,
            Permissions = permissions ?? ["dev-docs.read"]
        };
    }
}