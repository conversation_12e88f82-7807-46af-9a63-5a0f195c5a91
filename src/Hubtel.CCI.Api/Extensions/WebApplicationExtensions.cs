using System.Reflection;
using Akka.Actor;
using Hubtel.CCI.Api.Repositories;
using Hubtel.CCI.Api.Options;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.SwaggerUI;
using Hubtel.CCI.Api.Data;

namespace Hubtel.CCI.Api.Extensions;

public static class WebApplicationExtensions
{
    public static void UseHubtelActorSystem(this WebApplication app)
    {
        var actorSys = app.Services.GetRequiredService<ActorSystem>();

        _ = actorSys ?? throw new ArgumentNullException(nameof(app));
    }

    public static void UseHubtelSwagger(this WebApplication app)
    {
        var apiDocsConfig = app.Services.GetRequiredService<IOptions<ApiDocsConfig>>().Value;

        var apiVersionDescription = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

        if (apiDocsConfig.ShowSwaggerUi)
        {
            ShowSwagger(app, apiVersionDescription, apiDocsConfig);
        }

        if (!apiDocsConfig.ShowRedocUi) return;

        ShowRedoc(app, apiVersionDescription);
    }

    private static void ShowRedoc(WebApplication app, IApiVersionDescriptionProvider apiVersionDescription)
    {
        var groupNames = apiVersionDescription.ApiVersionDescriptions
            .Reverse()
            .Select(description => description.GroupName);

        foreach (var groupName in groupNames)
        {
            app.UseReDoc(options =>
            {
                options.DocumentTitle = Assembly.GetExecutingAssembly().GetName().Name;
                options.RoutePrefix = $"api-docs-{groupName}";
                options.SpecUrl = $"/swagger/{groupName}/swagger.json";
            });
        }
    }

    private static void ShowSwagger(WebApplication app, IApiVersionDescriptionProvider apiVersionDescription,
        ApiDocsConfig apiDocsConfig)
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            var projName = Assembly.GetExecutingAssembly().GetName().Name;
            var groupNames = apiVersionDescription.ApiVersionDescriptions
                .Reverse()
                .Select(description => description.GroupName);

            foreach (var groupName in groupNames)
            {
                c.SwaggerEndpoint(
                    $"/swagger/{groupName}/swagger.json",
                    $"{projName} - {groupName}");
            }

            var submitMethods = Array.Empty<SubmitMethod>();

            if (apiDocsConfig.EnableSwaggerTryIt)
            {
                submitMethods = new SubmitMethod[]
                {
                    SubmitMethod.Post,
                    SubmitMethod.Get,
                    SubmitMethod.Put,
                    SubmitMethod.Patch,
                    SubmitMethod.Delete,
                };
            }

            c.SupportedSubmitMethods(submitMethods);
        });
    }

    public static async Task RunMigrationsAsync(this WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        var services = scope.ServiceProvider;
        try
        {
            var storageContext = services.GetRequiredService<ApplicationDbContext>();
            var pendingMigrations = await storageContext.Database.GetPendingMigrationsAsync();
            var count = pendingMigrations.Count();
            if (count > 0)
            {
                logger.LogInformation("found {Count} pending migrations to apply. will proceed to apply them", count);
                await storageContext.Database.MigrateAsync();
                logger.LogInformation("done applying pending migrations");
            }
            else
            {
                logger.LogInformation("no pending migrations found! :)");
            }

            //SeedDb(storageContext)
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while performing migration.");
        }
    }
}