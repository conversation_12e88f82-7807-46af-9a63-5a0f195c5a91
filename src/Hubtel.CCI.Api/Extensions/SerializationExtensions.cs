using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using StackExchange.Redis;

namespace Hubtel.CCI.Api.Extensions;

public static class SerializationExtensions
{
    public static string Serialize<T>(this T @object, JsonSerializerSettings? settings = null)
        where T : notnull
    {
        settings ??= new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.Indented,
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        };
        return JsonConvert.SerializeObject(@object, settings);
    }


    public static T? Deserialize<T>(this string json, JsonSerializerSettings? settings = null)
    {
        if (string.IsNullOrEmpty(json))
            return default;

        return JsonConvert.DeserializeObject<T>(json, settings);
    }

    public static T? Deserialize<T>(this RedisValue json, JsonSerializerSettings? settings = null)
    {
        if (json.IsNull || !json.HasValue)
            return default;

        return JsonConvert.DeserializeObject<T>(json!, settings);
    }

    public static IEnumerable<T> Deserialize<T>(this RedisValue[]? jsonArray, JsonSerializerSettings? settings = null)
    {
        if (jsonArray is null || jsonArray.Length == 0)
            return [];

        var result = new List<T>(jsonArray.Length);

        result.AddRange((from jsonItem in jsonArray
            where jsonItem is { IsNull: false, HasValue: true }
            select Deserialize<T>(jsonItem, settings)));

        return result;
    }
}