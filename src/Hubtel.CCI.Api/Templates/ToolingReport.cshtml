@model Hubtel.CCI.Api.Data.Entities.ToolingReport

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tooling Assessment Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
            margin: 0;
            padding: 2rem;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .section {
            margin-bottom: 1.5rem;
            border: 1px solid #f3f4f6;
            padding: 1.5rem;
            border-radius: 12px;
        }

            .section:first-child {
                border-top: none;
                padding-top: 0;
            }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            margin-top: 0px;
            display: flex;
            align-items: center;
            color: #343a40;
        }

            .section-title i {
                margin-right: 0.75rem;
                color: #495057;
            }

        .section.overview {
            background-color: #eff6ff;
            border: 1px solid #dbeafe;
            border-radius: 12px;
            padding: 24px;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
        }

        .overview-item {
            background-color: #ffffff;
            padding: 1.5rem;
            border-radius: 8px;
        }

            .overview-item .label {
                font-size: 0.9rem;
                color: #6c757d;
                margin: 0 0 0.5rem 0;
            }

            .overview-item .value {
                font-size: 1.25rem;
                font-weight: 600;
                color: #212529;
                margin: 0;
            }

        .divider {
            margin-block: 16px;
            height: 1px;
            background-color: rgb(229 231 235);
        }

        .summary-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 0.75rem;
            color: #343a40;
        }

        .summary-content ul {
            list-style: none;
            padding-left: 1.25rem;
            margin: 0;
            color: #495057;
        }

            .summary-content ul li {
                position: relative;
                padding-left: 0.25rem;
                margin-bottom: 0.5rem;
            }

                .summary-content ul li::before {
                    content: "•";
                    position: absolute;
                    left: -1.25rem;
                    color: #6c757d;
                    font-weight: bold;
                }

        .shadow-sm {
            --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
        }

        .metric-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #f3f4f6;
        }

        .metric-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            color: #6c757d;
        }

        .metric-icon {
            font-size: 1.125rem;
        }

        .metric-card .value {
            font-size: 2rem;
            font-weight: 700;
            color: #212529;
            margin: 0;
        }

        .metric-card .sub-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin: 0.25rem 0 0 0;
        }

        .status-table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }

            .status-table th,
            .status-table td {
                padding: 1rem 0.5rem;
                border-bottom: 1px solid #e9ecef;
            }

            .status-table thead th {
                font-weight: 600;
                font-size: 0.8rem;
                text-transform: uppercase;
                color: #6c757d;
            }

            .status-table tbody td {
                color: #495057;
            }

        .status-critical {
            background-color: #fdecec;
            color: #c51f1f;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-weight: 500;
            font-size: 0.85rem;
        }

        .issues-list {
            list-style-type: disc;
            padding-left: 1.25rem;
            margin-bottom: 2rem;
        }

            .issues-list li {
                position: relative;
                margin-bottom: 0.5rem;
                color: #495057;
            }

        h3.security-issues-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #343a40;
        }

        .issue-item {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

            .issue-item h4 {
                font-size: 1rem;
                font-weight: 600;
                margin: 0 0 0.25rem 0;
            }

            .issue-item .description {
                color: #495057;
                margin: 0 0 1rem 0;
            }

        .issue-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

            .issue-details .affects {
                color: #6c757d;
            }

            .issue-details .projects {
                color: #495057;
                text-align: right;
            }

        .recommendation-list {
            list-style: none;
            padding-left: 1.5rem;
        }

            .recommendation-list li {
                position: relative;
                padding-left: 0.25rem;
                margin-bottom: 0.5rem;
                color: #495057;
            }

                .recommendation-list li::before {
                    content: "•";
                    position: absolute;
                    left: -1.5rem;
                    color: #28a745;
                    font-weight: bold;
                }

        .recommendation-text {
            color: #495057;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
          body {
            padding: 1rem;
          }

        .container {
            padding: 1.5rem;
        }

        .issue-details {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .issue-details .projects {
                text-align: left;
            }

    </style>
</head>
<body>
    <div class="container">
        <!-- Overview -->
        <div class="section overview">
            <h2 class="section-title">🏢 Overview</h2>
            <div class="overview-grid">
                <div class="overview-item">
                    <p class="label">Product Group</p>
                    <p class="value">@Model.ProductGroupName</p>
                </div>
                <div class="overview-item">
                    <p class="label">Assessment Date</p>
                    <p class="value">@Model.CreatedAt.ToShortDateString()</p>
                </div>
                <div class="overview-item">
                    <p class="label">Projects</p>
                    <p class="value">@Model.GeneralOverview.Projects</p>
                </div>
                <div class="overview-item">
                    <p class="label">Repositories</p>
                    <p class="value">@Model.GeneralOverview.Repositories</p>
                </div>
                <div class="overview-item">
                    <p class="label">Issues</p>
                    <p class="value">@Model.GeneralOverview.TotalIssues</p>
                </div>
                <div class="overview-item">
                    <p class="label">Security Issues</p>
                    <p class="value">@Model.GeneralOverview.SecurityIssues</p>
                </div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="section">
            <h2 class="section-title">📄 Executive Summary</h2>
            <div class="summary-content">
                <div>
                    <h4>Current State</h4>
                    <ul>
                        @foreach (var item in Model.ExecutiveSummary.CurrentState)
                        {
                            <li>@item</li>
                        }
                    </ul>
                </div>
                <div class="divider"></div>
                <div>
                    <h4>Key Issues</h4>
                    <ul>
                        @foreach (var item in Model.ExecutiveSummary.KeyIssues)
                        {
                            <li>@item</li>
                        }
                    </ul>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="section">
            <h2 class="section-title">📊 Key Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">
                        <p class="label">REPOSITORIES</p>
                        <span class="metric-icon">📁</span>
                    </div>
                    <p class="value">@Model.KeyMetricsOverview.Repositories</p>
                    <p class="sub-label">Azure DevOps</p>
                </div>
                <div class="metric-card">
                    <div class="metric-title">
                        <p class="label">Active Projects</p>
                        <span class="metric-icon">⚙️</span>
                    </div>
                    <p class="value">@Model.KeyMetricsOverview.ActiveProjects</p>
                    <p class="sub-label">Product Domains</p>
                </div>
                <div class="metric-card">
                    <div class="metric-title">
                        <p class="label">Critical Issues</p>
                        <span class="metric-icon">🚨</span>
                    </div>
                    <p class="value">@Model.KeyMetricsOverview.CriticalIssues</p>
                    <p class="sub-label">Security Vulnerabilities</p>
                </div>
            </div>
        </div>

        <!-- Detailed Project Status -->
        <div class="section">
            <h2 class="section-title">🔎 Detailed Project Status</h2>
            <table class="status-table">
                <thead>
                    <tr>
                        <th>Tool</th>
                        <th>Version</th>
                        <th>Projects</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.DetailedProjectStatus)
                    {
                        <tr>
                            <td>@item.Tool</td>
                            <td>@item.Version</td>
                            <td>@item.Projects</td>
                            <td><span class="status-critical">@item.Status</span></td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        <!-- Issues Summary -->
        <div class="section">
            <h2 class="section-title">❗️ Issues Summary</h2>
            <ul class="issues-list">
                @foreach (var item in Model.IssuesSummary.IssuesOverview)
                {
                    <li>@item</li>
                }
            </ul>

            <h3 class="security-issues-title">Security Issues (@Model.IssuesSummary.SecurityIssues.Count)</h3>
            @foreach (var item in Model.IssuesSummary.SecurityIssues)
            {
                <div class="issue-item">
                    <h4>@item.Title</h4>
                    <p class="description">@item.Description</p>
                    <div class="issue-details">
                        <span class="affects">Affect @item.CountOfAffectedServices projects</span>
                        <span class="projects">@string.Join(", ", item.AffectedServices)</span>
                    </div>
                </div>
            }
        </div>

        <!-- Recommendation -->
        <div class="section">
            <h2 class="section-title">✅ Recommendation</h2>
            <p class="recommendation-text">@Model.Recommendation.Overview</p>
            <div class="summary-content">
                <div>
                    <h3>Expected Benefits</h3>
                    <ul class="recommendation-list">
                        @foreach (var item in Model.Recommendation.ExpectedBenefits)
                        {
                            <li>@item</li>
                        }
                    </ul>
                </div>
                <div class="divider"></div>
                <div>
                    <h3>Success Metrics</h3>
                    <ul class="recommendation-list">
                        @foreach (var item in Model.Recommendation.SuccessMetrics)
                        {
                            <li>@item</li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
