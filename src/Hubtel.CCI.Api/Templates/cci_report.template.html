<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Team Dashboard PDF Render</title>
    <!--    <meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <meta name="viewport" content="width=1200">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            width: 100%;
            /* Set a fixed width for PDF generation */
            width: 210mm;
        }
        @page {
            size: A4 portrait;
            margin: 20mm;
        }

        @media print {
            .max-w-6xl {
                max-width: none !important;
            }

            body {
                width: 100%;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
        /* Disable responsive behavior for PDF */
        .container-pdf {
            width: 100%;
            max-width: 100% !important;
        }
        .grid-cols-pdf {
            grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
        }
    </style>
    <script>
        /* INJECT_CHART_DATA */

        // This will be replaced with actual data by C# code
    </script>
</head>
<body class="bg-gray-50 p-6">
<div class="space-y-6 max-w-6xl mx-auto">
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 p-6">
        <div class="flex items-center mb-4">
            <span class="text-2xl mr-3">🏢</span>
            <h2 class="text-xl font-semibold text-gray-900">Team Overview</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white rounded-lg p-4 shadow-sm">
                <p class="text-sm font-medium text-gray-500 mb-1">Product Team</p>
                <p class="text-lg font-semibold text-gray-900">{{TeamName}}</p>
            </div>
            <div class="bg-white rounded-lg p-4 shadow-sm">
                <p class="text-sm font-medium text-gray-500 mb-1">Product Group</p>
                <p class="text-lg font-semibold text-gray-900">{{ProductGroupName}}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-6">
            <span class="text-2xl mr-3">🎨</span>
            <h3 class="text-xl font-semibold text-gray-900 capitalize">{{Scope}} Metrics</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">CCI Average</h3>
                    <span class="text-lg">📈</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{CCIAverage}}</p>
                </div>
                <div class="mt-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border text-blue-600 bg-blue-50 border-blue-200">⭐ High Confidence</span>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Rank</h3>
                    <span class="text-lg">🏆</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">#{{Rank}}</p>
                </div>
                <p class="mt-1 text-sm text-gray-500">of {{ProductCount}} products</p>
            </div>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Code Coverage</h3>
                    <span class="text-lg">🛡️</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{CodeCoverage}}%</p>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Duplication</h3>
                    <span class="text-lg">📋</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{Duplication}}%</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Bugs</h3>
                    <span class="text-lg">🐛</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{Bugs}}</p>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Code Smells</h3>
                    <span class="text-lg">👃</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{CodeSmells}}</p>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Vulnerabilities</h3>
                    <span class="text-lg">🔒</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{Vulnerabilities}}</p>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-600">Total Lines of Code</h3>
                    <span class="text-lg">📝</span>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-gray-900">{{Tloc}}</p>
                </div>
            </div>
        </div>

        <div class="mt-8">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Historical Performance - {{Scope}}</h4>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Past 8 Days</h5>
                    <div class="h-48">
                        <canvas id="chart-days" width="400" height="140"></canvas>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Past 8 Weeks</h5>
                    <div class="h-48">
                        <canvas id="chart-weeks" width="400" height="140"></canvas>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Past 8 Months</h5>
                    <div class="h-48">
                        <canvas id="chart-months" width="400" height="140"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Code Growth - {{Scope}}</h4>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Past 8 Days</h5>
                    <div class="h-48">
                        <canvas id="cg-chart-days" width="400" height="140"></canvas>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Past 8 Weeks</h5>
                    <div class="h-48">
                        <canvas id="cg-chart-weeks" width="400" height="140"></canvas>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Past 8 Months</h5>
                    <div class="h-48">
                        <canvas id="cg-chart-months" width="400" height="140"></canvas>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<script>
    window.chartsRendered = false;

    function createLineChart(canvasId, data, labels, title) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: title,
                    data: data,
                    borderColor: '#4f46e5',
                    backgroundColor: 'rgba(79, 70, 229, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: '#4f46e5',
                    pointRadius: 3,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: true,
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: '#f3f4f6'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Historical Performance Charts
            createLineChart('chart-days', serverData.Days.Data, serverData.Days.Labels, 'Daily Performance');
            createLineChart('chart-weeks', serverData.Weeks.Data, serverData.Weeks.Labels, 'Weekly Performance');
            createLineChart('chart-months', serverData.Months.Data, serverData.Months.Labels, 'Monthly Performance');

            // Code Growth Charts
            createLineChart('cg-chart-days', serverData.CodeGrowth.Days.Data, serverData.CodeGrowth.Days.Labels, 'Daily Code Growth');
            createLineChart('cg-chart-weeks', serverData.CodeGrowth.Weeks.Data, serverData.CodeGrowth.Weeks.Labels, 'Weekly Code Growth');
            createLineChart('cg-chart-months', serverData.CodeGrowth.Months.Data, serverData.CodeGrowth.Months.Labels, 'Monthly Code Growth');

            window.chartsRendered = true;
        } catch (error) {
            console.error('Chart rendering error:', error);
            window.chartsRendered = true;
        }
    });

    // Fallback in case DOMContentLoaded doesn't fire
    setTimeout(() => {
        if (!window.chartsRendered) {
            console.warn('Fallback chart rendering timeout');
            window.chartsRendered = true;
        }
    }, 5000);
</script>
</body>
</html>