namespace Hubtel.CCI.Api.CommonConstants;

public static class ValidationConstants
{

    public static class EngineerJobLevelTypes
    {
        public const string Executive = "Executive";
        public const string NationalServicePersonnel = "NSS";
        public const string Intern = "Intern";
        public const string Senior = "Senior";
        public const string TeamLead = "Team Lead";
        public const string EngineeringManager = "Engineering Manager";
        public const string HeadOfEngineering = "Head of Engineering";
    }

    public static class AzureProxyRelated
    {
        public const string ReleaseDefinitionId = "ReleaseDefinitionId";
        public const string ReleaseId = "ReleaseId";
        public const string ApiVersion = "api-version";
        public const string Authorization = "Authorization";
        
        public const string Production = "production";
    }

    public static class EngineerDomainTypes
    {
        public const string Backend = "Backend";
        public const string Frontend = "Frontend";
        public const string MlData = "ML & Data";
    }
    
    public static class DeploymentStrategyTypes
    {
        public const string FullyAutomated = "Fully Automated";
        public const string AutomatedPlusManual = "Automated Plus Manual";
        public const string FullyManual = "Fully Manual";
    }
    
    public static class EngineerCompetenceRequirementTypes
    {
    public const string D1 = "D1";
    public const string D2 = "D2";
    public const string D3 = "D3";
    public const string D4 = "D4";
    }
    
    public static class ReceiveMerExposureTypes 
    {
        public const string Limited = "Limited";
        public const string Unlimited = "Unlimited";
    }
    
    public static class SendsMerExposureTypes 
    {
        public const string Limited = "Limited";
        public const string Unlimited = "Unlimited";
    }

    public static class RepositoryServiceStatus
    {
        public const string Retired = "Retired";
        public const string Active = "Active";
        public const string Inactive = "Inactive";
        public const string Legacy = "Legacy";
    }

    public static class RiskClassificationTypes
    {
        public const string High = "High";
        public const string Medium = "Medium";
        public const string Low = "Low";
    }
    public static class EndUsersTypes
    {
        public const string Business = "Business";
        public const string Customer = "Customer";
    }

    public static class RepositoryType
    {
        public const string Backend = "Backend";
        public const string Frontend = "Frontend";
    }
    
    public static class ServiceFacingDirection
    {
        public const string Inward = "Inward";
        public const string Outward = "Outward";
    }

    public static class ProductTeamScopes
    {
        public const string Overall = "Overall";
        public const string Backend = "Backend";
        public const string Frontend = "Frontend";
        public const string Miscellaneous = "Miscellaneous";
    }


    public static class ProductStatus
    {
        public const string Retired = "Retired";
        public const string Active = "Active";
        public const string Legacy = "Legacy";
    }
    
    public static class ServiceType
    {
        public const string Api = "Api";
        public const string Consumer = "Consumer";
        public const string Job = "Job";
        public const string Worker = "Worker";
        public const string Bot = "Bot";
        public const string Unknown = "Unknown";
        public const string WebApp = "WebApp";
    }
    
    public static class ProductTags
    {
        public const string TopPriority = "Top Priority";
        public const string ProductsMakingMoney = "Products Making Money";
        public const string AiLab = "AI Lab";
    }
    
    public static class SonarMetricKeys
    {
        public const string Bugs = "bugs";
        public const string NewBugs = "new_bugs";
        public const string Vulnerabilities = "vulnerabilities";
        public const string NewVulnerabilities = "new_vulnerabilities";
        public const string CodeSmells = "code_smells";
        public const string NewCodeSmells = "new_code_smells";
        public const string SecurityHotspots = "security_hotspots";
        public const string NewSecurityHotspots = "new_security_hotspots";
        public const string DuplicatedLinesDensity = "duplicated_lines_density";
        public const string ReopenedIssues = "reopened_issues";
        public const string SecurityRating = "security_rating";
        public const string ReliabilityRating = "reliability_rating";
        public const string NewCoverage = "new_coverage";
        public const string NewDuplicatedLinesDensity = "new_duplicated_lines_density";
        public const string NewLines = "new_lines";
        public const string Coverage = "coverage";
        public const string CognitiveComplexity = "cognitive_complexity";
        public const string Ncloc = "ncloc";
        public const string Lines = "lines";
        public const string Complexity = "complexity";
    }

    public static class CqtTeams
    {
        public const string CodeQuality = "Code Quality";
        public const string Tooling = "Tooling";
        public const string CiCd = "CI/CD";
    }

    public static class ReportType
    {
        public const string ProductGroupWeeklyReport = "ProductGroupWeeklyReport";
    }
}