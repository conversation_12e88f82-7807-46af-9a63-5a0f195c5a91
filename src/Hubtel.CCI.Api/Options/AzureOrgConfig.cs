namespace Hubtel.CCI.Api.Options;

public class AzureOrgConfig
{
    public string AccessToken { get; set; } = string.Empty;
    
    public string Organization { get; set; } = string.Empty;

    public string DevUrl { get; set; } = string.Empty;
    
    public string ReleaseUrl { get; set; } = string.Empty;
    
    public List<string> Projects { get; set; } = [];
    
    public string ApiVersion { get; set; } = "7.0";
    
    public int DefaultPageSize { get; set; } = 100;
    
    public int RequestTimeoutSeconds { get; set; } = 60;
    
    public string WorkItemToken { get; set; } = string.Empty;
}
