
namespace Hubtel.CCI.Api.Options;


/// <summary>
/// Represents the config option for the Teams Bot App Config
/// </summary>
public class TeamsBotAppConfig
{
    /// <summary>
    /// Gets or sets the Client Id
    /// </summary>
    public string ClientId { get; set; } = null!;

    /// <summary>
    /// Gets or sets the Client Secret
    /// </summary>
    public string ClientSecret { get; set; } = null!;


    /// <summary>
    /// Gets or sets the Tenant Id
    /// </summary>
    public string TenantId { get; set; } = null!;

    /// <summary>
    /// Gets or sets the RedirectUri
    /// </summary>
    public string RedirectUri { get; set; } = null!;

    /// <summary>
    /// Gets or sets the SqBotUser Email
    /// </summary>
    public string SqBotUser { get; set; } = null!;

    /// <summary>
    /// Get or sets the MsGraphMeRequestUr
    /// </summary>
    public string MsGraphMeRequestUri { get; set; } = null!;

    /// <summary>
    /// Get or sets the MsGraphChatRequestUri
    /// </summary>
    public string MsGraphChatRequestUri { get; set; } = null!;


    /// <summary>
    /// Gets or sets the Scopes
    /// </summary>
    public List<string> Scopes { get; set; } = [];
}